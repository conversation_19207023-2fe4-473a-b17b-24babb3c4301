export default {
  common: {
    appName: '数据生产开发系统',
    add: '新增',
    del: '删除',
    edit: '编辑',
    import: '导入',
    export: '导出',
    upload: '上传',
    search: '搜索',
    reset: '重置',
    operation: '操作',
    download: '下载',
    determine: '确认',
    cancel: '取消',
    translate: '翻译',
    layoutSize: '布局大小',
    logoutGo: '确定注销并退出系统吗？',
    logoutTip: '提示',
    delTips: '是否确认删除这些数据项？',
    delSucceed: '删除成功',
    uploadResults: '上传结果',
    preview: '预览',
    synchronization: '同步',
    generateCode: '生成代码',
    more: '更多',
  },
  router: {
    index: '首页',
    profile: '个人中心',
    genEdit: '修改生成配置',
    jobLog: '调度日志',
    formBuild: '表单配置',
    record: '流程处理',
    model: '流程设计',
    data: '字典数据',
    authUser: '分配用户',
    athRole: '分配角色',
    systemmanagement: '系统管理',
    Systemmonitoring: '系统监控',
    SystemTools: '系统工具',
    usermanagement: '用户管理',
    RoleManagement: '角色管理',
    Menumanagement: '菜单管理',
    DepartmentManagement: '部门管理',
    DictionaryManagement: '字典管理',
    Parametersettings: '参数管理',
    NoticeAnnouncement: '通知公告',
    Jobmanagement: '岗位管理',
    LogManagement: '日志管理',
    Onlineusers: '在线用户',
    ScheduledTasks: '定时任务',
    SentinelConsole: 'Sentinel控制台',
    NacosConsole: 'Nacos控制台',
    AdminConsole: 'Admin控制台',
    FormConstruction: '表单构建',
    ProcessMonitoring: '流程监听',
    ProcessExpression: '流程表达式',
    codegeneration: '代码生成',
    systeminterface: '系统接口',
    Operationlog: '操作日志',
    LoginLog: '登录日志',
    ReportDesign: '报表设计',
    organizationalStructure: '组织架构',
    workflowCenter: '工作流中心',
    processDefinition: '流程定义',
    myProcess: '我的流程',
    toDoTasks: '待办任务',
    completedTasks: '已办任务',
    processManagement: '流程管理',
    formConfiguration: '表单配置',
    taskManagement: '任务管理',
    QRcodegeneration: '二维码生成',
    attachmentManagement: '附件管理',
    myAttachment: '我的附件',
    dataroom: '可视化管理',
    largeScreen: '大屏设计器',
    dashboard: '仪表盘设计器',
  },
  login: {
    title: '数据生产开发系统',
    logIn: '登录',
    username: '七位工号',
    password: '密码',
    code: '验证码',
    loginStatus: '登 录 中...',
    rememberMe: '记住密码',
    register: '注册',
    google: '谷歌',
    firefox: '火狐',
    tip: '提示:系统不支持ie访问,推荐:',
    language: '切换语言',
  },
  register: {
    title: '数据生产开发系统',
    login: '使用已有账户登录',
    username: '七位工号',
    password: '密码',
    code: '验证码',
    confirmPassword: '确认密码',
    registerStatus: '注 册 中...',
    rememberMe: '记住密码',
    register: '注册',
    google: '谷歌',
    firefox: '火狐',
    tip: '提示:系统不支持ie访问,推荐:',
    language: '切换语言',
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeAll: '关闭所有'
  },
  settings: {
    drawerTitle: '主题风格设置',
    drawerColor: '主题颜色',
    title: '系统布局配置',
    tagsView: '开启 Tags-View',
    topNav: '开启 TopNav',
    fixedHeader: '固定 Header',
    dynamicTitle: '动态标题',
    sidebarLogo: '侧边栏 Logo',
    userProfile: '个人中心',
    layoutSettings: '布局设置',
    logout: '退出登录',
    saveSetting: '保存配置',
    resetSetting: '重置配置',
  },
  index: {
    introTitle: '公司简介',
    intro1: 'Jove成立于2004年，注册地址为：深圳市宝安区沙井街道和一社区和二工业区兴业西路8号。公司是一家专业从事印制电路板的研发、生产和销售的国家级高新技术企业。公司自创办以来，长期坚持质量为先、技术为核心、客户需求为导向的发展策略，专注于有优势、有前景的细分电路板市场，取得了多项国家技术专利，研发出多种特殊的产品工艺。',
    intro2: '公司目前有三个生产基地，分别位于深圳市宝安区沙井街道、松岗街道以及鹤山市鹤城镇。公司产品广泛应用于通信、新能源、工业控制、消费电子、汽车电子及医疗电子等应用领域，主要客户为：华为、中兴、Vertiv（维谛）、Samsung（三星）、Schneider（施耐德）、Asteelflash（飞旭）、LACROIX、LENZE（伦茨）、理邦等国内外知名企业。',
    intro3: 'Jove始终肩负着“以科技和实业利益社会、富强中国”的时代使命，致力于成为全球电子电路制造行业的先进企业。',
    visitWebsite: '访问官网',
    navigation: '快捷导航',
    deploy: '我的流程',
    todo: '待办任务',
    finished: '已办任务',
    online: '在线用户',
    question: '常见问题',
    question1: 'Q：没有相关权限',
    answer1: '权限严格管控,请发起IT服务单进行申请',
    question2: 'Q：不能查询或导出',
    answer2: '按照要求输入查询条件,必填项等信息,频繁重复点击将限制提示',
    information: '联系信息',
    developer: '开发者：',
    website: '官网：',
    logoTitle: '数据生产开发系统'
  },
  profile: {
    information: '个人信息',
    userName: '用户名称',
    phonenumber: '手机号码',
    email: '用户邮箱',
    deptName: '所属部门',
    roleGroup: '所属角色',
    createTime: '创建日期',
    userinfo: '基本资料',
    resetPwd: '修改密码',
    nickName: '用户昵称',
    sex: '性别',
    male: '男',
    female: '女',
    save: '保存',
    close: '关闭',
    nickNameRule: '用户昵称不能为空',
    emailRule1: '邮箱地址不能为空',
    emailRule2: '请输入正确的邮箱地址',
    phonenumberRule1: '手机号码不能为空',
    phonenumberRule2: '请输入正确的手机号码',
    updateTip: '修改成功',
    oldPassword: '旧密码',
    oldPasswordTip: '请输入旧密码',
    newPassword: '新密码',
    newPasswordTip: '请输入新密码',
    confirmPassword: '确认密码',
    confirmPasswordTip: '请确认密码',
    differentTip: '两次输入的密码不一致',
    oldPasswordRule: '旧密码不能为空',
    newPasswordRule1: '新密码不能为空',
    newPasswordRule2: '长度在 6 到 20 个字符',
    confirmPasswordRule: '确认密码不能为空',
  },
}
