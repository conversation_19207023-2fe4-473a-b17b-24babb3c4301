<template>
  <div
    class="reacquire flex justify-center blocks cursor-pointer"
    :style="{ lineHeight }"
    @click="emit('onclick', $event)"
  >
    <span>重新获取</span>
  </div>
</template>

<script setup>
import { defineEmits } from 'vue'

const props = defineProps({
  lineHeight: {
    type: String,
    default: '200px'
  }
})

const emit = defineEmits(['onclick'])
</script>

<style lang="scss" scoped>
.reacquire {
  user-select: none;
  color: rgb(168, 168, 168);

  span:hover {
    color: $primary-color;
    text-decoration: underline;
  }
}

.blocks {
  width: 100%;
  height: 100%;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
