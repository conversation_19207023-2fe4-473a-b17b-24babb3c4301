<template>
  <div ref="chartEl" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { useTheme } from '@/utils/useTheme'
const { theme } = useTheme()
const labelColor = computed(() => {
  return theme.value === 'dark' ? '#CBDCFF' : '#333333'
})
const chartEl = ref(null)
let chartInstance: echarts.ECharts | null = null

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      xAxis: ['1月', '2月', '3月'],
      series: [
        { name: '数量1', data: [10, 22, 18] },
        { name: '数量2', data: [15, 30, 25] },
      ],
      avgLines: [],
    }),
  },
  currentTheme: {
    type: String,
    default: 'dark',
  },
})

const initChart = () => {
  if (!chartEl.value) return
  chartInstance = echarts.init(chartEl.value)
  updateChart()
}

const calculateAvg = (data) => {
  const sum = data.reduce((a, b) => a + b, 0)
  return (sum / data.length).toFixed(2)
}

const updateChart = () => {
  if (!chartInstance) return

  let seriesData = [...props.data.series]
  // 添加平均线
  props.data.avgLines.forEach((avg) => {
    const avgValue = calculateAvg(avg.values)
    seriesData.push({
      name: `${avg.name}平均值`,
      type: 'line',
      data: new Array(props.data.xAxis.length).fill(avgValue),
      lineStyle: { type: 'dashed', width: 1, color: '#EBEEF5' },
      symbol: 'none',
      silent: true,
      tooltip: { formatter: `{b}: ${avgValue}` },
    })
  })

  chartInstance.setOption({
    xAxis: {
      type: 'category',
      data: props.data.xAxis,
      boundaryGap: false,
      axisLabel: {
        color: labelColor.value,
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        color: labelColor.value,
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 16,
      data: props.data.series.map((item) => item.name),
      top: 0,
      right: 0,
      textStyle: {
        color: labelColor.value,
      },
    },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    series: seriesData.map((item, index) => ({
      ...item,
      type: 'line',
      smooth: true,
    })),
  })
}

onMounted(() => {
  setTimeout(() => {
    initChart()
    window.addEventListener('resize', () => chartInstance?.resize())
  }, 0)
})

onUnmounted(() => {
  window.removeEventListener('resize', () => chartInstance?.resize())
  chartInstance?.dispose()
  chartInstance = null
})

watch(
  () => props.data,
  () => updateChart(),
  { deep: true }
)
watch(
  () => theme,
  () => updateChart(),
  { deep: true }
)
</script>
