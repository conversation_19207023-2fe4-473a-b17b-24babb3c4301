<script setup>
import { VPdfViewer } from "@vue-pdf-viewer/viewer";
import { ref, watch } from "vue";
import { qryTargetFileAttachInfo } from "@/api/smartschedu/plan";
import { getToken } from "@/utils/auth";
import request from "@/utils/request";
const props = defineProps({
  fileUrl: {
    type: String,
    default: "",
  },
});

const fileInfo = ref({});
function getFileInfo() {
  qryTargetFileAttachInfo(props.fileUrl).then(async (res) => {
    if (!res.data?.attachPath) {
      fileInfo.value = "";
      return;
    }
    const url = `${import.meta.env.VITE_APP_BASE_API}/file/statics${
      res.data.attachPath
    }`;
    const response = await fetch(url, {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    });

    const blob = await response.blob();

    fileInfo.value = URL.createObjectURL(blob);
  });
}

watch(
  () => props.fileUrl,
  () => {
    if (fileInfo.value) {
      URL.revokeObjectURL(fileInfo.value);
    }
    getFileInfo();
  }
);

getFileInfo();
</script>
<template>
  <div :style="{ width: '100%', height: '700px' }">
    <VPdfViewer
      :initialScale="1.25"
      :src="fileInfo"
      :render-text-layer="false"
      :render-annotation-layer="false"
    />
  </div>
</template>

<style scoped>
:deep(.vpv-pages-container-wrapper) {
  .vpv-pages-inner-container {
    z-index: 49;
    position: absolute;
    width: 100%;
    background: rgb(173, 171, 171);
  }
}
</style>
