<template>
  <div class="header-wrapper">
    <div class="header-menu">
      <div class="header-menu_current">
        {{ selectedMenu.name }}
      </div>
      <div class="header-menu_change" @click="toggleMenu"></div>
      <ul v-show="isOpenMenu" class="header-menu_menu">
        <li
          v-for="item in menuData"
          :key="item.id"
          class="header-menu_menu_item"
          :class="{
            'header-menu_menu_item_active': selectedMenu.id === item.id,
          }"
          @click="selectMenu(item.path)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <!-- <div class="seat" /> -->
    <div class="header-title">驾驶舱 <span class="header-title_btm" /></div>

    <div class="header-right">
      <slot name="right"></slot>
      <div class="header-types">
        <div
          class="header-types_item"
          :class="{
            'header-types_item_active': item.id === selectedTypes.id,
          }"
          v-for="item in types"
          :key="item.id"
          @click="selectTypes(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="header-date">{{ currentDate }}</div>
      <div class="header-switch">
        <Switch
          v-model="switchValue"
          active-text="浅"
          inactive-text="深"
          @change="handleTheme"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import router from '@/router'
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRoute } from 'vue-router'
import Switch from './components/Switch/index.vue'

const route = useRoute()

const props = defineProps({
  types: {
    type: Array,
    default: () => [
      { id: 1, name: '总览', key: 'all' },
      { id: 2, name: '地表水', key: 'shui' },
      { id: 3, name: '环境空气', key: 'kq' },
    ],
  },
})

const menuData = [
  { name: '智能调度', id: 0, path: '/msscreen/schedu' },
  { name: '监控预警', id: 1, path: '/msscreen/monitor' },
  { name: '数据资源', id: 2 },
]

const emit = defineEmits(['changeType', 'changeTheme'])

const selectedMenu = ref(menuData[0])
const isOpenMenu = ref(false)
const toggleMenu = () => {
  isOpenMenu.value = !isOpenMenu.value
}
const selectMenu = (item) => {
  if (!item) return
  isOpenMenu.value = false
  selectedMenu.value = menuData.filter((o) => o.path === item)[0]
  router.push(item)
}

const selectedTypes = ref(props.types[0])
const selectTypes = (item) => {
  selectedTypes.value = item
  emit('changeType', item)
}

const switchValue = ref(true)
const handleTheme = (v) => {
  emit('changeTheme', v)
}

const currentDate = ref('')
let timer = null
const updateTime = () => {
  const now = new Date()
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  currentDate.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onBeforeUnmount(() => {
  clearInterval(timer)
})

watch(() => route.path, selectMenu, { immediate: true })
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.header-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: url('@/assets/images/schedu/bg_top.png') no-repeat;
  background-size: 100% 100%;
  // display: flex;
  padding: px2rem(12) px2rem(24) 0 px2rem(22);
}

.header-menu {
  width: px2rem(116);
  display: flex;
  float: left;

  &_current {
    width: px2rem(100);
    height: px2rem(28);
    background: url('@/assets/images/schedu/bg_xitongqiehuan.png') no-repeat;
    background-size: 100%;
    font-family: PingFangSC-Semibold;
    font-size: px2rem(16);
    color: #023a93;
    letter-spacing: 0;
    text-align: center;
    line-height: px2rem(24);
    font-weight: 600;
  }
  &_change {
    width: px2rem(16);
    height: px2rem(14);
    margin-top: px2rem(6);
    background: url('@/assets/images/schedu/icon_qiehuan.png') no-repeat;
  }
  ul,
  li {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
  &_menu {
    background: #ffffff;
    box-shadow: 0px px2rem(2) px2rem(4) 0px rgba(0, 0, 0, 0.13);
    border-radius: px2rem(8);
    position: absolute;
    top: px2rem(48);

    &_item {
      width: px2rem(116);
      height: px2rem(48);
      line-height: px2rem(48);
      text-align: center;
      font-family: PingFangSC-Regular;
      font-size: px2rem(16);
      color: #999999;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;

      &_active {
        font-family: PingFangSC-Medium;
        color: #ffffff;
        font-weight: 500;
        background: url('@/assets/images/schedu/bg_qiehuan_sle.png') no-repeat;
        background-size: 100%;
      }
    }
  }
}

// .seat {
//   width: px2rem(1285);
// }

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-family: PingFangSC-Semibold;
  font-size: px2rem(36);
  color: #023a93;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;

  &_btm {
    width: px2rem(62);
    height: px2rem(4);
    background: #1472ff;
    border-radius: 0px 0px px2rem(2.5) px2rem(2.5);
    bottom: px2rem(-10);
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
  }
}

.header-right {
  display: flex;
  float: right;

  .header-types {
    width: px2rem(240);
    height: px2rem(28);
    background: rgba(36, 96, 206, 0.1);
    border-radius: px2rem(14);
    padding: 0 px2rem(16);
    display: flex;
    gap: px2rem(32);
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: px2rem(16);
    color: #999999;
    letter-spacing: 0;
    text-align: center;
    line-height: px2rem(24);
    font-weight: 400;
    margin-right: px2rem(24);

    &_item {
      cursor: pointer;
      white-space: nowrap;
      &_active {
        color: #023a93;
      }
    }

    &_item:not(:last-child)::after {
      content: ' ';
      position: absolute;
      right: px2rem(-16); /* 等于gap值的一半 */
      top: 25%;
      bottom: 25%;
      width: px2rem(1);
      background: red($color: #000000);
    }
  }
  .header-date {
    font-family: PingFangSC-Semibold;
    font-size: px2rem(14);
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
    margin-right: px2rem(24);
    height: px2rem(28);
    line-height: px2rem(28);
  }
  .header-switch {
    width: px2rem(44);
    height: px2rem(22);
    padding-top: px2rem(3);
  }
}
</style>
