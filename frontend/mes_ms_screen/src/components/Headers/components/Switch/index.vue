<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: false,
  },
  activeValue: {
    type: [<PERSON>olean, String, Number],
    default: true,
  },
  inactiveValue: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: false,
  },
  activeText: String,
  inactiveText: String,
  disabled: Boolean,
  loading: Boolean,
  size: {
    type: String,
    validator: (val) => ['small', 'middle', 'large'].includes(val),
    default: 'middle',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const isChecked = ref(props.modelValue === props.activeValue)

watch(
  () => props.modelValue,
  (val) => {
    isChecked.value = val === props.activeValue
  }
)

const handleClick = () => {
  if (props.disabled || props.loading) return

  const newValue = isChecked.value ? props.inactiveValue : props.activeValue
  isChecked.value = !isChecked.value
  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<template>
  <div
    class="switch-wrapper"
    :class="[
      `size-${size}`,
      { 'is-disabled': disabled },
      { 'is-loading': loading },
      { 'is-checked': isChecked },
    ]"
    @click="handleClick"
  >
    <div class="switch-core">
      <span v-if="activeText" class="active-text">{{ activeText }}</span>
      <span v-if="inactiveText" class="inactive-text">{{ inactiveText }}</span>
      <div class="switch-button"></div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.switch-wrapper {
  --active-color: #2460ce;
  --inactive-color: #dcdfe6;
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.switch-core {
  position: relative;
  width: px2rem(44);
  height: px2rem(22);
  border-radius: px2rem(11);
  background-color: var(--inactive-color);
  transition: all 0.3s;
}

.switch-button {
  position: absolute;
  top: 1px;
  left: 2px;
  width: px2rem(22);
  height: px2rem(22);
  border-radius: 50%;
  background-color: #fff;
  transition: all 0.3s;
}

.active-text,
.inactive-text {
  position: absolute;
  font-size: px2rem(14);
  color: #fff;
  user-select: none;
}

.active-text {
  left: px2rem(6);
  display: var(--active-display);
}

.inactive-text {
  right: px2rem(6);
  display: var(--inactive-display);
}

/* 尺寸控制 */
.size-small {
  --button-size: px2rem(12);
}
.size-small .switch-core {
  width: px2rem(30);
  height: px2rem(16);
}

.size-large {
  --button-size: px2rem(20);
}
.size-large .switch-core {
  width: px2rem(50);
  height: px2rem(24);
}

/* 状态控制 */
.is-checked .switch-core {
  background-color: var(--active-color);
}
.is-checked .switch-button {
  left: calc(100% - px2rem(20));
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.is-loading {
  cursor: wait;
}
</style>
