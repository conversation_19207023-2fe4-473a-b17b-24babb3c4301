<template>
  <div class="table-wrapper" ref="wrapperRef">
    <div class="table-wrapper-header">
      <div
        v-for="(col, index) in columns"
        :key="'h' + index"
        class="header-cell"
        :style="{ width: col.width }"
      >
        {{ col.title }}
      </div>
    </div>
    <div class="table-body" ref="bodyRef">
      <div
        v-for="(row, rowIndex) in processedData"
        :key="'r' + rowIndex"
        class="table-row"
        :class="{
          'top1-row': rowIndex === 0,
          'top2-row': rowIndex === 1,
          'top3-row': rowIndex === 2,
        }"
      >
        <div
          v-for="(col, colIndex) in columns"
          :key="'c' + colIndex"
          class="body-cell"
          :style="{ width: col.width }"
        >
          <slot v-if="$slots[col.slot]" :name="col.slot" :row="row" />
          <template v-else>
            <span
              :title="row[col.prop]"
              class="body-cell-first"
              v-if="colIndex === 0"
            >
              {{ row[col.prop] }}
            </span>
            <template v-else>
              <span :title="row[col.prop]">{{ row[col.prop] }}</span>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'

const props = defineProps({
  data: { type: Array, required: true },
  columns: { type: Array, required: true },
})

const wrapperRef = ref(null)
const bodyRef = ref(null)

const processedData = computed(() => {
  return [...props.data]
})

onMounted(() => {
  // const resizeObserver = new ResizeObserver(() => {
  //   if (wrapperRef.value && bodyRef.value) {
  //     const headerHeight = wrapperRef.value.querySelector(
  //       '.table-wrapper-header'
  //     ).offsetHeight
  //     bodyRef.value.style.maxHeight = `${
  //       wrapperRef.value.offsetHeight - headerHeight
  //     }px`
  //   }
  // })
  // resizeObserver.observe(wrapperRef.value)
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.table-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.table-wrapper-header {
  display: flex;
  background: #f5f7fa;
  border: 1px solid rgba(235, 238, 245, 1);
  margin-bottom: px2rem(8);
  height: px2rem(40);
  align-items: center;
  font-family: PingFangSC-Medium;
  font-size: px2rem(14);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

.table-body {
  flex: 1;
  overflow-y: auto;
  display: block;
}

.table-row {
  display: flex;
  background: rgba(20, 114, 255, 0.05);
  margin-bottom: px2rem(8);
  height: px2rem(40);
  line-height: px2rem(40);
  align-items: center;
  border: 1px solid transparent;
  border-image: linear-gradient(to right, #1472ff, #fff) 1;
  &:last-child {
    margin-bottom: 0;
  }
}

// .table-row > .body-cell:first-child {
//   width: px2rem(60) !important;
//   height: px2rem(20);
//   padding: 0;
//   margin: px2rem(10) px2rem(6);
//   background: url('@/assets/images/schedu/top_lan.png') no-repeat;
//   text-align: center;
//   font-family: PingFangSC-Medium;
//   font-size: px2rem(12);
//   color: #f4f7ff;
//   letter-spacing: 0;
//   font-weight: 500;
//   line-height: px2rem(20);
//   border-radius: 1px;
// }

// .top1-row > .body-cell:first-child {
//   background: url('@/assets/images/schedu/top_1.png') no-repeat;
// }

// .top2-row > .body-cell:first-child {
//   background: url('@/assets/images/schedu/top_2.png') no-repeat;
// }

// .top3-row > .body-cell:first-child {
//   background: url('@/assets/images/schedu/top_3.png') no-repeat;
// }

.table-row > .body-cell {
  font-family: PingFangSC-Regular;
  font-size: px2rem(14);
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-cell,
.body-cell {
  // padding: px2rem(16);
  flex-shrink: 0;
}

.top1-row {
  border-image: linear-gradient(to right, #ff1414, #fff) 1;
  background: rgba(255, 20, 20, 0.05);
}
.top2-row {
  border-image: linear-gradient(to right, #ff7514, #fff) 1;
  background: rgba(255, 121, 20, 0.05);
}
.top3-row {
  border-image: linear-gradient(to right, #35d4bc, #fff) 1;
  background: rgba(20, 255, 123, 0.05);
}
[data-theme='dark'] .table-wrapper {
  .table-wrapper-header {
    background: linear-gradient(
      90deg,
      rgba(130, 180, 255, 0.14) 0%,
      rgba(130, 180, 255, 0.05) 100%
    );
    border-image: linear-gradient(to right, #295dc2, rgba(41, 93, 194, 0.13)) 1;
    color: #cbdcff;
  }
  .table-row > .body-cell {
    color: #cbdcff;
  }
}
</style>
