<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

// type ChartType = 'line' | 'bar'
// interface SeriesData {
//   name: string
//   data: number[]
//   type?: ChartType
//   yAxisIndex?: number
//   smooth?: boolean
//   barWidth?: string
//   itemStyle?: { color?: string }
// }

interface Props {
  chartData: Object
}

const props = withDefaults(defineProps<Props>(), {})

const chartRef = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({ ...props.chartData })
}

const handleResize = () => {
  chartInstance?.resize()
}

onMounted(() => {
  setTimeout(() => {
    initChart()
    // window.addEventListener('resize', handleResize)
  }, 0)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
  chartInstance = null
})

watch(
  () => props.chartData,
  () => updateChart(),
  { deep: true }
)
</script>