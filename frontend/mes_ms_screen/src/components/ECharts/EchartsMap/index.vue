<template>
  <div class="map-wrapper">
    <el-button v-if="showChina" @click="goBack" class="china-btn">中国</el-button>
    <div class="map-container" ref="mapContainerRef"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, onUnmounted } from "vue";
import { init, registerMap } from "echarts";

const geoJsonMap = {
  安徽: "an_hui_geo.json",
  澳门: "ao_men_geo.json",
  北京: "bei_jing_geo.json",
  重庆: "chong_qing_geo.json",
  福建: "fu_jian_geo.json",
  甘肃: "gan_su_geo.json",
  广东: "guang_dong_geo.json",
  广西: "guang_xi_geo.json",
  贵州: "gui_zhou_geo.json",
  海南: "hai_nan_geo.json",
  河北: "he_bei_geo.json",
  河南: "he_nan_geo.json",
  黑龙江: "hei_long_jiang_geo.json",
  湖北: "hu_bei_geo.json",
  湖南: "hu_nan_geo.json",
  吉林: "ji_lin_geo.json",
  江苏: "jiang_su_geo.json",
  江西: "jiang_xi_geo.json",
  辽宁: "liao_ning_geo.json",
  内蒙古: "nei_meng_gu_geo.json",
  宁夏: "ning_xia_geo.json",
  青海: "qing_hai_geo.json",
  山东: "shan_dong_geo.json",
  山西: "shan_xi_2_geo.json", // 注意：山西省使用1号文件
  陕西: "shan_xi_1_geo.json", // 注意：陕西省使用2号文件
  上海: "shang_hai_geo.json",
  四川: "si_chuan_geo.json",
  台湾: "tai_wan_geo.json",
  天津: "tian_jin_geo.json",
  西藏: "xi_zang_geo.json",
  香港: "xiang_gang_geo.json",
  新疆: "xin_jiang_geo.json",
  云南: "yun_nan_geo.json",
  浙江: "zhe_jiang_geo.json",
  china: "china_geo.json",
};


const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const mapContainerRef = ref(null);
const showChina = ref(false);
let topChartIns = null;
let resizeObserver = null;

const removeChart = () => {
  if (topChartIns) {
    topChartIns.dispose();
    topChartIns = null;
  }
};

const getJsonData = async (value) => {
  const res = await fetch(`/msscreen/geoJson/${value}`);
  const data = await res.json();
  return data;
};

const initTopChart = async () => {
  if (!mapContainerRef.value || mapContainerRef.value.clientWidth === 0) {
    setTimeout(initTopChart, 100);
    return;
  }

  removeChart();

  const xData = [];
  const totalData = [];
  const completeData = [];
  const unCompleteTaskCount = [];

  props?.data?.forEach((item) => {
    xData.push(item.date);
    totalData.push(item.totalCount);
    completeData.push(item.completeTaskCount);
    unCompleteTaskCount.push(item.unCompleteTaskCount);
  });

  topChartIns = init(mapContainerRef.value);

  const data = await getJsonData(geoJsonMap["china"]);
  registerMap("china", data);
  initMap("china");

  let timeFn = null;

  // 清除旧事件监听，避免重复触发
  topChartIns.off("click");
  topChartIns.on("click", function (params) {
    clearTimeout(timeFn);
    const name = params.name;
    timeFn = setTimeout(function () {
      initMap(name);
      showChina.value = true;
    }, 250);
  });
};

function goBack() {
  showChina.value = false;
  initMap("china");
}

async function initMap(value) {
  const data = await getJsonData(geoJsonMap[value]);
  registerMap(value, data);

  const allData = Object.keys(geoJsonMap)
    .filter((k) => k !== "china")
    .map((name) => ({ name, value: Math.round(Math.random() * 400) }));

  const option = {
    tooltip: {
      show: true,
      formatter: (params) =>
        params.data ? `${params.name}：${params.data.value || 0}` : "",
    },
    visualMap: {
      text: ["", ""],
      showLabel: true,
      left: "50",
      selectedMode: false,
      pieces: [
        { gte: 301, label: "300以上", color: "#F02C2F" },
        { gte: 201, lte: 300, label: "201 - 300", color: "#006EFF" },
        { gte: 101, lte: 200, label: "101 - 200", color: "#F8C13B" },
        { gte: 0, lte: 100, label: "0 - 100", color: "#1BCC98" },
      ],
    },
    series: [
      {
        name: "MAP",
        type: "map",
        map: value,
        selectedMode: false,
        zoom: 1.25,
        label: {
          show: true,
          formatter: (params) =>
            params.value ? `{labelBg|${params.value ?? ""}}` : "",
          rich: {
            labelBg: {
              backgroundColor: "rgba(255,255,255,0.60)",
              padding: [4, 4],
              borderRadius: 100,
              borderColor: "rgba(255,255,255,0.3)",
              borderWidth: 2,
              width: 18,
              height: 18,
              textAlign: "center",
              verticalAlign: "middle",
            },
          },
          color: "#006EFF",
          fontSize: 12,
          lineHeight: 14,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
          },
        },
        itemStyle: {
          areaColor: "#1BCC98",
          borderColor: "#fff",
          borderWidth: 1.5,
        },
        data: allData,
      },
    ],
  };

  topChartIns.setOption(option);
  topChartIns.resize();
}


onMounted(() => {
  if (mapContainerRef.value) {
    resizeObserver = new ResizeObserver(() => {
      topChartIns?.resize();
    });
    resizeObserver.observe(mapContainerRef.value);

    setTimeout(() => {
      initTopChart();
    }, 50);
  }
});

onUnmounted(() => {
  if (resizeObserver && mapContainerRef.value) {
    resizeObserver.unobserve(mapContainerRef.value);
  }
  removeChart();
});

onBeforeUnmount(() => {
  if (resizeObserver && mapContainerRef.value) {
    resizeObserver.unobserve(mapContainerRef.value);
    resizeObserver = null;
  }
  removeChart();
});

watch(
  () => props.data,
  () => {
    removeChart();
    initTopChart();
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.map-container {
  height: 100%;
  width: 100%;
}

.map-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  .china-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;
  }
}
</style>
