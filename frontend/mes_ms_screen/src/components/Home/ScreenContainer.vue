<template>
  <div class="screen-container" :style="{ overflowY: wideScreen ? 'auto' : 'hidden' }">
    <div class="screen-wrapper" ref="screenWrapper" :style="wrapperStyle">
      <slot :scale="currentScale"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'

function debounce(fn, delay) {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => fn(...args), delay > 0 ? delay : 100)
  }
}

const props = defineProps({
  width: { type: [String, Number], default: 1920 },
  height: { type: [String, Number], default: 1080 },
  fullScreen: { type: Boolean, default: false },
  selfAdaption: { type: Boolean, default: true },
  delay: { type: Number, default: 300 },
  wrapperStyle: { type: Object, default: () => ({}) },
  wideScreen: { type: <PERSON>ole<PERSON>, default: false } // 新增：宽屏模式开关
})

const screenWrapper = ref(null)
const currentScale = ref(1)
const originalWidth = ref(+props.width)
const originalHeight = ref(+props.height)
const wideScreen = ref(props.wideScreen) // 宽屏模式状态

async function initSize() {
  if (!screenWrapper.value) return

  const parent = screenWrapper.value.parentNode
  if (parent) {
    parent.style.overflowX = 'hidden' // 禁止水平滚动
    parent.scrollLeft = 0
  }

  screenWrapper.value.style.width = originalWidth.value + 'px'
  screenWrapper.value.style.height = originalHeight.value + 'px'
}

function applyScale(scale) {
  if (!screenWrapper.value) return

  currentScale.value = scale
  screenWrapper.value.style.transform = `scale(${scale})`
  screenWrapper.value.style.transformOrigin = 'left top'

  // 宽屏模式：不居中，贴左显示（确保宽度铺满）
  if (wideScreen.value) {
    screenWrapper.value.style.margin = '0'
  } else {
    // 普通模式：居中显示
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const scaledWidth = originalWidth.value * scale
    const scaledHeight = originalHeight.value * scale

    const marginLeft = Math.max((windowWidth - scaledWidth) / 2, 0)
    const marginTop = Math.max((windowHeight - scaledHeight) / 2, 0)
    screenWrapper.value.style.margin = `${marginTop}px ${marginLeft}px`
  }
}

function updateScale() {
  if (!screenWrapper.value) return

  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  if (props.fullScreen) {
    // 全屏模式：非等比缩放
    const scaleX = windowWidth / originalWidth.value
    const scaleY = windowHeight / originalHeight.value
    currentScale.value = { x: scaleX, y: scaleY }
    screenWrapper.value.style.transform = `scale(${scaleX}, ${scaleY})`
    screenWrapper.value.style.margin = '0'
    return
  }

  if (wideScreen.value) {
    // 宽屏模式：仅按宽度缩放（确保宽度铺满）
    const scale = windowWidth / originalWidth.value
    applyScale(scale)
    return
  }

  // 普通模式：按宽高中的最小值缩放
  const scale = Math.min(windowWidth / originalWidth.value, windowHeight / originalHeight.value)
  applyScale(scale)
}

async function resizeHandler() {
  if (!props.selfAdaption) return
  await initSize()
  updateScale()
}

function removeStyles() {
  if (!screenWrapper.value) return
  const parent = screenWrapper.value.parentNode
  if (parent) {
    parent.style.overflow = ''
    parent.style.overflowX = ''
  }
  screenWrapper.value.style.width = ''
  screenWrapper.value.style.height = ''
  screenWrapper.value.style.transform = ''
  screenWrapper.value.style.margin = ''
  screenWrapper.value.style.transformOrigin = ''
}

let debouncedResize = null

defineExpose({ currentScale })

// 监听宽屏模式切换
watch(() => props.wideScreen, (val) => {
  wideScreen.value = val
  resizeHandler()
})

watch(() => props.selfAdaption, (val) => {
  if (val) {
    resizeHandler()
    window.addEventListener('resize', debouncedResize)
  } else {
    window.removeEventListener('resize', debouncedResize)
    removeStyles()
  }
}, { immediate: true })

onMounted(() => {
  debouncedResize = debounce(() => resizeHandler(), props.delay)
  if (props.selfAdaption) {
    resizeHandler()
    window.addEventListener('resize', debouncedResize)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', debouncedResize)
  removeStyles()
})
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  overflow-x: hidden; /* 禁止水平滚动条 */
}

.screen-wrapper {
  transition: transform 300ms ease;
  position: relative;
  overflow: hidden;
  z-index: 100;
  transform-origin: left top; /* 确保缩放原点在左上角 */
}
</style>