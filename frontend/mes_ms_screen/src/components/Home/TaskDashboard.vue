<template>
  <div class="home-dashboard">
    <div class="page-header">
      <h1 class="page-title">工作概览</h1>
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>工作台</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" v-for="card in statsCards" :key="card.name">
        <el-card class="stat-card" :shadow="'hover'">
          <div class="stat-content">
            <div class="stat-icon" :class="card.iconClass"></div>
            <div class="stat-info">
              <p class="stat-title">{{ card.title }}</p>
              <p class="stat-value" :class="card.valueClass">{{ card.value }}</p>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-trend" :class="card.trendClass">
              <i class="el-icon-caret-up"></i> {{ card.trend }}%
            </span>
            <span class="stat-period">较上周</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="main-content">
      <el-col :span="16" class="todo-section">
        <el-card class="task-card" :shadow="'hover'">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-checklist"></i> 待办任务 ({{ tasks.length }})
              </span>
              <el-button link size="small" @click="addTask">
                <i class="el-icon-plus"></i> 新建
              </el-button>
            </div>
          </template>
          <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-timeline>
              <el-timeline-item
                v-for="(task, index) in tasks"
                :key="task.id"
                :timestamp="task.time"
                placement="top"
              >
                <el-card class="task-item" :body-style="{ padding: '0px' }">
                  <div class="task-content">
                    <el-checkbox v-model="task.completed" @change="updateTaskStatus(task)"></el-checkbox>
                    <div class="task-text" :class="{ 'task-completed': task.completed }">
                      {{ task.content }}
                    </div>
                    <div class="task-actions">
                      <el-button link icon="el-icon-edit" @click="editTask(task)"></el-button>
                      <el-button link icon="el-icon-delete" @click="deleteTask(task)"></el-button>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </el-scrollbar>
        </el-card>
      </el-col>

      <el-col :span="8" class="notice-section">
        <el-card class="notice-card" :shadow="'hover'">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-bell"></i> 系统通知 ({{ notices.length }})
              </span>
              <el-badge :value="unreadCount" class="ml-2">
                <el-button link size="small">全部标为已读</el-button>
              </el-badge>
            </div>
          </template>
          <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-collapse v-model="activeNotice" accordion>
              <el-collapse-item
                v-for="(notice, index) in notices"
                :key="notice.id"
                :title="notice.title"
                :name="notice.id"
                @click="markAsRead(notice)"
              >
                <div class="notice-content" :class="{ 'notice-unread': !notice.read }">
                  <div>{{ notice.content }}</div>
                  <div class="notice-meta">
                    <span class="notice-time">{{ notice.time }}</span>
                    <span v-if="!notice.read" class="notice-dot"></span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="project-section">
      <el-col :span="24">
        <el-card class="project-card" :shadow="'hover'">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-folder-opened"></i> 进行中项目
              </span>
              <el-button link>查看全部</el-button>
            </div>
          </template>
          <el-table :data="projects" stripe border class="project-table">
            <el-table-column prop="name" label="项目名称" min-width="180"></el-table-column>
            <el-table-column prop="leader" label="负责人" min-width="100">
              <template #default="scope">
                <el-avatar size="small" :src="scope.row.avatar"></el-avatar>
                <span class="ml-2">{{ scope.row.leader }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" min-width="160">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" :color="progressColor(scope.row.progress)"></el-progress>
              </template>
            </el-table-column>
            <el-table-column prop="deadline" label="截止日期" min-width="120"></el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="scope">
                <el-button link @click="viewProject(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 数据模型
interface Task {
  id: number
  content: string
  time: string
  completed: boolean
}

interface Notice {
  id: number
  title: string
  content: string
  time: string
  read: boolean
}

interface Project {
  id: number
  name: string
  leader: string
  avatar: string
  progress: number
  deadline: string
}

// 状态管理
const activeNotice = ref<string | number | null>(null)
const tasks = ref<Task[]>([
  { id: 1, content: '完成系统首页开发', time: '2024-03-20', completed: false },
  { id: 2, content: '对接用户管理接口', time: '2024-03-19', completed: false },
  { id: 3, content: '项目需求评审会议', time: '2024-03-18', completed: true }
])

const notices = ref<Notice[]>([
  { id: 1, title: '系统维护通知', content: '将于2024-03-22 00:00至06:00进行系统维护', time: '2024-03-20 09:00', read: false },
  { id: 2, title: '版本更新公告', content: 'V2.1.0版本已发布，新增以下功能：1. 优化了任务管理模块；2. 增加了数据可视化图表；3. 修复了已知的几个bug。', time: '2024-03-19 15:30', read: false },
  { id: 3, title: '团队活动通知', content: '本周六将组织团队建设活动，请大家安排好时间参加。', time: '2024-03-18 10:20', read: true }
])

const projects = ref<Project[]>([
  { id: 1, name: '企业管理系统重构', leader: '张三', avatar: 'https://picsum.photos/id/1005/200/200', progress: 75, deadline: '2024-04-15' },
  { id: 2, name: '数据可视化平台', leader: '李四', avatar: 'https://picsum.photos/id/1012/200/200', progress: 45, deadline: '2024-05-20' },
  { id: 3, name: '移动应用开发', leader: '王五', avatar: 'https://picsum.photos/id/1027/200/200', progress: 60, deadline: '2024-04-30' }
])

const statsCards = reactive([
  {
    name: 'tasks',
    title: '待办任务',
    value: tasks.value.filter(t => !t.completed).length,
    iconClass: 'stat-icon-tasks',
    valueClass: 'text-primary',
    trend: 15,
    trendClass: 'text-success'
  },
  {
    name: 'notices',
    title: '未读通知',
    value: notices.value.filter(n => !n.read).length,
    iconClass: 'stat-icon-notices',
    valueClass: 'text-warning',
    trend: -5,
    trendClass: 'text-success'
  },
  {
    name: 'projects',
    title: '进行中项目',
    value: projects.value.length,
    iconClass: 'stat-icon-projects',
    valueClass: 'text-info',
    trend: 25,
    trendClass: 'text-success'
  },
  {
    name: 'deadlines',
    title: '即将截止',
    value: 2,
    iconClass: 'stat-icon-deadlines',
    valueClass: 'text-danger',
    trend: 0,
    trendClass: 'text-neutral'
  }
])

// 计算属性
const unreadCount = computed(() => notices.value.filter(n => !n.read).length)

// 方法
const updateTaskStatus = (task: Task) => {
  task.completed = !task.completed
  ElMessage({
    message: task.completed ? '任务已标记为完成' : '任务已重新激活',
    type: 'success',
    duration: 1500
  })
}

const addTask = () => {
  ElMessageBox.prompt('请输入任务内容', '新建任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '任务内容'
  }).then(({ value }) => {
    if (value) {
      const newTask: Task = {
        id: Date.now(),
        content: value,
        time: new Date().toISOString().split('T')[0],
        completed: false
      }
      tasks.value.unshift(newTask)
      ElMessage({
        type: 'success',
        message: '任务创建成功'
      })
    }
  }).catch(() => {})
}

const editTask = (task: Task) => {
  ElMessageBox.prompt('编辑任务内容', '编辑任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: task.content
  }).then(({ value }) => {
    if (value) {
      task.content = value
      ElMessage({
        type: 'success',
        message: '任务更新成功'
      })
    }
  }).catch(() => {})
}

const deleteTask = (task: Task) => {
  ElMessageBox.confirm(
    '确定要删除这个任务吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index !== -1) {
      tasks.value.splice(index, 1)
      ElMessage({
        type: 'success',
        message: '任务已删除'
      })
    }
  }).catch(() => {})
}

const markAsRead = (notice: Notice) => {
  if (!notice.read) {
    notice.read = true
    ElMessage({
      message: '通知已标记为已读',
      type: 'success',
      duration: 1000
    })
  }
}

const progressColor = (percentage: number) => {
  if (percentage < 30) return '#909399'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const viewProject = (project: Project) => {
  ElMessage({
    message: `查看项目: ${project.name}`,
    type: 'info'
  })
}

// 生命周期钩子
onMounted(() => {
document.querySelectorAll<HTMLElement>('.fade-in').forEach((el, index) => {
    el.style.opacity = '0'
    el.style.transform = 'translateY(20px)'
    el.style.transition = 'opacity 0.5s ease, transform 0.5s ease'
    el.style.transitionDelay = `${index * 0.1}s`
    
    setTimeout(() => {
      el.style.opacity = '1'
      el.style.transform = 'translateY(0)'
    }, 100)
  })
})
</script>

<style scoped>
/* 基础样式 */
.home-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.main-content{
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #303133;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  border-radius: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-icon-tasks {
  background-color: rgba(48, 133, 214, 0.1);
  color: #3085d6;
}

.stat-icon-notices {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.stat-icon-projects {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.stat-icon-deadlines {
  background-color: rgba(235, 108, 108, 0.1);
  color: #eb6c6c;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 16px 16px;
  font-size: 12px;
  color: #909399;
}

.text-primary {
  color: #3085d6;
}

.text-warning {
  color: #e6a23c;
}

.text-info {
  color: #67c23a;
}

.text-danger {
  color: #eb6c6c;
}

.text-success {
  color: #52c41a;
}

.text-neutral {
  color: #909399;
}

/* 卡片样式 */
.task-card, .notice-card, .project-card {
  border-radius: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.task-card:hover, .notice-card:hover, .project-card:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.scrollbar-wrapper {
  padding: 16px;
}

/* 任务样式 */
.task-item {
  margin-bottom: 12px;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.task-item:hover {
  border-color: #dcdfe6;
  transform: translateX(2px);
}

.task-content {
  display: flex;
  align-items: center;
  padding: 12px;
}

.task-text {
  flex: 1;
  margin: 0 12px;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
}

.task-completed {
  text-decoration: line-through;
  color: #909399;
}

.task-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.task-item:hover .task-actions {
  opacity: 1;
}

/* 通知样式 */
.notice-content {
  padding: 12px;
  transition: all 0.3s ease;
}

.notice-unread {
  background-color: rgba(48, 133, 214, 0.05);
}

.notice-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.notice-time {
  font-size: 12px;
  color: #909399;
}

.notice-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #3085d6;
}

/* 项目表格样式 */
.project-table {
  margin-top: 16px;
}

/* 动画类 */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>  