
<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: false
  },
  activeValue: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: true
  },
  inactiveValue: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: false
  },
  activeText: String,
  inactiveText: String,
  disabled: Boolean,
  loading: Boolean,
  size: {
    type: String,
    validator: val => ['small', 'middle', 'large'].includes(val),
    default: 'middle'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const isChecked = ref(props.modelValue === props.activeValue)

watch(() => props.modelValue, (val) => {
  isChecked.value = val === props.activeValue
})

const handleClick = () => {
  if (props.disabled || props.loading) return
  
  const newValue = isChecked.value ? props.inactiveValue : props.activeValue
  isChecked.value = !isChecked.value
  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<template>
  <div 
    class="switch-wrapper"
    :class="[
      `size-${size}`,
      { 'is-disabled': disabled },
      { 'is-loading': loading },
      { 'is-checked': isChecked }
    ]"
    @click="handleClick"
  >
    <div class="switch-core">
      <span v-if="activeText" class="active-text">{{ activeText }}</span>
      <span v-if="inactiveText" class="inactive-text">{{ inactiveText }}</span>
      <div class="switch-button"></div>
    </div>
  </div>
</template>

<style scoped>
.switch-wrapper {
  --active-color: #2460CE;
  --inactive-color: #00084B;
  --button-size: 20px;
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  padding-inline-start: 0 !important;
  margin: 0 !important;
}

.switch-core {
  position: relative;
  width: 44px;
  height: 22px;
  border-radius: 11px;
  background-color: var(--inactive-color);
  transition: all 0.3s;
}

.switch-button {
  position: absolute;
  top: 1px;
  left: 2px;
  width: var(--button-size);
  height: var(--button-size);
  border-radius: 50%;
  background-color: #fff;
  transition: all 0.3s;
}

.active-text, .inactive-text {
  position: absolute;
  font-size: 14px;
  color: #fff;
  user-select: none;
}

.active-text {
  left: 6px;
  display: var(--active-display);
}

.inactive-text {
  right: 6px;
  display: var(--inactive-display);
}

/* 尺寸控制 */
.size-small {
  --button-size: 12px;
}
.size-small .switch-core {
  width: 30px;
  height: 16px;
}

.size-large {
  --button-size: 20px;
}
.size-large .switch-core {
  width: 50px;
  height: 24px;
}

/* 状态控制 */
.is-checked .switch-core {
  background-color: var(--active-color);
}
.is-checked .switch-button {
  left: calc(100% - var(--button-size) - 2px);
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.is-loading {
  cursor: wait;
}
</style>
