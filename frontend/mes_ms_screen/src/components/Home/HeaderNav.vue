<template>
  <div class="header-wrapper">
    <div class="header-menu">
      <div class="header-menu_current">
        {{ selectedMenu.name }}
      </div>
      <div class="header-menu_change" @click="toggleMenu"></div>
      <ul v-show="isOpenMenu" class="header-menu_menu">
        <li
          v-for="item in menuData"
          :key="item.id"
          class="header-menu_menu_item"
          :class="{
            'header-menu_menu_item_active': selectedMenu.id === item.id,
          }"
          @click="selectMenu(item)"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="header-title">驾驶舱 <span class="header-title_btm" /></div>

    <div class="header-right">
      <slot name="right"></slot>
      <div class="header-types">
        <div
          class="header-types_item"
          :class="{
            'header-types_item_active': item.id === selectedTypes.id,
          }"
          v-for="item in types"
          :key="item.id"
          @click="selectTypes(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="header-date">{{ currentDate }}</div>
      <div class="header-switch">
        <Switch
          v-model="switchValue"
          active-text="浅"
          inactive-text="深"
          @change="handleTheme"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import router from '@/router'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Switch from '@/components/Home/switch.vue'
import { useRoute } from 'vue-router'
import { useTheme } from '@/utils/useTheme'
const route = useRoute()
const props = defineProps({
  menuData: {
    type: Array,
    default: () => [
      { name: '智能调度', id: 0, path: '/msscreen/schedu' },
      { name: '监控预警', id: 1, path: '/msscreen/monitor' },
      { name: '资源全景', id: 2, path: '/msscreen/dashzz' },
      { name: '数据洞察', id: 3, path: '/msscreen/dashdata' },
    ],
  },
  types: {
    type: Array,
    default: () => [
      { id: 1, name: '总览', key: 'all' },
      { id: 2, name: '地表水', key: 'shui' },
      { id: 3, name: '环境空气', key: 'kq' },
    ],
  },
})

const emit = defineEmits(['changeMenu', 'changeType', 'changeTheme'])

const selectedMenu = ref(props.menuData[0])
const isOpenMenu = ref(false)
const toggleMenu = () => {
  isOpenMenu.value = !isOpenMenu.value
}
const selectMenu = (item) => {
  selectedMenu.value = item
  emit('changeMenu', item)
  isOpenMenu.value = false
  if (item.path) {
    router.push(item.path)
  }
}

const selectedTypes = ref(props.types[0])
const selectTypes = (item) => {
  selectedTypes.value = item
  emit('changeType', item)
}

const switchValue = ref(true)
const { toggleTheme, theme } = useTheme()

const handleTheme = (v) => {
  // emit('changeTheme', v)
  toggleTheme() // todo gss 开发中 打开
}

const currentDate = ref('')
let timer = null
const updateTime = () => {
  const now = new Date()
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  currentDate.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)

  const currentPath = route.path.replace(/\/+$/, '') // 移除末尾斜杠
  const matched = props.menuData.find((item) =>
    currentPath.startsWith(item.path)
  )
  if (matched) {
    selectedMenu.value = matched
  }
})

onBeforeUnmount(() => {
  clearInterval(timer)
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';

.header-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: url('@/assets/images/schedu/bg_top.png') no-repeat;
  background-size: 100% 100%;
  padding: px2rem(12) px2rem(24) 0 px2rem(22);
  flex-shrink: 0;
  /* clearfix 防止浮动塌陷 */
  &::after {
    content: '';
    display: block;
    clear: both;
  }
}

.header-menu {
  width: px2rem(116);
  display: flex;
  float: left;

  &_current {
    width: px2rem(100);
    height: px2rem(28);
    background: url('@/assets/images/schedu/bg_xitongqiehuan.png') no-repeat;
    background-size: 100%;
    font-family: PingFangSC-Semibold;
    font-size: px2rem(16);
    color: #023a93;
    text-align: center;
    line-height: px2rem(24);
    font-weight: 600;
  }
  &_change {
    width: px2rem(16);
    height: px2rem(14);
    margin-top: px2rem(6);
    background: url('@/assets/images/schedu/icon_qiehuan.png') no-repeat;
    cursor: pointer;
  }
  ul,
  li {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
  &_menu {
    background: #ffffff;
    box-shadow: 0px px2rem(2) px2rem(4) 0px rgba(0, 0, 0, 0.13);
    border-radius: px2rem(8);
    position: absolute;
    top: px2rem(48);

    &_item {
      width: px2rem(116);
      height: px2rem(48);
      line-height: px2rem(48);
      text-align: center;
      font-family: PingFangSC-Regular;
      font-size: px2rem(16);
      color: #999999;
      font-weight: 400;
      cursor: pointer;

      &_active {
        font-family: PingFangSC-Medium;
        color: #ffffff;
        font-weight: 500;
        background: url('@/assets/images/schedu/bg_qiehuan_sle.png') no-repeat;
        background-size: 100%;
      }
    }
  }
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-family: PingFangSC-Semibold;
  font-size: px2rem(36);
  color: #023a93;
  text-align: center;
  font-weight: 600;

  &_btm {
    width: px2rem(62);
    height: px2rem(4);
    background: #1472ff;
    border-radius: 0 0 px2rem(2.5) px2rem(2.5);
    bottom: px2rem(-10);
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
  }
}

.header-right {
  display: flex;
  align-items: center; /* 垂直居中 */
  gap: px2rem(6); // 增加间距，避免挤压
  float: right;
}

.header-types {
  width: px2rem(240);
  height: px2rem(28);
  background: rgba(36, 96, 206, 0.1);
  border-radius: px2rem(14);
  padding: 0 px2rem(16);
  display: flex;
  gap: px2rem(32);
  justify-content: space-between;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: px2rem(16);
  color: #999999;
  font-weight: 400;
  margin-right: px2rem(24);
  position: relative; /* 伪元素定位需要相对定位 */

  &_item {
    cursor: pointer;
    position: relative;
    z-index: 1;
    white-space: nowrap;

    &_active {
      color: #023a93;
    }

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: px2rem(-16); /* 距离右侧16 */
      top: 25%;
      bottom: 25%;
      width: px2rem(1);
      background-color: #000000;
      opacity: 0.1;
      z-index: 0;
    }
  }
}

.header-date {
  font-family: PingFangSC-Semibold;
  font-size: px2rem(14);
  color: #333333;
  font-weight: 600;
  margin-right: px2rem(24);
  height: px2rem(28);
  line-height: px2rem(28);
  flex-shrink: 0;
  /* 固定宽度防止秒数变动导致宽度抖动 */
  width: px2rem(150);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-switch {
  width: px2rem(32);
  height: px2rem(20);
  display: flex;
  align-items: center;
  justify-content: center;
}
[data-theme='dark'] .header-wrapper {
  background: url('@/assets/images/schedu/bg_top_2.png') no-repeat;
  background-size: 100%;
  .header-title {
    color: #ffffff;
    &_btm {
      background: #8afcea;
    }
  }
  .header-menu {
    &_current {
      background: url('@/assets/images/schedu/bg_xitongqiehuan.png') no-repeat;
      background-size: 100%;
      color: #f4f7ff;
    }

    &_menu {
      background: #1a3a5e;
      box-shadow: 0px px2rem(2) px2rem(4) 0px rgba(0, 0, 0, 0.13);

      &_item {
        color: #cbdcff;
      }
    }
  }
  .header-types {
    background: rgba(244, 247, 255, 0.1);
    border-radius: px2rem(14);

    &_item {
      color: #cbdcff;

      &_active {
        color: #4f9fff;
      }

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: px2rem(-16);
        top: 25%;
        bottom: 25%;
        width: px2rem(1);
        background-color: #979797;
        opacity: 0.1;
        z-index: 0;
      }
    }
  }
  .header-date {
    color: #f4f7ff;
  }
}
</style>
