<template>
  <div class="headerBgColor">
    <div class="header-wrapper">
      <!-- 标题 -->
      <div class="header-title">
        驾驶舱
        <span class="header-title_btm" />
      </div>

      <!-- 菜单区域 -->
      <div class="header-menu">
        <div class="header-menu_current">{{ selectedMenu.name }}</div>
        <div class="header-menu_change" @click="toggleMenu" />
        <ul v-show="isOpenMenu" class="header-menu_menu">
          <li v-for="item in menuData" :key="item.id" class="header-menu_menu_item"
            :class="{ 'header-menu_menu_item_active': selectedMenu.id === item.id }" @click="selectMenu(item)">
            {{ item.name }}
          </li>
        </ul>
      </div>

      <!-- 右侧内容 -->
      <div class="header-right">
        <div class="header-types">
          <div class="header-types_item" :class="{ 'header-types_item_active': item.id === selectedTypes.id }"
            v-for="item in types" :key="item.id" @click="selectTypes(item)">
            {{ item.name }}
          </div>
        </div>
        <div class="header-date">{{ currentDate }}</div>
        <div class="header-switch">
          <Switch v-model="switchValue" active-text="浅" inactive-text="深" @change="handleTheme" />
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Switch from './switch.vue'

const props = defineProps({
  menuData: {
    type: Array,
    default: () => [
      { name: '智能调度', id: 0 },
      { name: '监控预警', id: 1 },
      { name: '数据资源', id: 2 },
    ],
  },
  types: {
    type: Array,
    default: () => [
      { id: 1, name: '总览' },
      { id: 2, name: '地表水' },
      { id: 3, name: '环境空气' },
    ],
  },
})

const emit = defineEmits(['changeMenu', 'changeType', 'changeTheme'])

const selectedMenu = ref(props.menuData[0])
const isOpenMenu = ref(false)
const selectedTypes = ref(props.types[0])
const switchValue = ref(true)
const currentDate = ref('')

let timer = null

const toggleMenu = () => {
  isOpenMenu.value = !isOpenMenu.value
}
const selectMenu = (item) => {
  selectedMenu.value = item
  isOpenMenu.value = false
  emit('changeMenu', item)
}
const selectTypes = (item) => {
  selectedTypes.value = item
  emit('changeType', item)
}
const handleTheme = (val) => {
  emit('changeTheme', val)
}

const updateTime = () => {
  const now = new Date()
  const format = (n) => String(n).padStart(2, '0')
  currentDate.value = `${now.getFullYear()}-${format(now.getMonth() + 1)}-${format(
    now.getDate()
  )} ${format(now.getHours())}:${format(now.getMinutes())}:${format(now.getSeconds())}`
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onBeforeUnmount(() => {
  clearInterval(timer)
})
</script>


<style scoped lang="scss">
.headerBgColor {
  position: relative;
  width: 100%;
  height: 72px;
  background-color: var(--main-bg);
}

.header-wrapper {
  width: 100%;
  height: 100%;
  padding: 0px 24px 24px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: url('@/assets/images/schedu/bg_top.png') no-repeat center center;
  background-size: 100% 100%;
  z-index: 0;
  box-sizing: border-box;
}

/* 标题 */
.header-title {
  position: absolute;
  left: 50%;
  top: 4px;
  transform: translateX(-50%);
  font-size: 36px;
  font-weight: 600;
  color: #023a93;
  z-index: 2;
  white-space: nowrap;

  &_btm {
    width: 62px;
    height: 4px;
    background: #1472ff;
    border-radius: 0 0 2.5px 2.5px;
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 左侧菜单 */
.header-menu {
  display: flex;
  position: relative;
  z-index: 3;

  &_current {
    width: 100px;
    height: 28px;
    background: url('@/assets/images/schedu/bg_xitongqiehuan.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;
    line-height: 28px;
    color: #023a93;
    font-weight: 600;
    font-size: 16px;
  }

  &_change {
    width: 16px;
    height: 14px;
    margin-top: 6px;
    background: url('@/assets/images/schedu/icon_qiehuan.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  &_menu {
    position: absolute;
    top: 38px;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.13);
    border-radius: 8px;
    overflow: hidden;
    z-index: 5;

    &_item {
      width: 116px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      font-size: 16px;
      color: #999;
      cursor: pointer;

      &_active {
        color: #ffffff;
        background: url('@/assets/images/schedu/bg_qiehuan_sle.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
}

.header-menu_menu {
  list-style: none;
  /* 去掉点 */
  padding: 0;
  /* 去掉默认内边距 */
  margin: 0;
  /* 去掉默认外边距 */
}

/* 右侧内容 */
.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 3;
  height: 100%;

  .header-types {
    display: flex;
    background: rgba(36, 96, 206, 0.1);
    border-radius: 16px;
    padding: 0 8px;
    height: 32px;
    align-items: center;

    &_item {
      padding: 0 12px; // 每个item左右一致
      height: 100%; // 撑满容器高度
      display: flex;
      align-items: center; // 垂直居中
      justify-content: center; // 水平居中
      font-size: 14px;
      color: #999;
      cursor: pointer;
      position: relative;

      &_active {
        color: #023a93;
        font-weight: 600;
      }

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 16px;
        width: 1px;
        background-color: #ccc;
      }
    }
  }

  .header-date {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
  }

  .header-switch {
    padding-top: 2px;
  }
}
</style>
