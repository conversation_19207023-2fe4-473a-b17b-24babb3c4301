<template>
  <div class="site-network-module module-box">
    <div class="module-title-box">
      <div class="module-title">站点联网状态</div>
      <div class="mt-btns">
        <div :class="isActiveBtn == 1 ? 'btn active' : 'btn'" @click="handleClick(1)">5分钟</div>
        <div :class="isActiveBtn == 2 ? 'btn active' : 'btn'" @click="handleClick(2)">1小时</div>
      </div>
    </div>
    <div class="sn-module-content">
      <div class="snm-nums">
        <div class="snm-item">
          <div class="snmi-t">联网站点</div>
          <div class="snmi-n">{{ isActiveBtn == 1 ? props.moduleData.lwzd : props.moduleData.lwzd5 }}</div>
        </div>
        <div class="snm-item">
          <div class="snmi-t">在线站点</div>
          <div class="snmi-n">{{ isActiveBtn == 1 ? props.moduleData.zxzd : props.moduleData.zxzd5 }}</div>
        </div>
        <div class="snm-item">
          <div class="snmi-t">离线站点</div>
          <div class="snmi-n">{{ isActiveBtn == 1 ? props.moduleData.lxzd : props.moduleData.lxzd5 }}</div>
        </div>
      </div>

      <div class="item-content">
        <DivTable :data="tableData" :columns="columns" />
      </div>
    </div>
  </div>
</template>

<script setup>
import DivTable from '../DivTable/index.vue'
// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  },
  moduleData: {
    type: Object
  }
})

// const data_all = {
//   lwzd: 2370, // 模拟数据,
//   zxzd: 2320,
//   lxzd: 50,
//   lwzd5: 2170, // 模拟数据,
//   zxzd5: 2120,
//   lxzd5: 50,
//   dataList: [
//     { name: '呼和浩特', name1: '二十九中', value: 6 },
//     { name: '呼和浩特', name1: '红旗小学', value: 5 },
//     { name: '包头', name1: '昆区政府', value: 4 },
//     { name: '包头', name1: '青山宾馆', value: 4 },
//     { name: '乌兰察布', name1: '集宁新区', value: 3 }
//   ],
//   dataList5: [
//     { name: '包头', name1: '昆区政府', value: 1 },
//     { name: '包头', name1: '青山宾馆', value: 2 },
//     { name: '呼和浩特', name1: '红旗小学', value: 1 },
//     { name: '呼和浩特', name1: '二十九中', value: 3 },
//     { name: '乌兰察布', name1: '集宁新区', value: 1 }
//   ]
// }

// const data_shui = {
//   lwzd: 1370, // 模拟数据,
//   zxzd: 1320,
//   lxzd: 50,
//   lwzd5: 170, // 模拟数据,
//   zxzd5: 120,
//   lxzd5: 50,
//   dataList: [
//     { name: '乌兰察布', name1: '二十九中', value: 2 },
//     { name: '呼和浩特', name1: '红旗小学', value: 5 },
//     { name: '北京', name1: '朝阳', value: 14 },
//     { name: '包头', name1: '青山宾馆', value: 3 },
//     { name: '乌兰察布', name1: '集宁新区', value: 6 }
//   ],
//   dataList5: [
//     { name: '上海', name1: '人民政府', value: 1 },
//     { name: '包头', name1: '青山宾馆', value: 2 },
//     { name: '重庆', name1: '大礼堂', value: 5 },
//     { name: '呼和浩特', name1: '二十九中', value: 13 },
//     { name: '乌兰察布', name1: '集宁新区', value: 11 }
//   ]
// }

// const data_kq = {
//   lwzd: 370, // 模拟数据,
//   zxzd: 320,
//   lxzd: 50,
//   lwzd5: 470, // 模拟数据,
//   zxzd5: 420,
//   lxzd5: 50,
//   dataList: [
//     { name: '北京', name1: '一中', value: 16 },
//     { name: '甘肃', name1: '兰州', value: 50 },
//     { name: '包头', name1: '昆区政府', value: 14 },
//     { name: '包头', name1: '青山宾馆', value: 13 },
//     { name: '乌兰察布', name1: '集宁新区', value: 23 }
//   ],
//   dataList5: [
//     { name: '包头', name1: '昆区政府', value: 11 },
//     { name: '包头', name1: '青山宾馆', value: 23 },
//     { name: '呼和浩特', name1: '红旗小学', value: 12 },
//     { name: '呼和浩特', name1: '二十九中', value: 33 },
//     { name: '乌兰察布', name1: '集宁新区', value: 14 }
//   ]
// }

// const totalData = computed(() => {
//   if (props.currentType == 'all') {
//     return data_all
//   } else if (props.currentType == 'shui') {
//     return data_shui
//   } else if (props.currentType == 'kq') {
//     return data_kq
//   }
// })

let isActiveBtn = ref(1)
const handleClick = (btn) => {
  isActiveBtn.value = btn
}

const tableData = computed(() => {
  if (props.moduleData.dataList) {
    if (isActiveBtn.value === 1) {
      return props.moduleData.dataList.map((item, index) => {
        return {
          index: `TOP ${index + 1}`,
          name: item.name,
          name1: item.name1,
          value: `${item.value}小时`
        }
      })
    } else {
      return props.moduleData.dataList5.map((item, index) => {
        return {
          index: `TOP ${index + 1}`,
          name: item.name,
          name1: item.name1,
          value: `${item.value}小时`
        }
      })
    }
  } else {
    return []
  }
})

const columns = [
  { prop: 'index', title: '序号', width: '84px' },
  { prop: 'name', title: '城市', width: '110px' },
  { prop: 'name1', title: '站点名称', width: '130px' },
  { prop: 'value', title: '离线时长' }
]
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.site-network-module {
  width: 100%;
  flex: 1;
  // height: px2rem(316);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .sn-module-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .snm-nums {
      height: px2rem(46);
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: px2rem(10);
      .snm-item {
        background: rgba(49, 103, 213, 0.05);
        border-radius: px2rem(3);
        padding: 0 px2rem(8);
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333333;
        .snmi-t {
          font-size: px2rem(14);
        }
        .snmi-n {
          font-size: px2rem(24);
          font-weight: 700;
        }
      }
    }
    .item-content {
      flex: 1;
      overflow-y: auto;
      margin-top: px2rem(10);
      :deep(.table-wrapper) {
        // width: 100%;
        // height: 100%;
        display: flex;
        flex-direction: column;
        .table-wrapper-header {
          .header-cell {
            padding: 0 px2rem(16);
          }
        }
        .table-body {
          flex: 1;
          overflow-y: auto;
        }
      }
    }
  }
}
[data-theme='dark'] .site-network-module {
  .sn-module-content {
    .snm-nums {
      .snm-item {
        background: none;
        background-image: linear-gradient(154deg, rgba(153, 192, 255, 0.13) 6%, rgba(255, 255, 255, 0) 100%);
        color: #ffffff;
      }
    }
  }
}
</style>