<template>
  <div class="report-num-module module-box">
    <div class="module-title-box">
      <div class="module-title">报警数量统计</div>
      <div class="mt-btns">
        <div :class="isActiveBtn == 1 ? 'btn active' : 'btn'" @click="handleClick(1)">当日</div>
        <div :class="isActiveBtn == 2 ? 'btn active' : 'btn'" @click="handleClick(2)">当月</div>
      </div>
    </div>
    <div class="rn-module-content">
      <div class="top-total">
        <div class="tt-left">
          <img src="@/assets/monitor/icon_shuizhanzongliang.png" />
          <div class="szzl-box">
            <div class="szzl-t">{{ siteStr }}</div>
            <div class="szzl-n">{{ reportNumModuleData.szzl }}</div>
          </div>
        </div>
        <div class="tt-right">
          <div class="ttr-style">
            <div class="ttr-t">报警站点</div>
            <div class="ttr-n">{{ reportNumModuleData.bjzd }}</div>
          </div>
          <div class="ttr-style">
            <div class="ttr-t">正常站点</div>
            <div class="ttr-n">{{ reportNumModuleData.zczd }}</div>
          </div>
        </div>
      </div>
      <div class="three-box">
        <div class="tb-item" @click="handleClickInfos(1)">
          <img src="@/assets/monitor/icon_baojingzongliang.png" />
          <div class="tbi-box">
            <div class="tbi-t">报警总量</div>
            <div class="tbi-n">{{ reportNumModuleData.bjzs }}</div>
          </div>
        </div>
        <div class="tb-item">
          <img src="@/assets/monitor/icon_yichuli.png" />
          <div class="tbi-box">
            <div class="tbi-t">已处理</div>
            <div class="tbi-n">{{ reportNumModuleData.ycl }}</div>
          </div>
        </div>
        <div class="tb-item">
          <img src="@/assets/monitor/icon_daichuli.png" />
          <div class="tbi-box">
            <div class="tbi-t">待处理</div>
            <div class="tbi-n">{{ reportNumModuleData.dcl }}</div>
          </div>
        </div>
      </div>
      <div class="four-box">
        <div class="fb-item">
          <div class="fbi-i-box">
            <img src="@/assets/monitor/icon_yanzhong_xiao.png" />
            <div class="fbi-t">严重</div>
          </div>
          <div class="fbi-n">{{ reportNumModuleData.yz }}</div>
        </div>
        <div class="fb-item">
          <div class="fbi-i-box">
            <img src="@/assets/monitor/icon_zhongyao_xiao.png" />
            <div class="fbi-t">重要</div>
          </div>
          <div class="fbi-n">{{ reportNumModuleData.zy }}</div>
        </div>
        <div class="fb-item">
          <div class="fbi-i-box">
            <img src="@/assets/monitor/icon_zhongdeng_xiao.png" />
            <div class="fbi-t">中等</div>
          </div>
          <div class="fbi-n">{{ reportNumModuleData.zd }}</div>
        </div>
        <div class="fb-item">
          <div class="fbi-i-box">
            <img src="@/assets/monitor/icon_yiban_xiao.png" />
            <div class="fbi-t">一般</div>
          </div>
          <div class="fbi-n">{{ reportNumModuleData.yb }}</div>
        </div>
      </div>
    </div>
    <el-dialog class="info-dialog" v-model="dialogVisible" width="920" :before-close="handleClose">
      <div class="id-dialog-content">
        <div class="id-header-title">站点列表</div>
        <div class="item-content">
          <DivTable :data="tableData" :columns="columns" />
        </div>
        <el-pagination layout="prev, pager, next" :total="50" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DivTable from '../DivTable/index.vue'

// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  }
})

// 当日当月
let isActiveBtn = ref(1)
const handleClick = (btn) => {
  isActiveBtn.value = btn
}

const reportNumModuleData = ref({})

const getReportNumModuleData = () => {
  let adr = ['呼和浩特', '包头', '乌兰察布']
  let bbr = ['二十九中', '红旗小学', '昆区政府', '青山宾馆', '集宁新区']

  let bjzd = Math.floor(Math.random() * 50)
  let zczd = Math.floor(Math.random() * 2320)
  let ycl = Math.floor(Math.random() * 100)
  let dcl = Math.floor(Math.random() * 100)
  let yz = Math.floor(Math.random() * 100)
  let zy = Math.floor(Math.random() * 100)
  let zd = Math.floor(Math.random() * 100)
  let yb = Math.floor(Math.random() * 100)
  let dataList = [
    { time: '07-25 09:25', name: '颗粒物参数改变报警', city: '呼和浩特', site: '二十九中', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:20 ', name: '颗粒物采样流量0值报警', city: '呼和浩特', site: '红旗小学', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:15', name: '天虹颗粒物设备脉冲频率波动异常', city: '包头', site: '昆区政府', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:10', name: '天虹颗粒物设备脉冲频率波动异常', city: '包头', site: '青山宾馆', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:10', name: '天虹颗粒物设备脉冲频率波动异常', city: '乌兰察布', site: '集宁新区', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:10', name: '远程软件运行报警', city: '沈阳', site: '浑南东路', status: 1, type: '远程软件安装报警' },
    { time: '07-25 09:05', name: '天虹颗粒物设备脉冲频率波动异常', city: '大连', site: '甘井子', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:05', name: '颗粒物参数改变报警', city: '济南', site: '农科所', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:00', name: '天虹颗粒物设备脉冲频率波动异常', city: '青岛', site: '黄岛区4号', status: 1, type: '关键参数异常报警' },
    { time: '07-25 09:00', name: '颗粒物采样流量0值报警', city: '北京', site: '密云新城', status: 1, type: '关键参数异常报警' },
    { time: '07-25 08:55', name: '远程软件运行报警', city: '南京', site: '玄武湖', status: 1, type: '远程软件安装报警' },
  ]

  let RNMData = {
    szzl: bjzd + zczd,
    bjzd,
    zczd,
    bjzs: ycl + dcl,
    ycl,
    dcl,
    yz,
    zy,
    zd,
    yb,
    dataList
  }
  reportNumModuleData.value = RNMData
}

onMounted(() => {
  getReportNumModuleData()
})

watch([isActiveBtn, () => props.currentType], () => getReportNumModuleData())

const siteStr = computed(() => {
  if (props.currentType == 'all') {
    return '站点总量'
  } else if (props.currentType == 'shui') {
    return '水站总量'
  } else if (props.currentType == 'kq') {
    return '气站总量'
  }
})
const dialogVisible = ref(false)

const handleClickInfos = (type) => {
  // 处理点击事件，可能是跳转到详情页或弹出信息
  console.log(`Clicked on info type: ${type}`)
  dialogVisible.value = true
}
const handleClose = () => {
  dialogVisible.value = false
}

const tableData = computed(() => {
  return reportNumModuleData.value.dataList.map((item, index) => {
    return {
      index: `TOP ${index + 1}`,
      statusText: item.status == 1 ? '严重' : '正常',
      ...item
    }
  })
})

const columns = [
  { prop: 'index', title: '序号', width: '72px' },
  { prop: 'time', title: '报警时间', width: '110px' },
  { prop: 'name', title: '报警名称', width: '260px' },
  { prop: 'city', title: '城市', width: '100px' },
  { prop: 'site', title: '站点', width: '100px' },
  { prop: 'statusText', title: '状态', width: '60px' },
  { prop: 'type', title: '报警类型' }
]
</script>

<style lang="scss">
.info-dialog {
  margin-top: 15vh;
  height: 600px;
  padding: 0;
  background-image: url('@/assets/monitor/dialog-bg.png');
  background-size: 100% 100%;
  border-radius: unset;
  background-color: unset;
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    height: 100%;
    overflow-y: auto;
  }
  .id-dialog-content {
    height: 100%;
    padding-bottom: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .id-header-title {
      padding-left: 16px;
      font-size: 16px;
      color: #ffffff;
      font-weight: 700;
      line-height: 40px;
      margin-bottom: 16px;
    }
    .item-content {
      padding: 0 16px;
      flex: 1;
      overflow-y: auto;
    }
    .table-wrapper {
      max-height: 500px;
    }
  }
  .el-pagination {
    margin-top: 10px;
    justify-content: center;
  }
}
</style>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.report-num-module {
  width: 100%;
  height: px2rem(316);
  .rn-module-content {
    .top-total {
      padding: 0 px2rem(16);
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: px2rem(74);
      background-image: url('@/assets/monitor/bg_zongliang.png');
      background-size: 100% 100%;
      .tt-left {
        display: flex;
        align-items: center;
        img {
          width: px2rem(40);
          height: px2rem(40);
          margin-right: px2rem(16);
        }
        .szzl-box {
          .szzl-t {
            font-size: px2rem(14);
            color: #ffffff;
            line-height: px2rem(20);
          }
          .szzl-n {
            margin-top: px2rem(6);
            font-size: px2rem(28);
            color: #ffffff;
            font-weight: 700;
            line-height: px2rem(30);
          }
        }
      }
      .tt-right {
        width: 50%;
        .ttr-style {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: px2rem(14);
          background-image: url('@/assets/monitor/bg_tongji_190.png');
          background-size: 100% 100%;
          padding-right: px2rem(8);
          .ttr-t {
            font-size: px2rem(14);
            color: #ffffff;
            line-height: px2rem(20);
            padding-left: px2rem(28);
            position: relative;
            &::before {
              content: '';
              position: absolute;
              display: block;
              left: px2rem(10);
              top: 50%;
              transform: translateY(-50%);
              width: px2rem(12);
              height: px2rem(12);
              background-image: url('@/assets/monitor/icon_dian_12.svg');
              background-size: 100% 100%;
            }
          }
          .ttr-n {
            font-size: px2rem(20);
            color: #ffffff;
            font-weight: 700;
            line-height: px2rem(24);
          }
        }
        .ttr-style:first-child {
          margin-top: 0;
        }
      }
    }

    .three-box {
      margin-top: px2rem(8);
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tb-item + .tb-item {
        margin-left: px2rem(10);
      }
      .tb-item {
        cursor: pointer;
        border-radius: px2rem(3);
        padding: 0 px2rem(14);
        background: rgba(49, 103, 213, 0.05);
        height: px2rem(74);
        display: flex;
        align-items: center;
        img {
          width: px2rem(40);
          height: px2rem(40);
          margin-right: px2rem(8);
        }
        .tbi-box {
          .tbi-t {
            font-size: px2rem(14);
            white-space: nowrap;
            color: #333333;
            font-weight: 500;
            line-height: px2rem(20);
          }
          .tbi-n {
            margin-top: px2rem(6);
            font-size: px2rem(24);
            color: #333333;
            font-weight: 700;
            line-height: px2rem(30);
          }
        }
      }
    }
    .four-box {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: px2rem(10);
      margin-top: px2rem(8);
      .fb-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 px2rem(12);
        background: rgba(49, 103, 213, 0.05);
        color: #333333;
        height: px2rem(36);
        border-radius: px2rem(3);
        .fbi-i-box {
          display: flex;
          align-items: center;
          img {
            width: px2rem(24);
            height: px2rem(24);
            margin-right: px2rem(16);
          }
        }
        .fbi-n {
          font-size: px2rem(24);
          font-weight: 700;
          line-height: px2rem(24);
        }
      }
    }
  }
}

[data-theme='dark'] .report-num-module {
  .top-total {
    background-image: url('@/assets/monitor/bg_zongliang_2.png');
    .tt-right {
      .ttr-style {
        &:first-child {
          background-image: url('@/assets/monitor/bg_tongji_190_2.png');
          .ttr-t::before {
            background-image: url('@/assets/monitor/icon_dian_12_huang.svg');
          }
        }
        &:last-child {
          background-image: url('@/assets/monitor/bg_tongji_190_lv.png');
          .ttr-t::before {
            background-image: url('@/assets/monitor/icon_dian_12_lv.svg');
          }
        }
      }
    }
  }
  .three-box {
    .tb-item {
      background: none;
      background-image: url('@/assets/monitor/bg_148_2.png');
      background-size: 100% 100%;
      .tbi-box {
        .tbi-t {
          color: #ffffff;
        }
        .tbi-n {
          color: #ffffff;
        }
      }
    }
  }
  .four-box {
    .fb-item {
      color: #ffffff;
      background: none;
      background-image: url('@/assets/monitor/bg_201_lan.png');
      background-size: 100% 100%;
    }
  }
}
</style>