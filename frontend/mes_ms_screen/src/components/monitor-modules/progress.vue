<template>
  <div class="progress-module module-box">
    <div class="module-title-box">
      <div class="module-title">{{ title }}</div>
      <div class="mt-btns">
        <div :class="isActiveBtn == 1 ? 'btn active' : 'btn'" @click="handleClick(1)">当日</div>
        <div :class="isActiveBtn == 2 ? 'btn active' : 'btn'" @click="handleClick(2)">当月</div>
      </div>
    </div>
    <div class="sn-module-content">
      <el-tabs v-model="activeTab" class="module-tabs">
        <el-tab-pane v-for="it in tabs" :key="it.value" :name="it.value" :label="it.label"></el-tab-pane>
      </el-tabs>
      <div class="snm-list">
        <div class="snm-item" v-for="(item, index) in sortList" :key="index">
          <div class="snmi-icon">
            {{ 'Top ' + (index + 1) }}
          </div>
          <div class="snmi-t" :title="item.name">{{ item.name }}</div>
          <div class="snmi-p">
            <el-progress :percentage="(item.value / maxNum) * 100" :show-text="false" :stroke-width="5" />
          </div>
          <div class="snmi-n">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  },
})

let title = ref('报警区域统计')

let tabs = ref([
  { label: '全部', value: 'total' },
  { label: '严重', value: 'yz' },
  { label: '重要', value: 'zy' },
  { label: '中等', value: 'zd' },
  { label: '一般', value: 'yb' },
])

let isActiveBtn = ref(1)
const handleClick = (btn) => {
  isActiveBtn.value = btn
}
let activeTab = ref('total')

const progressModuleData = ref({})

const getProgressModuleData = () => {
  let keys = [
    { label: '全部', value: 'total' },
    { label: '严重', value: 'yz' },
    { label: '重要', value: 'zy' },
    { label: '中等', value: 'zd' },
    { label: '一般', value: 'yb' }
  ]
  let adr = ['内蒙古', '辽宁', '山东', '北京', '江苏', '重庆', '成都']
  let RNMData = {}
  for (let i = 0; i < keys.length; i++) {
    const k = keys[i]
    RNMData[`${k.value}Data`] = []
    for (let j = 0; j < 7; j++) {
      RNMData[`${k.value}Data`].push({
        name: adr[j],
        value: Math.floor(Math.random() * 30)
      })
    }
  }
  progressModuleData.value = RNMData
}

onMounted(() => {
  getProgressModuleData()
})

watch([isActiveBtn, () => props.currentType], () => getProgressModuleData())

const sortList = computed(() => {
  if (progressModuleData.value[`${activeTab.value}Data`]) {
    return progressModuleData.value[`${activeTab.value}Data`].sort((a, b) => {
      return b.value - a.value
    })
  } else {
    return []
  }
})

// console.log('sortList====>', sortList)

const maxNum = computed(() => {
  return Math.max(...sortList.value.map((item) => item.value))
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.progress-module {
  width: 100%;
  // flex: 1;
  // height: px2rem(316); // 需要设置高度或者flex: 1
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .module-title-box {
    margin-bottom: 0 !important;
  }
  .sn-module-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .snm-list {
      margin-top: px2rem(20);
      flex: 1;
      overflow-y: auto;
      .snm-item + .snm-item {
        margin-top: px2rem(10);
      }
      .snm-item {
        display: flex;
        align-items: center;
        margin-bottom: px2rem(10);
        color: #333333;
        &:nth-child(1) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_1.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(243deg, #da6241 0%, #c04126 98%);
            }
          }
        }
        &:nth-child(2) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_2.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(243deg, #f3893c 0%, #ed6a0c 98%);
            }
          }
        }
        &:nth-child(3) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_3.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(243deg, #00d1b5 0%, #00a680 98%);
            }
          }
        }
        .snmi-icon {
          width: px2rem(60);
          height: px2rem(20);
          line-height: px2rem(20);
          text-align: center;
          background-image: url('@/assets/monitor/top_lan.png');
          background-size: 100% 100%;
          margin-right: px2rem(12);
          font-size: px2rem(12);
          color: #f4f7ff;
        }
        .snmi-t {
          // flex: 1;
          min-width: px2rem(50);
          max-width: px2rem(120);
          margin-right: px2rem(2);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: px2rem(14);
          line-height: px2rem(20);
        }
        .snmi-p {
          flex: 1;
          margin-right: px2rem(10);
        }
        :deep(.el-progress) {
          .el-progress-bar__outer {
            background-color: transparent;
          }
          .el-progress-bar__inner {
            background-image: linear-gradient(243deg, #2fb1ec 0%, #155bd4 98%);
          }
        }
        .snmi-n {
          width: px2rem(50);
          text-align: center;
          font-size: px2rem(16);
        }
      }
    }
  }
}
[data-theme='dark'] .progress-module {
  .module-title-box {
    margin-bottom: px2rem(-8) !important;
  }
  .snm-list {
    .snm-item {
      color: #cbdcff;
    }
  }
}
</style>
