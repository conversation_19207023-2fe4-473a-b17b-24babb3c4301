<template>
  <div class="pie-gl-module module-box">
    <div class="module-title-box">
      <div class="module-title">报警分类统计</div>
      <div class="mt-btns">
        <div :class="isActiveBtn == 1 ? 'btn active' : 'btn'" @click="handleClick(1)">当日</div>
        <div :class="isActiveBtn == 2 ? 'btn active' : 'btn'" @click="handleClick(2)">当月</div>
      </div>
    </div>
    <el-tabs v-model="activeTab" class="module-tabs">
      <el-tab-pane :name="1" label="报警原因"></el-tab-pane>
      <el-tab-pane :name="2" label="监控项目"></el-tab-pane>
    </el-tabs>
    <div class="pg-module-content">
      <Echarts :chart-data="chartData" />
    </div>
  </div>
</template>

<script setup>
import Echarts from '../ECharts/index.vue'
import { computed, ref } from 'vue'
import { useTheme } from '@/utils/useTheme';

// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  },
  moduleData: {
    type: Object
  }
})

// 当日当月
let isActiveBtn = ref(1)
const handleClick = (btn) => {
  isActiveBtn.value = btn
}

let activeTab = ref(1)

const pieGlModuleData = ref({})

const getPieGlModuleData = () => {
  let RNMData = {
    dataList: [
      { name: '仪器故障', value: Math.floor(Math.random() * 20) },
      { name: '站点停电', value: Math.floor(Math.random() * 20) },
      { name: '仪器检查', value: Math.floor(Math.random() * 20) },
      { name: '仪器出值延迟', value: Math.floor(Math.random() * 20) },
      { name: '落灰、进异物', value: Math.floor(Math.random() * 20) },
      { name: '其他', value: Math.floor(Math.random() * 20) }
    ],
    dataList1: [
      { name: '监测参数', value: Math.floor(Math.random() * 20) },
      { name: '设备运行', value: Math.floor(Math.random() * 20) },
      { name: '质控记录', value: Math.floor(Math.random() * 20) },
      { name: '资源监控', value: Math.floor(Math.random() * 20) },
      { name: '视频监控', value: Math.floor(Math.random() * 20) },
      { name: '其他', value: Math.floor(Math.random() * 20) }
    ]
  }
  pieGlModuleData.value = RNMData
}

onMounted(() => {
  getPieGlModuleData()
})

watch([isActiveBtn, () => props.currentType], () => getPieGlModuleData())

const datas = computed(() => {
  if (pieGlModuleData.value.dataList) {
    if (activeTab.value === 1) {
      return pieGlModuleData.value.dataList.map((item) => {
        return {
          name: item.name,
          value: item.value
        }
      })
    } else {
      return pieGlModuleData.value.dataList1.map((item) => {
        return {
          name: item.name,
          value: item.value
        }
      })
    }
  } else {
    return []
  }
})

const maxValue = Math.max(...datas.value.map((item) => item.value))
const { theme } = useTheme()
const color = computed(() => {
  return theme.value == 'light' ? '#000000' : '#ffffff'
})

let chartData = computed(() => {
  return {
    color: ['#4FA0FF', '#67C23A', '#FBD500', '#FF9A54', '#F56C6D', '#00BBF5'],
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    // legend: {
    //   data: datas.value.map((item) => item.name),
    //   type: 'scroll',
    //   orient: 'vertical',
    //   formatter: (name) => {
    //     let value = datas.value.find((item) => item.name === name)?.value || 0
    //     return `${name}    ${value}`
    //   },
    //   right: 10,
    //   tooltip: {
    //     show: false
    //   }
    // },
    series: [
      {
        name: '3D Pie Chart',
        type: 'pie',
        radius: ['40%', '80%'],
        itemStyle: {},
        label: {
          color: color.value,
          show: true,
          formatter: '{b}: {c} ({d}%)',
        },
        data: datas.value.map((item) => ({
          ...item,
          // 高度与占比成正比
          itemStyle: {
            height: (item.value / maxValue) * 30 // 30是最大高度基数
          }
        })),
        height: '100%'
      }
    ]
  }
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.pie-gl-module {
  width: 100%;
  height: px2rem(286);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .module-title-box {
    margin-bottom: 0 !important;
  }
  .pg-module-content {
    margin-top: px2rem(8);
    flex: 1;
  }
}

[data-theme='dark'] .pie-gl-module {
  .module-title-box {
    margin-bottom: px2rem(-8) !important;
  }
}
</style>