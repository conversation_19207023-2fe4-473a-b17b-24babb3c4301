<template>
  <div class="warning-trend-module module-box">
    <div class="module-title-box">
      <div class="module-title">报警趋势统计</div>
    </div>
    <div class="wt-module-content">
      <Echarts :chart-data="chartData" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '../ECharts/index.vue'
import { useTheme } from '@/utils/useTheme'

// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  },
  moduleData: {
    type: Object
  }
})
// const data_all = {
//   dataList: [
//     { date: '07-19', value1: 5, value2: 10, value3: 25, value4: 50 },
//     { date: '07-20', value1: 8, value2: 15, value3: 30, value4: 45 },
//     { date: '07-21', value1: 22, value2: 9, value3: 21, value4: 36 },
//     { date: '07-22', value1: 15, value2: 8, value3: 15, value4: 35 },
//     { date: '07-23', value1: 20, value2: 10, value3: 27, value4: 30 },
//     { date: '07-24', value1: 10, value2: 15, value3: 17, value4: 25 },
//     { date: '07-25', value1: 11, value2: 13, value3: 12, value4: 21 }
//   ]
// }
// const data_shui = {
//   dataList: [
//     { date: '07-19', value1: 15, value2: 20, value3: 21, value4: 26 },
//     { date: '07-20', value1: 9, value2: 15, value3: 23, value4: 33 },
//     { date: '07-21', value1: 11, value2: 23, value3: 30, value4: 32 },
//     { date: '07-22', value1: 15, value2: 11, value3: 21, value4: 31 },
//     { date: '07-23', value1: 13, value2: 5, value3: 11, value4: 17 },
//     { date: '07-24', value1: 10, value2: 6, value3: 10, value4: 16 },
//     { date: '07-25', value1: 8, value2: 11, value3: 10, value4: 14 }
//   ]
// }

// const data_kq = {
//   dataList: [
//     { date: '07-19', value1: 15, value2: 18, value3: 35, value4: 40 },
//     { date: '07-20', value1: 23, value2: 25, value3: 30, value4: 35 },
//     { date: '07-21', value1: 13, value2: 29, value3: 31, value4: 26 },
//     { date: '07-22', value1: 17, value2: 28, value3: 25, value4: 25 },
//     { date: '07-23', value1: 15, value2: 20, value3: 17, value4: 25 },
//     { date: '07-24', value1: 12, value2: 25, value3: 27, value4: 20 },
//     { date: '07-25', value1: 19, value2: 23, value3: 12, value4: 19 }
//   ]
// }

// const props.moduleData = computed(() => {
//   if (props.currentType == 'all') {
//     return data_all
//   } else if (props.currentType == 'shui') {
//     return data_shui
//   } else if (props.currentType == 'kq') {
//     return data_kq
//   }
// })

let datalistCopy = computed(() => {
  return props.moduleData.dataList.map((it) => {
    return {
      date: it.date,
      valueAll: it.value1 + it.value2 + it.value3 + it.value4,
      value1: it.value1,
      value2: it.value2,
      value3: it.value3,
      value4: it.value4
    }
  })
})

const valueType = [
  { name: '全部', value: 'valueAll', color: '#0065D5' },
  { name: '严重', value: 'value1', color: '#FA585A' },
  { name: '重要', value: 'value2', color: '#50BAFF' },
  { name: '中等', value: 'value3', color: '#FFBF00' },
  { name: '一般', value: 'value4', color: '#4DCB73' }
]

const { theme } = useTheme()
const color = computed(() => {
  return theme.value == 'light' ? '#000000' : '#ffffff'
})

const series = computed(() => {
  return valueType.map((type) => {
    return {
      type: 'line',
      smooth: true,
      symbol: 'circle',
      name: type.name,
      data: datalistCopy.value.map((da) => da[type.value]),
      itemStyle: {
        color: type.color
      }
    }
  })
})

let chartData = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 16,
      textStyle: {
        color: color.value
      },
      data: valueType.map((item) => item.name)
    },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLabel: {
        color: color.value
      },
      data: datalistCopy.value.map((item) => item.date)
    },
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: color.value
        }
      }
    ],
    series: series.value
  }
})

console.log('chartData====>', chartData.value)
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.warning-trend-module {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .wt-module-content {
    width: 100%;
    flex: 1;
  }
}
</style>