<template>
  <div class="warning-dispose-module module-box">
    <div class="module-title-box">
      <div class="module-title">报警处置统计</div>
      <div class="mt-btns">
        <div :class="isActiveBtn == 1 ? 'btn active' : 'btn'" @click="handleClick(1)">当日</div>
        <div :class="isActiveBtn == 2 ? 'btn active' : 'btn'" @click="handleClick(2)">当月</div>
      </div>
    </div>
    <div class="tabs-box">
      <el-tabs v-model="activeTab" class="module-tabs">
        <el-tab-pane :name="1" label="上站率"></el-tab-pane>
        <el-tab-pane :name="2" label="及时率"></el-tab-pane>
      </el-tabs>
      <div class="full-screen-btn" title="全屏">
        <el-icon color="#999999" size="26" @click="handleClickInfos('warning-dispose')"><FullScreen /></el-icon>
      </div>
    </div>
    <div class="wt-module-content">
      <Echarts :chart-data="chartData" />
    </div>
    <el-dialog class="full-screen-echarts-dialog" title="报警处置统计" v-model="dialogVisible" width="920px" :before-close="handleClose">
      <Echarts v-if="dialogVisible" :chart-data="chartData" />
    </el-dialog>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Echarts from '@/components/ECharts/index.vue'
import { FullScreen } from '@element-plus/icons-vue'
import { useTheme } from '@/utils/useTheme'

// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  }
})

const dialogVisible = ref(false)

const handleClickInfos = (type) => {
  dialogVisible.value = true
}
const handleClose = () => {
  dialogVisible.value = false
}

let isActiveBtn = ref(1)
const handleClick = (btn) => {
  isActiveBtn.value = btn
}

let activeTab = ref(1)

const warningDisposeModuleData = ref({ dataList: [] })


const getWarningDisposeModuleData = () => {
  let RNMData = {
    dataList: [
      { date: '中节能数字', value1: Math.floor(Math.random() * 20), value2: Math.floor(Math.random() * 100), value3: Math.floor(Math.random() * 100) },
      { date: '青岛吉美来', value1: Math.floor(Math.random() * 20), value2: Math.floor(Math.random() * 100), value3: Math.floor(Math.random() * 100) },
      { date: '华通力盛', value1: Math.floor(Math.random() * 20), value2: Math.floor(Math.random() * 100), value3: Math.floor(Math.random() * 100) },
      { date: '力合科技', value1: Math.floor(Math.random() * 20), value2: Math.floor(Math.random() * 100), value3: Math.floor(Math.random() * 100) },
      { date: '海康', value1: Math.floor(Math.random() * 20), value2: Math.floor(Math.random() * 100), value3: Math.floor(Math.random() * 100) }
    ]
  }
  warningDisposeModuleData.value = RNMData
}

onMounted(() => {
  getWarningDisposeModuleData()
})

watch([isActiveBtn, () => props.currentType], () => getWarningDisposeModuleData())

const { theme } = useTheme()
const color = computed(() => {
  return theme.value == 'light' ? '#000000' : '#ffffff'
})

let legendData = computed(() => {
  if (activeTab.value == 1) {
    return ['报警数', '上站率']
  } else {
    return ['报警数', '及时率']
  }
})

let yAxisData = computed(() => {
  if (activeTab.value == 1) {
    return [
      {
        type: 'value',
        name: '报警数',
        axisLabel: {
          color: color.value
        },
      },
      {
        type: 'value',
        name: '上站率',
        splitLine: {
          show: false
        },
        axisLabel: {
          color: color.value
        },
      }
    ]
  } else {
    return [
      {
        type: 'value',
        name: '报警数',
        axisLabel: {
          color: color.value
        },
      },
      {
        type: 'value',
        name: '及时率',
        splitLine: {
          show: false
        },
        axisLabel: {
          color: color.value
        },
      }
    ]
  }
})

let seriesData = computed(() => {
  let arr = [
    {
      type: 'bar',
      smooth: true,
      name: '报警数',
      data: warningDisposeModuleData.value.dataList.map((da) => da.value1),
      itemStyle: {
        color: '#0065D5'
      }
    }
  ]
  if (activeTab.value == 1) {
    arr.push({
      type: 'line',
      symbol: 'circle',
      smooth: true,
      name: '上站率',
      yAxisIndex: 1,
      data: warningDisposeModuleData.value.dataList.map((da) => da.value2),
      itemStyle: {
        color: '#4DCB73'
      }
    })
    return arr
  } else {
    arr.push({
      type: 'line',
      symbol: 'circle',
      smooth: true,
      name: '及时率',
      yAxisIndex: 1,
      data: warningDisposeModuleData.value.dataList.map((da) => da.value3),
      itemStyle: {
        color: '#4DCB73'
      }
    })
    return arr
  }
})

let chartData = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 16,
      textStyle: {
        color: color.value
      },
      data: legendData.value
    },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      axisLabel: {
        color: color.value
      },
      data: warningDisposeModuleData.value.dataList.map((item) => item.date)
    },
    yAxis: yAxisData.value,
    series: seriesData.value
  }
})

</script>

<style lang="scss">
.full-screen-echarts-dialog {
  margin-top: 15vh;
  height: 600px;
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
  }
}
</style>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.warning-dispose-module {
  width: 100%;
  height: px2rem(262);
  // overflow-y: auto;
  display: flex;
  flex-direction: column;
  .module-title-box {
    margin-bottom: 0 !important;
  }
  .tabs-box {
    position: relative;
    .full-screen-btn {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: px2rem(32);
      height: px2rem(32);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .wt-module-content {
    width: 100%;
    flex: 1;
  }
}
[data-theme='dark'] .warning-dispose-module {
  .module-title-box {
    margin-bottom: px2rem(-8) !important;
  }
}
</style>