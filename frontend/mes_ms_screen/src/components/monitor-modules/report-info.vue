<template>
  <div class="report-info-module module-box">
    <div class="module-title-box">
      <div class="module-title">实时报警信息</div>
    </div>
    <div class="ri-module-content">
      <div class="ri-info-types">
        <div class="riit-item" :class="{ active: activeType == 1 }" @click="handleChangeType(1)">
          <img src="@/assets/monitor/icon_yanzhong_da.png" />
          <div class="riit-t">严重</div>
        </div>
        <div class="riit-item" :class="{ active: activeType == 2 }" @click="handleChangeType(2)">
          <img src="@/assets/monitor/icon_zhongyao_da.png" />
          <div class="riit-t">重要</div>
        </div>
        <div class="riit-item" :class="{ active: activeType == 3 }" @click="handleChangeType(3)">
          <img src="@/assets/monitor/icon_zhongdeng_da.png" />
          <div class="riit-t">中等</div>
        </div>
        <div class="riit-item" :class="{ active: activeType == 4 }" @click="handleChangeType(4)">
          <img src="@/assets/monitor/icon_yiban_da.png" />
          <div class="riit-t">一般</div>
        </div>
      </div>
      <div class="table-box">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="name" label="城市" width="90" align="center"> </el-table-column>
          <el-table-column prop="site" label="站点" width="90" align="center"> </el-table-column>
          <el-table-column label="状态" width="110" align="center">
            <template #default="{ row }">
              <span class="statusStyle" :style="{ backgroundColor: status[row.type].color }">{{ status[row.type].name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="报警信息">
            <template #default="{ row }">
              <el-tooltip :content="row.reason" placement="right" effect="light">
                <div class="reasonStyle">{{ row.reason }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
// 从外部传入数据
const props = defineProps({
  currentType: {
    type: String,
    default: 'all'
  },
  moduleData: {
    type: Object
  }
})
// const data_all = {
//   dataList1: [
//     { name: '呼和浩特', site: '二十九中', type: 1, reason: '07-25 09:25 颗粒物参数改变报警' },
//     { name: '呼和浩特', site: '红旗小学', type: 1, reason: '07-25 09:20 颗粒物采样流量0值报警' },
//     { name: '包头', site: '昆区政府', type: 1, reason: '07-25 09:15 天虹颗粒物设备脉冲' },
//     { name: '包头', site: '青山宾馆', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' },
//     { name: '乌兰察布', site: '集宁新区', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' },
//     { name: '沈阳', site: '浑南东路', type: 1, reason: '07-25 09:10 远程软件运行报警' }
//   ],
//   dataList2: [
//     { name: '鞍山', site: '太阳城', type: 2, reason: '07-25 09:20 断数0.5h报警' },
//     { name: '葫芦岛', site: '化工街', type: 2, reason: '07-25 09:20 PM2.5大气温度恒值报警' },
//     { name: '锦州', site: '北湖公园', type: 2, reason: '07-25 09:20 PM2.5大气温度异常报警' },
//     { name: '济南', site: '农科所', type: 2, reason: '07-25 09:10 PM2.5大气温度恒值报警' },
//     { name: '济南', site: '济南四建', type: 2, reason: '07-25 09:10 PM2.5大气压力异常报警' },
//     { name: '济南', site: '机床二厂', type: 2, reason: '07-25 09:05 PM10大气压力异常报警' }
//   ],
//   dataList3: [
//     { name: '鞍山', site: '太阳城', type: 3, reason: '07-25 09:20 PM2.5区域趋势异常报警' },
//     { name: '葫芦岛', site: '化工街', type: 3, reason: '07-25 09:20 浓度倒挂报警' },
//     { name: '锦州', site: '北湖公园', type: 3, reason: '07-25 09:15 PM2.5区域趋势异常报警' },
//     { name: '济南', site: '农科所', type: 3, reason: '07-25 09:10 PM2.5区域趋势异常报警' },
//     { name: '济南', site: '济南四建', type: 3, reason: '07-25 09:05 浓度倒挂报警' },
//     { name: '济南', site: '机床二厂', type: 3, reason: '07-25 09:05 PM2.5区域趋势异常报警' }
//   ],
//   dataList4: [
//     { name: '鞍山', site: '太阳城', type: 4, reason: '07-25 09:20 PM2.5数据离群' },
//     { name: '葫芦岛', site: '化工街', type: 4, reason: '07-25 09:20 PM2.5数据洼地报警' },
//     { name: '锦州', site: '北湖公园', type: 4, reason: '07-25 09:15 PM2.5跨级别污染' },
//     { name: '济南', site: '农科所', type: 4, reason: '07-25 09:10 PM2.5数据洼地报警' },
//     { name: '济南', site: '济南四建', type: 4, reason: '07-25 09:05 CO数据离群' },
//     { name: '济南', site: '机床二厂', type: 4, reason: '07-25 09:05 PM2.5数据离群' }
//   ]
// }
// const data_shui = {
//   dataList1: [
//     { name: '包头', site: '昆区政府', type: 1, reason: '07-25 09:15 天虹颗粒物设备脉冲' },
//     { name: '呼和浩特', site: '红旗小学', type: 1, reason: '07-25 09:20 颗粒物采样流量0值报警' },
//     { name: '沈阳', site: '浑南东路', type: 1, reason: '07-25 09:10 远程软件运行报警' },
//     { name: '包头', site: '青山宾馆', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' },
//     { name: '呼和浩特', site: '二十九中', type: 1, reason: '07-25 09:25 颗粒物参数改变报警' },
//     { name: '乌兰察布', site: '集宁新区', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' }
//   ],
//   dataList2: [
//     { name: '葫芦岛', site: '化工街', type: 2, reason: '07-25 09:20 PM2.5大气温度恒值报警' },
//     { name: '锦州', site: '北湖公园', type: 2, reason: '07-25 09:20 PM2.5大气温度异常报警' },
//     { name: '济南', site: '济南四建', type: 2, reason: '07-25 09:10 PM2.5大气压力异常报警' },
//     { name: '鞍山', site: '太阳城', type: 2, reason: '07-25 09:20 断数0.5h报警' },
//     { name: '济南', site: '机床二厂', type: 2, reason: '07-25 09:05 PM10大气压力异常报警' },
//     { name: '济南', site: '农科所', type: 2, reason: '07-25 09:10 PM2.5大气温度恒值报警' }
//   ],
//   dataList3: [
//     { name: '鞍山', site: '太阳城', type: 3, reason: '07-25 09:20 PM2.5区域趋势异常报警' },
//     { name: '济南', site: '农科所', type: 3, reason: '07-25 09:10 PM2.5区域趋势异常报警' },
//     { name: '济南', site: '济南四建', type: 3, reason: '07-25 09:05 浓度倒挂报警' },
//     { name: '锦州', site: '北湖公园', type: 3, reason: '07-25 09:15 PM2.5区域趋势异常报警' },
//     { name: '葫芦岛', site: '化工街', type: 3, reason: '07-25 09:20 浓度倒挂报警' },
//     { name: '济南', site: '机床二厂', type: 3, reason: '07-25 09:05 PM2.5区域趋势异常报警' }
//   ],
//   dataList4: [
//     { name: '鞍山', site: '太阳城', type: 4, reason: '07-25 09:20 PM2.5数据离群' },
//     { name: '济南', site: '济南四建', type: 4, reason: '07-25 09:05 CO数据离群' },
//     { name: '葫芦岛', site: '化工街', type: 4, reason: '07-25 09:20 PM2.5数据洼地报警' },
//     { name: '济南', site: '农科所', type: 4, reason: '07-25 09:10 PM2.5数据洼地报警' },
//     { name: '锦州', site: '北湖公园', type: 4, reason: '07-25 09:15 PM2.5跨级别污染' },
//     { name: '济南', site: '机床二厂', type: 4, reason: '07-25 09:05 PM2.5数据离群' }
//   ]
// }
// const data_kq = {
//   dataList1: [
//     { name: '包头', site: '昆区政府', type: 1, reason: '07-25 09:15 天虹颗粒物设备脉冲' },
//     { name: '呼和浩特', site: '二十九中', type: 1, reason: '07-25 09:25 颗粒物参数改变报警' },
//     { name: '呼和浩特', site: '红旗小学', type: 1, reason: '07-25 09:20 颗粒物采样流量0值报警' },
//     { name: '乌兰察布', site: '集宁新区', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' },
//     { name: '沈阳', site: '浑南东路', type: 1, reason: '07-25 09:10 远程软件运行报警' },
//     { name: '包头', site: '青山宾馆', type: 1, reason: '07-25 09:10 天虹颗粒物设备脉冲' }
//   ],
//   dataList2: [
//     { name: '鞍山', site: '太阳城', type: 2, reason: '07-25 09:20 断数0.5h报警' },
//     { name: '葫芦岛', site: '化工街', type: 2, reason: '07-25 09:20 PM2.5大气温度恒值报警' },
//     { name: '锦州', site: '北湖公园', type: 2, reason: '07-25 09:20 PM2.5大气温度异常报警' },
//     { name: '济南', site: '农科所', type: 2, reason: '07-25 09:10 PM2.5大气温度恒值报警' },
//     { name: '上海', site: '东方明珠', type: 2, reason: '07-25 09:10 PM2.5大气压力异常报警' },
//     { name: '济南', site: '机床二厂', type: 2, reason: '07-25 09:05 PM10大气压力异常报警' }
//   ],
//   dataList3: [
//     { name: '鞍山', site: '太阳城', type: 3, reason: '07-25 09:20 PM2.5区域趋势异常报警' },
//     { name: '济南', site: '农科所', type: 3, reason: '07-25 09:10 PM2.5区域趋势异常报警' },
//     { name: '葫芦岛', site: '化工街', type: 3, reason: '07-25 09:20 浓度倒挂报警' },
//     { name: '锦州', site: '北湖公园', type: 3, reason: '07-25 09:15 PM2.5区域趋势异常报警' },
//     { name: '重庆', site: '重钢', type: 3, reason: '07-25 09:05 浓度倒挂报警' },
//     { name: '济南', site: '机床二厂', type: 3, reason: '07-25 09:05 PM2.5区域趋势异常报警' }
//   ],
//   dataList4: [
//     { name: '锦州', site: '北湖公园', type: 4, reason: '07-25 09:15 PM2.5跨级别污染' },
//     { name: '葫芦岛', site: '化工街', type: 4, reason: '07-25 09:20 PM2.5数据洼地报警' },
//     { name: '济南', site: '农科所', type: 4, reason: '07-25 09:10 PM2.5数据洼地报警' },
//     { name: '鞍山', site: '太阳城', type: 4, reason: '07-25 09:20 PM2.5数据离群' },
//     { name: '济南', site: '济南四建', type: 4, reason: '07-25 09:05 CO数据离群' },
//     { name: '济南', site: '机床三厂', type: 4, reason: '07-25 09:05 PM2.5数据离群' }
//   ]
// }

// const totalData = computed(() => {
//   if (props.currentType == 'all') {
//     return data_all
//   } else if (props.currentType == 'shui') {
//     return data_shui
//   } else if (props.currentType == 'kq') {
//     return data_kq
//   }
// })

const status = {
  1: { name: '严重', color: '#F02C2F' },
  2: { name: '重要', color: '#50BAFF' },
  3: { name: '中等', color: '#FFBF00' },
  4: { name: '一般', color: '#4DCB73' }
}

let activeType = ref(1)

const handleChangeType = (type) => {
  activeType.value = type
}

const tableData = computed(() => {
  if (props.moduleData[`dataList${activeType.value}`]) {
    return props.moduleData[`dataList${activeType.value}`]
  } else {
    return []
  }
  // if (activeType.value === 1) {
  //   return props.moduleData.dataList1
  // } else if (activeType.value === 2) {
  //   return props.moduleData.dataList2
  // } else if (activeType.value === 3) {
  //   return props.moduleData.dataList3
  // } else {
  //   return props.moduleData.dataList4
  // }
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.report-info-module {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .ri-module-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .ri-info-types {
      display: flex;
      justify-content: space-around;
      margin-bottom: px2rem(16);
      .riit-item {
        width: px2rem(70);
        height: px2rem(70);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: px2rem(6);
        background-image: url('@/assets/monitor/bg_nor.png');
        background-size: 100% 100%;
        &.active {
          background-image: url('@/assets/monitor/bg_sel.png');
        }
        img {
          width: px2rem(40);
          height: px2rem(40);
        }
        .riit-t {
          margin-top: px2rem(2);
          font-size: px2rem(14);
          line-height: px2rem(20);
          color: #333;
          font-weight: 500;
        }
      }
    }
    .table-box {
      flex: 1;
      overflow-y: auto;
      :deep(.el-table) {
        height: 100%;
        --el-table-border-color: #ebeef5; // 边框色
        --el-table-tr-bg-color: #ffffff; // row背景色
        --el-table-row-hover-bg-color: #ffffff !important; // row背景色
        --el-bg-color-overlay: #f5f7fa !important; // 头部背景色
        --el-text-color-regular: #999999 !important; // 字体颜色
        tr {
          color: #333333;
        }
      }
      .statusStyle {
        display: inline-block;
        padding: 0 px2rem(16);
        height: px2rem(24);
        line-height: px2rem(24);
        border-radius: px2rem(12);
        font-size: px2rem(12);
        color: #fff;
        text-align: center;
      }
      .reasonStyle {
        font-size: px2rem(14);
        color: #333;
        line-height: px2rem(20);
        max-width: px2rem(200);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
[data-theme='dark'] .report-info-module {
  .ri-module-content {
    .ri-info-types {
      .riit-item {
        border: 1px solid rgba(20, 114, 255, 0.3);
        .riit-t {
          color: #ffffff;
        }
      }
    }
  }
  .table-box {
    :deep(.el-table) {
      --el-table-border-color: rgba(41, 93, 194, 0.5); // 边框色
      --el-table-tr-bg-color: #141e33; // row背景色
      --el-table-row-hover-bg-color: #141e33 !important; // row背景色
      --el-bg-color-overlay: #23304f !important; // 头部背景色
      --el-text-color-regular: #cbdcff !important; // 字体颜色
      .el-table__header-wrapper,
      .el-table__fixed-header-wrapper {
        th {
          background-color: #24314f !important;
          color: #cbdcff !important;
        }
      }
      tr {
        color: #cbdcff;
      }
    }
    .reasonStyle {
      color: #cbdcff;
    }
  }
}
</style>