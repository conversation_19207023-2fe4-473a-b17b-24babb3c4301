<template>
  <div class="iframe-container">
    <iframe 
      :src="url"
      frameborder="0"
      class="iframe-content"
      @load="handleIframeLoad"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  url: {
    type: String,
    required: true
  }
})

const handleIframeLoad = () => {
  console.log('iframe加载完成')
}
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 50px);
}

.iframe-content {
  width: 100%;
  height: 100%;
}
</style>