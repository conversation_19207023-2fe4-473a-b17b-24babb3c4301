<template>
   <div class="chart-box-container">
      <div class="chart-box" ref="chart"></div>
   </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
   list: {
      type: Array,
      default: []
   }
})

// 转换数据并计算总值
const dataList = props.list.map(item => ({
   ...item,
   value: Number(item.val),
   itemStyle: { color: item.labelColor }
}))

const total = dataList.reduce((sum, item) => sum + item.value, 0)

const seriesData = dataList.map(item => ({
   ...item,
   value: Number(((item.value / total) * 100).toFixed(2))
}))

const option = {
   tooltip: {
      trigger: 'item',
      formatter: params => {
         return `${params.name}<br/>
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
            ${params.data.val} (${params.percent}%)`
      },
      textStyle: {
         fontSize: 12
      }
   },
   legend: {
      show: false
   },
   series: [
      {
         name: '分类占比',
         type: 'pie',
         radius: ['0%', '70%'], 
         avoidLabelOverlap: false,
         label: {
            show: true,
            position: 'outside',
            fontSize: 11,
            color: '#333',
            lineHeight: 14,
            formatter: params => {              
               const name = params.name.replace(/(.{6})/g, '$1\n')
               return `${name}\n${params.percent}%`
            }
         },
         labelLine: {
            length: 8,
            length2: 10,
            smooth: true,
            lineStyle: {
               color: '#ccc'
            }
         },
         labelLayout: {
            hideOverlap: true
         },
         data: seriesData
      }
   ]
}

const chart = ref(null)

onMounted(() => {
  nextTick(() => {
    const myChart = echarts.init(chart.value)
    myChart.setOption(option)
    window.addEventListener('resize', () => {
      myChart.resize()
    })
  })
})

</script>

<style lang="scss" scoped>
@import '@/assets/styles/rem-base.scss';

.chart-box-container {
   display: flex;
   width: 100%;
   height: px2rem(176);
   .chart-box {
      width: 100%;
      height: 100%;
   }
}
</style>
