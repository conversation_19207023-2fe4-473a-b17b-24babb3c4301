<template>
    <div class="chart-box-container">
        <div class="chart-box" ref="chart"></div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
    list: {
        type: Array,
        default: []
    },
    colors: {
        type: Array,
        default: []
    }
})
const option = {
    color: props.colors,
    tooltip: {
        trigger: 'item',
        formatter: ({seriesIndex, dataIndex, name, marker}) => {
            if (seriesIndex === 0) {
                return `${marker} ${name} ${props.list[dataIndex].percent}%`;
            }
        }
    },
    series: [
        {
            top: "middle",
            left: 0,
            width: "50%",
            height: "80%",
            type: "funnel",
            sort: "ascending",
            gap: 0,
            z: 1,
            minSize: 10,
            maxSize: 150,
            label: {
                show: true,
                position: "inside",
                formatter: ({ dataIndex }) => {
                    return `${props.list[dataIndex].percent}%`;
                },
            },
            data: props.list,
        },
        {
            top: "middle",
            left: 0,
            width: "10%",
            height: "80%",
            type: "funnel",
            gap: 0,
            z: -1,
            minSize: 200,
            maxSize: 200,
            label: {
                show: true,
                color: "#999999",
                position: "right",
                width: 50,
            },
            labelLine: {
                show: true,
                length: 200,
                lineStyle: {
                    width: 1,
                    color: "#e8e9f1",
                    type: "dashed",
                },
            },
            itemStyle: {
                color: "transparent",
                borderWidth: 0,
                opacity: 1,
            },
            data: props.list,
        },
    ],
};

const chart = ref(null);

onMounted(() => {
    setTimeout(() => {
        const mychart = echarts.init(chart.value);
        mychart.setOption(option);
    }, 100)
})


</script>

<style lang="scss" scoped>
@import '@/assets/styles/rem-base.scss';

.chart-box-container {
    .chart-box {
        width: 100%;
        height: px2rem(132);
    }

}
</style>
