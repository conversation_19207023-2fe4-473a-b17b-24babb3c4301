<template>
    <div class="statistics-total-tox">
        <div class="total-left">
            <div class="icon"
                :style="{backgroundImage: `url(${total.iconUrl})`}">
            </div>
            <div class="total-info">
                <div class="total-label">{{ total.name }}</div>
                <div class="total-num">{{ total.val }}</div>
            </div>
        </div>
        
        <div class="mechine-box">
            <div v-for="(item, index) in list"
                :key="index"
                class="machine-item"
                :class="item.customClass">
                <div class="item-left">
                    <img :src="icon_dian" alt="" class="icon-dian">
                    <div class="label">{{ item.name }}</div>
                </div>
                <div class="num">{{ item.val }}</div>
                
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { ref, onMounted, watch } from 'vue'
import icon_dian from '@/assets/images/dash/icon_dian_12.svg'

const props = defineProps({
    list: {
        type: Array,
        default: []
    },
    total: {
        type: Object,
        default: () => {}
    }
})
</script>
  
<style lang="scss" scoped>
@import '@/assets/styles/rem-base.scss';


.statistics-total-tox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: px2rem(9) px2rem(16);
    background: url('@/assets/images/dash/bg_zhandianjiance_lan.png') no-repeat 0 0;
    background-size: 100% 100%;
    .total-left {
        display: flex;
        align-items: center;
        .icon {
            width: px2rem(40);
            height: px2rem(40);
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 100% 100%;
        }
        .total-info {
            display: flex;
            flex-direction: column;
            margin-left: px2rem(16);
            .icon {
                width: px2rem(40);
                height: px2rem(40);
            }
            .total-label {
                font-family: PingFangSC-Medium;
                font-size: px2rem(14);
                color: #FFFFFF;
                font-weight: 500;
                line-height: 1;
                margin-bottom: px2rem(8);
            }
            .total-num {
                font-size: px2rem(28);
                color: #FFFFFF;
                line-height: px2rem(30);
                font-weight: 700;
            }
        }
    }
    
    .mechine-box {
        display: flex;
        flex-direction: column;
        width: 100%;
        .machine-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: px2rem(3);
            width: px2rem(190);
            height: px2rem(20);
            background: url('@/assets/images/dash/bg_tongji_190.png') no-repeat 0 0;
            background-size: 100% auto;
            margin-bottom: px2rem(12);
            &:nth-last-child(1){
                margin-bottom: 0;
            }
            
            .label, 
            .num {
                font-family: PingFangSC-Medium;
                font-size: px2rem(14);
                color: #FFFFFF;
                vertical-align: middle;
            }
            .item-left {
                display: flex;
                .icon-dian {
                    display: block;
                    width: px2rem(12);
                    height: px2rem(12);
                    margin-left: px2rem(16);
                    margin-top: px2rem(3);
                }
                .label {
                    font-weight: 500;
                    margin-left: px2rem(6);
                }
                
            }
            .num {
                font-size: px2rem(20);
                text-align: right;
                margin-right: px2rem(16);
                font-weight: 700;
                line-height: 1;
                margin-bottom: px2rem(3);
            }
        }
    }
    
}

</style>