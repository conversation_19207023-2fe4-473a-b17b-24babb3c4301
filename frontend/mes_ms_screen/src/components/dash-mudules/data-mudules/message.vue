<template>
  <div class="important-message-module module-box">
    <BoxTitle v-slot:text :showIcon="false">重要事项</BoxTitle>

    <div class="msg-scroll-content">
      <vue3SeamlessScroll
        :list="messageList"
        class="scroll-list"
        :step="0.2"
        :limitScrollNum="1"
        :hover="false"
      >
        <div class="msg-item" v-for="item in messageList" :key="item.id">
          <div class="msg-left">🔔</div>
          <div class="msg-center">
            <div class="msg-header">
              <span class="msg-title">{{ item.title }}</span>
              <span class="msg-time">{{ item.time }}</span>
            </div>
            <div class="msg-content" :title="item.content">{{ item.content }}</div>
          </div>
          <div class="msg-right">
            <el-button size="small" type="primary" class="custom-round-btn" @click="handleView(item)">
              去看看
            </el-button>
          </div>
        </div>
      </vue3SeamlessScroll>
    </div>

    <el-dialog v-model="dialogVisible" title="重要事项详情" width="500px">
      <p><strong>{{ currentMsg.title }}</strong></p>
      <p>{{ currentMsg.time }}</p>
      <p style="margin-top: 12px">{{ currentMsg.content }}</p>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElDialog, ElButton } from 'element-plus'
import BoxTitle from '@/components/dash-mudules/BoxTitle'
import 'element-plus/es/components/dialog/style/css'
import 'element-plus/es/components/button/style/css'

const messageList = ref([
  {
    id: 'msg-1',
    title: '水质异常预警',
    time: '2025-07-24 14:30',
    content: '地表水采样点 PH 值超过阈值，请及时处理。'
  },
  {
    id: 'msg-2',
    title: '监测数据上传失败',
    time: '2025-07-24 13:00',
    content: '南门子站点 12 点数据上传失败，请检查设备状态。'
  },
  {
    id: 'msg-3',
    title: 'AI识别异常行为',
    time: '2025-07-24 12:45',
    content: '摄像头识别到异常人员靠近重点区域，请确认安全性。'
  },
  {
    id: 'msg-4',
    title: '气象异常通知',
    time: '2025-07-24 11:20',
    content: '未来 1 小时可能出现强降雨，请提前做好防范。'
  }
])

const dialogVisible = ref(false)
const currentMsg = ref({ title: '', time: '', content: '' })

const handleView = (item) => {
  currentMsg.value = item
  dialogVisible.value = true
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';

.important-message-module {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .msg-scroll-content {
    flex: 1;
    overflow-y: auto;
    padding: px2rem(8) 0;

    .scroll-list {
      display: flex;
      flex-direction: column;
      gap: px2rem(12);
    }

    .msg-item {
      display: flex;
      align-items: center;
      background: #f7f9fc;
      border-radius: px2rem(6);
      padding: px2rem(8) px2rem(12);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .msg-left {
        font-size: px2rem(18);
        margin-right: px2rem(12);
        flex-shrink: 0;
      }

      .msg-center {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: px2rem(4);

        .msg-header {
          display: flex;
          justify-content: space-between;
          font-size: px2rem(14);
          font-weight: 500;
          color: #333;

          .msg-title {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .msg-time {
            font-size: px2rem(12);
            color: #999;
          }
        }

        .msg-content {
          font-size: px2rem(13);
          color: #666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .msg-right {
        margin-left: px2rem(12);
        flex-shrink: 0;

        .custom-round-btn {
          border-radius: 12px !important;
        }
      }
    }
  }
}
</style>
