<template>
  <div class="date-tab">
    <div class="tab-btns">
      <div
        v-for="item in options"
        :key="item.value"
        :class="modelValue === item.value ? 'btn active' : 'btn'"
        @click="handleClick(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  options: {
    type: Array,
    default: () => [
      { label: '当月', value: 'month' },
      { label: '当日', value: 'day' }
    ]
  }
})

const emit = defineEmits(['update:modelValue'])

const handleClick = (val) => {
  if (val !== props.modelValue) {
    emit('update:modelValue', val)
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';

.date-tab {
  width: px2rem(120);
  user-select: none;

  .tab-btns {
    display: flex;
    gap: px2rem(2);
    background: #f0f3ff;
    border-radius: px2rem(20);
    padding: px2rem(4);
  }

  .btn {
    flex: 1;
    text-align: center;
    font-family: PingFangSC-Medium;
    font-size: px2rem(14);
    color: #666;
    padding: px2rem(6) 0;
    border-radius: px2rem(16);
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #1472FF;
      color: #fff;
      box-shadow: 0 0 px2rem(6) rgba(74, 108, 255, 0.4);
    }

    &:hover:not(.active) {
      color: #1472FF;
      background: rgba(74, 108, 255, 0.1);
    }
  }
}
</style>
