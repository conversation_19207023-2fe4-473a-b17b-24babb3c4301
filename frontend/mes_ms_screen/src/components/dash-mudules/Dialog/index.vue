<template>
    <slot name="triggerArea"></slot>
    <el-dialog
        center draggable
        :modelValue="dialogVisible" 
        width="30%"
        :title="title"
    >
      
      <slot name="dialogContent"></slot>
    </el-dialog>
  </template>

<script setup>
import { ref } from 'vue';
import { ElMessageBox } from 'element-plus';
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    dialogVisible: {
        type: Boolean,
        default: false
    }
})
</script>
<style lang="scss" scoped>
/* .dialog-footer button:first-child {
    margin-right: 10px;
} */
</style>