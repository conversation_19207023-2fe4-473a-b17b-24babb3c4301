<template>
  <div class="title-box">
    <slot name="text"></slot>
    <img
      v-if="showIcon"
      src="@/assets/images/icon_gengduo.png"
      class="icon"
      @click="onIconClick"
    />
  </div>
</template>

<script setup>

// props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})


const emit = defineEmits(['icon-click'])

const onIconClick = () => {
  emit('icon-click')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/rem-base.scss';

.title-box {
  display: flex;
  align-items: center;
  gap: px2rem(12);
  padding: px2rem(12) 0;
  font-family: PingFangSC-Medium;
  font-size: px2rem(16);
  color: #333333;
  font-weight: bold;
  &::before {
    content: '';
    width: px2rem(2);
    height: px2rem(16);
    background-color: #1472FF;
  }

  .icon {
    cursor: pointer;
    width: px2rem(20); 
    height: auto;
  }
}
</style>
