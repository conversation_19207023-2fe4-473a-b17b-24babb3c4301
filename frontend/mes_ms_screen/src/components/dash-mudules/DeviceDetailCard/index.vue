<template>
    <div class="num-detail-box">
        <div v-for="(item, index) in list" 
            key="index"
            class="detail-item"
            :class="item.customClass">
            <div class="icon">
                <img :src="item.iconUrl" alt="">
            </div>
            <div class="info-box">
                <div class="label">{{ item.name }}</div>
                <div class="num">{{ item.val }}</div>
            </div>
        </div>
    </div>
</template>
  
<script setup>
import { ref, onMounted, watch } from 'vue'
const props = defineProps({
   list: {
        type: Array,
        default: []
   }
})
</script>
  
<style lang="scss" scoped>
@import '@/assets/styles/rem-base.scss';

.num-detail-box {
    display: flex;
    margin-top: px2rem(8);
    border-radius: px2rem(3);
    .detail-item {
        display: flex;
        align-items: center;
        flex: 1;
        height: px2rem(74);
        background: rgba(49,103,213,0.05);
        border-radius: px2rem(3);
        margin-right: px2rem(11);
        .icon {
            width: px2rem(40);
            height: px2rem(40);
            margin-left: px2rem(16);
            img {
                display: block;
                width: 100%;
                height: 100%;
            }
        }
        .info-box {
            text-align: left;
            margin-left: px2rem(10);
            .label {
                font-family: PingFangSC-Medium;
                font-size: px2rem(14);
                color: #333333;
                font-weight: 500;
            }
            .num {
                font-size: px2rem(28);
                color: #333333;
                line-height: px2rem(30);
                font-weight: 700;
            }
        }
        &:last-child {
            margin-right: 0;
        }
    }
}
</style>