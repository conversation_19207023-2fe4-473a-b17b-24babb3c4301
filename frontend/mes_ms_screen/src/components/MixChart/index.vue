<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { useTheme } from '@/utils/useTheme'
const { theme } = useTheme()

const labelColor = computed(() => {
  return theme.value === 'dark' ? '#CBDCFF' : '#333333'
})

type ChartType = 'line' | 'bar'
interface SeriesData {
  name: string
  data: number[]
  type?: ChartType
  yAxisIndex?: number
  smooth?: boolean
  barWidth?: string
  itemStyle?: { color?: string }
}

interface Props {
  chartData: {
    xAxis: string[]
    series: SeriesData[]
  }
  options?: Partial<EChartsOption>
  width?: string
  height?: string
  currentTheme?: String
}
const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '200px',
  options: () => ({}),
})

const chartRef = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  const defaultOptions: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: { type: 'cross' },
    },
    legend: {
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 16,
      data: props.chartData.series.map((item) => item.name),
      textStyle: {
        color: labelColor.value,
      },
    },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: props.chartData.xAxis,
      axisLabel: {
        color: labelColor.value,
      },
    },
    yAxis: [
      {
        type: 'value',
        // name: '数值1',
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: labelColor.value,
        },
      },
      {
        type: 'value',
        // name: '数值2',
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: (value) => `${value}%`, // 关键配置：添加百分号
          color: labelColor.value,
        },
      },
    ],
    series: props.chartData.series.map((series) => ({
      name: series.name,
      type: series.type || 'line',
      data: series.data,
      yAxisIndex: series.yAxisIndex || 0,
      smooth: series.smooth,
      barWidth: series.barWidth,
      itemStyle: series.itemStyle,
    })),
  }

  chartInstance.setOption({ ...defaultOptions, ...props.options })
}

onMounted(() => {
  setTimeout(() => {
    initChart()
    window.addEventListener('resize', () => chartInstance?.resize())
  }, 0)
})

onUnmounted(() => {
  window.removeEventListener('resize', () => chartInstance?.resize())
  chartInstance?.dispose()
  chartInstance = null
})

watch(
  () => props.chartData,
  () => updateChart(),
  { deep: true }
)

watch(
  () => props.options,
  () => updateChart(),
  { deep: true }
)
watch(
  () => theme,
  () => updateChart(),
  { deep: true }
)
</script>
