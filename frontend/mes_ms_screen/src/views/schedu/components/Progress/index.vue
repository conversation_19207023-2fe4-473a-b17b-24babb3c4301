<template>
  <div class="progress-module module-box">
    <div v-if="props.showTitle" class="module-title-box">
      <div class="module-title">{{ props.title }}</div>
      <!-- <div class="mt-btns"></div> -->
    </div>
    <div class="sn-module-content">
      <el-tabs v-model="activeTab" class="snm-tabs">
        <el-tab-pane
          v-for="it in tabs"
          :key="it.value"
          :name="it.value"
          :label="it.label"
        ></el-tab-pane>
      </el-tabs>
      <div class="snm-list">
        <div class="snm-item" v-for="(item, index) in sortList" :key="index">
          <div class="snmi-icon">
            {{ 'Top ' + (index + 1) }}
          </div>
          <div class="snmi-t" :title="item.name">{{ item.name }}</div>
          <div class="snmi-p">
            <el-progress
              :percentage="(item.value / maxNum) * 100"
              :show-text="false"
              :stroke-width="5"
            />
          </div>
          <div class="snmi-n">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 从外部传入数据
const props = defineProps({
  title: {
    type: String,
    default: '报警区域统计',
  },
  propsData: {
    type: Object,
    default: {
      totalData: [
        { name: '内蒙古', value: 17 },
        { name: '辽宁', value: 12 },
        { name: '山东', value: 11 },
        { name: '北京', value: 9 },
        { name: '江苏', value: 8 },
      ],
      yzData: [
        { name: '内蒙古', value: 5 },
        { name: '辽宁', value: 2 },
        { name: '山东', value: 2 },
        { name: '北京', value: 1 },
        { name: '江苏', value: 1 },
      ],
      zyData: [
        { name: '内蒙古', value: 1 },
        { name: '辽宁', value: 4 },
        { name: '山东', value: 3 },
        { name: '北京', value: 3 },
        { name: '江苏', value: 2 },
      ],
      zdData: [
        { name: '内蒙古', value: 1 },
        { name: '辽宁', value: 3 },
        { name: '山东', value: 3 },
        { name: '北京', value: 2 },
        { name: '江苏', value: 3 },
      ],
      ybData: [
        { name: '内蒙古', value: 10 },
        { name: '辽宁', value: 3 },
        { name: '山东', value: 3 },
        { name: '北京', value: 3 },
        { name: '江苏', value: 2 },
      ],
    },
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  tabs: {
    type: Object,
    default: [
      { label: '全部', value: 'total' },
      { label: '严重', value: 'yz' },
      { label: '重要', value: 'zy' },
      { label: '中等', value: 'zd' },
      { label: '一般', value: 'yb' },
    ],
  },
})
let activeTab = ref('total')

const sortList = computed(() => {
  if (props.propsData[`${activeTab.value}Data`]) {
    return props.propsData[`${activeTab.value}Data`].sort((a, b) => {
      return b.value - a.value
    })
  } else {
    return []
  }
})

console.log('sortList====>', sortList)

// }
//   props.propsData.dataList.sort((a, b) => {
//     return b.value - a.value
//   })
// )

const maxNum = computed(() => {
  return Math.max(...sortList.value.map((item) => item.value))
})

// const tabs = [
//   { label: '全部', value: 'total' },
//   { label: '严重', value: 'processed' },
//   { label: '重要', value: 'zy' },
//   { label: '中等', value: 'zd' },
//   { label: '一般', value: 'yb' },
// ]
</script>

<style scoped lang="scss">
@import '@/assets/styles/rem-base.scss';
.progress-module {
  width: 100%;
  // flex: 1;
  // height: px2rem(316); // 需要设置高度或者flex: 1
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .sn-module-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .snm-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 0;
        .el-tabs__nav-scroll {
          display: flex;
          justify-content: center;
          .el-tabs__item {
            font-size: px2rem(14);
            &.is-active {
              font-weight: 600;
            }
          }
        }
      }
    }
    .snm-list {
      margin-top: px2rem(20);
      flex: 1;
      overflow-y: auto;
      .snm-item + .snm-item {
        margin-top: px2rem(10);
      }
      .snm-item {
        display: flex;
        align-items: center;
        margin-bottom: px2rem(10);
        &:nth-child(1) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_1.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(
                243deg,
                #da6241 0%,
                #c04126 98%
              );
            }
          }
        }
        &:nth-child(2) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_2.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(
                243deg,
                #f3893c 0%,
                #ed6a0c 98%
              );
            }
          }
        }
        &:nth-child(3) {
          .snmi-icon {
            background-image: url('@/assets/monitor/top_3.png');
          }
          :deep(.el-progress) {
            .el-progress-bar__inner {
              background-image: linear-gradient(
                243deg,
                #00d1b5 0%,
                #00a680 98%
              );
            }
          }
        }
        .snmi-icon {
          width: px2rem(60);
          height: px2rem(20);
          line-height: px2rem(20);
          text-align: center;
          background-image: url('@/assets/monitor/top_lan.png');
          background-size: 100% 100%;
          margin-right: px2rem(12);
          font-size: px2rem(12);
          color: #f4f7ff;
        }
        .snmi-t {
          // flex: 1;
          min-width: px2rem(50);
          max-width: px2rem(120);
          margin-right: px2rem(2);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: px2rem(14);
          line-height: px2rem(20);
          color: #333333;
        }
        .snmi-p {
          flex: 1;
          margin-right: px2rem(10);
        }
        :deep(.el-progress) {
          .el-progress-bar__outer {
            background-color: transparent;
          }
          .el-progress-bar__inner {
            background-image: linear-gradient(243deg, #2fb1ec 0%, #155bd4 98%);
          }
        }
        .snmi-n {
          width: px2rem(50);
          text-align: center;
          font-size: px2rem(16);
          color: #333333;
        }
      }
    }
  }
}
[data-theme='dark'] .progress-module {
  .snmi-t {
    color: #cbdcff !important;
  }
  .snm-tabs {
    :deep(.el-tabs__header) {
      .el-tabs__nav-scroll {
        .el-tabs__item {
          color: #cbdcff !important;
          &.is-active {
            font-weight: 600 !important;
            color: #4f9fff !important;
          }
        }
      }
    }
  }
  .snm-list {
    .snmi-n {
      color: #cbdcff !important;
    }
  }
}
</style>
