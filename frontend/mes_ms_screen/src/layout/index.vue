<template>
  <div class="page-wrapper">
    <ScaleScreen :width="1920" :height="1080">
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </ScaleScreen>
  </div>
</template>

<script setup>
// import useAppStore from '@/store/modules/app'
// import useSettingsStore from '@/store/modules/settings'
import { useRemResize } from '@/utils/remResize'
import ScaleScreen from '../components/scale-screen/scale-screen.vue'
import { watch } from 'vue';
import { provideTheme } from '../utils/useTheme';

// 初始化REM适配
useRemResize()
const { theme } = provideTheme()
    
watch(theme, (newVal) => {
  document.documentElement.setAttribute('data-theme', newVal)
}, { immediate: true })

// const settingsStore = useSettingsStore()
// const theme = computed(() => settingsStore.theme)
// const sidebar = computed(() => useAppStore().sidebar)
// const device = computed(() => useAppStore().device)

// const classObj = computed(() => ({
//   hideSidebar: !sidebar.value.opened,
//   openSidebar: sidebar.value.opened,
//   withoutAnimation: sidebar.value.withoutAnimation,
//   mobile: device.value === 'mobile'
// }))

// watch(
//   () => device.value,
//   () => {
//     if (device.value === 'mobile' && sidebar.value.opened) {
//       useAppStore().closeSideBar({ withoutAnimation: false })
//     }
//   }
// )

// watchEffect(() => {
//   if (width.value - 1 < WIDTH) {
//     useAppStore().toggleDevice('mobile')
//     useAppStore().closeSideBar({ withoutAnimation: true })
//   } else {
//     useAppStore().toggleDevice('desktop')
//   }
// })

// const settingRef = ref(null)
// function setLayout() {
//   settingRef.value.openSetting()
// }
</script>

<style lang="scss" scoped>
.page-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-image: linear-gradient(180deg, rgba(207, 225, 242, 0.6) 0%, #f2f9ff 65%);
}

[data-theme='dark'] .page-wrapper {
  background-image: linear-gradient(180deg, #152445 0%, #070a22 100%);
}
</style>