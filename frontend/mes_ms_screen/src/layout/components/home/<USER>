<template>
  <div class="home-container">
    <!-- 内容区 - 使用动态组件 -->
    <main class="main-content">
      <div v-if="activeMenu === 'home'">
        <home-task />
      </div>
      <component 
        v-else 
        :is="currentComponent" 
        :key="activeMenu"
        class="embedded-view"
      />
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import HomeTask from '@/components/Home/TaskDashboard'
import useUserStore from "@/store/modules/user";

// 导入各模块组件
import DataCollection from '@/layout/index' // 数据采集与处理
import DataAudit from '@/views/index' // 数据审核
import MonitoringWarning from '@/views/index' // 监控预警

const userStore = useUserStore()

// 系统模块配置（关联组件）
const systemModules = ref([
  { 
    label: '数据采集与处理', 
    value: 'water', 
    component: DataCollection,
    icon: 'el-icon-s-water' 
  },
  { 
    label: '数据审核', 
    value: 'air', 
    component: DataAudit,
    icon: 'el-icon-s-air' 
  },
  { 
    label: '监控预警', 
    value: 'dash', 
    component: MonitoringWarning,
    icon: 'el-icon-s-data-line' 
  },
])

// 当前激活菜单
const activeMenu = ref('home')

// 当前显示的组件
const currentComponent = ref(null)

// 用户信息
const userInfo = computed(() => {
  return {
    nickName: userStore.nickName || '未登录',
    avatar: userStore.avatar
  }
})

// 菜单切换
const handleMenuSelect = (index) => {
  activeMenu.value = index
  
  if (index !== 'home') {
    // 查找对应的模块配置
    const module = systemModules.value.find(m => m.value === index)
    
    if (module) {
      // 设置当前显示的组件
      currentComponent.value = module.component
    } else {
      console.warn('未找到模块配置:', index)
    }
  }
}

// 处理用户命令
const handleUserCommand = (command) => {
  switch(command) {
    case 'system':
      // 这里如果需要跳转路由，保留原有逻辑
      // router.push('/index')
      break
    case 'profile':
      // router.push('/user')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录处理
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？', 
      '提示', 
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        customClass: 'logout-confirm'
      }
    )
    // 执行退出逻辑
    userStore.logout()
    // router.push('/login')
  } catch {
    // 取消操作
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .top-header {
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 40px;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    
    .logo-container {
      width: 200px;
      .logo {
        height: 44px;
      }
    }
    
    .nav-menu {
      flex: 1;
      border-bottom: none;
      margin-left: 80px;
      
      .el-menu-item {
        margin-right: 24px;
        font-weight: 500;
        height: 64px;
        line-height: 64px;
        
        &.is-active {
          color: #409eff;
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            left: 12px;
            right: 12px;
            bottom: 0;
            height: 2px;
            background: #409eff;
          }
        }
        
        &:hover:not(.is-active) {
          color: #66b1ff;
        }
      }
    }
    
    .user-panel {
      margin-left: 40px;
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        gap: 8px;
        
        .user-name {
          margin-left: 10px;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .main-content {
    flex: 1;
    overflow: scroll;
    background: #f5f7fa;
    
    .embedded-view {
      width: 100%;
      min-height: calc(100vh - 64px); /* 减去头部高度 */
      background: white;
      padding: 20px;
    }
  }
  
  .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 12px;
    z-index: 10;
    
    .loading-icon {
      font-size: 24px;
      color: #409eff;
    }
    
    p {
      color: #909399;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .home-container .top-header {
    padding: 0 20px;
    
    .nav-menu {
      margin-left: 40px;
      
      .el-menu-item {
        margin-right: 16px;
        font-size: 14px;
      }
    }
    
    .user-panel {
      margin-left: 20px;
    }
  }
}

@media (max-width: 768px) {
  .home-container .top-header {
    .logo-container {
      width: 140px;
    }
    
    .nav-menu {
      display: none;
    }
    
    .user-panel {
      margin-left: auto;
    }
  }
}

// 退出确认框动画
.logout-confirm .el-message-box__content {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>