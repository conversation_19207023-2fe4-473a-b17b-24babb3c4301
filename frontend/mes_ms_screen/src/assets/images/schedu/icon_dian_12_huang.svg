<?xml version="1.0" encoding="UTF-8"?>
<svg width="12px" height="14px" viewBox="0 0 12 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_dian_12_huang</title>
    <defs>
        <linearGradient x1="50%" y1="1.16888727%" x2="50%" y2="96.3709843%" id="linearGradient-1">
            <stop stop-color="#C2A729" stop-opacity="0.0250201459" offset="0.93780731%"></stop>
            <stop stop-color="#C29329" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="2.2325209%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFCA08" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1大屏-监控预警-总览" transform="translate(-268.000000, -146.000000)">
            <g id="报警数量统计" transform="translate(24.000000, 80.000000)">
                <g id="编组-3" transform="translate(16.000000, 56.000000)">
                    <g id="编组-13" transform="translate(212.000000, 3.000000)">
                        <g id="icon_dian_12_huang" transform="translate(16.000000, 8.000000)">
                            <polygon id="多边形" stroke="url(#linearGradient-2)" stroke-width="0.5" fill="url(#linearGradient-1)" opacity="0.5" points="6 0 11.1961524 3 11.1961524 9 6 12 0.803847577 9 0.803847577 3"></polygon>
                            <polygon id="多边形" fill="#FFBF00" points="6 3 8.59807621 4.5 8.59807621 7.5 6 9 3.40192379 7.5 3.40192379 4.5"></polygon>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>