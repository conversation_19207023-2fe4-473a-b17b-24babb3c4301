{"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "数据生产系统", "author": "<PERSON><PERSON>团队", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": ""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@idbase/idbase-auth": "^7.6.8", "@idbase/idbase-vue": "^5.8.14", "@supermapgis/iclient-leaflet": "^11.3.0", "@vue-pdf-viewer/viewer": "^2.5.4", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "^1.9.0", "clipboard": "2.0.11", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-stat": "^1.2.0", "element-plus": "^2.9.10", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "^3.3.2", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "nprogress": "0.2.0", "pdfjs-dist": "^5.3.31", "mockjs": "^1.1.0", "pinia": "^3.0.2", "splitpanes": "3.1.5", "vue": "^3.5.13", "vue-cropper": "1.1.1", "vue-router": "^4.5.1", "vue3-seamless-scroll": "^3.0.2", "vuedraggable": "4.1.0", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.8", "@types/node": "^22.15.19", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "^4.2.0", "sass": "1.77.5", "typescript": "^5.8.3", "unocss": "^66.1.2", "unplugin-auto-import": "0.17.6", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.5.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-top-level-await": "^1.5.0"}, "overrides": {"quill": "2.0.2"}}