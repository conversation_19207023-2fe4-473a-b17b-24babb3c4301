import{N as C}from"./ScreenNavBar-qBdlSVbA.js";import{i as p}from"./index-BrbPvnuX.js";import{_ as S,r as a,o as V,c as E,a as D,d as l,b as t,u as i,t as o,m as d,G as n,H as N,i as k,D as B}from"./index-B3fBvuMC.js";import"./switch-DJpMdJKf.js";const L={class:"dashboard-container"},z={class:"dashboard-main"},O={class:"security-stats"},A={class:"stat-card critical"},M={class:"stat-value"},P={class:"stat-card warning"},G={class:"stat-value"},H={class:"stat-card normal"},I={class:"stat-value"},Q={class:"stat-card"},R={class:"stat-value"},X={class:"charts-section"},j={class:"chart-card"},q={class:"chart-content"},F={class:"chart-card"},J={class:"chart-content"},K={class:"vuln-list"},U={__name:"safe",setup(W){const f=a(3),_=a(8),h=a(15),y=a(82),r=a(null),c=a(null),b=a([{name:"权限漏洞",value:6},{name:"代码漏洞",value:5},{name:"配置漏洞",value:8},{name:"病毒木马",value:3},{name:"其他",value:4}]),v=a([{date:"6/10",count:12},{date:"6/11",count:8},{date:"6/12",count:15},{date:"6/13",count:7},{date:"6/14",count:10},{date:"6/15",count:5}]),g=a([{name:"远程代码执行漏洞",level:"高危",affect:"12台服务器",discoverTime:"2023-06-15 09:12",status:"未处理"},{name:"SQL注入漏洞",level:"高危",affect:"5台服务器",discoverTime:"2023-06-15 08:45",status:"处理中"},{name:"弱口令漏洞",level:"中危",affect:"8台设备",discoverTime:"2023-06-14 16:20",status:"未处理"},{name:"端口暴露",level:"低危",affect:"3台服务器",discoverTime:"2023-06-14 14:10",status:"已处理"}]),w={高危:"danger",中危:"warning",低危:"info"},T=()=>{const e=p(r.value);return e.setOption({tooltip:{trigger:"item"},legend:{orient:"vertical",left:10,textStyle:{color:getComputedStyle(document.documentElement).getPropertyValue("--primary-text-color")}},series:[{name:"漏洞类型",type:"pie",radius:"70%",data:b.value,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),e},x=()=>{const e=p(c.value);return e.setOption({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:v.value.map(s=>s.date)},yAxis:{type:"value",min:0},series:[{name:"安全事件",type:"line",data:v.value.map(s=>s.count),smooth:!0,lineStyle:{width:3},itemStyle:{radius:4}}]}),e},u=e=>{e.resize()};return V(()=>{const e=T(),s=x();window.addEventListener("resize",()=>{u(e),u(s)})}),(e,s)=>(D(),E("div",L,[l(i(C),{title:"安全大屏"}),t("div",z,[t("div",O,[t("div",A,[s[0]||(s[0]=t("div",{class:"stat-title"},"高危漏洞",-1)),t("div",M,o(f.value),1),s[1]||(s[1]=t("div",{class:"stat-desc"},"需立即处理",-1))]),t("div",P,[s[2]||(s[2]=t("div",{class:"stat-title"},"中危漏洞",-1)),t("div",G,o(_.value),1),s[3]||(s[3]=t("div",{class:"stat-desc"},"24小时内处理",-1))]),t("div",H,[s[4]||(s[4]=t("div",{class:"stat-title"},"低危漏洞",-1)),t("div",I,o(h.value),1),s[5]||(s[5]=t("div",{class:"stat-desc"},"本周内处理",-1))]),t("div",Q,[s[6]||(s[6]=t("div",{class:"stat-title"},"安全评分",-1)),t("div",R,o(y.value)+"分",1),s[7]||(s[7]=t("div",{class:"stat-desc"},"满分100分",-1))])]),t("div",X,[t("div",j,[s[8]||(s[8]=t("div",{class:"chart-title"},"漏洞类型分布",-1)),t("div",q,[t("div",{ref_key:"pieChart",ref:r,class:"chart-dom"},null,512)])]),t("div",F,[s[9]||(s[9]=t("div",{class:"chart-title"},"安全事件趋势",-1)),t("div",J,[t("div",{ref_key:"lineChart",ref:c,class:"chart-dom"},null,512)])])]),t("div",K,[s[10]||(s[10]=t("div",{class:"list-title"},"最新漏洞详情",-1)),l(i(B),{data:g.value,border:""},{default:d(()=>[l(i(n),{prop:"name",label:"漏洞名称"}),l(i(n),{prop:"level",label:"危险等级"},{default:d(m=>[l(i(N),{type:w[m.row.level]},{default:d(()=>[k(o(m.row.level),1)]),_:2},1032,["type"])]),_:1}),l(i(n),{prop:"affect",label:"影响资产"}),l(i(n),{prop:"discoverTime",label:"发现时间"}),l(i(n),{prop:"status",label:"处理状态"})]),_:1},8,["data"])])])]))}},st=S(U,[["__scopeId","data-v-598b1d91"]]);export{st as default};
