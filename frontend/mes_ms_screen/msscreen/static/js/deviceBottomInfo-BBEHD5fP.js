import{_ as d}from"./index.vue_vue_type_script_setup_true_lang-CW6cgbhn.js";import{_ as n,g as t,c as u,a as m,b as r,d as f}from"./index-B3fBvuMC.js";import"./index-BrbPvnuX.js";const _={class:"repair-fault-trend-module module-box"},y={class:"module-content"},v={__name:"deviceBottomInfo",props:{currentType:{type:String,default:"default"}},setup(b){const c={dataList:[{date:"07-19",fault:8,repair:12,replace:4},{date:"07-20",fault:5,repair:15,replace:6},{date:"07-21",fault:10,repair:10,replace:5},{date:"07-22",fault:6,repair:14,replace:7},{date:"07-23",fault:12,repair:18,replace:4},{date:"07-24",fault:9,repair:11,replace:5},{date:"07-25",fault:7,repair:9,replace:6}]},i=t(()=>c),o=t(()=>i.value.dataList.map(e=>({date:e.date,fault:e.fault,repair:e.repair,replace:e.replace}))),l=[{name:"故障",key:"fault",color:"#0065D5"},{name:"维修",key:"repair",color:"#67C23A"},{name:"更换备机",key:"replace",color:"#00BBF5"}],s=t(()=>l.map(e=>({type:"line",smooth:!0,name:e.name,data:o.value.map(a=>a[e.key]),symbol:"circle",symbolSize:8,itemStyle:{color:e.color},lineStyle:{color:e.color,width:2}}))),p=t(()=>({tooltip:{trigger:"axis"},legend:{icon:"circle",itemWidth:8,itemHeight:8,itemGap:16,top:0,data:l.map(e=>e.name)},grid:{left:10,right:15,bottom:5,top:35,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:o.value.map(e=>e.date)},yAxis:{type:"value"},series:s.value}));return(e,a)=>(m(),u("div",_,[a[0]||(a[0]=r("div",{class:"module-title-box"},[r("div",{class:"module-title"},"设备维修与故障记录")],-1)),r("div",y,[f(d,{"chart-data":p.value},null,8,["chart-data"])])]))}},B=n(v,[["__scopeId","data-v-bd11c001"]]);export{B as default};
