import{_ as z,y as H,r as n,o as N,z as A,c as r,a as o,b as a,A as F,t as l,B as I,F as S,l as f,n as k,i as b,x as U,d as q,C as E}from"./index-B3fBvuMC.js";import{S as L}from"./switch-DJpMdJKf.js";const O={class:"header-wrapper"},P={class:"header-menu"},R={class:"header-menu_current"},W={class:"header-menu_menu"},Y=["onClick"],j={class:"header-right"},G={class:"header-types"},J=["onClick"],K={class:"header-date"},Q={class:"header-switch"},X={__name:"HeaderNav",props:{menuData:{type:Array,default:()=>[{name:"智能调度",id:0,path:"/msscreen/schedu"},{name:"监控预警",id:1,path:"/msscreen/monitor"},{name:"资源全景",id:2,path:"/msscreen/dashzz"},{name:"数据洞察",id:3,path:"/msscreen/dashdata"}]},types:{type:Array,default:()=>[{id:1,name:"总览",key:"all"},{id:2,name:"地表水",key:"shui"},{id:3,name:"环境空气",key:"kq"}]}},emits:["changeMenu","changeType","changeTheme"],setup(i,{emit:$}){const D=H(),u=i,h=$,c=n(u.menuData[0]),d=n(!1),M=()=>{d.value=!d.value},T=e=>{c.value=e,h("changeMenu",e),d.value=!1,e.path&&E.push(e.path)},p=n(u.types[0]),w=e=>{p.value=e,h("changeType",e)},v=n(!0),C=e=>{h("changeTheme",e)},_=n("");let g=null;const y=()=>{const e=new Date,s=String(e.getHours()).padStart(2,"0"),t=String(e.getMinutes()).padStart(2,"0"),m=String(e.getSeconds()).padStart(2,"0"),V=e.getFullYear(),x=String(e.getMonth()+1).padStart(2,"0"),B=String(e.getDate()).padStart(2,"0");_.value=`${V}-${x}-${B} ${s}:${t}:${m}`};return N(()=>{y(),g=setInterval(y,1e3);const e=D.path.replace(/\/+$/,""),s=u.menuData.find(t=>e.startsWith(t.path));s&&(c.value=s)}),A(()=>{clearInterval(g)}),(e,s)=>(o(),r("div",O,[a("div",P,[a("div",R,l(c.value.name),1),a("div",{class:"header-menu_change",onClick:M}),F(a("ul",W,[(o(!0),r(S,null,f(i.menuData,t=>(o(),r("li",{key:t.id,class:k(["header-menu_menu_item",{"header-menu_menu_item_active":c.value.id===t.id}]),onClick:m=>T(t)},l(t.name),11,Y))),128))],512),[[I,d.value]])]),s[1]||(s[1]=a("div",{class:"header-title"},[b("驾驶舱 "),a("span",{class:"header-title_btm"})],-1)),a("div",j,[U(e.$slots,"right",{},void 0,!0),a("div",G,[(o(!0),r(S,null,f(i.types,t=>(o(),r("div",{class:k(["header-types_item",{"header-types_item_active":t.id===p.value.id}]),key:t.id,onClick:m=>w(t)},l(t.name),11,J))),128))]),a("div",K,l(_.value),1),a("div",Q,[q(L,{modelValue:v.value,"onUpdate:modelValue":s[0]||(s[0]=t=>v.value=t),"active-text":"浅","inactive-text":"深",onChange:C},null,8,["modelValue"])])])]))}},te=z(X,[["__scopeId","data-v-37dc35ac"]]);export{te as H};
