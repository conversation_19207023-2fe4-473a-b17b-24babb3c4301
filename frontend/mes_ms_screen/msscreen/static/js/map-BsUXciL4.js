import v from"./index-CI4xxFYj.js";import{_ as d,g as r,r as M,c as m,a as c,d as f}from"./index-B3fBvuMC.js";import"./index-BrbPvnuX.js";const s={class:"map-box module-box"},p={__name:"map",props:{totalData:{type:Object,default:{dataList:[{date:"07-13",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-14",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-15",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-16",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-17",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-18",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-19",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)},{date:"07-20",value1:Math.floor(Math.random()*10),value2:Math.floor(Math.random()*10),value3:Math.floor(Math.random()*10),value4:Math.floor(Math.random()*10)}]}},currentType:{type:String,default:"all"},isActiveBtn:{type:Number,default:1}},setup(t){const u=t;let e=r(()=>u.totalData.dataList.map(a=>({date:a.date,valueAll:a.value1+a.value2+a.value3+a.value4,value1:a.value1,value2:a.value2,value3:a.value3,value4:a.value4})));const o=[{name:"全部",value:"valueAll",color:"#0065D5"},{name:"严重",value:"value1",color:"#FA585A"},{name:"重要",value:"value2",color:"#50BAFF"},{name:"中等",value:"value3",color:"#FFBF00"},{name:"一般",value:"value4",color:"#4DCB73"}],h=M(o.map(a=>({type:"line",smooth:!0,name:a.name,data:e.value.map(l=>l[a.value]),itemStyle:{color:a.color}})));let n=r(()=>({tooltip:{trigger:"axis"},legend:{icon:"circle",itemWidth:8,itemHeight:8,itemGap:16,data:o.map(a=>a.name)},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:e.value.map(a=>a.date)},yAxis:[{type:"value"}],series:h.value}));return console.log("chartData====>",n.value),(a,l)=>(c(),m("div",s,[f(v,{currentType:t.currentType,isActiveBtn:t.isActiveBtn},null,8,["currentType","isActiveBtn"])]))}},g=d(p,[["__scopeId","data-v-1f9ed0b9"]]);export{g as default};
