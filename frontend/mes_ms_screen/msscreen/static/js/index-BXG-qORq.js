import{_ as m,r as g,w as V,c as l,a as s,b as d,k as o,t as u,n as f}from"./index-B3fBvuMC.js";const h={class:"switch-core"},x={key:0,class:"active-text"},b={key:1,class:"inactive-text"},k={__name:"index",props:{modelValue:{type:[Boolean,String,Number],default:!1},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},activeText:String,inactiveText:String,disabled:Boolean,loading:Boolean,size:{type:String,validator:e=>["small","middle","large"].includes(e),default:"middle"}},emits:["update:modelValue","change"],setup(e,{emit:r}){const a=e,n=r,t=g(a.modelValue===a.activeValue);V(()=>a.modelValue,i=>{t.value=i===a.activeValue});const v=()=>{if(a.disabled||a.loading)return;const i=t.value?a.inactiveValue:a.activeValue;t.value=!t.value,n("update:modelValue",i),n("change",i)};return(i,c)=>(s(),l("div",{class:f(["switch-wrapper",[`size-${e.size}`,{"is-disabled":e.disabled},{"is-loading":e.loading},{"is-checked":t.value}]]),onClick:v},[d("div",h,[e.activeText?(s(),l("span",x,u(e.activeText),1)):o("",!0),e.inactiveText?(s(),l("span",b,u(e.inactiveText),1)):o("",!0),c[0]||(c[0]=d("div",{class:"switch-button"},null,-1))])],2))}},B=m(k,[["__scopeId","data-v-a7d4698c"]]);export{B as default};
