import{B as i}from"./index-C4TfGCsC.js";import{S as g,C as m}from"./index-VkXiS0_E.js";import{i as b,a as _,b as v,c as u,D as C}from"./icon_shuishebei-B-ne4_mM.js";import{_ as f,c as p,a as B,b as l,d as a,m as t,i as s,u as e}from"./index-B3fBvuMC.js";import"./index-BrbPvnuX.js";const x={class:"device-container module-box"},D={class:"device-body"},k={class:"device-left"},y={class:"device-right"},F={__name:"deviceTopInfo",setup(S){const n=[{name:"备机",val:100,customClass:"bei"},{name:"原机",val:256,customClass:"yuan"}],r={name:"在线设备",val:"356",iconUrl:b},c=[{id:1,name:"默飞世尔",val:1230,labelColor:"#4FA0FF",labelBgStyle:"background-image: linear-gradient(90deg, rgba(79,160,255,0.30) 3%, rgba(79,160,255,0.00) 99%);"},{id:2,name:"河北先河",val:800,labelColor:"#67C23A",labelBgStyle:"background-image: linear-gradient(90deg, rgba(103,194,58,0.30) 3%, rgba(103,194,58,0.00) 99%);"},{id:3,name:"API",val:1500,labelColor:"#FBD500",labelBgStyle:"background-image: linear-gradient(90deg, rgba(251,213,0,0.30) 3%, rgba(251,213,0,0.00) 99%);"},{id:4,name:"武汉天虹",val:100,labelColor:"#FF9A54",labelBgStyle:"background-image: linear-gradient(90deg, rgba(255,154,84,0.30) 3%, rgba(255,154,84,0.00) 97%);"},{id:5,name:"安徽蓝盾",val:100,labelColor:"#F56C6D",labelBgStyle:"background-image: linear-gradient(90deg, rgba(245,108,109,0.30) 3%, rgba(245,108,109,0.00) 99%);"}],d=[{id:1,name:"在用",val:80,iconUrl:_,customClass:"using"},{id:2,name:"库存",val:12,iconUrl:v,customClass:"store"},{id:3,name:"修护中",val:12,iconUrl:u,customClass:"maintain"}];return(h,o)=>(B(),p("div",x,[l("div",D,[l("div",k,[a(e(i),{showIcon:!1},{text:t(()=>[o[0]||(o[0]=s("设备类别与状态"))]),_:1,__:[0]}),a(e(g),{list:n,total:r}),a(e(C),{list:d})]),l("div",y,[a(e(i),{showIcon:!1},{text:t(()=>[o[1]||(o[1]=s("监测设备统计"))]),_:1,__:[1]}),a(e(m),{list:c})])])]))}},N=f(F,[["__scopeId","data-v-3494f604"]]);export{N as default};
