import{B as f}from"./index-C4TfGCsC.js";import{_ as k,r as x,g as v,c as e,a as s,d as y,b as a,m as g,i as w,u as C,F as c,l as u,n as T,t as i,v as d}from"./index-B3fBvuMC.js";const B={class:"consumable-dashboard"},M={class:"tab-switch centered"},z=["onClick"],N={class:"horizontal-scroll-table"},S={__name:"ConsumaTop",setup(V){const h=["低值耗材","关键元器件","试剂","标准物质"],r=x("低值耗材"),m={};h.forEach((l,n)=>{const t=Math.floor(5+Math.random()*6);m[l]=Array.from({length:t},(p,b)=>({name:`类型 ${b+1}`,stock:Math.floor(80+Math.random()*50)+n*10}))});const o=v(()=>m[r.value]||[]),_=v(()=>{const l=o.value.length;return l<=7?{width:`${100/l}%`,minWidth:"auto"}:{width:"135px",minWidth:"100px"}});return(l,n)=>(s(),e("div",B,[y(C(f),{showIcon:!1},{text:g(()=>[n[0]||(n[0]=w("各类耗材数量统计"))]),_:1,__:[0]}),a("div",M,[(s(),e(c,null,u(h,t=>a("div",{key:t,class:T(["tab-item",{active:t===r.value}]),onClick:p=>r.value=t},i(t),11,z)),64))]),a("div",N,[a("table",{class:"stock-table",style:d({width:o.value.length>7?o.value.length*100+"px":"100%"})},[a("thead",null,[a("tr",null,[(s(!0),e(c,null,u(o.value,t=>(s(),e("th",{key:t.name,style:d(_.value)},i(t.name),5))),128))])]),a("tbody",null,[a("tr",null,[(s(!0),e(c,null,u(o.value,t=>(s(),e("td",{key:t.name,style:d(_.value)},i(t.stock),5))),128))])])],4)])]))}},E=k(S,[["__scopeId","data-v-b36215a4"]]);export{E as default};
