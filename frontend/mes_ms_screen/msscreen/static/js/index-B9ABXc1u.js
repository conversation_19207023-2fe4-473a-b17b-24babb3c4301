import{i as d}from"./index-BrbPvnuX.js";import{_ as l,r as n,o as u,f as p,w as m,c,a as f}from"./index-B3fBvuMC.js";const v={__name:"index",props:{data:{type:Array,default:()=>[{id:1,value:200,name:"规则创建"},{id:2,value:50,name:"数据审核"},{id:3,value:100,name:"监控预警"},{id:4,value:50,name:"资源管理"},{id:5,value:50,name:"质量管理"},{id:6,value:50,name:"手工创建"}]},colors:{type:Array,default:()=>["#4FA0FF","#67C23A","#FBD500","#FF9A54","#F56C6D","#00BBF5","#48E5E5","#2B8EF3"]}},setup(i){const t=i,r=n(null);let e=null;const a=()=>{const o={color:t.colors,tooltip:{trigger:"item"},series:[{type:"pie",data:t.data,radius:["30%","60%"],itemStyle:{borderWidth:1,borderColor:"rgba(255,255,255,.5)"},label:{show:!0,formatter:"{b}: {c} ({d}%)"}}]};e.setOption(o)},s=()=>{e=d(r.value),a()};return u(()=>{setTimeout(()=>{s(),window.addEventListener("resize",()=>e==null?void 0:e.resize())},0)}),p(()=>{window.removeEventListener("resize",()=>e==null?void 0:e.resize()),e==null||e.dispose(),e=null}),m(()=>t.data,()=>a(),{deep:!0}),(o,_)=>(f(),c("div",{ref_key:"chartRef",ref:r,class:"ring-chart"},null,512))}},y=l(v,[["__scopeId","data-v-f8677d4d"]]);export{y as default};
