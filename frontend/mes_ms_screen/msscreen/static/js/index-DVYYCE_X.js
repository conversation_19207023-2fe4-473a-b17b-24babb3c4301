import{_ as h,r as u,g as k,o as v,c as e,a as t,b as c,F as l,l as n,v as y,t as p,n as w,x as D}from"./index-B3fBvuMC.js";const R={class:"table-wrapper-header"},g=["title"],B=["title"],S={__name:"index",props:{data:{type:Array,required:!0},columns:{type:Array,required:!0}},setup(o){const f=o,_=u(null),b=u(null),m=k(()=>[...f.data]);return v(()=>{}),(d,q)=>(t(),e("div",{class:"table-wrapper",ref_key:"wrapperRef",ref:_},[c("div",R,[(t(!0),e(l,null,n(o.columns,(s,a)=>(t(),e("div",{key:"h"+a,class:"header-cell",style:y({width:s.width})},p(s.title),5))),128))]),c("div",{class:"table-body",ref_key:"bodyRef",ref:b},[(t(!0),e(l,null,n(m.value,(s,a)=>(t(),e("div",{key:"r"+a,class:w(["table-row",{"top1-row":a===0,"top2-row":a===1,"top3-row":a===2}])},[(t(!0),e(l,null,n(o.columns,(r,i)=>(t(),e("div",{key:"c"+i,class:"body-cell",style:y({width:r.width})},[d.$slots[r.slot]?D(d.$slots,r.slot,{key:0,row:s},void 0,!0):(t(),e(l,{key:1},[i===0?(t(),e("span",{key:0,title:s[r.prop],class:"body-cell-first"},p(s[r.prop]),9,g)):(t(),e("span",{key:1,title:s[r.prop]},p(s[r.prop]),9,B))],64))],4))),128))],2))),128))],512)],512))}},A=h(S,[["__scopeId","data-v-7eb211a0"]]);export{A as D};
