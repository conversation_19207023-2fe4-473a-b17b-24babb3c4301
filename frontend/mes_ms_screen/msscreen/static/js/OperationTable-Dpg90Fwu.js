import{B as b}from"./index-C4TfGCsC.js";import{e as f,r as h,g as v,h as o,c as x,a as y,d as e,b as s,m as l,i as C,u as w,t as B,_ as T}from"./index-B3fBvuMC.js";const k={class:"operation-company-table module-box"},L={class:"table-wrapper"},N={class:"wrap-text"},S={class:"table-pagination"},r=5,V=f({__name:"OperationTable",props:{companyList:{default:()=>[]}},setup(p){const i=p,n=h(1),d=v(()=>{const a=(n.value-1)*r;return i.companyList.slice(a,a+r)}),_=a=>{n.value=a};return(a,c)=>{const t=o("el-table-column"),u=o("el-table"),m=o("el-pagination");return y(),x("div",k,[e(w(b),{showIcon:!1},{text:l(()=>[c[0]||(c[0]=C("运维公司统计"))]),_:1,__:[0]}),s("div",L,[e(u,{data:d.value,border:"",style:{width:"100%"},"header-cell-class-name":"table-header"},{default:l(()=>[e(t,{prop:"name",label:"运维公司","min-width":"160",align:"center"},{default:l(({row:g})=>[s("span",N,B(g.name),1)]),_:1}),e(t,{prop:"score",label:"机构考评分",align:"center"}),e(t,{prop:"staffCount",label:"人员数",align:"center"}),e(t,{prop:"avgScore",label:"人员均分",align:"center"})]),_:1},8,["data"]),s("div",S,[e(m,{layout:"prev, pager, next",total:a.companyList.length,"page-size":r,"current-page":n.value,onCurrentChange:_,background:""},null,8,["total","current-page"])])])])}}}),I=T(V,[["__scopeId","data-v-35d952cd"]]);export{I as default};
