import{_ as r,r as _,c as m,a as v,b as a,n as c,u as o}from"./index-B3fBvuMC.js";const b={class:"date-tab"},p={class:"tab-btns"},u={__name:"index",emits:["changeDate"],setup(f,{emit:i}){const d=i;let t=_("d");const n=s=>{t.value=s,d("changeDate",s)};return(s,e)=>(v(),m("div",b,[a("div",p,[a("div",{class:c(o(t)=="m"?"btn active":"btn"),onClick:e[0]||(e[0]=l=>n("m"))}," 当月 ",2),a("div",{class:c(o(t)=="d"?"btn active":"btn"),onClick:e[1]||(e[1]=l=>n("d"))}," 当日 ",2)])]))}},x=r(u,[["__scopeId","data-v-2ee4e225"]]);export{x as default};
