import{_ as d,j as p,a as i,m,c as _,k as f,O as e,b as o,d as a,T as u}from"./index-B3fBvuMC.js";import g from"./SiteMonitorTop-Bd4FdHj0.js";import k from"./SiteMonitorScroll-PesPu0C4.js";import"./index-C4TfGCsC.js";import"./icon_biaozhunwuzhi-BnhnKLd7.js";import"./index-BrbPvnuX.js";const b={class:"dialog-body"},v={class:"top-section"},V={class:"bottom-section"},B={__name:"SiteMonitorDialog",props:{dialogVisible:Boolean},emits:["update:dialogVisible"],setup(l,{emit:c}){const r=l,n=c;function t(){n("update:dialogVisible",!1)}return(C,s)=>(i(),p(u,{name:"fade"},{default:m(()=>[r.dialogVisible?(i(),_("div",{key:0,class:"custom-dialog-mask",onClick:e(t,["self"])},[o("div",{class:"custom-dialog-wrapper",onClick:s[0]||(s[0]=e(()=>{},["stop"]))},[o("div",{class:"dialog-header"},[s[1]||(s[1]=o("span",{class:"title"},"耗材使用情况",-1)),o("span",{class:"close-btn",onClick:t},"×")]),o("div",b,[o("div",v,[a(g)]),o("div",V,[a(k)])])])])):f("",!0)]),_:1}))}},T=d(B,[["__scopeId","data-v-209c2e8a"]]);export{T as default};
