import{_ as $,r as n,o as B,z as V,c as o,a as c,b as t,i as x,A as N,t as d,B as b,F as y,l as f,n as k,d as A}from"./index-B3fBvuMC.js";import{S as F}from"./switch-DJpMdJKf.js";const I={class:"headerBgColor"},z={class:"header-wrapper"},U={class:"header-menu"},E={class:"header-menu_current"},H={class:"header-menu_menu"},L=["onClick"],O={class:"header-right"},Y={class:"header-types"},j=["onClick"],q={class:"header-date"},G={class:"header-switch"},J={__name:"ScreenNavBar",props:{menuData:{type:Array,default:()=>[{name:"智能调度",id:0},{name:"监控预警",id:1},{name:"数据资源",id:2}]},types:{type:Array,default:()=>[{id:1,name:"总览"},{id:2,name:"地表水"},{id:3,name:"环境空气"}]}},emits:["changeMenu","changeType","changeTheme"],setup(r,{emit:M}){const h=r,i=M,u=n(h.menuData[0]),l=n(!1),m=n(h.types[0]),v=n(!0),_=n("");let p=null;const T=()=>{l.value=!l.value},w=e=>{u.value=e,l.value=!1,i("changeMenu",e)},C=e=>{m.value=e,i("changeType",e)},D=e=>{i("changeTheme",e)},g=()=>{const e=new Date,s=a=>String(a).padStart(2,"0");_.value=`${e.getFullYear()}-${s(e.getMonth()+1)}-${s(e.getDate())} ${s(e.getHours())}:${s(e.getMinutes())}:${s(e.getSeconds())}`};return B(()=>{g(),p=setInterval(g,1e3)}),V(()=>{clearInterval(p)}),(e,s)=>(c(),o("div",I,[t("div",z,[s[1]||(s[1]=t("div",{class:"header-title"},[x(" 驾驶舱 "),t("span",{class:"header-title_btm"})],-1)),t("div",U,[t("div",E,d(u.value.name),1),t("div",{class:"header-menu_change",onClick:T}),N(t("ul",H,[(c(!0),o(y,null,f(r.menuData,a=>(c(),o("li",{key:a.id,class:k(["header-menu_menu_item",{"header-menu_menu_item_active":u.value.id===a.id}]),onClick:S=>w(a)},d(a.name),11,L))),128))],512),[[b,l.value]])]),t("div",O,[t("div",Y,[(c(!0),o(y,null,f(r.types,a=>(c(),o("div",{class:k(["header-types_item",{"header-types_item_active":a.id===m.value.id}]),key:a.id,onClick:S=>C(a)},d(a.name),11,j))),128))]),t("div",q,d(_.value),1),t("div",G,[A(F,{modelValue:v.value,"onUpdate:modelValue":s[0]||(s[0]=a=>v.value=a),"active-text":"浅","inactive-text":"深",onChange:D},null,8,["modelValue"])])])])]))}},Q=$(J,[["__scopeId","data-v-8cfb8247"]]);export{Q as N};
