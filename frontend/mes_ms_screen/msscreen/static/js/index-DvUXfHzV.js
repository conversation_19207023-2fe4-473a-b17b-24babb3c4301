import{_ as b,R as y,r as g,o as C,g as i,w as U,c as r,a as o,b as s,n as c,t as x,F as D,l as z}from"./index-B3fBvuMC.js";const L="/msscreen/static/png/bg_enter-CCA_V-3K.png",w="/msscreen/static/png/bg_enter_dark--ClQtJY6.png",I="/msscreen/static/png/source_light-PVoioVYs.png",M="/msscreen/static/png/safe_light-aeeCph16.png",S="/msscreen/static/png/zl_light-D2MtuPEV.png",V="/msscreen/static/png/source_dark-Dyzk9LYN.png",E="/msscreen/static/png/safe_dark-d-3pqGwU.png",F="/msscreen/static/png/zl_dark-CO6xgJl4.png",A={class:"dv-index-main"},B={class:"dv-btn-group"},P=["onClick"],Y=["src"],$={__name:"index",setup(q){const u=y(),d=g([{num:"one",lightUrl:I,darkUrl:V,route:"/screen_source"},{num:"three",lightUrl:M,darkUrl:E,route:"/screen_safe"},{num:"four",lightUrl:S,darkUrl:F,route:"/screen_qulity"}]),e=g(!1);C(()=>{const t=localStorage.getItem("theme-preference");t?e.value=t==="dark":e.value=window.matchMedia("(prefers-color-scheme: dark)").matches,n()});const m=()=>{e.value=!e.value,localStorage.setItem("theme-preference",e.value?"dark":"light"),n()},p=t=>e.value?t.darkUrl:t.lightUrl,_=t=>`btn-item-${t}`,h=i(()=>e.value?w:L),v=i(()=>({"--bg-image":`url(${h.value})`,"--primary-color":e.value?"#1a1a2e":"#f8f9fa","--secondary-color":e.value?"#e2e2e2":"#333333","--accent-color":e.value?"#4CAF50":"#2196F3"})),n=()=>{const t=document.documentElement;Object.entries(v.value).forEach(([l,a])=>{t.style.setProperty(l,a)})},k=t=>{u.push(t)};return U(e,()=>{n()}),(t,l)=>(o(),r("div",{class:c(["box",e.value?"dark-theme":"light-theme"])},[s("div",{class:"theme-toggle",onClick:m},[s("i",{class:c(["fa",e.value?"fa-sun-o":"fa-moon-o"])},null,2),s("span",null,x(e.value?"浅色模式":"深色模式"),1)]),s("div",A,[s("div",B,[(o(!0),r(D,null,z(d.value,(a,f)=>(o(),r("div",{key:f,class:c(["btn-item",[_(a.num)]]),onClick:G=>k(a.route)},[s("img",{src:p(a),alt:"功能按钮"},null,8,Y)],10,P))),128))])])],2))}},N=b($,[["__scopeId","data-v-cfa93665"]]);export{N as default};
