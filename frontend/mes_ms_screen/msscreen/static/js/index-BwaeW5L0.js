import{_ as o,h as n,c as s,a as d,x as a,d as i,m as r,F as c}from"./index-B3fBvuMC.js";const u={__name:"index",props:{title:{type:String,default:""},dialogVisible:{type:Boolean,default:!1}},setup(e){return(t,g)=>{const l=n("el-dialog");return d(),s(c,null,[a(t.$slots,"triggerArea",{},void 0,!0),i(l,{center:"",draggable:"",modelValue:e.dialogVisible,width:"30%",title:e.title},{default:r(()=>[a(t.$slots,"dialogContent",{},void 0,!0)]),_:3},8,["modelValue","title"])],64)}}},_=o(u,[["__scopeId","data-v-7203e130"]]);export{_ as default};
