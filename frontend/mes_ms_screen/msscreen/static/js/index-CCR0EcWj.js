import{H as b}from"./HeaderNav-CwnasXYK.js";import{R as N,P as w,S as z,W as R,a as k,b as B,c as L}from"./report-info-CCkw5xY4.js";import I from"./map-BsUXciL4.js";import{_ as W,r as d,w as A,o as C,c as S,a as H,b as M,d as o,u as a}from"./index-B3fBvuMC.js";import"./switch-DJpMdJKf.js";import"./index-DVYYCE_X.js";import"./index.vue_vue_type_script_setup_true_lang-CW6cgbhn.js";import"./index-BrbPvnuX.js";import"./index-CI4xxFYj.js";const O={class:"monitor-screen"},V={class:"header-box"},$={class:"content-container"},j={class:"left"},E={class:"center"},G={class:"right"},q={__name:"index",setup(F){const T=d(1),t=d("all"),y=e=>{t.value=e.key};A([t],()=>f());const s=d({}),m=d({dataList:[]}),c=d({}),f=()=>{D(),P(),g()},D=()=>{let e=["呼和浩特","包头","乌兰察布"],h=["二十九中","红旗小学","昆区政府","青山宾馆","集宁新区"],u=Math.floor(Math.random()*50),l=Math.floor(Math.random()*2320),r=Math.floor(Math.random()*100),n=Math.floor(Math.random()*100),i=[],p=[];for(let v=0;v<9;v++){const _={name:e[Math.floor(Math.random()*3)],name1:h[Math.floor(Math.random()*5)],value:Math.floor(Math.random()*6)};i.push(_),p.push(_)}let x={lwzd:u+l,zxzd:u,lxzd:l,lwzd5:r+n,zxzd5:r,lxzd5:n,dataList:i,dataList5:p};s.value=x},P=()=>{let e={dataList:[{date:"07-19",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-20",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-21",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-22",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-23",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-24",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)},{date:"07-25",value1:Math.floor(Math.random()*20),value2:Math.floor(Math.random()*20),value3:Math.floor(Math.random()*20),value4:Math.floor(Math.random()*20)}]};m.value=e},g=()=>{let e=["呼和浩特","包头","乌兰察布","沈阳","重庆","葫芦岛"],h=["二十九中","红旗小学","昆区政府","青山宾馆","集宁新区","浑南东路","太阳城","农科所"],u=["07-25 09:15 PM2.5跨级别污染","07-25 09:20 PM2.5数据洼地报警","07-25 09:10 PM2.5数据洼地报警","07-25 09:20 PM2.5数据离群","07-25 09:05 CO数据离群","07-25 09:05 PM2.5数据离群","07-25 09:15 PM2.5跨级别污染","07-25 09:20 PM2.5数据洼地报警","07-25 09:10 PM2.5数据洼地报警","07-25 09:20 PM2.5数据离群","07-25 09:05 CO数据离群","07-25 09:05 PM2.5数据离群"],l={};for(let r=0;r<4;r++){l[`dataList${r+1}`]=[];for(let n=0;n<7;n++)l[`dataList${r+1}`].push({name:e[Math.floor(Math.random()*6)],site:h[Math.floor(Math.random()*8)],type:r+1,reason:u[Math.floor(Math.random()*12)]})}c.value=l};return C(()=>{f()}),(e,h)=>(H(),S("div",O,[M("div",V,[o(b,{onChangeType:y})]),M("div",$,[M("div",j,[o(N,{currentType:a(t)},null,8,["currentType"]),o(w,{currentType:a(t)},null,8,["currentType"]),o(z,{currentType:a(t),moduleData:a(s)},null,8,["currentType","moduleData"])]),M("div",E,[o(I,{currentType:a(t),isActiveBtn:a(T)},null,8,["currentType","isActiveBtn"]),o(R,{currentType:a(t),moduleData:a(m)},null,8,["currentType","moduleData"])]),M("div",G,[o(k,{currentType:a(t)},null,8,["currentType"]),o(B,{currentType:a(t)},null,8,["currentType"]),o(L,{currentType:a(t),moduleData:a(c)},null,8,["currentType","moduleData"])])])]))}},oa=W(q,[["__scopeId","data-v-833b3877"]]);export{oa as default};
