import{_ as m,r as g,w as V,c as l,a as s,b as o,k as d,t as u,n as h}from"./index-B3fBvuMC.js";const f={class:"switch-core"},x={key:0,class:"active-text"},b={key:1,class:"inactive-text"},k={__name:"switch",props:{modelValue:{type:[Boolean,String,Number],default:!1},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},activeText:String,inactiveText:String,disabled:Boolean,loading:Boolean,size:{type:String,validator:e=>["small","middle","large"].includes(e),default:"middle"}},emits:["update:modelValue","change"],setup(e,{emit:r}){const a=e,c=r,t=g(a.modelValue===a.activeValue);V(()=>a.modelValue,i=>{t.value=i===a.activeValue});const v=()=>{if(a.disabled||a.loading)return;const i=t.value?a.inactiveValue:a.activeValue;t.value=!t.value,c("update:modelValue",i),c("change",i)};return(i,n)=>(s(),l("div",{class:h(["switch-wrapper",[`size-${e.size}`,{"is-disabled":e.disabled},{"is-loading":e.loading},{"is-checked":t.value}]]),onClick:v},[o("div",f,[e.activeText?(s(),l("span",x,u(e.activeText),1)):d("",!0),e.inactiveText?(s(),l("span",b,u(e.inactiveText),1)):d("",!0),n[0]||(n[0]=o("div",{class:"switch-button"},null,-1))])],2))}},S=m(k,[["__scopeId","data-v-687a71a9"]]);export{S};
