import{_ as B,r as s,o as b,z as A,c as o,a as d,b as a,A as F,t as l,B as I,F as S,l as k,n as f,i as N,d as z}from"./index-B3fBvuMC.js";import U from"./index-BXG-qORq.js";const q={class:"header-wrapper"},E={class:"header-menu"},H={class:"header-menu_current"},L={class:"header-menu_menu"},O=["onClick"],Y={class:"header-types"},j=["onClick"],G={class:"header-date"},J={class:"header-switch"},K={__name:"index",props:{menuData:{type:Array,default:()=>[{name:"智能调度",id:0},{name:"监控预警",id:1},{name:"数据资源",id:2}]},types:{type:Array,default:()=>[{id:1,name:"总览",key:"all"},{id:2,name:"地表水",key:"shui"},{id:3,name:"环境空气",key:"kq"}]}},emits:["changeMenu","changeType","changeTheme"],setup(c,{emit:w}){const h=c,r=w,i=s(h.menuData[0]),u=s(!1),x=()=>{u.value=!u.value},M=e=>{i.value=e,r("changeMenu",e)},v=s(h.types[0]),T=e=>{v.value=e,r("changeType",e)},p=s(!0),D=e=>{r("changeTheme",e)},_=s("");let g=null;const y=()=>{const e=new Date,n=String(e.getHours()).padStart(2,"0"),t=String(e.getMinutes()).padStart(2,"0"),m=String(e.getSeconds()).padStart(2,"0"),$=e.getFullYear(),C=String(e.getMonth()+1).padStart(2,"0"),V=String(e.getDate()).padStart(2,"0");_.value=`${$}-${C}-${V} ${n}:${t}:${m}`};return b(()=>{y(),g=setInterval(y,1e3)}),A(()=>{clearInterval(g)}),(e,n)=>(d(),o("div",q,[a("div",E,[a("div",H,l(i.value.name),1),a("div",{class:"header-menu_change",onClick:x}),F(a("ul",L,[(d(!0),o(S,null,k(c.menuData,t=>(d(),o("li",{key:t.id,class:f(["header-menu_menu_item",{"header-menu_menu_item_active":i.value.id===t.id}]),onClick:m=>M(t)},l(t.name),11,O))),128))],512),[[I,u.value]])]),n[1]||(n[1]=a("div",{style:{width:"1285px"}},null,-1)),n[2]||(n[2]=a("div",{class:"header-title"},[N("驾驶舱 "),a("span",{class:"header-title_btm"})],-1)),a("div",Y,[(d(!0),o(S,null,k(c.types,t=>(d(),o("div",{class:f(["header-types_item",{"header-types_item_active":t.id===v.value.id}]),key:t.id,onClick:m=>T(t)},l(t.name),11,j))),128))]),a("div",G,l(_.value),1),a("div",J,[z(U,{modelValue:p.value,"onUpdate:modelValue":n[0]||(n[0]=t=>p.value=t),"active-text":"浅","inactive-text":"深",onChange:D},null,8,["modelValue"])])]))}},R=B(K,[["__scopeId","data-v-b25629aa"]]);export{R as default};
