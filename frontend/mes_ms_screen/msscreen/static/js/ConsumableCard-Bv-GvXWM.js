import{_ as b,o as _,N as g,c as p,a as v,b as t,t as i}from"./index-B3fBvuMC.js";const f={class:"consumable-card"},h={class:"card-header"},C={class:"card-left"},x=["src"],y={class:"title-text"},k={class:"card-metrics"},q={class:"card-item"},A=["data-target"],F={class:"card-item"},B=["data-target"],N={class:"card-item"},w=["data-target"],E={class:"card-item"},M=["data-target"],S={__name:"ConsumableCard",props:{item:{type:Object,required:!0}},setup(a){const o=()=>{document.querySelectorAll(".number-animate").forEach(e=>{const l=parseFloat(e.getAttribute("data-target")||"0"),r=500,d=performance.now(),s=m=>{const n=Math.min((m-d)/r,1),u=(l*n).toFixed(2);e.textContent=u,n<1&&requestAnimationFrame(s)};requestAnimationFrame(s)})};return _(async()=>{await g(),o()}),(c,e)=>(v(),p("div",f,[t("div",h,[t("div",C,[t("img",{src:a.item.icon,alt:"icon",class:"card-icon"},null,8,x),t("span",y,[t("span",null,i(a.item.title.slice(0,2)),1),e[0]||(e[0]=t("br",null,null,-1)),t("span",null,i(a.item.title.slice(2)),1)])]),t("div",k,[t("div",q,[e[1]||(e[1]=t("span",{class:"item-label"},"实时库存：",-1)),t("span",{class:"item-value number-animate","data-target":a.item.stock},"0",8,A)]),t("div",F,[e[2]||(e[2]=t("span",{class:"item-label"},"类别总数：",-1)),t("span",{class:"item-value number-animate","data-target":a.item.categoryCount},"0",8,B)]),t("div",N,[e[3]||(e[3]=t("span",{class:"item-label"},"出库量：",-1)),t("span",{class:"item-value number-animate","data-target":a.item.outCount},"0",8,w)]),t("div",E,[e[4]||(e[4]=t("span",{class:"item-label"},"入库量：",-1)),t("span",{class:"item-value number-animate","data-target":a.item.inCount},"0",8,M)])])])]))}},j=b(S,[["__scopeId","data-v-332d1473"]]);export{j as default};
