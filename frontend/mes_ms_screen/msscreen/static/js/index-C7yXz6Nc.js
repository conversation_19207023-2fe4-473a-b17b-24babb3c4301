import{i as D}from"./index-BrbPvnuX.js";import"./linesGL-Dv4775iD.js";import{_ as R,r as z,o as E,c as d,a as y,b as m,F as C,l as F,v as b,t as _}from"./index-B3fBvuMC.js";const N={class:"chart-box-container"},q={class:"chart-info"},A={class:"item-name"},V={class:"item-val"},$=.2,H={__name:"index",props:{list:{type:Array,default:[]}},setup(x){const I=x.list.map(e=>({...e,itemStyle:{color:e.labelColor}}));function g(e,r,a,M,i,f){let p=(e+r)/3,h=e*Math.PI*2,l=r*Math.PI*2,t=p*Math.PI*2;e===0&&r===1&&(a=!1),i=typeof i<"u"?i:1/3;let s=a?Math.cos(t)*.1:0,c=a?Math.sin(t)*.1:0,u=1;return{u:{min:-Math.PI,max:Math.PI*3,step:Math.PI/32},v:{min:0,max:Math.PI*2,step:Math.PI/20},x:function(o,n){return o<h?s+Math.cos(h)*(1+Math.cos(n)*i)*u:o>l?s+Math.cos(l)*(1+Math.cos(n)*i)*u:s+Math.cos(o)*(1+Math.cos(n)*i)*u},y:function(o,n){return o<h?c+Math.sin(h)*(1+Math.cos(n)*i)*u:o>l?c+Math.sin(l)*(1+Math.cos(n)*i)*u:c+Math.sin(o)*(1+Math.cos(n)*i)*u},z:function(o,n){return o<-Math.PI*.5||o>Math.PI*2.5?Math.sin(o):Math.sin(n)>0?$*f:-1}}}function w(e,r){let a=[],M=0,i=0,f=0,p=[],h=[],l=(1-r)/(1+r);for(let t=0;t<e.length;t++){M+=e[t].value;let s={name:typeof e[t].name>"u"?`series${t}`:e[t].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:e[t],pieStatus:{selected:!1,hovered:!1,k:l}};if(typeof e[t].itemStyle<"u"){let c={};typeof e[t].itemStyle.color<"u"&&(c.color=e[t].itemStyle.color),typeof e[t].itemStyle.opacity<"u"&&(c.opacity=e[t].itemStyle.opacity),s.itemStyle=c}a.push(s)}for(let t=0;t<a.length;t++)f=i+a[t].pieData.value,a[t].pieData.startRatio=i/M,a[t].pieData.endRatio=f/M,a[t].parametricEquation=g(a[t].pieData.startRatio,a[t].pieData.endRatio,!1,!1,l,a[t].pieData.value),i=f,(a[t].pieData.endRatio+a[t].pieData.startRatio)*Math.PI,Math.log(Math.abs(a[t].pieData.value+1))*.1,e[t].itemStyle.color,p.push(a[t].name);return a=a.concat(h),a.push({name:"mouseoutSeries",type:"surface",parametric:!0,wireframe:{show:!1},itemStyle:{opacity:.3,color:"#00BBF5"},parametricEquation:{u:{min:0,max:Math.PI*2,step:Math.PI/20},v:{min:0,max:Math.PI,step:Math.PI/20},x:function(t,s){return(Math.sin(s)*Math.sin(t)+Math.sin(t))/Math.PI*2},y:function(t,s){return(Math.sin(s)*Math.cos(t)+Math.cos(t))/Math.PI*2},z:function(t,s){return Math.cos(s)>0?-.5:-2}}}),a.push({name:"mouseoutSeries",type:"surface",parametric:!0,wireframe:{show:!1},itemStyle:{opacity:.1,color:"#00BBF5"},parametricEquation:{u:{min:0,max:Math.PI*2,step:Math.PI/20},v:{min:0,max:Math.PI,step:Math.PI/20},x:function(t,s){return(Math.sin(s)*Math.sin(t)+Math.sin(t))/Math.PI*2.2},y:function(t,s){return(Math.sin(s)*Math.cos(t)+Math.cos(t))/Math.PI*2.2},z:function(t,s){return Math.cos(s)>0?-2:-3}}}),a}let P=0;I.forEach(e=>{P+=e.val});const B=w(I.map(e=>(e.value=Number((e.val/P*100).toFixed(2)),e)),.8),v={tooltip:{formatter:e=>{if(e.seriesName!=="mouseoutSeries"&&e.seriesName!=="pie2d")return`${e.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${e.color};"></span>${v.series[e.seriesIndex].pieData.val}`},textStyle:{fontSize:12}},label:{show:!1},xAxis3D:{min:-1.5,max:1.5},yAxis3D:{min:-1.5,max:1.5},zAxis3D:{min:-1,max:1},grid3D:{show:!1,boxHeight:7,bottom:"0%",viewControl:{distance:120,alpha:25,beta:60,autoRotate:!1}},series:B},S=z(null);return E(()=>{D(S.value).setOption(v)}),(e,r)=>(y(),d("div",N,[m("div",{class:"chart-box",ref_key:"chart",ref:S},null,512),m("div",q,[(y(!0),d(C,null,F(x.list,a=>(y(),d("div",{key:a.id,class:"info-item",style:b(a.labelBgStyle)},[m("div",{class:"item-label",style:b({backgroundColor:a.labelColor})},null,4),m("div",A,_(a.name),1),m("div",V,_(a.value),1)],4))),128))])]))}},Y=R(H,[["__scopeId","data-v-629632f0"]]);export{Y as default};
