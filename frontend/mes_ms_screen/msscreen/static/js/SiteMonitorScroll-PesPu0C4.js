import{i as c}from"./index-BrbPvnuX.js";import{B as d}from"./index-C4TfGCsC.js";import{_ as f,r as p,o as u,N as x,z as m,c as b,a as h,d as y,b as _,m as S,i as v,u as g}from"./index-B3fBvuMC.js";const w={class:"scrollable-line-chart-wrapper"},z={__name:"SiteMonitorScroll",setup(B){const o=p(null);let e=null,t=null;const s=new Date().getFullYear(),r=Array.from({length:10},(l,a)=>`${s-9+a} 年`),i=[520,580,490,630,760,910,880,1020,1100,1070];function n(){const l={backgroundColor:"transparent",grid:{top:22,left:8,right:22,bottom:20,containLabel:!0},xAxis:{type:"category",data:r,boundaryGap:!1,axisTick:{show:!1},axisLine:{lineStyle:{color:"#DEE2EB"}},axisLabel:{color:"#666666",fontSize:12,margin:16}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DEE2EB"}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#999"}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:Math.min(100,6/r.length*100),height:20,bottom:-20,handleSize:"100%",handleStyle:{color:"#0065D5"},fillerColor:"rgba(20,114,255,0.2)",borderColor:"#0065D5"},{type:"inside",xAxisIndex:0,start:0,end:Math.min(100,6/r.length*100)}],series:[{name:"点位数量",type:"line",data:i,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2,color:"#1472FF"},itemStyle:{color:"#1472FF"},label:{show:!0,position:"top",color:"#1472FF",fontSize:12},areaStyle:{color:"rgba(20,114,255,0.1)"}}]};e.setOption(l)}return u(async()=>{await x(),o.value&&(e=c(o.value),n(),t=new ResizeObserver(()=>e==null?void 0:e.resize()),t.observe(o.value))}),m(()=>{t&&o.value&&t.unobserve(o.value),e==null||e.dispose()}),(l,a)=>(h(),b("div",w,[y(g(d),{showIcon:!1},{text:S(()=>[a[0]||(a[0]=v("（国控）数据点位数量变更折线图"))]),_:1,__:[0]}),_("div",{ref_key:"chartRef",ref:o,class:"scrollable-line-chart-inner"},null,512)]))}},F=f(z,[["__scopeId","data-v-18dbe6ff"]]);export{F as default};
