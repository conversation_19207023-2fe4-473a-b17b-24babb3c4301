import{_ as P,R as U,M as E,r as _,h as i,c as w,a as f,d as s,b as u,m as r,j as z,k as B,t as K,u as o,S as y,O as M,i as N,U as T,W as I,X as S}from"./index-B3fBvuMC.js";const $={class:"register"},j={class:"title"},A={class:"register-code"},D=["src"],F={key:0},H={key:1},L={style:{float:"right"}},O={__name:"register",setup(W){const V="国家网数据生产系统",x=U(),{proxy:b}=E(),t=_({username:"",password:"",confirmPassword:"",code:"",uuid:""}),k={username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,e,a)=>{t.value.password!==e?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},h=_(""),c=_(!1),p=_(!0);function m(){b.$refs.registerRef.validate(l=>{l&&(c.value=!0,I(t.value).then(e=>{const a=t.value.username;S.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{x.push("/login")}).catch(()=>{})}).catch(()=>{c.value=!1,p&&v()}))})}function v(){T().then(l=>{p.value=l.captchaEnabled===void 0?!0:l.captchaEnabled,p.value&&(h.value="data:image/gif;base64,"+l.img,t.value.uuid=l.uuid)})}return v(),(l,e)=>{const a=i("svg-icon"),g=i("el-input"),d=i("el-form-item"),C=i("el-button"),R=i("router-link"),q=i("el-form");return f(),w("div",$,[s(q,{ref:"registerRef",model:o(t),rules:k,class:"register-form"},{default:r(()=>[u("h3",j,K(o(V)),1),s(d,{prop:"username"},{default:r(()=>[s(g,{modelValue:o(t).username,"onUpdate:modelValue":e[0]||(e[0]=n=>o(t).username=n),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:r(()=>[s(a,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(d,{prop:"password"},{default:r(()=>[s(g,{modelValue:o(t).password,"onUpdate:modelValue":e[1]||(e[1]=n=>o(t).password=n),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(d,{prop:"confirmPassword"},{default:r(()=>[s(g,{modelValue:o(t).confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).confirmPassword=n),type:"password",size:"large","auto-complete":"off",placeholder:"确认密码",onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),o(p)?(f(),z(d,{key:0,prop:"code"},{default:r(()=>[s(g,{size:"large",modelValue:o(t).code,"onUpdate:modelValue":e[3]||(e[3]=n=>o(t).code=n),"auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:y(m,["enter"])},{prefix:r(()=>[s(a,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),u("div",A,[u("img",{src:o(h),onClick:v,class:"register-code-img"},null,8,D)])]),_:1})):B("",!0),s(d,{style:{width:"100%"}},{default:r(()=>[s(C,{loading:o(c),size:"large",type:"primary",style:{width:"100%"},onClick:M(m,["prevent"])},{default:r(()=>[o(c)?(f(),w("span",H,"注 册 中...")):(f(),w("span",F,"注 册"))]),_:1},8,["loading"]),u("div",L,[s(R,{class:"link-type",to:"/login"},{default:r(()=>e[4]||(e[4]=[N("使用已有账户登录")])),_:1,__:[4]})])]),_:1})]),_:1},8,["model"]),e[5]||(e[5]=u("div",{class:"el-register-footer"},[u("span",null,"Copyright © 2018-2025 ruoyi.vip All Rights Reserved.")],-1))])}}},J=P(O,[["__scopeId","data-v-127a6d38"]]);export{J as default};
