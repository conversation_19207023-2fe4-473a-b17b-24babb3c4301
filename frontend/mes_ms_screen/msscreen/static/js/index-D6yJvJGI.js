import{H as x}from"./HeaderNav-CwnasXYK.js";import{B as C}from"./index-C4TfGCsC.js";import{_ as V,r as u,h as k,c as g,a as _,d as s,b as e,m as p,i as f,u as r,F as h,l as I,t as c,E as M,p as S,q as v}from"./index-B3fBvuMC.js";import $ from"./ConsumableDashboard-Bq4O5Fbx.js";import y from"./map-CC1f94AI.js";import B from"./opusers-8PgnVvnP.js";import"./index-BrbPvnuX.js";import"./linesGL-Dv4775iD.js";/* empty css                                                                             */import U from"./SiteMonitoring-D8AqNt9z.js";import w from"./devices-Bi3mBod9.js";import E from"./OperationDialog-4gET7Gi5.js";import N from"./deviceDialog-C-D87dMr.js";import H from"./ConsumaDialog-fNeVLVpq.js";import T from"./SiteMonitorDialog-C8LPxZhK.js";import"./switch-DJpMdJKf.js";import"./ConsumableCard-Bv-GvXWM.js";import"./timeFilter-PW16EO3x.js";import"./icon_biaozhunwuzhi-BnhnKLd7.js";import"./index-EWKaWdrL.js";import"./index-lnVDfRHP.js";import"./index-VkXiS0_E.js";import"./icon_shuishebei-B-ne4_mM.js";import"./OperationTop-Dq8SrjwQ.js";import"./OperationTable-Dpg90Fwu.js";import"./OperatonScroll-oMwH5PXp.js";import"./deviceTopInfo-CxF1B4I5.js";import"./deviceBottomInfo-BBEHD5fP.js";import"./index.vue_vue_type_script_setup_true_lang-CW6cgbhn.js";import"./ConsumaTop-pz3_HKNS.js";import"./ConsumaTable-DcDhuDBC.js";import"./CousumaScroll-DpwYIumy.js";import"./SiteMonitorTop-Bd4FdHj0.js";import"./SiteMonitorScroll-PesPu0C4.js";const F={class:"important-message-module module-box"},L={class:"msg-scroll-content"},O={class:"msg-center"},q={class:"msg-header"},z={class:"msg-title"},A={class:"msg-time"},P=["title"],R={class:"msg-right"},j={style:{"margin-top":"12px"}},G={__name:"message",setup(b){const a=u([{title:"水质异常预警",time:"2025-07-24 14:30",content:"地表水采样点 PH 值超过阈值，请及时处理。"},{title:"监测数据上传失败",time:"2025-07-24 13:00",content:"南门子站点 12 点数据上传失败，请检查设备状态。"},{title:"AI识别异常行为",time:"2025-07-24 12:45",content:"摄像头识别到异常人员靠近重点区域，请确认安全性。"},{title:"气象异常通知",time:"2025-07-24 11:20",content:"未来 1 小时可能出现强降雨，请提前做好防范。"}]),n=u(!1),i=u({title:"",time:"",content:""}),m=d=>{i.value=d,n.value=!0};return(d,l)=>{const t=k("vue3SeamlessScroll");return _(),g("div",F,[s(r(C),{showIcon:!1},{text:p(()=>[l[1]||(l[1]=f("重要事项"))]),_:1,__:[1]}),e("div",L,[s(t,{list:a.value,class:"scroll-list",step:.2,limitScrollNum:1,hover:!1},{default:p(()=>[(_(!0),g(h,null,I(a.value,(o,D)=>(_(),g("div",{class:"msg-item",key:D},[l[3]||(l[3]=e("div",{class:"msg-left"},"🔔",-1)),e("div",O,[e("div",q,[e("span",z,c(o.title),1),e("span",A,c(o.time),1)]),e("div",{class:"msg-content",title:o.content},c(o.content),9,P)]),e("div",R,[s(r(M),{size:"small",type:"primary",class:"custom-round-btn",onClick:et=>m(o)},{default:p(()=>l[2]||(l[2]=[f(" 去看看 ")])),_:2,__:[2]},1032,["onClick"])])]))),128))]),_:1},8,["list"])]),s(r(S),{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=o=>n.value=o),title:"重要事项详情",width:"500px"},{default:p(()=>[e("p",null,[e("strong",null,c(i.value.title),1)]),e("p",null,c(i.value.time),1),e("p",j,c(i.value.content),1)]),_:1},8,["modelValue"])])}}},J=V(G,[["__scopeId","data-v-c2982d3a"]]),K={class:"monitor-screen"},Q={class:"header-box"},W={class:"content-container"},X={class:"left"},Y={class:"center"},Z={class:"right"},tt={__name:"index",setup(b){const a=u(!1),n=u(!1),i=u(!1),m=u(!1),d=l=>{const t=l.type,o=l.name;console.log("🤣",o),a.value=!1,n.value=!1,i.value=!1,m.value=!1,t=="siteMonitor"?a.value=!0:t=="consumDash"?i.value=!0:t=="deviceManager"?m.value=!0:t=="opuserDash"&&(n.value=!0)};return(l,t)=>(_(),g("div",K,[e("div",Q,[s(x,null,{right:p(()=>t[4]||(t[4]=[])),_:1})]),e("div",W,[e("div",X,[s(U,{onIconClick:d}),t[5]||(t[5]=e("div",{class:"spacer"},null,-1)),s(B,{onIconClick:d})]),e("div",Y,[s(y),s(J)]),e("div",Z,[s(r(w),{onIconClick:d}),t[6]||(t[6]=e("div",{class:"spacer"},null,-1)),s($,{onIconClick:d})])]),s(N,{dialogVisible:r(m),"onUpdate:dialogVisible":t[0]||(t[0]=o=>v(m)?m.value=o:null)},null,8,["dialogVisible"]),s(H,{dialogVisible:r(i),"onUpdate:dialogVisible":t[1]||(t[1]=o=>v(i)?i.value=o:null)},null,8,["dialogVisible"]),s(E,{dialogVisible:r(n),"onUpdate:dialogVisible":t[2]||(t[2]=o=>v(n)?n.value=o:null)},null,8,["dialogVisible"]),s(T,{dialogVisible:r(a),"onUpdate:dialogVisible":t[3]||(t[3]=o=>v(a)?a.value=o:null)},null,8,["dialogVisible"])]))}},Tt=V(tt,[["__scopeId","data-v-1cc734fc"]]);export{Tt as default};
