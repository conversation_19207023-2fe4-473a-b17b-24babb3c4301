import{i as k,r as p}from"./index-BrbPvnuX.js";import{_ as y,r as b,o as z,f as B,z as M,w as T,h as F,c as E,a as v,j as D,k as A,b as O,m as R,i as W}from"./index-B3fBvuMC.js";const $={class:"map-wrapper"},J={__name:"index",props:{data:{type:Array,default:()=>[]}},setup(C){const c={安徽:"an_hui_geo.json",澳门:"ao_men_geo.json",北京:"bei_jing_geo.json",重庆:"chong_qing_geo.json",福建:"fu_jian_geo.json",甘肃:"gan_su_geo.json",广东:"guang_dong_geo.json",广西:"guang_xi_geo.json",贵州:"gui_zhou_geo.json",海南:"hai_nan_geo.json",河北:"he_bei_geo.json",河南:"he_nan_geo.json",黑龙江:"hei_long_jiang_geo.json",湖北:"hu_bei_geo.json",湖南:"hu_nan_geo.json",吉林:"ji_lin_geo.json",江苏:"jiang_su_geo.json",江西:"jiang_xi_geo.json",辽宁:"liao_ning_geo.json",内蒙古:"nei_meng_gu_geo.json",宁夏:"ning_xia_geo.json",青海:"qing_hai_geo.json",山东:"shan_dong_geo.json",山西:"shan_xi_2_geo.json",陕西:"shan_xi_1_geo.json",上海:"shang_hai_geo.json",四川:"si_chuan_geo.json",台湾:"tai_wan_geo.json",天津:"tian_jin_geo.json",西藏:"xi_zang_geo.json",香港:"xiang_gang_geo.json",新疆:"xin_jiang_geo.json",云南:"yun_nan_geo.json",浙江:"zhe_jiang_geo.json",china:"china_geo.json"},r=C,n=b(null),g=b(!1);let o=null,t=null;const _=()=>{o&&(o.dispose(),o=null)},j=async a=>await(await fetch(`/msscreen/geoJson/${a}`)).json(),u=async()=>{var m;if(!n.value||n.value.clientWidth===0){setTimeout(u,100);return}_();const a=[],s=[],i=[],d=[];(m=r==null?void 0:r.data)==null||m.forEach(l=>{a.push(l.date),s.push(l.totalCount),i.push(l.completeTaskCount),d.push(l.unCompleteTaskCount)}),o=k(n.value);const e=await j(c.china);p("china",e),h("china");let f=null;o.off("click"),o.on("click",function(l){clearTimeout(f);const w=l.name;f=setTimeout(function(){h(w),g.value=!0},250)})};function x(){g.value=!1,h("china")}async function h(a){const s=await j(c[a]);p(a,s);const i=Object.keys(c).filter(e=>e!=="china").map(e=>({name:e,value:Math.round(Math.random()*400)})),d={tooltip:{show:!0,formatter:e=>e.data?`${e.name}：${e.data.value||0}`:""},visualMap:{text:["",""],showLabel:!0,left:"50",selectedMode:!1,pieces:[{gte:301,label:"300以上",color:"#F02C2F"},{gte:201,lte:300,label:"201 - 300",color:"#006EFF"},{gte:101,lte:200,label:"101 - 200",color:"#F8C13B"},{gte:0,lte:100,label:"0 - 100",color:"#1BCC98"}]},series:[{name:"MAP",type:"map",map:a,selectedMode:!1,zoom:1.25,label:{show:!0,formatter:e=>e.value?`{labelBg|${e.value??""}}`:"",rich:{labelBg:{backgroundColor:"rgba(255,255,255,0.60)",padding:[4,4],borderRadius:100,borderColor:"rgba(255,255,255,0.3)",borderWidth:2,width:18,height:18,textAlign:"center",verticalAlign:"middle"}},color:"#006EFF",fontSize:12,lineHeight:14},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},itemStyle:{areaColor:"#1BCC98",borderColor:"#fff",borderWidth:1.5},data:i}]};o.setOption(d),o.resize()}return z(()=>{n.value&&(t=new ResizeObserver(()=>{o==null||o.resize()}),t.observe(n.value),setTimeout(()=>{u()},50))}),B(()=>{t&&n.value&&t.unobserve(n.value),_()}),M(()=>{t&&n.value&&(t.unobserve(n.value),t=null),_()}),T(()=>r.data,()=>{_(),u()},{deep:!0,immediate:!0}),(a,s)=>{const i=F("el-button");return v(),E("div",$,[g.value?(v(),D(i,{key:0,onClick:x,class:"china-btn"},{default:R(()=>s[0]||(s[0]=[W("中国")])),_:1,__:[0]})):A("",!0),O("div",{class:"map-container",ref_key:"mapContainerRef",ref:n},null,512)])}}},V=y(J,[["__scopeId","data-v-a359b952"]]);export{V as E};
