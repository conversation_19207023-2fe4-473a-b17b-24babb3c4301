import{_ as f,j as _,a as r,m as b,c as g,k,O as n,b as o,d as t,u as v,T as C}from"./index-B3fBvuMC.js";import h from"./OperationTop-Dq8SrjwQ.js";import V from"./OperationTable-Dpg90Fwu.js";import x from"./OperatonScroll-oMwH5PXp.js";import"./index-C4TfGCsC.js";import"./timeFilter-PW16EO3x.js";import"./icon_biaozhunwuzhi-BnhnKLd7.js";import"./index-BrbPvnuX.js";const N={class:"device-dialog-body"},O={class:"section-wrapper topConsuma"},w={class:"section-wrapper bottomConsuma"},B={__name:"OperationDialog",props:{dialogVisible:Boolean},emits:["update:dialogVisible"],setup(c,{emit:l}){const m=c,p=l;function s(){p("update:dialogVisible",!1)}const d=["华安科技","中电运维","信诚服务","腾达机电","博远能源","启迪运维","中科信息","智恒科技","德昌智能","维达系统"].map(e=>{const a=Math.floor(Math.random()*50)+5,i=(Math.random()*40+60).toFixed(1),u=(Number(i)-Math.random()*10).toFixed(1);return{name:e,score:Number(i),staffCount:a,avgScore:Number(u)}});return(e,a)=>(r(),_(C,{name:"fade"},{default:b(()=>[m.dialogVisible?(r(),g("div",{key:0,class:"custom-dialog-mask",onClick:n(s,["self"])},[o("div",{class:"custom-dialog-wrapper",onClick:a[0]||(a[0]=n(()=>{},["stop"]))},[o("div",{class:"dialog-header"},[a[1]||(a[1]=o("span",{class:"title"},"人员与机构情况",-1)),o("span",{class:"close-btn",onClick:s},"×")]),o("div",N,[o("div",O,[t(h)]),o("div",w,[t(V,{"company-list":v(d)},null,8,["company-list"]),t(x)])])])])):k("",!0)]),_:1}))}},I=f(B,[["__scopeId","data-v-ec736898"]]);export{I as default};
