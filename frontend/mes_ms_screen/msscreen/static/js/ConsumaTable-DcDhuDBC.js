import{B as m}from"./index-C4TfGCsC.js";import{e as h,r as v,g as f,h as s,c as y,a as x,d as e,b as l,m as r,i as C,u as w,t as k,_ as B}from"./index-B3fBvuMC.js";const T={class:"inventory-warning-table module-box"},L={class:"table-wrapper"},N={class:"stock-danger"},V={class:"table-pagination"},c=5,z=h({__name:"ConsumaTable",props:{inventoryList:{default:()=>[]}},setup(i){const p=i,o=v(1),d=f(()=>{var t;const a=(o.value-1)*c;return((t=p.inventoryList)==null?void 0:t.slice(a,a+c))||[]}),_=a=>{o.value=a};return(a,t)=>{const n=s("el-table-column"),u=s("el-table"),g=s("el-pagination");return x(),y("div",T,[e(w(m),{showIcon:!1},{text:r(()=>[t[0]||(t[0]=C("库存预警列表"))]),_:1,__:[0]}),l("div",L,[e(u,{data:d.value,border:"",style:{width:"100%"},"header-cell-class-name":"table-header"},{default:r(()=>[e(n,{prop:"type",label:"耗材类型",align:"center"}),e(n,{prop:"category",label:"耗材分类",align:"center"}),e(n,{label:"库存量",align:"center"},{default:r(({row:b})=>[l("span",N,k(b.stock),1)]),_:1}),e(n,{prop:"warehouse",label:"所属仓库",align:"center"})]),_:1},8,["data"]),l("div",V,[e(g,{layout:"prev, pager, next",total:a.inventoryList.length,"page-size":c,"current-page":o.value,onCurrentChange:_,background:""},null,8,["total","current-page"])])])])}}}),P=B(z,[["__scopeId","data-v-ddb41b62"]]);export{P as default};
