import{B as g}from"./index-C4TfGCsC.js";import{i as p,a as v,b as h,c as b}from"./icon_biaozhunwuzhi-BnhnKLd7.js";import{_ as f,c as e,a,d as m,b as t,m as F,i as B,u as k,F as l,l as r,v as d,t as c}from"./index-B3fBvuMC.js";const x={class:"site-monitor-top"},y={class:"card-grid"},D={class:"left-section"},S={class:"card-icon"},T=["src"],A={class:"card-title"},I={class:"right-section"},M={class:"info-value"},N=["src"],V={class:"block-content"},w={class:"info-label"},C={class:"info-value"},z={__name:"SiteMonitorTop",setup(E){const o=[{id:1,title:"站点总数",icon:p,count:1342},{id:2,title:"实现自动校对站点",icon:v,count:865},{id:3,title:"智慧站点",icon:h,count:432},{id:4,title:"改造情况",icon:b,upgrading:56,expected:98}],_=[{label:"正在改造站点",value:o[3].upgrading},{label:"预计年底完成",value:o[3].expected}],n={站点总数:"linear-gradient(to right, #0065D5, #3B8AFF)",实现自动校对站点:"linear-gradient(to right, #00BBF5, #46D9FF)",智慧站点:"linear-gradient(to right, #67C23A, #90D878)",改造情况:"linear-gradient(to right, #FF9A54, #FFBB86)"};return(L,i)=>(a(),e("div",x,[m(k(g),{showIcon:!1},{text:F(()=>[i[0]||(i[0]=B("站点监测"))]),_:1,__:[0]}),t("div",y,[(a(!0),e(l,null,r(o.slice(0,3),s=>(a(),e("div",{key:s.id,class:"card normal-card",style:d({background:n[s.title]})},[t("div",D,[t("div",S,[t("img",{src:s.icon,alt:"icon"},null,8,T)]),t("div",A,c(s.title),1)]),t("div",I,[t("span",M,c(s.count),1),i[1]||(i[1]=t("span",{class:"info-label"},"个",-1))])],4))),128)),t("div",{class:"card double-card",style:d({background:n.改造情况})},[(a(),e(l,null,r(_,(s,u)=>t("div",{class:"info-block",key:u},[t("img",{src:o[3].icon,class:"block-icon"},null,8,N),t("div",V,[t("div",w,c(s.label),1),t("div",C,c(s.value),1)])])),64))],4)])]))}},H=f(z,[["__scopeId","data-v-c3da1797"]]);export{H as default};
