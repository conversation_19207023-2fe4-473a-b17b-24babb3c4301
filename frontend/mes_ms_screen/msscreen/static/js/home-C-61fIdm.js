import{P as L,_ as w,g as u,o as x,h as n,c as a,a as c,b as l,d as t,m as e,F as p,l as f,i as y,t as r}from"./index-B3fBvuMC.js";/* empty css                                                             */const S=L("user",{state:()=>({userInfo:{avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",nickname:"Admin",todoList:[],tasks:[]}}),actions:{initUserInfo(){this.userInfo={avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",nickname:"Admin",todoList:[{id:1,title:"待审批流程",status:0},{id:2,title:"月度报告",status:0}],tasks:[{id:1,title:"项目会议",time:"14:00"},{id:2,title:"客户回访",time:"16:30"}]}},logout(){this.$reset(),this.initUserInfo(),window.location.reload()}}}),U={class:"home-container"},B={class:"home-main"},N={class:"title"},V={class:"time"},A={class:"title"},C={__name:"home",setup(F){const i=S(),h=u(()=>{var o;return((o=i.userInfo)==null?void 0:o.todoList)||[]}),g=u(()=>{var o;return((o=i.userInfo)==null?void 0:o.tasks)||[]});return x(()=>{i.userInfo.todoList||i.initUserInfo()}),(o,d)=>{const k=n("el-tag"),_=n("el-card"),m=n("el-col"),v=n("alarm-clock"),I=n("el-icon"),b=n("el-row");return c(),a("div",U,[l("div",B,[t(b,{gutter:20},{default:e(()=>[t(m,{span:12},{default:e(()=>[t(_,{header:"我的待办"},{default:e(()=>[(c(!0),a(p,null,f(h.value,s=>(c(),a("div",{key:s.id,class:"todo-item"},[t(k,{type:"info"},{default:e(()=>d[0]||(d[0]=[y("未完成")])),_:1,__:[0]}),l("span",N,r(s.title),1)]))),128))]),_:1})]),_:1}),t(m,{span:12},{default:e(()=>[t(_,{header:"今日任务"},{default:e(()=>[(c(!0),a(p,null,f(g.value,s=>(c(),a("div",{key:s.id,class:"task-item"},[t(I,null,{default:e(()=>[t(v)]),_:1}),l("span",V,r(s.time),1),l("span",A,r(s.title),1)]))),128))]),_:1})]),_:1})]),_:1})])])}}},M=w(C,[["__scopeId","data-v-c82163d6"]]);export{M as default};
