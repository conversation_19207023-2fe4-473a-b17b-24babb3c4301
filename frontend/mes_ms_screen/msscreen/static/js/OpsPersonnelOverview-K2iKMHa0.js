import{i as m}from"./index-BrbPvnuX.js";import"./linesGL-Dv4775iD.js";/* empty css                                                                             */import{_ as v,r as u,o as p,c as n,a as d,b as e,t as i,u as _,F as f,l as x,v as c}from"./index-B3fBvuMC.js";const y={class:"ops-personnel-overview module-box"},h={class:"personnel-cards"},k={class:"card total"},S={class:"number"},B={class:"chart-box-container"},C={class:"chart-info"},F={class:"item-name"},w={class:"item-val"},D=80,A=20,O={__name:"OpsPersonnelOverview",setup(N){const r=[{id:1,name:"名称1设备有限公司",val:20,labelColor:"#4FA0FF",labelBgStyle:"background-image: linear-gradient(90deg, rgba(79,160,255,0.30) 3%, rgba(79,160,255,0.00) 99%)"},{id:2,name:"这是一个名字很长的运维公司",val:100,labelColor:"#67C23A",labelBgStyle:"background-image: linear-gradient(90deg, rgba(103,194,58,0.30) 3%, rgba(103,194,58,0.00) 99%)"},{id:3,name:"名称三设备有限公司",val:50,labelColor:"#FBD500",labelBgStyle:"background-image: linear-gradient(90deg, rgba(251,213,0,0.30) 3%, rgba(251,213,0,0.00) 99%)"},{id:4,name:"名称四设备有限公司",val:20,labelColor:"#FF9A54",labelBgStyle:"background-image: linear-gradient(90deg, rgba(255,154,84,0.30) 3%, rgba(255,154,84,0.00) 97%)"},{id:5,name:"名称五设备有限公司",val:17,labelColor:"#F56C6D",labelBgStyle:"background-image: linear-gradient(90deg, rgba(245,108,109,0.30) 3%, rgba(245,108,109,0.00) 99%)"},{id:6,name:"名称六六六设备有限公司",val:39,labelColor:"#00BBF5",labelBgStyle:"background-image: linear-gradient(90deg, rgba(0,187,245,0.30) 3%, rgba(0,187,245,0.00) 99%)"}],g=r.reduce((l,a)=>l+a.val,0),o=u(null);p(()=>{const l=b(r),a={tooltip:{formatter:t=>{if(t.seriesName!=="mouseoutSeries"&&t.seriesName!=="pie2d")return`${t.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${t.color};"></span>${t.data.value}`},textStyle:{fontSize:12}},label:{show:!1},xAxis3D:{min:-1.5,max:1.5},yAxis3D:{min:-1.5,max:1.5},zAxis3D:{min:-1,max:1},grid3D:{boxHeight:7,bottom:"0%",viewControl:{distance:120,alpha:25,beta:60,autoRotate:!1}},series:l};m(o.value).setOption(a)});function b(l){return[{name:"运维人员",type:"pie",radius:"60%",data:l.map(a=>({value:a.val,name:a.name})),label:{show:!1},itemStyle:{borderRadius:8}}]}return(l,a)=>(d(),n("div",y,[e("div",h,[e("div",k,[a[0]||(a[0]=e("div",{class:"title"},"运维人员总数",-1)),e("div",S,i(_(g)),1)]),e("div",{class:"card working"},[a[1]||(a[1]=e("div",{class:"title"},"工作中人员",-1)),e("div",{class:"number"},i(D))]),e("div",{class:"card idle"},[a[2]||(a[2]=e("div",{class:"title"},"空闲人员",-1)),e("div",{class:"number"},i(A))])]),e("div",B,[e("div",{class:"chart-box",ref_key:"pieChart",ref:o},null,512),e("div",C,[(d(),n(f,null,x(r,s=>e("div",{key:s.id,class:"info-item",style:c(s.labelBgStyle)},[e("div",{class:"item-label",style:c({backgroundColor:s.labelColor})},null,4),e("div",F,i(s.name),1),e("div",w,i(s.val),1)],4)),64))])])]))}},E=v(O,[["__scopeId","data-v-ee09fa21"]]);export{E as default};
