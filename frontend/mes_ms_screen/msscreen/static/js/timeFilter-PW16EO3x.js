import{_ as d,c as s,a as l,b as i,F as u,l as m,n as p,t as _}from"./index-B3fBvuMC.js";const b={class:"date-tab"},v={class:"tab-btns"},h=["onClick"],k={__name:"timeFilter",props:{modelValue:{type:String,required:!0},options:{type:Array,default:()=>[{label:"当月",value:"month"},{label:"当日",value:"day"}]}},emits:["update:modelValue"],setup(t,{emit:o}){const n=t,c=o,r=a=>{a!==n.modelValue&&c("update:modelValue",a)};return(a,y)=>(l(),s("div",b,[i("div",v,[(l(!0),s(u,null,m(t.options,e=>(l(),s("div",{key:e.value,class:p(t.modelValue===e.value?"btn active":"btn"),onClick:V=>r(e.value)},_(e.label),11,h))),128))])]))}},C=d(k,[["__scopeId","data-v-d0b92147"]]);export{C as D};
