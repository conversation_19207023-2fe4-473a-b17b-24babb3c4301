import{N as D}from"./ScreenNavBar-qBdlSVbA.js";import{i as d,L}from"./index-BrbPvnuX.js";import{_ as S,r as a,o as k,c as A,a as E,d as o,b as t,u as i,t as l,m as u,G as r,H as z,i as N,D as O}from"./index-B3fBvuMC.js";import"./switch-DJpMdJKf.js";const G={class:"dashboard-container"},V={class:"dashboard-main"},q={class:"monitor-overview"},R={class:"overview-item"},H={class:"item-value"},I={class:"overview-item"},M={class:"item-value danger"},P={class:"overview-item"},X={class:"item-value"},j={class:"overview-item"},F={class:"item-value"},J={class:"charts-grid"},K={class:"chart-card large"},Q={class:"chart-content"},U={class:"chart-card"},W={class:"chart-content"},Y={class:"chart-card"},Z={class:"chart-content"},$={class:"alert-list"},tt={__name:"qulity",setup(et){const f=a(328),g=a(12),b=a(96),y=a(5),v=a(null),m=a(null),p=a(null),h=a(96.5),w=a([{name:"连接异常",value:4},{name:"性能超标",value:3},{name:"数据异常",value:2},{name:"其他",value:3}]),_=a([{hour:"00时",count:1},{hour:"04时",count:0},{hour:"08时",count:2},{hour:"12时",count:3},{hour:"16时",count:4},{hour:"20时",count:2}]),C=a([{time:"16:42:30",resource:"服务器-05",type:"连接异常",level:"严重",status:"处理中"},{time:"16:15:22",resource:"存储-02",type:"性能超标",level:"警告",status:"已处理"},{time:"15:30:18",resource:"交换机-03",type:"数据异常",level:"警告",status:"已处理"},{time:"14:50:05",resource:"服务器-12",type:"连接异常",level:"严重",status:"处理中"}]),x=()=>{const s=d(v.value);return s.setOption({series:[{type:"gauge",startAngle:90,endAngle:-270,pointer:{show:!1},progress:{roundCap:!0,clip:!1,itemStyle:{color:h.value>90?"#10b981":"#f59e0b"}},axisLine:{lineStyle:{width:40}},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},title:{fontSize:16},detail:{valueAnimation:!0,fontSize:30,offsetCenter:[0,0],formatter:"{value}%"},data:[{value:h.value,name:"正常率"}]}]}),s},T=()=>{const s=d(m.value);return s.setOption({tooltip:{trigger:"item"},series:[{type:"pie",radius:"70%",data:w,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),s},B=()=>{const s=d(p.value);return s.setOption({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:_.value.map(e=>e.hour)},yAxis:{type:"value",min:0},series:[{name:"异常数量",type:"bar",data:_.value.map(e=>e.count),itemStyle:{color:new L(0,0,0,1,[{offset:0,color:"#3398DB"},{offset:1,color:"#30BAE7"}])}}]}),s},c=s=>{s.resize()};return k(()=>{const s=x(),e=T(),n=B();window.addEventListener("resize",()=>{c(s),c(e),c(n)})}),(s,e)=>(E(),A("div",G,[o(i(D),{title:"监控大屏"}),t("div",V,[t("div",q,[t("div",R,[e[0]||(e[0]=t("div",{class:"item-label"},"监控点数",-1)),t("div",H,l(f.value),1)]),t("div",I,[e[1]||(e[1]=t("div",{class:"item-label"},"异常数量",-1)),t("div",M,l(g.value),1)]),t("div",P,[e[2]||(e[2]=t("div",{class:"item-label"},"监控覆盖率",-1)),t("div",X,l(b.value)+"%",1)]),t("div",j,[e[3]||(e[3]=t("div",{class:"item-label"},"响应时效",-1)),t("div",F,l(y.value)+"分钟",1)])]),t("div",J,[t("div",K,[e[4]||(e[4]=t("div",{class:"chart-title"},"实时监控状态",-1)),t("div",Q,[t("div",{ref_key:"gaugeChart",ref:v,class:"chart-dom"},null,512)])]),t("div",U,[e[5]||(e[5]=t("div",{class:"chart-title"},"异常类型分布",-1)),t("div",W,[t("div",{ref_key:"pieChart",ref:m,class:"chart-dom"},null,512)])]),t("div",Y,[e[6]||(e[6]=t("div",{class:"chart-title"},"异常趋势",-1)),t("div",Z,[t("div",{ref_key:"barChart",ref:p,class:"chart-dom"},null,512)])])]),t("div",$,[e[7]||(e[7]=t("div",{class:"list-title"},"实时异常告警",-1)),o(i(O),{data:C.value,border:""},{default:u(()=>[o(i(r),{prop:"time",label:"发生时间"}),o(i(r),{prop:"resource",label:"关联资源"}),o(i(r),{prop:"type",label:"异常类型"}),o(i(r),{prop:"level",label:"级别"},{default:u(n=>[o(i(z),{type:n.row.level==="严重"?"danger":"warning"},{default:u(()=>[N(l(n.row.level),1)]),_:2},1032,["type"])]),_:1}),o(i(r),{prop:"status",label:"处理状态"})]),_:1},8,["data"])])])]))}},lt=S(tt,[["__scopeId","data-v-11db5efc"]]);export{lt as default};
