import{H as t}from"./HeaderNav-CwnasXYK.js";import{R as r,S as a,P as i,W as d,a as c,b as n,c as _}from"./report-info-CCkw5xY4.js";import l from"./map-BxVLwPVC.js";import{_ as p,c as m,a as u,b as o,d as e,m as f}from"./index-B3fBvuMC.js";import"./switch-DJpMdJKf.js";import"./index-DVYYCE_X.js";import"./index.vue_vue_type_script_setup_true_lang-CW6cgbhn.js";import"./index-BrbPvnuX.js";import"./index-EWKaWdrL.js";const h={class:"monitor-screen"},M={class:"header-box"},v={class:"content-container"},x={class:"left"},g={class:"center"},b={class:"right"},N={__name:"index",setup(k){return(B,s)=>(u(),m("div",h,[o("div",M,[e(t,null,{right:f(()=>s[0]||(s[0]=[])),_:1})]),o("div",v,[o("div",x,[e(r),e(a),e(i)]),o("div",g,[e(l),e(d)]),o("div",b,[e(c),e(n),e(_)])])]))}},D=p(N,[["__scopeId","data-v-64cecdcb"]]);export{D as default};
