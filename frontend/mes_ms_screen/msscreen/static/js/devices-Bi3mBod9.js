import{i as y}from"./index-BrbPvnuX.js";import{B as h}from"./index-C4TfGCsC.js";import{S as g}from"./index-lnVDfRHP.js";import{S as x,C as S}from"./index-VkXiS0_E.js";import{i as k,a as B,b as F,c as D,D as w}from"./icon_shuishebei-B-ne4_mM.js";import{_ as p,r as z,o as A,c as b,a as v,b as _,d as i,m,i as u,u as o}from"./index-B3fBvuMC.js";const $={class:"chart-box-container"},T={__name:"index",props:{list:{type:Array,default:[]},colors:{type:Array,default:[]}},setup(l){const t=l,s={color:t.colors,tooltip:{trigger:"item",formatter:({seriesIndex:a,dataIndex:r,name:c,marker:d})=>{if(a===0)return`${d} ${c} ${t.list[r].percent}%`}},series:[{top:"middle",left:0,width:"50%",height:"80%",type:"funnel",sort:"ascending",gap:0,z:1,minSize:10,maxSize:150,label:{show:!0,position:"inside",formatter:({dataIndex:a})=>`${t.list[a].percent}%`},data:t.list},{top:"middle",left:0,width:"10%",height:"80%",type:"funnel",gap:0,z:-1,minSize:200,maxSize:200,label:{show:!0,color:"#999999",position:"right",width:50},labelLine:{show:!0,length:200,lineStyle:{width:1,color:"#e8e9f1",type:"dashed"}},itemStyle:{color:"transparent",borderWidth:0,opacity:1},data:t.list}]},n=z(null);return A(()=>{setTimeout(()=>{y(n.value).setOption(s)},100)}),(a,r)=>(v(),b("div",$,[_("div",{class:"chart-box",ref_key:"chart",ref:n},null,512)]))}},I=p(T,[["__scopeId","data-v-4da8ac49"]]),L={class:"device-container module-box"},U={__name:"devices",emits:{"icon-click":l=>typeof l=="object"},setup(l,{emit:t}){const s=t,n=()=>{s("icon-click",{type:"deviceManager",name:"设备管理"})},a=[{name:"备机",val:100,customClass:"bei"},{name:"原机",val:256,customClass:"yuan"}],r={name:"在线设备",val:"356",iconUrl:k},c=["#53DCA1","#5C92FF","#9C65C1","#FBC939","#F37074"],d=[{value:10,name:"1~2年设备数据",percent:1},{value:80,name:"3~4年设备数据",percent:40},{value:60,name:"4~6年设备数据",percent:29},{value:40,name:"6~8年设备数据",percent:21},{value:20,name:"8~10年设备数据",percent:6}],f=[{id:1,name:"公务用车运行维护费",val:1230,labelColor:"#4FA0FF",labelBgStyle:"background-image: linear-gradient(90deg, rgba(79,160,255,0.30) 3%, rgba(79,160,255,0.00) 99%);"},{id:2,name:"办公费",val:800,labelColor:"#67C23A",labelBgStyle:"background-image: linear-gradient(90deg, rgba(103,194,58,0.30) 3%, rgba(103,194,58,0.00) 99%);"},{id:3,name:"差旅费",val:1500,labelColor:"#FBD500",labelBgStyle:"background-image: linear-gradient(90deg, rgba(251,213,0,0.30) 3%, rgba(251,213,0,0.00) 99%);"},{id:4,name:"差旅费222",val:100,labelColor:"#FF9A54",labelBgStyle:"background-image: linear-gradient(90deg, rgba(255,154,84,0.30) 3%, rgba(255,154,84,0.00) 97%);"},{id:5,name:"哈哈哈哈哈哈哈",val:100,labelColor:"#F56C6D",labelBgStyle:"background-image: linear-gradient(90deg, rgba(245,108,109,0.30) 3%, rgba(245,108,109,0.00) 99%);"}],C=[{id:1,name:"在用",val:80,iconUrl:B,customClass:"using"},{id:2,name:"库存",val:12,iconUrl:F,customClass:"store"},{id:3,name:"修护中",val:12,iconUrl:D,customClass:"maintain"}];return(N,e)=>(v(),b("div",L,[i(o(h),{onIconClick:n},{text:m(()=>[e[0]||(e[0]=u("设备管理"))]),_:1,__:[0]}),i(o(x),{list:a,total:r}),i(o(w),{list:C}),i(o(g),null,{text:m(()=>[e[1]||(e[1]=u("监测设备统计"))]),_:1,__:[1]}),e[3]||(e[3]=_("div",{class:"sectionHeight"},null,-1)),i(o(S),{list:f}),i(o(g),null,{text:m(()=>[e[2]||(e[2]=u("设备寿命情况"))]),_:1,__:[2]}),i(o(I),{list:d,colors:c})]))}},W=p(U,[["__scopeId","data-v-28f07b30"]]);export{W as default};
