import{i as ie,L as ae}from"./index-BrbPvnuX.js";import{e as oe,r as u,o as X,f as ue,w as g,c as r,a as c,_ as be,g as De,h as te,b as e,d,i as D,t as i,n as S,j as Be,k as Ae,F as H,l as W,m as he}from"./index-B3fBvuMC.js";import{D as we}from"./index-DVYYCE_X.js";import Ye from"./index-lzTZIjto.js";import{H as Ce}from"./HeaderNav-CwnasXYK.js";import ze from"./index-CI4xxFYj.js";import"./linesGL-Dv4775iD.js";/* empty css                                                              */import ne from"./index-B2TjNc6a.js";import Se from"./index-B9ABXc1u.js";import"./switch-DJpMdJKf.js";const w="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='12px'%20height='14px'%20viewBox='0%200%2012%2014'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3eicon_dian_12%3c/title%3e%3cdefs%3e%3clinearGradient%20x1='50%25'%20y1='1.16888727%25'%20x2='50%25'%20y2='99.1511418%25'%20id='linearGradient-1'%3e%3cstop%20stop-color='%23FFFFFF'%20stop-opacity='0.0250201459'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='%23FFFFFF'%20offset='97.1625908%25'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient%20x1='50%25'%20y1='2.2325209%25'%20x2='50%25'%20y2='100%25'%20id='linearGradient-2'%3e%3cstop%20stop-color='%23FFFFFF'%20stop-opacity='0'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='%23FFFFFF'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cg%20id='大屏'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='1-1大屏-监控预警-总览'%20transform='translate(-268.000000,%20-180.000000)'%3e%3cg%20id='报警数量统计'%20transform='translate(24.000000,%2080.000000)'%3e%3cg%20id='编组-3'%20transform='translate(16.000000,%2056.000000)'%3e%3cg%20id='编组-13'%20transform='translate(212.000000,%2037.000000)'%3e%3cg%20id='icon_dian_12'%20transform='translate(16.000000,%208.000000)'%3e%3cpolygon%20id='多边形'%20stroke='url(%23linearGradient-2)'%20stroke-width='0.5'%20fill='url(%23linearGradient-1)'%20opacity='0.5'%20points='6%200%2011.1961524%203%2011.1961524%209%206%2012%200.803847577%209%200.803847577%203'%3e%3c/polygon%3e%3cpolygon%20id='多边形'%20fill='%23FFFFFF'%20points='6%203%208.59807621%204.5%208.59807621%207.5%206%209%203.40192379%207.5%203.40192379%204.5'%3e%3c/polygon%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Ge="data:image/png;base64,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",Oe=oe({__name:"index",props:{chartData:{},options:{default:()=>({})},width:{default:"100%"},height:{default:"200px"}},setup(G){const a=G,o=u(null);let A=null;const B=()=>{o.value&&(A=ie(o.value),p())},p=()=>{if(!A)return;const v={tooltip:{trigger:"axis"},legend:{icon:"circle",itemWidth:8,itemHeight:8,itemGap:16,data:a.chartData.series.map(s=>s.name)},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:a.chartData.xAxis},yAxis:[{type:"value",splitLine:{show:!1}},{type:"value",splitLine:{show:!1},axisLabel:{formatter:s=>`${s}%`}}],series:a.chartData.series.map(s=>({name:s.name,type:s.type||"line",data:s.data,yAxisIndex:s.yAxisIndex||0,smooth:s.smooth,barWidth:s.barWidth,itemStyle:s.itemStyle}))};A.setOption({...v,...a.options})};return X(()=>{setTimeout(()=>{B(),window.addEventListener("resize",()=>A==null?void 0:A.resize())},0)}),ue(()=>{window.removeEventListener("resize",()=>A==null?void 0:A.resize()),A==null||A.dispose(),A=null}),g(()=>a.chartData,()=>p(),{deep:!0}),g(()=>a.options,()=>p(),{deep:!0}),(v,s)=>(c(),r("div",{ref_key:"chartRef",ref:o,style:{width:"100%",height:"100%"}},null,512))}}),Ee=oe({__name:"index",props:{data:{type:Object,default:()=>({xAxis:["1月","2月","3月"],series:[{name:"数量1",data:[10,22,18]},{name:"数量2",data:[15,30,25]}],avgLines:[]})}},setup(G){const a=u(null);let o=null;const A=G,B=()=>{a.value&&(o=ie(a.value),v())},p=s=>(s.reduce((x,j)=>x+j,0)/s.length).toFixed(2),v=()=>{if(!o)return;const s=A.data.series.map(m=>m.name);A.data.avgLines.forEach(m=>{const x=p(m.values);A.data.series.push({name:`${m.name}平均值`,type:"line",data:new Array(A.data.xAxis.length).fill(x),lineStyle:{type:"dashed",width:1,color:"#EBEEF5"},symbol:"none",silent:!0,tooltip:{formatter:`{b}: ${x}`}})}),o.setOption({xAxis:{type:"category",data:A.data.xAxis,boundaryGap:!1},yAxis:{type:"value",splitLine:{show:!1}},tooltip:{trigger:"axis"},legend:{icon:"circle",itemWidth:8,itemHeight:8,itemGap:16,data:s,top:0,right:0},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},series:A.data.series.map((m,x)=>({...m,type:"line",smooth:!0}))})};return X(()=>{setTimeout(()=>{B(),window.addEventListener("resize",()=>o==null?void 0:o.resize())},0)}),ue(()=>{window.removeEventListener("resize",()=>o==null?void 0:o.resize()),o==null||o.dispose(),o=null}),g(()=>A.data,()=>v(),{deep:!0}),(s,m)=>(c(),r("div",{ref_key:"chartEl",ref:a,style:{width:"100%",height:"100%"}},null,512))}}),Fe="data:image/png;base64,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",He="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAJ9klEQVRYCXVYXWwcVxU+M7P+98aOs01i8+PEDT9Kk1TgiKbqj9RGUSuiIlU8IJUS8cITPPCAKqQiVTwg4IEHpJYXeIGAUEFBAhVKkBCBQIKoSJtUDaFJG7t2qGPHSRz/bdY7M3zfOfeMx+vtXc/ec8/f/e45594760jQ8jzvRrcLzwCeBE+OJyv1pH1MWRpkrleWu165b5WXZe180H8WRVFeCeDGwWgFBpY2OmCL8JDmU6Yx3NRcz3squE2rcjsd6kbAJjGIe/F0BKuycmCpY6fb9bTx5rROAKb3lLuMNPnl5uNN8xPgYFkTtCs72x17T77TZQBl2uXsnS7blXnkl9uG+QmwE0/ZwGlXbNc7z3U5QZl2OfneyCvzy/pOU16mpRKsKWDhsil9aeVc7dTCb75Wz1fGszyroWDFHq4JSlEMxY28mDzqsXK0L+ut65tMbWdiSU7v7Bx+/kjXM9NwS3AFSG4SFuJjQeA7Lbu6crF2av63P5uX9wfyKFUgtGsFSEDK53cBjnqBD5AO2vTMB+mCbwu6Odw18sATXc9OtWDJLRzrYYVc5Oz8n76KTT4wlO2UHBu+mTUlA5FmqWRKZ9qnSjclhYzyDHLyjG+6zWxNminsyc/Nj/kKemYzNHP3f9/RyVu+yin23MtKc+nTjFYFn1o0IrPZlKRRE6aR5BqdVCMgUabRZcRyHI0WRaw5z9YjlDNqOc5ai3IOG48gnEEGH5hrLc0eUcF6sDTVDrAAR6U0a9RQG5LGABl1yD3yEbmeTojEGaJgDnOULGsNU+Bjk0YqszpE3QIkARCwgecy+EcLLlQXFIBjCwxjbseh+wBjPQfZk+EtZyqxZqzaUtaBjb4jHpUsBUCk6rHBz8vengckTddCSk1P04w0UsdtD/U9IYf6nwx66+nXUijsjQ8AjoNAlW4bQasnrBARZBriJJOuqFtGkjGZbl6RarJVVpMl1B0ip5GyTcH0aUSZ8pC6amLHLOvTeRZdiz5xaGQtPB5BjpR2gCYO33mGTRA3UWIACJB0/uG+Mfl49VPyxsJpmDK6uRzsPyx7+vYHK0LzBgp/ZxdehZ6l//7eh2SsZ5/MNCbl7OIfDSyXg8UgL1YKbm493eUOUAdBzv0oMTa4FjQm4Oeh2lHpTnrl6vJF6Y2rOvH2zg/Jnt4DG92WRucX/64LIWtbZYeMde/VZ7p+RSYb/7WNBv96fFnAiIMrYsvBLwAqQ9lYux4XTC/fKZA9pprg2LZUhmRmdUpW1hYRvfuV90FfWyv3yKXlfzNOMr7l8UKtO+7VrGShJOw85WSK0oPFMzryCHpRat5Z4HmG0AMY0OJIVGOdYP/gg3J16S0Z7fuYjPSMFZO2Ix4efEreWDwt/cmA7Or5ZKHCtHOTaORYryGKhYIRRQSJeENoPYKsQYLLAJaNm+c6otebVNVkZnVSGvldWU0X4YEu2EIt6jCSgco25VxePi87uz4q1cpW3VwEqJErNpbbmxc68ggyah5BSiMCUXCx7TTdrRD8c+6kvL14XpIokUrcYU/SIbN3p+Td+ps0DZMSZPjoeQcaC+hDJL+x+0VLL7OEcovCZRZjLnVgWEhrNj3FhO9AcwKMAcKOAzsPabHQuKnOk4RpgXvs7o6kT0aRPtboxOp/oBVJI6vj5llTmoc4rnylF7NbcjdbVWCcgzPqMphmnLFojoG0NgfoY/YoO9u5gutNHXDDoHHFfAg8wg7iqj87ckzurN3SCBFYV9wjp+d/J6fmT9CV8mPdabg9YMtG/zzk9ZaBThwTV5FiD5bqOkBqeNMI8mKP8KLNKGX6a8Aca31iwjhGhCHji8KZud9ruqfr72DzfEKL3jYBSgSe9f7WexcgGXks0CJoR0yeciFFiv2YUaAOkINCQCe60wgyQZKYDjTjc0ZLe8ZbBqXwmW1HkLq6jFX3yVDnDvnH/CsKnueU3rd6qxAs4g5fuOvxmE/KeWfzRuIUeIoItp6DLsg1etDXwiUYPRBpj2jCkR8Rcc46bcrxie/L5eVz6vvJ4WOaStdhYq0GLVo/mXxBZut4O5JwzDCammY7KThLaIjHxnPQBbr6HCnkKlmDWuOQZgqWK02wYNQjlsS3nS+Mfr2wrVYG5c/XX7aoIx68aXsr/fLt+34pv5r6oby+8Fety6I+NbWYI2PwNrdyigsNppdppXMWdJyYiLHUugOwGDu5ni3LlaUL8qO3n4Nn2xBHhp/RUliPYKaL6En69Zj53PBXpJ6uyMnZnyOh3OP2WgbUROdZLGiPawGOSgTHCdjzPiatDRG0As/lRuOaVDu3yJnZV+R2Yw5H0Jwsr93RdGoZ4C26Px6QB4eO6iJpz118L14u9vQfUJ+8sVgi+kZuNek4CqAOUOEbCgbA9BQoJuKmYOOYDm83r8ubt8/glqjh8H5VS4GL+OLu5+S+gUNyaeE1QEnlwOAj8qXd35StHduDPY8p+/AnQFPfBy0YIQiOgxMq7Sl25HTEkwFurLFnGtg6ccYtrM3LpcV/aWSPv/tdzYyfZz++/C0m2nYuepYKm2eA5UHHDIDWN9Lqb+GquD4thwrBAQa5ddx5GkWsgTRbI63Lw8NPyWp+R/YM7lcg5PMKo5YulxMahZ06LRdunZYTky/KxNJFOfHeSzpmOTRlTTPBGtRjCJmxRdJj0eiyeN3SQRABEQ3DVHaAyh/e+6kc3H5YHt35dOGhPaFQ5drKFVluLsj4tsP6UPfg0ONckczVr8nr83/BaxzPRh4ynE8DQWMLO8C1noMWKqILUSODaarjEH554gfyi4nvcR40SgwIFHRS8mwiK2uNDDjnb/5No+MRsnc/mgAc7l8NBIOAKJYc6wTIYvv3wUrceQPKNVrwcr+6ekHTwrG1AA4DTkBvDljrTKNvi1DQjBJ5AMIFK42eNnYZMIrx++pm/UsjyOVyNj42D/reSvUcjwpe/hMrAJfjzSTsZNorvDBWozINIGwEwifTH+3Yqej14fGFzVL8M4B88PA+gh87G5q+DxIg57DlBrBHR4+9hNfxhcn6RZQz3jpoFyYmyYOiPC7TBk8N+BX0wtHCY0rBYZOgt/9S8OhKb44O7Hue2mpjX7p2K5h1gYI9UHt07uiuLz/b3zlwEi+mN1TTI6g9Exea83WNttJytKlVtrfI8pQgsGwG9/Sv99bGD72w9/i1oKrqwTs20eZ/HlGBBcKHNKvXed67rHXsdi73cTu9drxNdoxgIwBAp42GbB6kdr3zXJf6Zdrl5Hsjr8wv6ztNeZnmq67cDoZlAWl/QGpzOQdO+4TeU1aexMfsW/212rhOwcdu118s70Bi/7qiijlyJe/Jb0c7UPYuL9O0cx2Xk1duH8RH9eFdHCjr0H4Nzxwe/tJp1+jEJ/LeHfuYvdPtfFBW9uM67eyUxwj+H8QehBDm3A+hAAAAAElFTkSuQmCC",le="data:image/png;base64,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",se="data:image/png;base64,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",P="data:image/png;base64,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",q="data:image/png;base64,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*********************************+l2tuK0cTcedFB+cEXx4H630cTgxnhQ7EldJWLphspYNwDXM5SBNHDWH9xt9syZ9WDy+5yfucRHB5vQnJm9JGoxydchDG6MB0dGwiwyEq4otBlPOjPlWgQ0Ax68elP22GGzp09He/rM5ptYWon2ld+muSQdsbjRovXBluPBtCJ48koSsZ/F+Y1yMSa05BLvCxf69u/l0nah6BvT/l3BHv+o2aPPRPvzfLTPPoBF7zC7wmny3L/MvvOnYFduSqzJoocRDKojEtTehSHZyMBOfOXRUTcWDwqQgKmHHo3uA/D4P+v2FK6jVIw/SHnBnGNL9vNTZk89r2E6OTKolAuYxCFQGaTG+ifo7caDvh0mEQw9muz51+q2uOJwiWIG6DZkGaSa02mRwak+Apb7ZeYcHHMy7+3FgwDkcBqwxDH3wmslfkwn8lsnEa9HyYUApo1gcj3n4yy27rDFgXh9iuFJohBrqIM6SYpwJllssNMLpV29DXCOauxPAN3ZYwzZEW+WZ4PpV+GV333GLuWTJMeDiRZZD+qCmqFsFq43Gz9j64svXS5R7P/N3BiuTYu3A7bdsK/OQ/jGeDBdfQBKM4tFPVXLli7WWl9c7JZ/YQwc/P/SRrB4h1e2NMKx3z9iv8EVSkEGSpLW3DQe5OMpTmyzC+/bao8Shtdfvx50QRZLHr78+Abg1OGY4WtQN7EegOseUWU9OhX1XmXOYzcpru2qqkFf+vW4/VJ+7w67+vWHbUHGYXz53QemN8WD3CF3mMKPvBwPtuno94MswoXk2tZpm9d9NYAq3VFzJVdxPFZ1njZXxNwhVlsmaeMdIVu1jbvnOotyZ1hxBVxNtNLdNsdole+oW1NcNao/8tH9YP8CcSNuZt9c2hAQ3OyLqmNLftSlyT2i5n5QXyuxAJTuCZXEio4axWxctEddAftdIu0TXAHrElNJbcQORvBiN/lTXXfUnEYx37CqH6ejq5XKha6ASXNzrJGAqYoZEA826/YyR2N3PB4siRYURNTQTXl3pTxhvmFV7CbR+OZ4r5yL0Njj0R21xrS5hXDxIRLdsN6hxkFaWqLAHfUi4xbUNss5TZatmCKOBE2Z/3xYbeywvxK6YLMAVWf/S2ezqgLlDMKmmMgMijkXE310BZyvfzVmY9K4Fa6Ac/+tuhnnjprvqehXwHlAcoRuLGLwv2wYcByKQ6eHAAAAAElFTkSuQmCC",Y="data:image/png;base64,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",k="data:image/png;base64,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*********************************+l2tuK0cTcedFB+cEXx4H630cTgxnhQ7EldJWLphspYNwDXM5SBNHDWH9xt9syZ9WDy+5yfucRHB5vQnJm9JGoxydchDG6MB0dGwiwyEq4otBlPOjPlWgQ0Ax68elP22GGzp09He/rM5ptYWon2ld+muSQdsbjRovXBluPBtCJ48koSsZ/F+Y1yMSa05BLvCxf69u/l0nah6BvT/l3BHv+o2aPPRPvzfLTPPoBF7zC7wmny3L/MvvOnYFduSqzJoocRDKojEtTehSHZyMBOfOXRUTcWDwqQgKmHHo3uA/D4P+v2FK6jVIw/SHnBnGNL9vNTZk89r2E6OTKolAuYxCFQGaTG+ifo7caDvh0mEQw9muz51+q2uOJwiWIG6DZkGaSa02mRwak+Apb7ZeYcHHMy7+3FgwDkcBqwxDH3wmslfkwn8lsnEa9HyYUApo1gcj3n4yy27rDFgXh9iuFJohBrqIM6SYpwJllssNMLpV29DXCOauxPAN3ZYwzZEW+WZ4PpV+GV333GLuWTJMeDiRZZD+qCmqFsFq43Gz9j64svXS5R7P/N3BiuTYu3A7bdsK/OQ/jGeDBdfQBKM4tFPVXLli7WWl9c7JZ/YQwc/P/SRrB4h1e2NMKx3z9iv8EVSkEGSpLW3DQe5OMpTmyzC+/bao8Shtdfvx50QRZLHr78+Abg1OGY4WtQN7EegOseUWU9OhX1XmXOYzcpru2qqkFf+vW4/VJ+7w67+vWHbUHGYXz53QemN8WD3CF3mMKPvBwPtuno94MswoXk2tZpm9d9NYAq3VFzJVdxPFZ1njZXxNwhVlsmaeMdIVu1jbvnOotyZ1hxBVxNtNLdNsdole+oW1NcNao/8tH9YP8CcSNuZt9c2hAQ3OyLqmNLftSlyT2i5n5QXyuxAJTuCZXEio4axWxctEddAftdIu0TXAHrElNJbcQORvBiN/lTXXfUnEYx37CqH6ejq5XKha6ASXNzrJGAqYoZEA826/YyR2N3PB4siRYURNTQTXl3pTxhvmFV7CbR+OZ4r5yL0Njj0R21xrS5hXDxIRLdsN6hxkFaWqLAHfUi4xbUNss5TZatmCKOBE2Z/3xYbeywvxK6YLMAVWf/S2ezqgLlDMKmmMgMijkXE310BZyvfzVmY9K4Fa6Ac/+tuhnnjprvqehXwHlAcoRuLGLwv2wYcByKQ6eHAAAAAElFTkSuQmCC",je={class:"schedu-screen"},Ze={class:"screen-head"},Qe={class:"screen-content"},Ke={class:"screen-content_l"},Ne={class:"item-common l1"},Re={class:"item-content"},Te={class:"content-y"},Le=["src"],Me={class:"content-y_target"},Ue={class:"content-y_category"},We={class:"content-y_category_item"},Pe={class:"content-y_category_item"},qe={class:"content-m"},ke=["src"],Xe={class:"content-m_target"},Ve={class:"content-m_category"},Je={class:"content-m_category_item"},Ie={class:"content-m_category_item"},_e={class:"item-common l2"},$e={class:"date-tab"},ea={class:"item-content"},aa={class:"item-common l3"},ta={class:"date-tab"},Aa={class:"item-content"},na={class:"screen-content_c"},la={class:"item-common c1"},sa={class:"item-common c2"},ia={class:"tab-btns"},oa={class:"item-content"},ua={class:"item-efficiency"},ma=["src"],ca={class:"item-efficiency_item_text"},da={class:"item-efficiency_item_text_top"},ra={class:"item-efficiency_item_text_btm"},va={class:"item-efficiency_item_text_btm_unit"},ga={class:"screen-content_r"},pa={class:"item-common r1"},xa={class:"item-content"},fa={class:"content-a"},ya={class:"content-a_target"},ba={class:"content-a_category"},Da={class:"content-a_category_item"},Ba={class:"content-a_category_item"},ha={class:"item-common r2"},wa={class:"item-category"},Ya=["onClick"],Ca=["src"],za={class:"item-category_item_text"},Sa={class:"item-category_item_text_top"},Ga={class:"item-category_item_text_btm"},Oa={class:"item-content"},Ea={class:"item-common r3"},Fa={class:"date-picker"},Ha={class:"item-content"},ja={class:"card-box-swap"},Za={class:"box-container"},Qa={class:"date"},Ka={class:"category-container"},Na=["aria-label"],Ra={class:"category-label"},Ta={__name:"index",setup(G){const a=u("all"),o=n=>{a.value=n.key},A={all:{1:{pic:Fe,label:"年度(进度/目标)",num:"210/500"},2:{label:"地表水",num:"150/300",num1:"15/40"},3:{label:"环境空气",num:"60/200",num1:"5/10"},4:{label:"月度(进度/目标)",num:"20/50",pic:He}},shui:{1:{label:"水上月(进度/目标)",num:"210/300",pic:se},2:{label:"周质控",num:"100/100",num1:"20/120"},3:{label:"月质控",num:"110/200",num1:"210/170"},4:{label:"水本月(进度/目标)",num:"210/400",pic:le}},kq:{1:{label:"气上月(进度/目标)",num:"210/500",pic:se},2:{label:"周质控",num:"110/100",num1:"30/130"},3:{label:"月质控",num:"130/400",num1:"180/370"},4:{label:"气本月(进度/目标)",num:"210/500",pic:le}}},B={type:"line",yAxisIndex:1,lineStyle:{color:"#0EBEF5"},symbol:"circle",symbolSize:8},p={type:"bar",barWidth:24,itemStyle:{color:{type:"linear",y:1,x2:0,colorStops:[{offset:0,color:"#0065D5"},{offset:1,color:"#009EEC"}],global:!1}}},v={all:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"杨洼闸（王家摆）"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"桑梓红旗闸"},{id:"重庆市",time:"2025/07/19",name:"无人机采集任务",score:"后西吴桥"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"海淀万柳"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"顺义新城"},{id:"山东省",time:"2025/07/19",name:"无人机采集任务",score:"奥体中心"}],shui:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"杨洼闸（王家摆）"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"桑梓红旗闸"},{id:"重庆市",time:"2025/07/19",name:"无人机采集任务",score:"后西吴桥"}],kq:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"海淀万柳"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"顺义新城"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"奥体中心"}]},s={all:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"膳马庙村北"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"尔王庄泵站"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"密云新城"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"门头沟三家店"},{id:"山东省",time:"2025/07/19",name:"无人机采集任务",score:"丰台云岗"}],shui:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"膳马庙村北"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"尔王庄泵站"}],kq:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"密云新城"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"门头沟三家店"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"丰台云岗"}]},m={all:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"杨洼闸（王家摆"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"桑梓红旗闸"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"后西吴桥"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"膳马庙村北"},{id:"山东省",time:"2025/07/19",name:"无人机采集任务",score:"尔王庄泵站"},{id:"江苏省",time:"2025/07/19",name:"无人机采集任务",score:"黄白桥"},{id:"安徽省",time:"2025/07/19",name:"无人机采集任务",score:"塘汉公路桥"},{id:"辽宁省",time:"2025/07/19",name:"无人机采集任务",score:"海河大闸"},{id:"浙江省",time:"2025/07/19",name:"无人机采集任务",score:"海淀万柳"},{id:"湖南省",time:"2025/07/19",name:"无人机采集任务",score:"顺义新城"},{id:"重庆市",time:"2025/07/19",name:"无人机采集任务",score:"奥体中心"},{id:"江西省",time:"2025/07/19",name:"无人机采集任务",score:"密云新城"}],shui:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"杨洼闸（王家摆"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"桑梓红旗闸"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"后西吴桥"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"膳马庙村北"},{id:"山东省",time:"2025/07/19",name:"无人机采集任务",score:"尔王庄泵站"},{id:"江苏省",time:"2025/07/19",name:"无人机采集任务",score:"黄白桥"},{id:"安徽省",time:"2025/07/19",name:"无人机采集任务",score:"塘汉公路桥"},{id:"辽宁省",time:"2025/07/19",name:"无人机采集任务",score:"海河大闸"}],kq:[{id:"北京市",time:"2025/07/19",name:"无人机采集任务",score:"海淀万柳"},{id:"上海市",time:"2025/07/19",name:"无人机采集任务",score:"顺义新城"},{id:"河北省",time:"2025/07/19",name:"无人机采集任务",score:"奥体中心"},{id:"河南省",time:"2025/07/19",name:"无人机采集任务",score:"密云新城"}]},x=[{prop:"id",title:"省份",width:"19%"},{prop:"name",title:"任务名称",width:"32%"},{prop:"score",title:"点位",width:"25%"},{prop:"time",title:"执行时间",width:"22%"}],j={all:[{name:"未执行",type:"un",value:"6",icon:P},{name:"执行中",type:"in",value:"5",icon:q},{name:"已完成",type:"fi",value:"12",icon:Y}],shui:[{name:"未执行",type:"un",value:"3",icon:P},{name:"执行中",type:"in",value:"2",icon:q},{name:"已完成",type:"fi",value:"8",icon:Y}],kq:[{name:"未执行",type:"un",value:"3",icon:P},{name:"执行中",type:"in",value:"3",icon:q},{name:"已完成",type:"fi",value:"4",icon:Y}]},Z={all:{all:120,kong:70,zt:50},shui:{all:80,kong:50,zt:30},kq:{all:40,kong:20,zt:20}},O={all:{task:[3.2,4,2.3,3,2.4,3.1],day:[2,3,2,2,3,2],taskAvg:[3.2,4,2.3,3,2.4,3.1],dayAvg:[2,3,2,2,3,2]},shui:{task:[2,2,1,2,2.1,2],day:[1,2,1.3,1,1.4,1.9],taskAvg:[2,2,1,2,2.1,2],dayAvg:[1,2,1.3,1,1.4,1.9]},kq:{task:[1.2,2,1.3,1,.4,1.1],day:[1,1,.7,1,1.6,.1],taskAvg:[1.2,2,1.3,1,.4,1.1],dayAvg:[1,1,.7,1,1.6,.1]}},Q={all:{bar:[120,200,150,120,200,150],line1:[2,5,3,2,5,3],line2:[4,5,3,4,5,3]},shui:{bar:[80,120,100,60,100,100],line1:[1,2,2,2,2,1],line2:[2,2.5,1,2,2,2]},kq:{bar:[40,80,50,60,100,50],line1:[1,3,1,0,3,2],line2:[2,2.5,2,2,3,1]}},h=u("in"),f=u(s[a.value]),me=n=>{h.value=n,n==="un"&&(f.value=v[a.value]),n==="in"&&(f.value=s[a.value]),n==="fi"&&(f.value=m[a.value])};g(()=>h,()=>dataChange());const ce={all:[{name:"平均单次任务活动数",value:"4",icon:Y,unit:"个"},{name:"单人平均每天活动数",value:"3",icon:k,unit:"次"}],shui:[{name:"平均单次任务活动数",value:"2",icon:Y,unit:"个"},{name:"单人平均每天活动数",value:"1",icon:k,unit:"次"}],kq:[{name:"平均单次任务活动数",value:"2",icon:Y,unit:"个"},{name:"单人平均每天活动数",value:"2",icon:k,unit:"次"}]},de=[{label:"行政区域",value:"total"},{label:"运维公司",value:"yz"}],V={all:{totalData:[{name:"北京市",value:300},{name:"上海市",value:200},{name:"重庆市",value:30},{name:"河北省",value:190},{name:"河南省",value:120},{name:"山东省",value:170},{name:"江苏省",value:120},{name:"安徽省",value:110},{name:"辽宁省",value:90},{name:"浙江省",value:80}],yzData:[{name:"武汉天虹环保产业发展有限公司",value:50},{name:"杭州绿洁科技股份有限公司",value:30},{name:"厦门隆力德环境技术开发有限公司",value:52},{name:"宇星科技发展(深圳)有限公司",value:20},{name:"青岛吉美来科技有限公司",value:20},{name:"聚光科技(杭州)股份有限公司",value:50},{name:"安徽蓝盾光电子股份有限公司",value:30},{name:"河南省鑫属实业有限公司",value:52}]},shui:{totalData:[{name:"北京市",value:150},{name:"上海市",value:80},{name:"重庆市",value:20},{name:"河北省",value:90},{name:"河南省",value:80},{name:"山东省",value:100},{name:"江苏省",value:100},{name:"安徽省",value:60},{name:"辽宁省",value:50},{name:"浙江省",value:30}],yzData:[{name:"武汉天虹环保产业发展有限公司",value:30},{name:"杭州绿洁科技股份有限公司",value:10},{name:"厦门隆力德环境技术开发有限公司",value:22},{name:"宇星科技发展(深圳)有限公司",value:10},{name:"青岛吉美来科技有限公司",value:10},{name:"聚光科技(杭州)股份有限公司",value:25},{name:"安徽蓝盾光电子股份有限公司",value:10},{name:"河南省鑫属实业有限公司",value:22}]},kq:{totalData:[{name:"北京市",value:150},{name:"上海市",value:20},{name:"重庆市",value:10},{name:"河北省",value:100},{name:"河南省",value:40},{name:"山东省",value:70},{name:"江苏省",value:20},{name:"安徽省",value:50},{name:"辽宁省",value:40},{name:"浙江省",value:50}],yzData:[{name:"武汉天虹环保产业发展有限公司",value:20},{name:"杭州绿洁科技股份有限公司",value:20},{name:"厦门隆力德环境技术开发有限公司",value:30},{name:"宇星科技发展(深圳)有限公司",value:10},{name:"青岛吉美来科技有限公司",value:10},{name:"聚光科技(杭州)股份有限公司",value:25},{name:"安徽蓝盾光电子股份有限公司",value:20},{name:"河南省鑫属实业有限公司",value:30}]}},K={all:{totalData:[{name:"北京市",value:37},{name:"上海市",value:32},{name:"重庆市",value:31},{name:"河北省",value:29},{name:"河南省",value:28},{name:"山东省",value:37},{name:"江苏省",value:32},{name:"安徽省",value:31},{name:"辽宁省",value:29},{name:"浙江省",value:28}],yzData:[{name:"聚光科技(杭州)股份有限公司",value:13},{name:"安徽蓝盾光电子股份有限公司",value:5},{name:"河南省鑫属实业有限公司",value:9},{name:"厦门隆力德环境技术开发有限公司",value:5},{name:"武汉天虹环保产业发展有限公司",value:6},{name:"杭州绿洁科技股份有限公司",value:5},{name:"宇星科技发展(深圳)有限公司",value:3},{name:"青岛吉美来科技有限公司",value:5}]},shui:{totalData:[{name:"北京市",value:17},{name:"上海市",value:12},{name:"重庆市",value:20},{name:"河北省",value:10},{name:"河南省",value:10},{name:"山东省",value:17},{name:"江苏省",value:10},{name:"安徽省",value:11},{name:"辽宁省",value:19},{name:"浙江省",value:18}],yzData:[{name:"聚光科技(杭州)股份有限公司",value:8},{name:"安徽蓝盾光电子股份有限公司",value:3},{name:"河南省鑫属实业有限公司",value:7},{name:"厦门隆力德环境技术开发有限公司",value:4},{name:"武汉天虹环保产业发展有限公司",value:5},{name:"杭州绿洁科技股份有限公司",value:2},{name:"宇星科技发展(深圳)有限公司",value:2},{name:"青岛吉美来科技有限公司",value:3}]},kq:{totalData:[{name:"北京市",value:20},{name:"上海市",value:20},{name:"重庆市",value:11},{name:"河北省",value:19},{name:"河南省",value:18},{name:"山东省",value:20},{name:"江苏省",value:22},{name:"安徽省",value:11},{name:"辽宁省",value:10},{name:"浙江省",value:10}],yzData:[{name:"聚光科技(杭州)股份有限公司",value:5},{name:"安徽蓝盾光电子股份有限公司",value:2},{name:"河南省鑫属实业有限公司",value:2},{name:"厦门隆力德环境技术开发有限公司",value:1},{name:"武汉天虹环保产业发展有限公司",value:1},{name:"杭州绿洁科技股份有限公司",value:3},{name:"宇星科技发展(深圳)有限公司",value:1},{name:"青岛吉美来科技有限公司",value:2}]}},J={all:[{id:1,value:200,name:"规则创建"},{id:2,value:150,name:"数据审核"},{id:3,value:100,name:"监控预警"},{id:4,value:150,name:"资源管理"},{id:5,value:150,name:"质量管理"},{id:6,value:150,name:"手工创建"}],shui:[{id:1,value:130,name:"规则创建"},{id:2,value:100,name:"数据审核"},{id:3,value:60,name:"监控预警"},{id:4,value:100,name:"资源管理"},{id:5,value:100,name:"质量管理"},{id:6,value:100,name:"手工创建"}],kq:[{id:1,value:70,name:"规则创建"},{id:2,value:50,name:"数据审核"},{id:3,value:40,name:"监控预警"},{id:4,value:50,name:"资源管理"},{id:5,value:50,name:"质量管理"},{id:6,value:50,name:"手工创建"}]},N={all:[{id:1,value:40,name:"规则创建"},{id:2,value:30,name:"数据审核"},{id:3,value:50,name:"监控预警"},{id:4,value:40,name:"资源管理"},{id:5,value:25,name:"质量管理"},{id:6,value:20,name:"手工创建"}],shui:[{id:1,value:20,name:"规则创建"},{id:2,value:15,name:"数据审核"},{id:3,value:30,name:"监控预警"},{id:4,value:25,name:"资源管理"},{id:5,value:15,name:"质量管理"},{id:6,value:15,name:"手工创建"}],kq:[{id:1,value:20,name:"规则创建"},{id:2,value:15,name:"数据审核"},{id:3,value:20,name:"监控预警"},{id:4,value:15,name:"资源管理"},{id:5,value:10,name:"质量管理"},{id:6,value:5,name:"手工创建"}]},R=u("d"),T=u(N[a.value]),re=n=>{R.value=n,T.value=n==="d"?N[a.value]:J[a.value]},ve=u([{label:"0",colorClass:"bg-gray"},{label:"1-10",colorClass:"bg-beige"},{label:"11-100",colorClass:"bg-light-orange"},{label:"100+",colorClass:"bg-orange"}]),ge=n=>n===0?"bg-gray":n>0&&n<=10?"bg-beige":n>10&&n<=100?"bg-light-orange":n>100?"bg-orange":"";u([]);const I={},_=u({}),y=u(new Date),pe=new Date().getDate(),$=De(()=>{const n=y.value.getFullYear(),t=y.value.getMonth()+1,U=new Date(n,t,0).getDate(),E=new Date().getMonth()+1;return Array.from({length:U},(l,b)=>{const z=b+1,F=n>new Date().getFullYear()||n===new Date().getFullYear()&&t>E||n===new Date().getFullYear()&&t===E&&z>pe;return{date:`${n}-${String(t).padStart(2,"0")}-${String(z).padStart(2,"0")}`,totalCount:F?null:Math.floor(Math.random()*100)+1}})}),xe=()=>{$.value.map(n=>{I[n==null?void 0:n.date]=n}),_.value=I};g($,()=>xe(),{deep:!0}),g(()=>R,()=>dataChange());const L=u("d"),M=u(K[a.value]),fe=n=>{L.value=n,M.value=n==="d"?K[a.value]:V[a.value]};g(()=>L,()=>dataChange());const ye=()=>{console.log("currentType====>",a.value),T.value=R.value==="d"?N[a.value]:J[a.value],M.value=L.value==="d"?K[a.value]:V[a.value],h.value==="un"&&(f.value=v[a.value]),h.value==="in"&&(f.value=s[a.value]),h.value==="fi"&&(f.value=m[a.value])};g(a,()=>ye(),{deep:!0});const C=u("disp"),ee=n=>{C.value=n};return X(()=>{y.value=new Date}),(n,t)=>{const U=te("el-date-picker"),E=te("el-calendar");return c(),r("div",je,[e("div",Ze,[d(Ce,{onChangeType:o})]),e("div",Qe,[e("div",Ke,[e("div",Ne,[t[8]||(t[8]=e("div",{class:"item-title"},[e("span"),D("数量目标统计")],-1)),e("div",Re,[e("div",Te,[e("img",{class:"content-y_img",src:A[a.value][1].pic,alt:""},null,8,Le),e("div",Me,[e("div",null,i(A[a.value][1].label),1),e("div",null,i(A[a.value][1].num),1)]),e("div",Ue,[e("div",We,[t[4]||(t[4]=e("img",{src:w,alt:""},null,-1)),e("div",null,i(A[a.value][2].label),1),e("div",null,i(A[a.value][2].num),1)]),e("div",Pe,[t[5]||(t[5]=e("img",{src:w,alt:""},null,-1)),e("div",null,i(A[a.value][3].label),1),e("div",null,i(A[a.value][3].num),1)])])]),e("div",qe,[e("img",{class:"content-m_img",src:A[a.value][4].pic,alt:""},null,8,ke),e("div",Xe,[e("div",null,i(A[a.value][4].label),1),e("div",null,i(A[a.value][4].num),1)]),e("div",Ve,[e("div",Je,[t[6]||(t[6]=e("img",{src:w,alt:""},null,-1)),e("div",null,i(A[a.value][2].label),1),e("div",null,i(A[a.value][2].num1),1)]),e("div",Ie,[t[7]||(t[7]=e("img",{src:w,alt:""},null,-1)),e("div",null,i(A[a.value][3].label),1),e("div",null,i(A[a.value][3].num1),1)])])])])]),e("div",_e,[e("div",$e,[d(ne,{onChangeDate:re})]),t[9]||(t[9]=e("div",{class:"item-title"},[e("span"),D("调度计划来源统计")],-1)),e("div",ea,[d(Se,{data:T.value},null,8,["data"])])]),e("div",aa,[e("div",ta,[d(ne,{onChangeDate:fe})]),t[10]||(t[10]=e("div",{class:"item-title"},[e("span"),D("调度任务分布TOP10")],-1)),e("div",Aa,[d(Ye,{showTitle:!1,tabs:de,propsData:M.value},null,8,["propsData"])])])]),e("div",na,[e("div",la,[d(ze,{currentType:a.value},null,8,["currentType"])]),e("div",sa,[e("div",ia,[e("div",{class:S(C.value==="disp"?"btn active":"btn"),onClick:t[0]||(t[0]=l=>ee("disp"))}," 调度效率统计 ",2),e("div",{class:S(C.value==="act"?"btn active":"btn"),onClick:t[1]||(t[1]=l=>ee("act"))}," 活动效率统计 ",2)]),e("div",oa,[C.value=="act"?(c(),Be(Oe,{key:0,"chart-data":{xAxis:["1月","2月","3月","4月","5月","6月"],series:[{...p,name:"工单平均用时",data:Q[a.value].bar},{...B,name:"工单首次响应时长",data:Q[a.value].line1},{...B,name:"工单按时完成率",data:Q[a.value].line2}]}},null,8,["chart-data"])):Ae("",!0),C.value==="disp"?(c(),r(H,{key:1},[e("div",ua,[(c(!0),r(H,null,W(ce[a.value],l=>(c(),r("div",{class:"item-efficiency_item",key:l},[e("img",{src:l.icon,alt:""},null,8,ma),e("div",ca,[e("div",da,i(l.name),1),e("div",ra,[D(i(l.value)+" ",1),e("span",va,i(l.unit),1)])])]))),128))]),d(Ee,{data:{xAxis:["1月","2月","3月","4月","5月","6月"],series:[{name:"平均单次任务活动数",data:O[a.value].task,lineStyle:{color:"#4DCB73"},symbol:"none",areaStyle:{color:new ae(0,0,0,1,[{offset:0,color:"rgba(77,203,115,0.8)"},{offset:1,color:"rgba(77,203,115,0.1)"}])}},{name:"单人平均每天活动数",data:O[a.value].day,lineStyle:{color:"#0065D5"},symbol:"none",areaStyle:{color:new ae(0,0,0,1,[{offset:0,color:"rgba(0,101,213,0.8)"},{offset:1,color:"rgba(0,101,213,0.1)"}])}}],avgLines:[{name:"单次任务活动数累计平均值",values:O[a.value].taskAvg},{name:"单人每天活动数累计平均值",values:O[a.value].dayAvg}]}},null,8,["data"])],64)):Ae("",!0)])])]),e("div",ga,[e("div",pa,[t[17]||(t[17]=e("div",{class:"item-title"},[e("span"),D("运维人员状态统计")],-1)),e("div",xa,[e("div",fa,[t[16]||(t[16]=e("img",{class:"content-a_img",src:Ge,alt:""},null,-1)),e("div",ya,[t[11]||(t[11]=e("div",null,"人员总数",-1)),e("div",null,i(Z[a.value].all),1)]),e("div",ba,[e("div",Da,[t[12]||(t[12]=e("img",{src:w,alt:""},null,-1)),t[13]||(t[13]=e("div",null,"空闲人员",-1)),e("div",null,i(Z[a.value].kong),1)]),e("div",Ba,[t[14]||(t[14]=e("img",{src:w,alt:""},null,-1)),t[15]||(t[15]=e("div",null,"在途人员",-1)),e("div",null,i(Z[a.value].zt),1)])])])])]),e("div",ha,[t[18]||(t[18]=e("div",{class:"item-title"},[e("span"),D("调度任务状态统计")],-1)),e("div",wa,[(c(!0),r(H,null,W(j[a.value],l=>(c(),r("div",{class:S(["item-category_item",{"item-category_item_active":l.type===h.value}]),key:l,onClick:b=>me(l.type)},[e("img",{src:l.icon,alt:""},null,8,Ca),e("div",za,[e("div",Sa,i(l.name),1),e("div",Ga,i(l.value),1)])],10,Ya))),128))]),e("div",Oa,[d(we,{data:f.value,columns:x},null,8,["data"])])]),e("div",Ea,[e("div",Fa,[d(U,{modelValue:y.value,"onUpdate:modelValue":t[2]||(t[2]=l=>y.value=l),type:"month",placeholder:"选择日期",style:{width:"120px"},clearable:!1},null,8,["modelValue"])]),t[19]||(t[19]=e("div",{class:"item-title"},[e("span"),D("调度任务日历")],-1)),e("div",Ha,[e("div",ja,[e("div",Za,[d(E,{modelValue:y.value,"onUpdate:modelValue":t[3]||(t[3]=l=>y.value=l)},{"date-cell":he(({data:l})=>{var b,z,F;return[e("div",{class:S([ge((z=(b=_.value)==null?void 0:b[l.day])==null?void 0:z.totalCount),"date-cell"])},[e("div",Qa,i((F=l.day.split("-").slice(0))==null?void 0:F[2]),1)],2)]}),_:1},8,["modelValue"]),e("div",Ka,[(c(!0),r(H,null,W(ve.value,(l,b)=>(c(),r("div",{key:b,class:"category-item"},[e("span",{class:S(["color-chip",l.colorClass]),"aria-label":`${l.label}数量区间`},null,10,Na),e("span",Ra,i(l.label),1)]))),128))])])])])])])])])}}},_a=be(Ta,[["__scopeId","data-v-a3b67fa5"]]);export{_a as default};
