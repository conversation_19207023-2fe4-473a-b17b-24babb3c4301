import{_ as D,r as x,g as i,h as v,c as s,a as l,k,b as a,t as m,d as _,m as z,F as p,l as b,j as V,u as w,q as B}from"./index-B3fBvuMC.js";const N={class:"progress-module module-box"},T={key:0,class:"module-title-box"},j={class:"module-title"},C={class:"sn-module-content"},L={class:"snm-list"},F={class:"snmi-icon"},O=["title"],S={class:"snmi-p"},q={class:"snmi-n"},E={__name:"index",props:{title:{type:String,default:"报警区域统计"},propsData:{type:Object,default:{totalData:[{name:"内蒙古",value:17},{name:"辽宁",value:12},{name:"山东",value:11},{name:"北京",value:9},{name:"江苏",value:8}],yzData:[{name:"内蒙古",value:5},{name:"辽宁",value:2},{name:"山东",value:2},{name:"北京",value:1},{name:"江苏",value:1}],zyData:[{name:"内蒙古",value:1},{name:"辽宁",value:4},{name:"山东",value:3},{name:"北京",value:3},{name:"江苏",value:2}],zdData:[{name:"内蒙古",value:1},{name:"辽宁",value:3},{name:"山东",value:3},{name:"北京",value:2},{name:"江苏",value:3}],ybData:[{name:"内蒙古",value:10},{name:"辽宁",value:3},{name:"山东",value:3},{name:"北京",value:3},{name:"江苏",value:2}]}},showTitle:{type:Boolean,default:!0},tabs:{type:Object,default:[{label:"全部",value:"total"},{label:"严重",value:"yz"},{label:"重要",value:"zy"},{label:"中等",value:"zd"},{label:"一般",value:"yb"}]}},setup(c){const n=c;let t=x("total");const r=i(()=>n.propsData[`${t.value}Data`]?n.propsData[`${t.value}Data`].sort((o,u)=>u.value-o.value):[]);console.log("sortList====>",r);const h=i(()=>Math.max(...r.value.map(o=>o.value)));return(o,u)=>{const f=v("el-tab-pane"),y=v("el-tabs"),g=v("el-progress");return l(),s("div",N,[n.showTitle?(l(),s("div",T,[a("div",j,m(n.title),1)])):k("",!0),a("div",C,[_(y,{modelValue:w(t),"onUpdate:modelValue":u[0]||(u[0]=e=>B(t)?t.value=e:t=e),class:"snm-tabs"},{default:z(()=>[(l(!0),s(p,null,b(c.tabs,e=>(l(),V(f,{key:e.value,name:e.value,label:e.label},null,8,["name","label"]))),128))]),_:1},8,["modelValue"]),a("div",L,[(l(!0),s(p,null,b(r.value,(e,d)=>(l(),s("div",{class:"snm-item",key:d},[a("div",F,m("Top "+(d+1)),1),a("div",{class:"snmi-t",title:e.name},m(e.name),9,O),a("div",S,[_(g,{percentage:e.value/h.value*100,"show-text":!1,"stroke-width":5},null,8,["percentage"])]),a("div",q,m(e.value),1)]))),128))])])])}}},M=D(E,[["__scopeId","data-v-903833a0"]]);export{M as default};
