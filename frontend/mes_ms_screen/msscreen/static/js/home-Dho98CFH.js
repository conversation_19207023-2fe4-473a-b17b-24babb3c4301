import{N as X}from"./ScreenNavBar-qBdlSVbA.js";import{_ as V,r as s,w as L,o as $,z as j,c as I,a as O,b as e,x as P,v as H,h as U,j as Y,m as i,d as n,u as r,i as o,t as p,n as _,D as G,G as k,H as q}from"./index-B3fBvuMC.js";import{i as M}from"./index-BrbPvnuX.js";import J from"./index-BwaeW5L0.js";import C from"./index-CEHnJdj9.js";import W from"./index-C7yXz6Nc.js";import"./switch-DJpMdJKf.js";import"./linesGL-Dv4775iD.js";const K={__name:"ScreenContainer",props:{width:{type:[String,Number],default:1920},height:{type:[String,Number],default:1080},fullScreen:{type:Boolean,default:!1},selfAdaption:{type:Boolean,default:!0},delay:{type:Number,default:300},wrapperStyle:{type:Object,default:()=>({})},wideScreen:{type:Boolean,default:!1}},setup(D,{expose:B}){function E(t,c){let g=null;return function(...v){g&&clearTimeout(g),g=setTimeout(()=>t(...v),c>0?c:100)}}const u=D,l=s(null),y=s(1),w=s(+u.width),m=s(+u.height),b=s(u.wideScreen);async function T(){if(!l.value)return;const t=l.value.parentNode;t&&(t.style.overflowX="hidden",t.scrollLeft=0),l.value.style.width=w.value+"px",l.value.style.height=m.value+"px"}function S(t){if(l.value)if(y.value=t,l.value.style.transform=`scale(${t})`,l.value.style.transformOrigin="left top",b.value)l.value.style.margin="0";else{const c=window.innerWidth,g=window.innerHeight,v=w.value*t,x=m.value*t,A=Math.max((c-v)/2,0),d=Math.max((g-x)/2,0);l.value.style.margin=`${d}px ${A}px`}}function z(){if(!l.value)return;const t=window.innerWidth,c=window.innerHeight;if(u.fullScreen){const v=t/w.value,x=c/m.value;y.value={x:v,y:x},l.value.style.transform=`scale(${v}, ${x})`,l.value.style.margin="0";return}if(b.value){const v=t/w.value;S(v);return}const g=Math.min(t/w.value,c/m.value);S(g)}async function h(){u.selfAdaption&&(await T(),z())}function F(){if(!l.value)return;const t=l.value.parentNode;t&&(t.style.overflow="",t.style.overflowX=""),l.value.style.width="",l.value.style.height="",l.value.style.transform="",l.value.style.margin="",l.value.style.transformOrigin=""}let f=null;return B({currentScale:y}),L(()=>u.wideScreen,t=>{b.value=t,h()}),L(()=>u.selfAdaption,t=>{t?(h(),window.addEventListener("resize",f)):(window.removeEventListener("resize",f),F())},{immediate:!0}),$(()=>{f=E(()=>h(),u.delay),u.selfAdaption&&(h(),window.addEventListener("resize",f))}),j(()=>{window.removeEventListener("resize",f),F()}),(t,c)=>(O(),I("div",{class:"screen-container",style:H({overflowY:b.value?"auto":"hidden"})},[e("div",{class:"screen-wrapper",ref_key:"screenWrapper",ref:l,style:H(D.wrapperStyle)},[P(t.$slots,"default",{scale:y.value},void 0,!0)],4)],4))}},Q=V(K,[["__scopeId","data-v-54728b90"]]),Z={class:"dashboard-container"},ee={class:"box-container"},ae={class:"left"},te={class:"box stations"},le={class:"box ops"},se={class:"middle"},ne={class:"box maps"},re={class:"box notice"},ie={class:"right"},oe={class:"box devices"},de={class:"box consume"},ue={class:"dashboard-main"},ce={class:"overview-cards"},ve={class:"card"},ge={class:"card-value"},pe={class:"card"},me={class:"card-value"},be={class:"card"},fe={class:"card-value"},_e={class:"card"},we={class:"card-value"},he={class:"charts-row"},ye={class:"chart-card"},Se={class:"chart-content"},xe={class:"chart-card"},Ce={class:"chart-content"},Be={class:"resource-list"},Te={__name:"home",setup(D){const B=s(!1),E=s("这是个弹窗的标题"),u=s(128),l=s(112),y=s(78),w=s(92),m=s(5),b=s(2),T=s(-3),S=s(1),z=s(null),h=s(null),F=s([{name:"服务器",value:45},{name:"存储",value:30},{name:"网络设备",value:15},{name:"安全设备",value:10}]),f=s([{date:"1月",value:65},{date:"2月",value:68},{date:"3月",value:72},{date:"4月",value:70},{date:"5月",value:75},{date:"6月",value:78}]),t=s([{name:"服务器-01",type:"服务器",status:"在线",usage:"82%",updateTime:"2023-06-15 09:30"},{name:"存储-02",type:"存储",status:"在线",usage:"65%",updateTime:"2023-06-15 10:15"},{name:"交换机-01",type:"网络设备",status:"在线",usage:"45%",updateTime:"2023-06-15 08:45"},{name:"防火墙-01",type:"安全设备",status:"离线",usage:"0%",updateTime:"2023-06-14 22:30"}]),c=[{id:1,name:"公务用车运行维护费",val:1230,labelColor:"#4FA0FF",labelBgStyle:"background-image: linear-gradient(90deg, rgba(79,160,255,0.30) 3%, rgba(79,160,255,0.00) 99%);"},{id:2,name:"办公费",val:800,labelColor:"#67C23A",labelBgStyle:"background-image: linear-gradient(90deg, rgba(103,194,58,0.30) 3%, rgba(103,194,58,0.00) 99%);"},{id:3,name:"差旅费",val:1500,labelColor:"#FBD500",labelBgStyle:"background-image: linear-gradient(90deg, rgba(251,213,0,0.30) 3%, rgba(251,213,0,0.00) 99%);"},{id:4,name:"差旅费222",val:100,labelColor:"#FF9A54",labelBgStyle:"background-image: linear-gradient(90deg, rgba(255,154,84,0.30) 3%, rgba(255,154,84,0.00) 97%);"},{id:5,name:"哈哈哈哈哈哈哈",val:100,labelColor:"#F56C6D",labelBgStyle:"background-image: linear-gradient(90deg, rgba(245,108,109,0.30) 3%, rgba(245,108,109,0.00) 99%);"}],g=[{id:1,name:"名称1设备有限公司",val:20,labelColor:"#4FA0FF",labelBgStyle:"background-image: linear-gradient(90deg, rgba(79,160,255,0.30) 3%, rgba(79,160,255,0.00) 99%);"},{id:2,name:"这是一个名字很长的运维公司",val:100,labelColor:"#67C23A",labelBgStyle:"background-image: linear-gradient(90deg, rgba(103,194,58,0.30) 3%, rgba(103,194,58,0.00) 99%);"},{id:3,name:"名称三设备有限公司",val:50,labelColor:"#FBD500",labelBgStyle:"background-image: linear-gradient(90deg, rgba(251,213,0,0.30) 3%, rgba(251,213,0,0.00) 99%);"},{id:4,name:"名称四设备有限公司",val:20,labelColor:"#FF9A54",labelBgStyle:"background-image: linear-gradient(90deg, rgba(255,154,84,0.30) 3%, rgba(255,154,84,0.00) 97%);"},{id:5,name:"名称五设备有限公司",val:17,labelColor:"#F56C6D",labelBgStyle:"background-image: linear-gradient(90deg, rgba(245,108,109,0.30) 3%, rgba(245,108,109,0.00) 99%);"},{id:6,name:"名称六六六设备有限公司",val:39,labelColor:"#00BBF5",labelBgStyle:"background-image: linear-gradient(90deg, rgba(0,187,245,0.30) 3%, rgba(0,187,245,0.00) 99%);"}],v=()=>{const d=M(z.value);return d.setOption({tooltip:{trigger:"item"},legend:{orient:"vertical",left:10,textStyle:{color:getComputedStyle(document.documentElement).getPropertyValue("--primary-text-color")}},series:[{name:"资源类型",type:"pie",radius:"70%",data:F.value,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),d},x=()=>{const d=M(h.value);return d.setOption({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:f.value.map(a=>a.date)},yAxis:{type:"value"},series:[{name:"使用率",type:"line",data:f.value.map(a=>a.value),smooth:!0,areaStyle:{}}]}),d},A=d=>{d.resize()};return $(()=>{const d=v(),a=x();window.addEventListener("resize",()=>{A(d),A(a)})}),(d,a)=>{const R=U("el-button");return O(),Y(r(Q),{width:1920,height:1080,selfAdaption:!0,class:"scale-wrap"},{default:i(()=>[e("div",Z,[n(X,{title:"资源总览"}),e("div",ee,[e("div",ae,[e("div",te,[n(r(C),null,{text:i(()=>[a[1]||(a[1]=o("水运站点监测"))]),_:1,__:[1]})]),e("div",le,[n(r(C),null,{text:i(()=>[a[2]||(a[2]=o("运维人员概览"))]),_:1,__:[2]}),n(r(W),{list:g})])]),e("div",se,[e("div",ne,[n(r(C),null,{text:i(()=>[a[3]||(a[3]=o("地图区域"))]),_:1,__:[3]})]),e("div",re,[n(r(C),null,{text:i(()=>[a[4]||(a[4]=o("重要事项"))]),_:1,__:[4]})])]),e("div",ie,[e("div",oe,[n(r(C),null,{text:i(()=>[a[5]||(a[5]=o("设备管理"))]),_:1,__:[5]}),n(r(W),{list:c})]),e("div",de,[n(r(C),null,{text:i(()=>[a[6]||(a[6]=o("耗材管理"))]),_:1,__:[6]})])]),n(r(J),{title:E.value,dialogVisible:B.value},{triggerArea:i(()=>[n(R,{onClick:a[0]||(a[0]=()=>{B.value=!B.value})},{default:i(()=>a[7]||(a[7]=[o(" 点我 ")])),_:1,__:[7]})]),dialogContent:i(()=>a[8]||(a[8]=[o(" 这是一个弹窗 ")])),_:1},8,["title","dialogVisible"])]),e("div",ue,[e("div",ce,[e("div",ve,[a[9]||(a[9]=e("div",{class:"card-title"},"总资源数",-1)),e("div",ge,p(u.value),1),e("div",{class:_(["card-trend",m.value>0?"up":"down"])},[e("i",{class:_(["fa",m.value>0?"fa-arrow-up":"fa-arrow-down"])},null,2),o(" "+p(Math.abs(m.value))+"% ",1)],2)]),e("div",pe,[a[10]||(a[10]=e("div",{class:"card-title"},"在线资源",-1)),e("div",me,p(l.value),1),e("div",{class:_(["card-trend",b.value>0?"up":"down"])},[e("i",{class:_(["fa",b.value>0?"fa-arrow-up":"fa-arrow-down"])},null,2),o(" "+p(Math.abs(b.value))+"% ",1)],2)]),e("div",be,[a[11]||(a[11]=e("div",{class:"card-title"},"资源利用率",-1)),e("div",fe,p(y.value)+"%",1),e("div",{class:_(["card-trend",T.value>0?"up":"down"])},[e("i",{class:_(["fa",T.value>0?"fa-arrow-up":"fa-arrow-down"])},null,2),o(" "+p(Math.abs(T.value))+"% ",1)],2)]),e("div",_e,[a[12]||(a[12]=e("div",{class:"card-title"},"资源健康度",-1)),e("div",we,p(w.value)+"/100",1),e("div",{class:_(["card-trend",S.value>0?"up":"down"])},[e("i",{class:_(["fa",S.value>0?"fa-arrow-up":"fa-arrow-down"])},null,2),o(" "+p(Math.abs(S.value))+"分 ",1)],2)])]),e("div",he,[e("div",ye,[a[13]||(a[13]=e("div",{class:"chart-title"},"资源类型分布",-1)),e("div",Se,[e("div",{ref_key:"pieChart",ref:z,class:"chart-dom"},null,512)])]),e("div",xe,[a[14]||(a[14]=e("div",{class:"chart-title"},"资源使用趋势",-1)),e("div",Ce,[e("div",{ref_key:"lineChart",ref:h,class:"chart-dom"},null,512)])])]),e("div",Be,[a[15]||(a[15]=e("div",{class:"list-title"},"资源状态详情",-1)),n(r(G),{data:t.value,border:""},{default:i(()=>[n(r(k),{prop:"name",label:"资源名称"}),n(r(k),{prop:"type",label:"资源类型"}),n(r(k),{prop:"status",label:"状态"},{default:i(N=>[n(r(q),{type:N.row.status==="在线"?"success":"danger"},{default:i(()=>[o(p(N.row.status),1)]),_:2},1032,["type"])]),_:1}),n(r(k),{prop:"usage",label:"使用率"}),n(r(k),{prop:"updateTime",label:"最后更新"})]),_:1},8,["data"])])])])]),_:1})}}},He=V(Te,[["__scopeId","data-v-d335b68e"]]);export{He as default};
