import{_ as m,L as S,r as b,o as V,z as w,h as f,j as y,a as C,m as o,A as x,b as t,n as N,i,d as l,u as r,B,T as j}from"./index-B3fBvuMC.js";const U={class:"setting_inner"},k={class:"setting_body"},T={class:"setting_item"},q={class:"setting_content"},z={class:"setting_item"},A={class:"setting_content"},D={class:"setting_item"},I={class:"setting_content"},L={__name:"setting",setup(M,{expose:g}){const a=S(),d=b(!1);function v(){d.value=!0}function _(n,e){a.setScreenState(e,n)}return V(()=>{if(typeof document<"u"&&d.value){const n=document.querySelector(".setting");n&&n.parentNode!==document.body&&document.body.appendChild(n)}}),w(()=>{const n=document.querySelector(".setting");n&&n.parentNode&&n.parentNode.removeChild(n)}),g({init:v,settingShow:d}),(n,e)=>{const u=f("el-radio"),p=f("el-radio-group");return C(),y(j,{name:"yh-setting-fade"},{default:o(()=>[x(t("div",{class:N(["setting",{settingShow:d.value}])},[t("div",{class:"setting_dislog",onClick:e[0]||(e[0]=s=>d.value=!1)}),t("div",U,[e[18]||(e[18]=t("div",{class:"setting_header"},"设置",-1)),t("div",k,[e[16]||(e[16]=t("div",{class:"left_shu"},"全局设置",-1)),t("div",T,[e[9]||(e[9]=t("span",{class:"setting_label"},[i(" 是否进行自动适配 "),t("span",{class:"setting_label_tip"},"(默认分辨率1920*1080)"),i(": ")],-1)),t("div",q,[l(p,{modelValue:r(a).isScale,"onUpdate:modelValue":e[1]||(e[1]=s=>r(a).isScale=s),onChange:e[2]||(e[2]=s=>_(s,"isScale"))},{default:o(()=>[l(u,{value:!0},{default:o(()=>e[7]||(e[7]=[i("是")])),_:1,__:[7]}),l(u,{value:!1},{default:o(()=>e[8]||(e[8]=[i("否")])),_:1,__:[8]})]),_:1},8,["modelValue"])])]),e[17]||(e[17]=t("div",{class:"left_shu"},"实时监测",-1)),t("div",z,[e[12]||(e[12]=t("span",{class:"setting_label"},"设备提醒自动轮询:",-1)),t("div",A,[l(p,{modelValue:r(a).sbtxSwiper,"onUpdate:modelValue":e[3]||(e[3]=s=>r(a).sbtxSwiper=s),onChange:e[4]||(e[4]=s=>_(s,"sbtxSwiper"))},{default:o(()=>[l(u,{value:!0},{default:o(()=>e[10]||(e[10]=[i("是")])),_:1,__:[10]}),l(u,{value:!1},{default:o(()=>e[11]||(e[11]=[i("否")])),_:1,__:[11]})]),_:1},8,["modelValue"])])]),t("div",D,[e[15]||(e[15]=t("span",{class:"setting_label"},"实时预警轮播:",-1)),t("div",I,[l(p,{modelValue:r(a).ssyjSwiper,"onUpdate:modelValue":e[5]||(e[5]=s=>r(a).ssyjSwiper=s),onChange:e[6]||(e[6]=s=>_(s,"ssyjSwiper"))},{default:o(()=>[l(u,{value:!0},{default:o(()=>e[13]||(e[13]=[i("是")])),_:1,__:[13]}),l(u,{value:!1},{default:o(()=>e[14]||(e[14]=[i("否")])),_:1,__:[14]})]),_:1},8,["modelValue"])])])])])],2),[[B,d.value]])]),_:1})}}},$=m(L,[["__scopeId","data-v-440e6b47"]]);export{$ as default};
