import B from"./ConsumableCard-Bv-GvXWM.js";import{D as V}from"./timeFilter-PW16EO3x.js";import{B as w}from"./index-C4TfGCsC.js";import{i as r,a as u,b as l,c as d}from"./icon_biaozhunwuzhi-BnhnKLd7.js";import{_ as A,r as N,g as T,o as q,w as I,c as m,a as n,b as C,d as p,m as j,i as E,u as _,F as M,l as L,j as S}from"./index-B3fBvuMC.js";const U={class:"consumable-dashboard"},z={class:"dashboard-header"},G={class:"card-grid"},H={__name:"ConsumableDashboard",emits:{"icon-click":a=>typeof a=="object"},setup(a,{emit:k}){const g=k,b=()=>{g("icon-click",{type:"consumDash",name:"耗材管理"})},e=N("day"),f={day:[{id:1,title:"低值耗材",icon:r,stock:5523,categoryCount:4,outCount:510.25,inCount:42.75},{id:2,title:"关键元器件",icon:u,stock:1254.5,categoryCount:8,outCount:120.3,inCount:15.2},{id:3,title:"试剂",icon:l,stock:875.9,categoryCount:12,outCount:75.6,inCount:30.4},{id:4,title:"标准物质",icon:d,stock:325.15,categoryCount:6,outCount:25.8,inCount:10.25}],month:[{id:1,title:"低值耗材",icon:r,stock:5523,categoryCount:4,outCount:7850.5,inCount:1242.25},{id:2,title:"关键元器件",icon:u,stock:1254.5,categoryCount:8,outCount:1850.75,inCount:450.2},{id:3,title:"试剂",icon:l,stock:875.9,categoryCount:12,outCount:1250.3,inCount:650.4},{id:4,title:"标准物质",icon:d,stock:325.15,categoryCount:6,outCount:520.8,inCount:180.25}]},y=T(()=>f[e.value]),s=()=>{document.querySelectorAll(".number-animate").forEach(t=>{const o=parseFloat(t.getAttribute("data-target")||"0"),x=800,v=performance.now(),c=D=>{const i=Math.min((D-v)/x,1),F=(o*i).toFixed(2);t.textContent=F,i<1&&requestAnimationFrame(c)};requestAnimationFrame(c)})};return q(s),I(e,s),(h,t)=>(n(),m("div",U,[C("div",z,[p(_(w),{onIconClick:b},{text:j(()=>[t[1]||(t[1]=E("耗材管理"))]),_:1,__:[1]}),p(_(V),{modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=o=>e.value=o)},null,8,["modelValue"])]),C("div",G,[(n(!0),m(M,null,L(y.value,o=>(n(),S(B,{key:o.id,item:o},null,8,["item"]))),128))])]))}},R=A(H,[["__scopeId","data-v-2c945839"]]);export{R as default};
