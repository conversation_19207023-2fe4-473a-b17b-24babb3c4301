import{B as h}from"./index-C4TfGCsC.js";import{D as f}from"./timeFilter-PW16EO3x.js";import{b as k,c as x,i as y,a as T}from"./icon_biaozhunwuzhi-BnhnKLd7.js";import{_ as w,r as F,g as V,o as B,N as D,w as N,c as o,a as n,b as t,d as l,m as A,i as q,u as d,F as E,l as I,t as u}from"./index-B3fBvuMC.js";const M={class:"consumable-dashboard"},O={class:"card-grid-wrapper"},S={class:"dashboard-header"},L={class:"card-grid-scroll"},U={class:"card-header"},j={class:"card-left"},z=["src"],G={class:"title-text"},H={class:"card-metrics"},J={class:"card-item"},K=["data-target"],P={class:"card-item"},Q=["data-target"],R={class:"card-item"},W=["data-target"],X={class:"card-item"},Y=["data-target"],Z={__name:"OperationTop",emits:["icon-click"],setup($,{emit:tt}){const e=F("head"),m={head:[{id:1,title:"总部一部",icon:y,stock:120,categoryCount:110,outCount:25,inCount:5},{id:2,title:"总部二部",icon:T,stock:80,categoryCount:65,outCount:18,inCount:3}],org:[{id:1,title:"华南机构",icon:k,stock:90,categoryCount:75,outCount:10,inCount:2},{id:2,title:"华北机构",icon:x,stock:100,categoryCount:80,outCount:12,inCount:4}]},_=V(()=>m[e.value]||[]),i=()=>{document.querySelectorAll(".number-animate").forEach(a=>{const s=parseFloat(a.getAttribute("data-target")||"0"),g=800,v=performance.now(),r=b=>{const c=Math.min((b-v)/g,1),C=(s*c).toFixed(0);a.textContent=C,c<1&&requestAnimationFrame(r)};requestAnimationFrame(r)})};return B(async()=>{await D(),i()}),N(e,i),(p,a)=>(n(),o("div",M,[t("div",O,[t("div",S,[l(d(h),{showIcon:!1},{text:A(()=>[a[1]||(a[1]=q("当前人数与状态"))]),_:1,__:[1]}),l(d(f),{modelValue:e.value,"onUpdate:modelValue":a[0]||(a[0]=s=>e.value=s),options:[{label:"总部",value:"head"},{label:"机构",value:"org"}]},null,8,["modelValue"])]),t("div",L,[(n(!0),o(E,null,I(_.value,s=>(n(),o("div",{key:s.id,class:"consumable-card"},[t("div",U,[t("div",j,[t("img",{src:s.icon,alt:"icon",class:"card-icon"},null,8,z),t("span",G,[t("span",null,u(s.title.slice(0,2)),1),a[2]||(a[2]=t("br",null,null,-1)),t("span",null,u(s.title.slice(2)),1)])]),t("div",H,[t("div",J,[a[3]||(a[3]=t("span",{class:"item-label"},"实时人数：",-1)),t("span",{class:"item-value number-animate","data-target":s.stock},"0",8,K)]),t("div",P,[a[4]||(a[4]=t("span",{class:"item-label"},"在线人数：",-1)),t("span",{class:"item-value number-animate","data-target":s.categoryCount},"0",8,Q)]),t("div",R,[a[5]||(a[5]=t("span",{class:"item-label"},"维护任务：",-1)),t("span",{class:"item-value number-animate","data-target":s.outCount},"0",8,W)]),t("div",X,[a[6]||(a[6]=t("span",{class:"item-label"},"告警数：",-1)),t("span",{class:"item-value number-animate","data-target":s.inCount},"0",8,Y)])])])]))),128))])])]))}},nt=w(Z,[["__scopeId","data-v-cd3ce73b"]]);export{nt as default};
