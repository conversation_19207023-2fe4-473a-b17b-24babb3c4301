import{_ as u,j as f,a as i,m as _,c as g,k as C,O as l,b as o,d as a,u as k,T as b}from"./index-B3fBvuMC.js";import v from"./ConsumaTop-pz3_HKNS.js";import h from"./ConsumaTable-DcDhuDBC.js";import V from"./CousumaScroll-DpwYIumy.js";import"./index-C4TfGCsC.js";import"./index-BrbPvnuX.js";import"./timeFilter-PW16EO3x.js";const w={class:"device-dialog-body"},y={class:"section-wrapper topConsuma"},B={class:"section-wrapper bottomConsuma"},x={__name:"ConsumaDialog",props:{dialogVisible:Boolean},emits:["update:dialogVisible"],setup(r,{emit:n}){const c=r,m=n;function t(){m("update:dialogVisible",!1)}const d=["低值耗材","高值耗材","关键元器件","标准物质","试剂","参比材料","检测试纸","对照品","其它"].flatMap((e,s)=>Array.from({length:5},(M,p)=>({type:e,category:`分类 ${p+1}`,stock:Math.floor(Math.random()*10),warehouse:`仓库 ${s+1}`})));return(e,s)=>(i(),f(b,{name:"fade"},{default:_(()=>[c.dialogVisible?(i(),g("div",{key:0,class:"custom-dialog-mask",onClick:l(t,["self"])},[o("div",{class:"custom-dialog-wrapper",onClick:s[0]||(s[0]=l(()=>{},["stop"]))},[o("div",{class:"dialog-header"},[s[1]||(s[1]=o("span",{class:"title"},"耗材使用情况",-1)),o("span",{class:"close-btn",onClick:t},"×")]),o("div",w,[o("div",y,[a(v)]),o("div",B,[a(V),a(h,{"inventory-list":k(d)},null,8,["inventory-list"])])])])])):C("",!0)]),_:1}))}},I=u(x,[["__scopeId","data-v-9824ab2d"]]);export{I as default};
