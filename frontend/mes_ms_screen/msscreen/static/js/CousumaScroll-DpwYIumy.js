import{i as x}from"./index-BrbPvnuX.js";import{B as v}from"./index-C4TfGCsC.js";import{D as b}from"./timeFilter-PW16EO3x.js";import{_,r as n,o as g,N as w,z as A,w as M,c as S,a as B,b as d,d as c,m as z,i as C,u as m}from"./index-B3fBvuMC.js";const D={class:"scrollable-line-chart-wrapper"},E={class:"dashboard-header"},F={__name:"CousumaScroll",setup(T){const a=n(null);let o=null,l=null;const h=[{label:"月",value:"month"},{label:"日",value:"day"}],t=n("month"),u={month:Array.from({length:12},(r,e)=>`${e+1}月`),day:Array.from({length:31},(r,e)=>`7/${e+1}`)},f={month:{低值耗材:[120,100,90,130,110,80,70,85,95,105,115,125],关键元器件:[60,65,70,60,55,50,45,48,52,58,63,66],试剂:[80,85,78,82,88,91,94,97,100,102,98,95],标准物质:[30,28,26,24,22,20,25,27,29,31,33,35]},day:{低值耗材:Array.from({length:31},()=>Math.floor(8+Math.random()*6)),关键元器件:Array.from({length:31},()=>Math.floor(4+Math.random()*4)),试剂:Array.from({length:31},()=>Math.floor(7+Math.random()*4)),标准物质:Array.from({length:31},()=>Math.floor(1+Math.random()*3))}},p={低值耗材:"#0065D5",关键元器件:"#67C23A",试剂:"#00BBF5",标准物质:"#FF9A54"};function i(){if(!o)return;const r=u[t.value],e=Object.entries(f[t.value]).map(([s,y])=>({name:s,type:"line",smooth:!0,showSymbol:!1,data:y,lineStyle:{width:2,color:p[s]}}));o.setOption({grid:{top:50,left:10,right:10,bottom:30,containLabel:!0},legend:{top:8,right:10,textStyle:{color:"#666",fontSize:12},itemWidth:10,itemHeight:6},tooltip:{trigger:"axis"},xAxis:{type:"category",data:r,axisTick:{show:!1},axisLine:{lineStyle:{color:"#DEE2EB"}},axisLabel:{margin:16,interval:0,rotate:0,color:"#666",fontSize:12}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DEE2EB"}},axisLine:{show:!1},axisLabel:{color:"#999"},axisTick:{show:!1}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:t.value==="day"?6/31*100:6/12*100,height:20,bottom:-20,handleSize:"100%",handleStyle:{color:"#1472FF"},fillerColor:"rgba(20,114,255,0.2)",borderColor:"#1472FF"},{type:"inside",xAxisIndex:0,start:0,end:t.value==="day"?6/31*100:6/12*100}],series:e})}return g(async()=>{await w(),a.value&&(o=x(a.value),i(),l=new ResizeObserver(()=>o==null?void 0:o.resize()),l.observe(a.value))}),A(()=>{l&&a.value&&l.unobserve(a.value),o==null||o.dispose()}),M(t,()=>{i()}),(r,e)=>(B(),S("div",D,[d("div",E,[c(m(v),{showIcon:!1},{text:z(()=>[e[1]||(e[1]=C("耗材管理"))]),_:1,__:[1]}),c(m(b),{options:h,modelValue:t.value,"onUpdate:modelValue":e[0]||(e[0]=s=>t.value=s)},null,8,["modelValue"])]),d("div",{ref_key:"chartRef",ref:a,class:"scrollable-line-chart-inner"},null,512)]))}},N=_(F,[["__scopeId","data-v-9854786e"]]);export{N as default};
