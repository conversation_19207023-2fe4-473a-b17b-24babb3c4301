import{_ as p,r as m,j as f,a as i,m as _,c as g,k as u,O as t,b as e,d as a,T as v}from"./index-B3fBvuMC.js";import k from"./deviceTopInfo-CxF1B4I5.js";import b from"./deviceBottomInfo-BBEHD5fP.js";import"./index-C4TfGCsC.js";import"./index-VkXiS0_E.js";import"./index-BrbPvnuX.js";import"./icon_shuishebei-B-ne4_mM.js";import"./index.vue_vue_type_script_setup_true_lang-CW6cgbhn.js";const V={class:"device-dialog-body"},B={class:"section-wrapper"},w={class:"section-wrapper"},C={__name:"deviceDialog",props:{dialogVisible:Boolean},emits:["update:dialogVisible"],setup(l,{emit:c}){const r=l,n=c,d=m("当年");function s(){n("update:dialogVisible",!1)}return(D,o)=>(i(),f(v,{name:"fade"},{default:_(()=>[r.dialogVisible?(i(),g("div",{key:0,class:"custom-dialog-mask",onClick:t(s,["self"])},[e("div",{class:"custom-dialog-wrapper",onClick:o[0]||(o[0]=t(()=>{},["stop"]))},[e("div",{class:"dialog-header"},[o[1]||(o[1]=e("span",{class:"title"},"设备管理",-1)),e("span",{class:"close-btn",onClick:s},"×")]),e("div",V,[e("div",B,[a(k)]),e("div",w,[a(b,{"time-range":d.value},null,8,["time-range"])])])])])):u("",!0)]),_:1}))}},M=p(C,[["__scopeId","data-v-d9f88674"]]);export{M as default};
