import{_ as o,g as c,h as l,c as _,a as n,b as t,K as r,d,t as p,u as m,m as u,i as v}from"./index-B3fBvuMC.js";const f="/msscreen/static/png/404-N4aRkdWY.png",a="/msscreen/static/png/404_cloud-CPexjtDj.png",g={class:"wscn-http404-container"},h={class:"wscn-http404"},x={class:"bullshit"},b={class:"bullshit__headline"},k={__name:"404",setup(N){let e=c(()=>"找不到网页！");return(V,s)=>{const i=l("router-link");return n(),_("div",g,[t("div",h,[s[3]||(s[3]=r('<div class="pic-404" data-v-aa824f31><img class="pic-404__parent" src="'+f+'" alt="404" data-v-aa824f31><img class="pic-404__child left" src="'+a+'" alt="404" data-v-aa824f31><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-aa824f31><img class="pic-404__child right" src="'+a+'" alt="404" data-v-aa824f31></div>',1)),t("div",x,[s[1]||(s[1]=t("div",{class:"bullshit__oops"}," 404错误! ",-1)),t("div",b,p(m(e)),1),s[2]||(s[2]=t("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),d(i,{to:"/index",class:"bullshit__return-home"},{default:u(()=>s[0]||(s[0]=[v(" 返回首页 ")])),_:1,__:[0]})])])])}}},B=o(k,[["__scopeId","data-v-aa824f31"]]);export{B as default};
