import{i}from"./index-BrbPvnuX.js";import{e as l,r as u,o as p,f as c,w as f,c as d,a as m}from"./index-B3fBvuMC.js";const k=l({__name:"index",props:{chartData:{}},setup(n){const o=n,t=u(null);let e=null;const s=()=>{t.value&&(e&&e.dispose(),e=i(t.value),r())},r=()=>{e&&e.setOption({...o.chartData})},a=()=>{e==null||e.resize()};return p(()=>{setTimeout(()=>{s()},0)}),c(()=>{window.removeEventListener("resize",a),e==null||e.dispose(),e=null}),f(()=>o.chartData,()=>r(),{deep:!0}),(h,_)=>(m(),d("div",{ref_key:"chartRef",ref:t,style:{width:"100%",height:"100%"}},null,512))}});export{k as _};
