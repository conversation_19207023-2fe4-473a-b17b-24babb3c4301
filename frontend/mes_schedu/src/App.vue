<template>
  <header class="global-header" v-if="isLoggedIn">
      <CustomHeader v-if="isLoggedIn && isFinishHome"/>
  </header>
  <div class="main-content-wrapper" :class="isLoggedIn ? 'mainPadd':''">
    <router-view />
  </div>
</template>

<script setup>
import CustomHeader from '@/components/Home/NavigationBar'
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import usePermissionStore from '@/store/modules/permission.js'
import useUserStore from '@/store/modules/user.js'

const homeStore = useUserStore()
const userStore = usePermissionStore()

// 计算属性：判断用户是否已登录
const isLoggedIn = computed(() => {
  return userStore.sidebarRouters.length > 0 ? true:false
})

// 计算属性：判断用户是否已完成首页
const isFinishHome = computed(() => {
  return homeStore.homeLoad == true ? true:false
})

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})


</script>

<style scoped lang="scss">

.global-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000; // 确保在最上层
  height: 60px; // 根据实际高度调整
}

.main-content-wrapper {
  height: 100vh;
  overflow: auto;
  background: #F7F7F7;
}

.mainPadd{
  padding-top: 64px; // 为固定导航栏留出空间
}

</style>
