@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './ruoyi.scss';

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  height: calc(100vh - 154px);
  >div {
    height: 100%;
  }
  .table-container {
    position: relative;
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.form-btn {
  width: 340px;
  .el-form-item__content {
    justify-content: flex-end;

  }
}

.el-tree {
  --el-tree-node-content-height: 40px !important;
}

.el-form--inline {

  .el-input, .el-textarea {
  --el-input-width: 240px;  
  }
  .el-select {
    --el-select-width: 240px;
  }
}

.query-form {
  display: inline-flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0;
  width: 100%;
  column-gap: 20px;
  .el-form-item {
    margin-right: 0;
    width: calc((100% - 40px) / 3);
    .el-input, .el-textarea {
      width: 100% !important;  
    }
    .el-select {
      width: 100% !important;  
    }
  }
}

.table-header {
  padding-right: 0;
  margin-top: 12px;
  &::before {
    content: "";
    display: block;
    width: calc(100% - 20px);
    height: 1px;
    background-color: var(--el-border-color);
    position: absolute;
    top: -12px; /* 调整位置，显示在容器上方 */
    left: 10px;
  }
}

.table-title {
  position: relative;
  height: 24px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  font-weight: 500;
  padding-left: 10px;
  margin-bottom: 16px;
  width: calc(100% - 80px);
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background: #1472ff;
  }
}

.custom-action-column {

  .cell {
    text-align: left;
    padding-left: 10px;
  }
  
}

// 详情页面展示
.detail-row {
  margin-bottom: 20px;
  padding: 0 12px;
  .detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .detail-label {
      text-align: right;
      color: rgba(102, 102, 102, 0.7);
      margin-right: 6px;
      font-weight: 400;
      font-size: 14px;
    }

    .detail-value {
      width: 240px;
      flex: 1;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }
}