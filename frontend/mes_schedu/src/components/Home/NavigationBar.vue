<template>
  <header class="top-header">
    <!-- 左侧logo和系统名称 -->
    <div class="logo-container">
      <img src="@/assets/logo/logo.png" alt="系统logo" class="logo" />
      <span class="system-name">国家网数据生产系统</span>
    </div>

    <!-- 中间导航菜单 -->
    <div class="nav-wrapper">
      <el-scrollbar wrap-class="nav-scroll-wrapper">
        <ul class="nav-menu">
          <li
            v-for="item in topbarRouters"
            :key="item.name"
            :class="[
              'nav-menu-item',
              { 'is-active': item.path === activeMenu },
            ]"
            @click="handleMenuSelect(item)"
          >
            <span class="menu-text">{{ item.meta?.title || item.name }}</span>
          </li>
        </ul>
      </el-scrollbar>
    </div>

    <!-- 右侧功能区 -->
    <div class="right-panel">
      <!-- 通知区域 -->
      <div class="notification-area">
        <div class="notification-scroll">
          <div
            class="notification-item"
            v-for="(item, index) in notifications"
            :key="index"
            :class="{ active: index === currentNotificationIndex }"
          >
            <i class="el-icon-bell" />
            <span>{{ item }}</span>
          </div>
        </div>
      </div>

      <!-- 消息通知 -->
      <div class="message-notification" @click="showNotifications">
        <div class="message-icon">
          <i class="el-icon-bell" />
        </div>
        <span class="message-count">{{ notificationCount }}</span>
      </div>

      <!-- 用户信息 -->
      <el-dropdown @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userInfo.avatar" />
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/user/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided command="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { ElMessageBox } from "element-plus";
import useUserStore from "@/store/modules/user";
import usePermissionStore from "@/store/modules/permission.js";
import { useRouter, useRoute } from "vue-router";

const userStore = useUserStore();
const permissionStore = usePermissionStore();
const router = useRouter();
const route = useRoute();
const emit = defineEmits(["userCommand", "logout"]);

const props = defineProps({
  activeMenu: {
    type: String,
    default: "",
  },
});

const currentActiveMenu = ref(
  props.activeMenu || import.meta.env.VITE_ROUTE_PATH || ""
);
// 监听props变化并更新activeMenu
const activeMenu = ref(
  props.activeMenu || import.meta.env.VITE_ROUTE_PATH || ""
);

watch(
  () => props.activeMenu,
  (newVal) => {
    if (newVal) {
      activeMenu.value = newVal;
      currentActiveMenu.value = newVal; // 同时更新当前激活菜单
    }
  }
);

const isLoading = ref(true);

// 获取顶部导航路由
const topbarRouters = computed(() => {
  return permissionStore.topbarRouters.filter((route) => !route.hidden);
});

// 用户信息计算属性
const userInfo = computed(() => ({
  nickName: userStore.nickName || "未登录",
  avatar:
    userStore.avatar ||
    "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
}));

// 通知相关
const notificationCount = ref(3);
const notifications = ref([
  "系统将于今晚23:00-次日01:00进行维护，请提前做好准备。",
  "重要通知：下周一将进行系统升级，请各位用户提前安排好工作。",
  "新功能已上线，点击查看详情。",
]);
const currentNotificationIndex = ref(0);

// 监听topbarRouters变化，确保路由加载后更新活跃菜单
watch(
  topbarRouters,
  (newRoutes) => {
    if (newRoutes.length > 0) {
      updateActiveMenu(route.fullPath);
    }
  },
  { immediate: true }
);

function startRouteWatcher() {
  watch(
    () => route.fullPath,
    (newPath) => {
      updateActiveMenu(newPath);
    },
    { immediate: true }
  );
}

// 更新活跃菜单
function updateActiveMenu(path) {
  // 获取当前时间
  const now = new Date();

  // 获取时、分、秒和毫秒
  const hours = String(now.getHours()).padStart(2, '0');       // 补零到两位
  const minutes = String(now.getMinutes()).padStart(2, '0');   // 补零到两位
  const seconds = String(now.getSeconds()).padStart(2, '0');   // 补零到两位
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0'); // 补零到三位

  // 格式化输出成一个字符串
  const formattedTime = `${hours}:${minutes}:${seconds}.${milliseconds}`;

  // 输出结果
  console.log("当前时间:", formattedTime);
  let tempPath = path;
  if (tempPath === "/") {
    tempPath = window.location.pathname;
  }
  const lowerCasePath = tempPath.toLowerCase();
  // 首先尝试精确匹配
  const exactMatch = topbarRouters.value.find((route) => {
    return route.path?.toLowerCase() === lowerCasePath;
  });
  if (exactMatch) {
    activeMenu.value = exactMatch.path;
    currentActiveMenu.value = exactMatch.path;
    return;
  }

  // 然后尝试前缀匹配
  const prefixMatch = topbarRouters.value.find((route) => {
    const routePath = route.path?.toLowerCase() || "";
    return lowerCasePath.startsWith(routePath + "/");
  });

  if (prefixMatch) {
    activeMenu.value = prefixMatch.path;
    currentActiveMenu.value = prefixMatch.path;
    return;
  }

  // 尝试匹配首页路由
  const homeRoute = topbarRouters.value.find(
    (route) =>
      route.path?.toLowerCase() === "/" || route.name?.toLowerCase() === "index"
  );

  if (homeRoute) {
    activeMenu.value = homeRoute.path;
  } else {
    activeMenu.value = import.meta.env.VITE_ROUTE_PATH || "/";
  }
}

// 菜单选择处理
const handleMenuSelect = (index) => {
  if (index.path === "/") {
    // 对主系统使用根路径
    window.open("/", "_blank");
  } else if (index.path) {
    const fullUrl = `${window.location.origin}${index.path}/`;
    window.open(fullUrl, "_blank");
  } else {
    console.warn("未找到匹配的路由:", index);
  }
};

// 用户命令处理
const handleUserCommand = (command) => {
  if (command === "logout") {
    logout();
  } else {
    emit("userCommand", command);
  }
};

// 退出登录
const logout = () => {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = `/${import.meta.env.VITE_ROUTE_PATH}/home`;
      });
    })
    .catch(() => {});
};

// 显示通知面板
const showNotifications = () => {
  console.log("显示通知面板");
};

// 开始通知滚动
let notificationTimer = null;

const startNotificationScroll = () => {
  notificationTimer = setInterval(() => {
    currentNotificationIndex.value =
      (currentNotificationIndex.value + 1) % notifications.value.length;
  }, 5000);
};

// 停止通知滚动
const stopNotificationScroll = () => {
  if (notificationTimer) {
    clearInterval(notificationTimer);
    notificationTimer = null;
  }
};

// 检查路由是否已加载
onMounted(async () => {
  await nextTick(); // 确保 Vue Router 已初始化

  const actualPath = window.location.pathname;
  if (actualPath !== route.path) {
    router.replace(actualPath);
  }
  if (permissionStore.topbarRouters.length === 0) {
    permissionStore.generateRoutes().then(() => {
      isLoading.value = false;
      // nextTick(() => {
      //   updateActiveMenu(route.fullPath);
      // });
      startRouteWatcher();
    });
  } else {
    isLoading.value = false;
    startRouteWatcher();

    // nextTick(() => {
    //   updateActiveMenu(route.fullPath);
    // });
  }

  startNotificationScroll();
});

// 组件销毁时清理
onBeforeUnmount(() => {
  stopNotificationScroll();
});
</script>

<style lang="scss" scoped>
.top-header {
  height: 64px;
  display: flex;
  align-items: center;
  background: #165dff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-sizing: border-box;

  .logo-container {
    display: flex;
    align-items: center;
    min-width: 320px;
    padding: 0 12px;

    .logo {
      width: 32px;
      height: 32px;
      object-fit: contain;
    }

    .system-name {
      color: #ffffff;
      font-size: 24px;
      letter-spacing: 0;
      line-height: 36px;
      font-weight: 400;
      margin-left: 12px;
      transform: skewX(-12deg);
    }
  }

  .nav-wrapper {
    display: flex;
    align-items: center;
    flex: 1;
    height: 64px;
    overflow-x: scroll;

    // 自定义滚动条样式（仅适用于WebKit内核浏览器）
    &::-webkit-scrollbar {
      height: 0px; // 滚动条高度
    }

    &::-webkit-scrollbar-track {
      background: transparent; // 滚动条轨道背景透明
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.3); // 滚动条滑块半透明白色
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.5); // 悬停时颜色加深
    }
    .nav-menu {
      display: flex;
      height: 100%;
      list-style: none;
      margin: 0;
      padding: 0;
      background: transparent;

      .nav-menu-item {
        margin: 0 4px;
        padding: 0 16px;
        font-weight: 500;
        height: 64px;
        line-height: 64px;
        color: rgba(255, 255, 255, 0.9);
        border-radius: 4px 4px 0 0;
        transition: all 0.2s ease;
        flex-shrink: 0;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &:hover {
          background-color: rgba(255, 255, 255, 0.05);
          color: white;
        }

        &.is-active {
          background-color: #e8f3ff;
          color: #165dff !important;
          box-shadow: 0 -2px 6px rgba(22, 93, 255, 0.2);
          font-weight: 600;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0.9),
              rgba(255, 255, 255, 0.8)
            );
            z-index: 1;
          }

          .menu-icon,
          .menu-text {
            position: relative;
            z-index: 2;
          }

          .menu-text::after {
            content: "";
            position: absolute;
            bottom: -14px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 2px;
            background-color: #165dff;
            transition: width 0.3s, background-color 0.3s;
          }
        }
      }

      .menu-icon {
        margin-right: 6px;
        font-size: 16px;
        transition: color 0.2s;
      }

      .menu-text {
        font-size: 14px;
        white-space: nowrap;
        position: relative;
      }
    }
  }

  .right-panel {
    display: flex;
    align-items: center;
    padding-right: 16px;
    flex-shrink: 0;

    .notification-area {
      width: 200px;
      margin-right: 16px;
      height: 100%;
      display: flex;
      align-items: center;

      .notification-scroll {
        height: 40px;
        overflow: hidden;
        position: relative;

        .notification-item {
          position: absolute;
          top: 0;
          left: 0;
          opacity: 0;
          transition: opacity 0.5s ease-in-out;
          display: flex;
          align-items: center;
          width: 100%;

          &.active {
            opacity: 1;
          }

          .el-icon-bell {
            color: #ffd700;
            margin-right: 8px;
          }

          span {
            color: white;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .message-notification {
      position: relative;
      cursor: pointer;
      margin-right: 16px;

      .message-icon {
        background-color: #0e42d2;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s;

        &:active {
          transform: scale(0.95);
        }

        .el-icon-bell {
          color: white;
        }
      }

      .message-count {
        position: absolute;
        top: -4px;
        right: -4px;
        background-color: #ff4d4f;
        color: white;
        font-size: 10px;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .user-info {
      cursor: pointer;

      .el-avatar {
        border: 2px solid rgba(255, 255, 255, 0.3);
        transition: all 0.2s;

        &:hover {
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  // 优化通知过渡效果
  .notification-item {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;

    &.active {
      opacity: 1;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .logo-container {
      min-width: auto;

      .system-name {
        display: none;
      }
    }

    .right-panel .notification-area {
      display: none;
    }

    .nav-menu .el-menu-item {
      margin: 0 4px;
      padding: 0 12px;
    }
  }

  @media (max-width: 768px) {
    .top-header {
      padding: 0 10px;
    }

    .nav-wrapper .nav-menu .el-menu-item {
      padding: 0 8px;
      margin: 0 2px;
    }

    .right-panel {
      margin-left: 10px;
    }
  }
}
</style>
