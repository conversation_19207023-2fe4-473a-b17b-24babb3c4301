<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 09:44:21
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-09 09:50:16
 * @FilePath     :  / src / components / Title / index.vue
-->
<template>
  <div class="common-title-container" v-bind="props">
    {{ titleName }}
    <slot name="extra"></slot>
  </div>
</template>

<script setup>
const props = defineProps({
  titleName: {
    type: String,
    default: false,
  },
});
</script>
<style lang="scss" scoped>
.common-title-container {
  position: relative;
  height: 24px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  font-weight: 500;
  padding-left: 10px;
  margin-bottom: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background: #1472ff;
  }
}
</style>
