<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 09:44:21
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-09 11:07:44
 * @FilePath     :  / src / components / SubTitle / index.vue
-->
<template>
    <div class="common-sub-title-container"><span>{{titleName}}</span><span class="unit">{{unit}}</span></div>
</template>

<script setup>

const props = defineProps({
  titleName: {
    type: String,
    default: '-',
  },
  unit: {
    type: String,
    default: '',
  }
});
</script>
<style lang="scss" scoped>
  .common-sub-title-container {
      position: relative;
      height: 24px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      font-weight: 500;
      padding-left: 10px;
      margin-bottom: 16px;
      width: calc(100% - 80px);
      padding-left: 22px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      .unit{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        font-weight: 400;
      }
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 14px;
        background: url('@/assets/images/erjibiaoqian.png');
      }
  }
</style>