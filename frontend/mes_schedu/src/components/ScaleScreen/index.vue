<template>
  <div class="screen-wrapper" ref="screenWrapper" :style="wrapperStyle">
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from "vue";

const props = defineProps({
  width: {
    type: [String, Number],
    default: 1920,
  },
  height: {
    type: [String, Number],
    default: 1080,
  },
  fullScreen: {
    type: Boolean,
    default: false,
  },
  autoScale: {
    type: [Object, Boolean],
    default: true,
  },
  selfAdaption: {
    type: Boolean,
    default: true,
  },
  delay: {
    type: Number,
    default: 100,
  },
  boxStyle: {
    type: Object,
    default: () => ({}),
  },
  wrapperStyle: {
    type: Object,
    default: () => ({}),
  },
});

const screenWrapper = ref(null);
const currentWidth = ref(0);
const currentHeight = ref(0);
const originalWidth = ref(0);
const originalHeight = ref(0);
const observer = ref(null);

const emit = defineEmits();

const debounce = (fn, delay) => {
  let timer = null;
  return function (...args) {
    timer = setTimeout(
      () => {
        typeof fn === "function" && fn.apply(null, args);
        clearTimeout(timer);
      },
      delay > 0 ? delay : 100
    );
  };
};

const initSize = async () => {
  return new Promise((resolve) => {
    const parent = screenWrapper.value.parentNode;
    parent.style.overflow = "hidden";
    parent.style.height = "calc(100vh - 105px)";
    parent.scrollLeft = 0;
    parent.scrollTop = 0;

    nextTick(() => {
      if (props.width && props.height) {
        currentWidth.value = props.width;
        currentHeight.value = props.height;
      } else {
        currentWidth.value = screenWrapper.value.clientWidth;
        currentHeight.value = screenWrapper.value.clientHeight;
      }

      if (!originalHeight.value || !originalWidth.value) {
        originalWidth.value = parent.clientWidth - 12;
        originalHeight.value = parent.clientHeight - 12;
      }
      resolve();
    });
  });
};

const updateSize = () => {
  if (currentWidth.value && currentHeight.value) {
    screenWrapper.value.style.width = `${currentWidth.value}px`;
    screenWrapper.value.style.height = `${currentHeight.value}px`;
  } else {
    screenWrapper.value.style.width = `${originalWidth.value}px`;
    screenWrapper.value.style.height = `${originalHeight.value}px`;
  }
};

const updateScale = () => {
  const parent = screenWrapper.value.parentNode;
  const parentWidth = parent.clientWidth - 12;
  const parentHeight = parent.clientHeight - 12;
  const realWidth = currentWidth.value || originalWidth.value;
  const realHeight = currentHeight.value || originalHeight.value;

  const widthScale = parentWidth / realWidth;
  const heightScale = parentHeight / realHeight;

  screenWrapper.value.style.transform = `scale(${widthScale},${heightScale})`;
  screenWrapper.value.style.margin = "0";
};

const initMutationObserver = () => {
  // 监听父元素尺寸变化
  const parent = screenWrapper.value.parentNode;

  if (parent) {
    observer.value = new ResizeObserver(() => {
      onResize();
    });
    observer.value.observe(parent);
  }
};

const clearListener = () => {
  // window.removeEventListener("resize", onResize);
};

const addListener = () => {
  // window.addEventListener("resize", onResize);
};

const clearStyle = () => {
  screenWrapper.value.parentNode.style.overflow = "auto";
  screenWrapper.value.style = "";
};

const resize = async () => {
  if (!props.selfAdaption) return;
  emit("resetOther");
  await initSize();
  updateSize();
  updateScale();
};

const onResize = debounce(() => {
  resize();
}, props.delay);

watch(
  () => props.selfAdaption,
  (val) => {
    if (val) {
      resize();
      addListener();
    } else {
      clearListener();
      clearStyle();
    }
  }
);

onMounted(() => {
  nextTick(() => {
    if (props.selfAdaption) {
      resize();
      addListener();
      initMutationObserver(); // 确保初始化时就开始监听
    }
  });
});

onBeforeUnmount(() => {
  clearListener();
  const parent = screenWrapper.value.parentNode;

  if (observer.value) {
    observer.value.unobserve(parent);
  }
});
</script>

<style scoped>
.screen-wrapper {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  position: relative;
  overflow: hidden;
  z-index: 100;
  transform-origin: left top;
}
</style>
