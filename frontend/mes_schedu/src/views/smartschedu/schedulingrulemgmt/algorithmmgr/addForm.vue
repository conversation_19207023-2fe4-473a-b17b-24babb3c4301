<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="700px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
    >
      <el-form-item label="算法名称" prop="algorithmName">
        <el-input
          v-model="form.algorithmName"
          placeholder="请输入算法名称"
          maxlength="30"
        />
      </el-form-item>
      <el-form-item label="算法编码" prop="algorithmCode">
        <el-input
          v-model="form.algorithmCode"
          placeholder="请输入算法编码"
          maxlength="30"
        />
      </el-form-item>
      <el-form-item label="调度时间" prop="algCronRule">
        <!-- <el-date-picker
          v-model="form.algCronRule"
          type="datetime"
          placeholder="请选择调度时间"
          style="width: 100%"
        /> -->
        <el-input placeholder="请输入调度时间" v-model="form.algCronRule" />
      </el-form-item>
      <el-form-item label="是否启用" prop="algStatus">
        <el-switch v-model="form.algStatus"></el-switch>
      </el-form-item>
      <el-form-item label="算法描述" prop="algDesc">
        <el-input
          v-model="form.algDesc"
          type="textarea"
          placeholder="请输入算法描述"
          maxlength="30"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm()"
          >保 存</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import { saveAlgorithmInfo } from "@/api/smartschedu/algorithm";
import { nextTick, watch } from "vue";

const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const data = reactive({
  form: { algStatus: 0 },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    algorithmCode: undefined,
    algorithmName: undefined,
    algCronRule: undefined,
    algDesc: undefined,
    algStatus: 0,
  },
  rules: {
    algStatus: [
      {
        required: true,
        message: "请选择算法状态",
        trigger: ["blur", "change"],
      },
    ],
    algorithmCode: [
      {
        required: true,
        message: "请输入算法编码",
        trigger: ["blur", "change"],
      },
    ],
    algorithmName: [
      {
        required: true,
        message: "请输入算法名称",
        trigger: ["blur", "change"],
      },
    ],
    algCronRule: [
      {
        required: true,
        message: "请输入调用时间",
        trigger: ["blur", "change"],
      },
    ],
    algDesc: [
      {
        required: true,
        message: "请输入算法描述",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          algStatus: newVal.algStatus === 1 ? true : false,
          isInit: true,
        };
      });
    }
  }
);

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    algorithmCode: undefined,
    cityName: undefined,
    algorithmName: undefined,
    algCronRule: undefined,
    algStatus: 0,
    algDesc: undefined,
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtnSave.value = true;
      saveAlgorithmInfo({
        ...form.value,
        algStatus: form.value.algStatus ? 1 : 0,
      })
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          props?.getList();
          loadingBtnSave.value = false;
        })
        .catch(() => {
          loadingBtnSave.value = false;
        });
    }
  });
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
