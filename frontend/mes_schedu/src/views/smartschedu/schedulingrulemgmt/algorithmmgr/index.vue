<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
          class="query-form"
        >
          <el-form-item label="算法名称" prop="algorithmName">
            <el-input
              v-model="queryParams.algorithmName"
              placeholder="请输入算法名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="算法编码" prop="algorithmCode">
            <el-input
              v-model="queryParams.algorithmCode"
              placeholder="请输入算法编码"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="算法状态" prop="algStatus">
            <el-select
              v-model="queryParams.algStatus"
              placeholder="请选择算法状态"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in algorithmStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item />
          <el-form-item />
          <el-form-item class="form-btn">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="24"
          style="display: flex; justify-content: space-between"
          class="mb8 table-header"
        >
          <el-col :span="12">
            <div style="width: 100%" class="table-title">调度算法列表</div>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
              >新增算法</el-button
            >
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dataList"
          @selection-change="handleSelectionChange"
          max-height="calc(100vh - 400px)"
          style="width: 100%"
          v-el-table-infinite-scroll="load"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
            fixed
          />
          <el-table-column
            label="算法编码"
            align="center"
            key="algorithmCode"
            prop="algorithmCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="算法名称"
            align="center"
            key="algorithmName"
            prop="algorithmName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="调用时间"
            align="center"
            key="algCronRule"
            prop="algCronRule"
            :show-overflow-tooltip="true"
            width="180"
          />
          <el-table-column
            label="算法描述"
            align="center"
            key="algDesc"
            prop="algDesc"
            :show-overflow-tooltip="true"
            width="220"
          />

          <el-table-column label="启用状态" align="center">
            <template #default="scope">
              <el-switch
                :model-value="scope.row.algStatus"
                :active-value="1"
                :inactive-value="0"
                @change="(value) => handleStatusChange(value, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            key="createTime"
            prop="createTime"
          />
          <el-table-column
            label="创建人"
            align="center"
            key="createBy"
            prop="createBy"
          />
          <el-table-column
            label="操作"
            align="center"
            width="150"
            class-name="small-padding fixed-width custom-action-column"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handleUpdate(scope.row, 'edit')"
                >编辑</el-button
              >
              <el-button link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
      :type="openType"
    ></add-form>
  </div>
</template>

<script setup name="Algorithmmgr">
import addForm from "./addForm.vue";
import {
  qryAlgorithmList,
  delAlgorithmInfo,
  saveAlgorithmInfo,
} from "@/api/smartschedu/algorithm";
import { algorithmStatus } from "../../common/optionsData";

import { ref, watch } from "vue";
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  algorithmName: undefined,
  algorithmCode: undefined,
  status: undefined,
});
const { proxy } = getCurrentInstance();

const dataList = ref([{}]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const openType = ref("");
const checkedKeyList = ref([]);

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryAlgorithmList({ ...queryParams.value }).then((res) => {
    loading.value = false;
    dataList.value = res.data?.data;
    total.value = res.data.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  
  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const delIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除编号为"' + delIds + '"的数据项？')
    .then(function () {
      return delAlgorithmInfo({ id: delIds });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 编辑按钮操作 */
function handleUpdate(row, type) {
  openType.value = type;
  editRecord.value = { ...row };
  title.value = `${type === "view" ? "查看" : "编辑"}调度算法`;
  visible.value = true;
}

function handleStatusChange(value, row) {
  if (!("algStatus" in row) || value === row.algStatus) {
    return;
  }
  saveAlgorithmInfo({
    ...row,
    algStatus: value ? 1 : 0,
  }).then((response) => {
    getList();
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  visible.value = true;
  openType.value = "add";
  title.value = "新增算法";
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-right-btn {
  margin-left: unset;
}
</style>
