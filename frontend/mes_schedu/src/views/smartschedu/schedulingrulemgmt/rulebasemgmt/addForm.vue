<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="700px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
    >
      <el-form-item label="规则名称" prop="algruleName">
        <el-input
          v-model="form.algruleName"
          placeholder="请输入规则名称"
          maxlength="30"
        />
      </el-form-item>
      <!-- <el-form-item label="规则编码" prop="algruleCode">
        <el-input
          v-model="form.algruleCode"
          placeholder="请输入规则编码"
          maxlength="30"
        />
      </el-form-item> -->
      <el-form-item label="规则类型" prop="algruleType">
        <el-select v-model="form.algruleType" placeholder="请选择规则类型">
          <el-option
            v-for="item in algruleType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="二级类型" prop="algruleSubtype">
        <el-select v-model="form.algruleSubtype" placeholder="请选择二级类型">
          <el-option
            v-for="item in algSubtype"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="algruleStatus">
        <el-switch v-model="form.algruleStatus"></el-switch>
      </el-form-item>
      <el-form-item label="关联算法" prop="algorithmId">
        <el-select
          v-model="form.algorithmId"
          filterable
          placeholder="请选择关联算法"
        >
          <el-option
            v-for="item in algList"
            :key="item.id"
            :label="item.algorithmName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生效区域" prop="effectiveRegion">
        <el-tree-select
          :disabled="form.id"
          :data="areaTreeList"
          v-model="form.effectiveRegion"
          placeholder="请选择生效区域"
          :props="{
            label: 'areaName',
            children: 'children',
            value: 'areaCode',
          }"
          show-checkbox
          node-key="areaCode"
          @select-change="selectedareaCodes"
          :show-checked-strategy="TreeSelect.SHOW_PARENT"
          multiple
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm()"
          >保 存</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import {
  saveAlgorithmLibrary,
  qryAlgorithmList,
} from "@/api/smartschedu/algorithm";
import { nextTick, watch, computed } from "vue";
import { algruleType, sampleType } from "../../common/optionsData";
import { getSimAreaTreeInfo } from "@/api/smartschedu/common";
import { ElTreeSelect } from "element-plus";
const selectedareaCodes = computed(() => {
  const traverse = (nodes) => {
    let codes = [];
    nodes.forEach((node) => {
      if (node.checked || node.indeterminate) {
        codes.push(node.areaCode);
      }
      if (node.children && node.children.length > 0) {
        codes = codes.concat(traverse(node.children));
      }
    });
    return codes;
  };
  return traverse(areaTreeList.value);
});
const TreeSelect = ElTreeSelect;
const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const areaTreeList = ref([]);
const algList = ref([]);
const algSubtype = ref([]);
const data = reactive({
  form: { algruleStatus: 0 },
  rules: {
    algruleName: [
      {
        required: true,
        message: "请输入规则名称",
        trigger: "blur",
      },
    ],
    algruleCode: [
      { required: true, message: "请输入规则编码", trigger: "blur" },
    ],
    algruleType: [
      {
        required: true,
        message: "请选择规则类型",
        trigger: ["blur", "change"],
      },
    ],
    algruleStatus: [
      {
        required: true,
        message: "请选择规则状态",
        trigger: ["blur", "change"],
      },
    ],
    algruleSubtype: [
      {
        required: true,
        message: "请选择二级类型",
        trigger: ["blur", "change"],
      },
    ],
    algorithmId: [
      {
        required: true,
        message: "请选择关联算法",
        trigger: ["blur", "change"],
      },
    ],
    effectiveRegion: [
      {
        required: true,
        message: "请选择生效区域",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          effectiveRegion: newVal.effectiveRegion.split(","),
          algruleStatus: newVal.algruleStatus === 1 ? true : false,
          isInit: true,
        };
      });
    }
  }
);

watch(
  () => form.value.algruleType,
  async (val) => {
    if (!form.value.isInit) {
      form.value.algruleSubtype = undefined;
      form.value.isInit = false;
    }
    const res = await proxy.useBusDict(val);
    nextTick(() => {
      algSubtype.value = res?.[val]?.value;
    });
  },
  { immediate: true, deep: true }
);

watch(
  () => form.value.algorithmId,
  (val) => {
    if (val) {
      const res = algList.value.find((item) => item.id === val);
      form.value.algorithmName = res?.algorithmName;
      form.value.algorithmCode = res?.algorithmCode;
    }
  },
  { immediate: true, deep: true }
);

function getAreaTreeList() {
  getSimAreaTreeInfo().then((response) => {
    areaTreeList.value = response.data;
  });
}
/** 查询算法列表 */
function getAlgorithmList() {
  qryAlgorithmList({ pageNo: 1, pageSize: 9999 }).then((res) => {
    algList.value = res.data?.data;
  });
}
/** 重置操作表单 */
function reset() {
  form.value = {
    algruleName: undefined,
    algruleCode: undefined,
    algruleType: undefined,
    algruleStatus: undefined,
    algruleSubtype: undefined,
    algorithmId: undefined,
    effectiveRegion: undefined,
  };
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtnSave.value = true;
      saveAlgorithmLibrary({
        ...form.value,
        effectiveRegion: form.value.effectiveRegion.join(","),
      })
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          props?.getList();
          loadingBtnSave.value = false;
        })
        .catch(() => {
          loadingBtnSave.value = false;
        });
    }
  });
}

getAlgorithmList();
getAreaTreeList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
