<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
          class="query-form"
        >
          <el-form-item label="算法名称" prop="algorithmName">
            <el-input
              v-model="queryParams.algorithmName"
              placeholder="请输入算法名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="算法编码" prop="algorithmCode">
            <el-input
              v-model="queryParams.algorithmCode"
              placeholder="请输入算法编码"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in algorithmStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="调用日期" prop="invokeDate">
            <el-date-picker
              type="daterange"
              v-model="queryParams.invokeDate"
              placeholder="选择调用日期"
              style="width: 240px"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="shortcuts"
            />
          </el-form-item>
          <el-form-item class="form-btn">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="24"
          style="display: flex; justify-content: space-between"
          class="mb8 table-header"
        >
          <el-col :span="12">
            <div style="width: 100%" class="table-title">算法调用情况列表</div>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="Download"
              @click="exportList"
              v-hasPermi="['system:user:add']"
              >导 出</el-button
            >
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dataList"
          @selection-change="handleSelectionChange"
          max-height="calc(100vh - 400px)"
          style="width: 100%"
          v-el-table-infinite-scroll="load"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
            fixed
          />
          <el-table-column
            label="算法编码"
            align="center"
            key="algorithmCode"
            prop="algorithmCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="算法名称"
            align="center"
            key="algorithmName"
            prop="algorithmName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="调用日期"
            align="center"
            key="invokeDate"
            prop="invokeDate"
            :show-overflow-tooltip="true"
            width="180"
          />
          <el-table-column
            label="调用时间"
            align="center"
            key="invokeTime"
            prop="invokeTime"
            :show-overflow-tooltip="true"
            width="180"
          />
          <el-table-column
            label="处理时长"
            align="center"
            key="avgDuration"
            prop="avgDuration"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="处理计划数"
            align="center"
            key="planCount"
            prop="planCount"
          />
          <el-table-column
            label="生成任务数"
            align="center"
            key="taskCount"
            prop="taskCount"
          />
          <el-table-column
            label="状态"
            align="center"
            key="status"
            prop="status"
          />

          <!-- <el-table-column
            label="操作"
            align="center"
            width="150"
            class-name="small-padding fixed-width custom-action-column"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handleUpdate(scope.row, 'view')"
                >详情</el-button
              >
            </template>
          </el-table-column> -->
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
      :type="openType"
    ></add-form>
  </div>
</template>

<script setup name="Algorithminvocation">
import addForm from "./addForm.vue";
import {
  qryAlgorithmCallList,
  exportAlgorithmCall,
} from "@/api/smartschedu/algorithm";
import { algorithmStatus } from "../../common/optionsData";
import { downloadFile } from "@/utils/index";
import { ref } from "vue";
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  algorithmName: undefined,
  algorithmCode: undefined,
  status: undefined,
});
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const editRecord = ref(null);
const openType = ref("");

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryAlgorithmCallList({ ...queryParams.value }).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 导出按钮操作 */
function exportList() {
  exportAlgorithmCall({
    algorithmCode: queryParams.value.algorithmCode,
    algorithmName: queryParams.value.algorithmName,
    invokeDate: queryParams.value.invokeDate,
  }).then((res) => {
    if (res) {
      // 获取请求返回的 headers
      downloadFile(res, "算法调用执行记录.xlsx");
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  //   reset();
  //   visible.value = true;
  //   openType.value = "add";
  //   title.value = "新增规则配置";
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-right-btn {
  margin-left: unset;
}
</style>
