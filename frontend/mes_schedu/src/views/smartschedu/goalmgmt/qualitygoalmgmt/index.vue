<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16">
          <area-tree @handleCheck="handleCheckChange"></area-tree>
        </pane>
        <!--用户数据-->
        <pane class="table-container" size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="100px"
              class="query-form"
            >
              <common-form-search
                :needActivity="false"
                v-model:queryParams="queryParams"
              />
              <el-form-item label="监测参数" prop="monitIndex">
                <el-select
                  v-model="queryParams.monitIndex"
                  placeholder="监测参数"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in monitorParams"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="年度" prop="statYear">
                <el-date-picker
                  v-model="queryParams.statYear"
                  type="year"
                  placeholder="选择年度"
                  value-format="YYYY"
                  style="width: 240px"
                ></el-date-picker>
              </el-form-item>
              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row
              :gutter="24"
              style="display: flex; justify-content: space-between"
              class="mb8 table-header"
            >
              <el-col :span="12">
                <div style="width: 100%" class="table-title">
                  质量目标管理列表
                </div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['system:user:add']"
                  >新增质量目标</el-button
                >
              </el-col>
              <!-- <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
                :columns="columns"
              ></right-toolbar> -->
            </el-row>

            <el-table
              v-loading="loading"
              :data="dataList"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
              @selection-change="handleSelectionChange"
              max-height="calc(100vh - 460px)"
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
                fixed
                v-if="columns[0].visible"
              />
              <el-table-column
                label="年度"
                align="center"
                key="statYear"
                prop="statYear"
                width="120"
              />
              <el-table-column
                label="省"
                align="center"
                key="provinceName"
                prop="provinceName"
                v-if="columns[1].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="市"
                align="center"
                key="cityName"
                prop="cityName"
                v-if="columns[2].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="站点名称"
                align="center"
                key="siteName"
                prop="siteName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="站点类型"
                align="center"
                v-if="columns[4].visible"
                width="160"
              >
                <template #default="scope">
                  <span>
                    {{
                      scope.row.siteType === "01" &&
                      scope.row.businessType === "water" &&
                      scope.row.hasAutomaticStation === "N"
                        ? "水断面"
                        : scope.row.siteTypeName
                    }}
                    {{
                      scope.row.hasAutomaticStation === "Y" &&
                      scope.row.businessType === "water"
                        ? "(自动站)"
                        : ""
                    }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                label="业务分类"
                align="center"
                key="businessTypeName"
                prop="businessTypeName"
                v-if="columns[5].visible"
              />
              <el-table-column
                label="监测参数"
                align="center"
                key="monitIndexName"
                prop="monitIndexName"
                v-if="columns[6].visible"
                width="190"
              />
              <el-table-column label="准确度" align="center" width="160">
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.accuracyResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.accuracy) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="精密度" align="center" width="160">
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.precisionResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.precision) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="数据有效率"
                align="center"
                key="effectivenessRate"
                prop="effectivenessRate"
                v-if="columns[8].visible"
                width="160"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.effectivenessRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.effectivenessRate) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="质控合格率"
                align="center"
                key="quactrlPassRate"
                prop="quactrlPassRate"
                v-if="queryParams.businessType === 'water'"
                width="160"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.quactrlPassRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.quactrlPassRate) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="数据获取率"
                align="center"
                key="captureRate"
                prop="captureRate"
                v-if="queryParams.businessType === 'air'"
                width="160"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.captureRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.captureRate) }}
                  </div>
                </template></el-table-column
              >
              <el-table-column
                label="创建人"
                align="center"
                key="createBy"
                prop="createBy"
                v-if="columns[10].visible"
                width="120"
              />
              <el-table-column
                label="创建时间"
                align="center"
                key="createTime"
                prop="createTime"
                v-if="columns[11].visible"
                width="160"
              />
              <el-table-column
                label="修改时间"
                align="center"
                key="updateTime"
                prop="updateTime"
                v-if="columns[12].visible"
                width="160"
              />
              <el-table-column
                label="操作"
                align="center"
                width="150"
                class-name="small-padding fixed-width"
                fixed="right"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:user:edit']"
                    >编辑</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:user:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <add-form
      v-if="open"
      :title="modalTitle"
      @formSubmitted="getList"
      v-model:open="open"
      :editRecord="editRecord"
    >
    </add-form>
  </div>
</template>

<script setup name="Qualitygoalmgmt">
import useAppStore from "@/store/modules/app";
import {
  delQualityTarget,
  qryQualityTargetList,
} from "@/api/smartschedu/target";
import addForm from "./addForm.vue";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import { watch } from "vue";
import areaTree from "@/views/smartschedu/component/areaTree.vue";
import commonFormSearch from "@/views/smartschedu/component/commonFormSearch";
import dayjs from "dayjs";

const emit = defineEmits();
const monitorParams = ref([]);

const appStore = useAppStore();
const { proxy } = getCurrentInstance();
const dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const modalTitle = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const checkedKeyList = ref([]);
const siteIds = ref([]);
const water_quality_index = ref([]);
const gas_quality_index = ref([]);

// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `省`, visible: true },
  { key: 2, label: `市`, visible: true },
  { key: 3, label: `站点名称`, visible: true },
  { key: 4, label: `站点类型`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `业务分类`, visible: true },
  { key: 7, label: `监测参数`, visible: true },
  { key: 8, label: `偏离目标`, visible: true },
  { key: 9, label: `当前偏离`, visible: true },
  { key: 11, label: `创建人`, visible: true },
  { key: 12, label: `创建时间`, visible: true },
  { key: 13, label: `修改时间`, visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    monitIndex: undefined,
  },
});

const { queryParams, form } = toRefs(data);

watch(
  [() => water_quality_index.value, () => gas_quality_index.value],
  ([newVal, newVal2]) => {
    if (
      monitorParams.value.length === 0 &&
      newVal.length > 0 &&
      newVal2.length > 0
    ) {
      monitorParams.value = [...newVal, ...newVal2];
    }
  }
);
// 处理查询表单联动逻辑
watch(
  [() => queryParams.value.businessType],
  ([newBusinessType], [oldBusinessType]) => {
    if (newBusinessType !== oldBusinessType) {
      // 监测参数
      queryParams.value.monitIndex = "";
      getList();
      if (newBusinessType !== oldBusinessType) {
        if (!newBusinessType) {
          monitorParams.value = [
            ...water_quality_index.value,
            ...gas_quality_index.value,
          ];
        } else {
          monitorParams.value =
            newBusinessType === "water"
              ? water_quality_index.value
              : gas_quality_index.value;
        }
      }
    }
  },
  { deep: true }
);

function getPercent(value) {
  if (!value) return "0%";

  return (value * 100).toFixed(2) + "%";
}

async function getInitData() {
  const res = await proxy.useBusDict(
    "water_quality_index",
    "gas_quality_index"
  );
  monitorParams.value = [
    ...res?.water_quality_index.value,
    ...res?.gas_quality_index.value,
  ];
  water_quality_index.value = res?.water_quality_index.value;
  gas_quality_index.value = res?.gas_quality_index.value;
}

const handleCheckChange = (obj, checkedKeys) => {
  const cityCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "city")
    ?.map((item) => item.id);
  const siteId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "site")
    ?.map((item) => item.id);
  siteIds.value = siteId;
  checkedKeyList.value = cityCode;
  getList();
};

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryQualityTargetList({
    ...queryParams.value,
    cityCode: checkedKeyList.value?.join(","),
    siteId: siteIds.value?.join(","),
    startTime: dateRange.value[0] ? proxy.parseTime(dateRange.value[0]) : "",
    endTime: dateRange.value[1]
      ? proxy.parseTime(dayjs(dateRange.value[1]).endOf("day").toDate())
      : "",
  }).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  //
  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除该条数据项？")
    .then(function () {
      return delQualityTarget(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    monitIndex: undefined,
    activitySubtype: undefined,
    qualityTarget: undefined,
  };
  editRecord.value = {};
  proxy.resetForm("siteRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  modalTitle.value = `新增质量目标`;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  editRecord.value = { ...row };
  open.value = true;
  modalTitle.value = `修改质量目标`;
}

getInitData();
getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}

.percent {
  display: inline-block;
  min-width: 60px;
  &.left {
    text-align: left;
  }
  &.right {
    text-align: right;
  }
}
</style>
