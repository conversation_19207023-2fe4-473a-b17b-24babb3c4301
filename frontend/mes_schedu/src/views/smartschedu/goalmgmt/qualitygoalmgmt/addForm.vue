<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="800px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="siteRef"
      class="el-form--inline"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="年度" prop="statYear">
        <el-date-picker
          v-model="form.statYear"
          type="year"
          placeholder="选择年份"
          value-format="YYYY"
          style="width: 240px"
          :disabled="editRecord.id"
        ></el-date-picker>
      </el-form-item>
      <common-form
        v-model:form="form"
        :editRecord="editRecord"
        :disabled="editRecord.id"
      ></common-form>

      <el-form-item label="监测参数" prop="monitIndex">
        <el-select
          v-model="form.monitIndex"
          value-key="id"
          placeholder="监测参数"
          :disabled="editRecord.id"
        >
          <el-option
            v-for="dict in monitorParams"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="准确度" prop="accuracy">
        <el-input
          v-model="form.accuracy"
          placeholder="请输入准确度"
          maxlength="30"
          :disabled="false"
        />
      </el-form-item>
      <el-form-item label="精密度" prop="precision">
        <el-input
          v-model="form.precision"
          placeholder="请输入精密度"
          maxlength="30"
          :disabled="false"
        />
      </el-form-item>
      <el-form-item :disabled="false" label="不确定度" prop="uncertainty">
        <el-input
          v-model="form.uncertainty"
          placeholder="请输入不确定度"
          maxlength="30"
          :disabled="false"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import commonForm from "../../component/commonForm.vue";
import { getSiteType, getBusinessType } from "@/api/smartschedu/common";

const props = defineProps({
  rules: { type: Object, default: () => {} },
  title: { type: String, default: "" },
  open: {
    type: Boolean,
    default: false,
  },
  editRecord: { type: Object, default: () => {} },
});
import { updateQualityTarget } from "@/api/smartschedu/target";
const emit = defineEmits();

const { proxy } = getCurrentInstance();

const loadingBtn = ref(false);
const siteTypeOptions = ref(undefined);
const businessTypeOptions = ref(undefined);

const water_quality_index = ref([]);
const gas_quality_index = ref([]);
const monitorParams = ref([]);

const data = reactive({
  form: {},
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    monitIndex: [
      {
        required: true,
        message: "请选择监测参数",
        trigger: ["blur", "change"],
      },
    ],
    uncertainty: [
      { required: true, message: "请输入不确定度", trigger: "blur" },
    ],
    accuracy: [{ required: true, message: "请输入准确度", trigger: "blur" }],
    precision: [{ required: true, message: "请输入精密度", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
    statYear: [
      {
        required: true,
        message: "请选择年份",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { form, rules } = toRefs(data);

// 回填表单数据
watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      initData(newVal);
    }
  },
  { deep: true }
);

function initData(newVal) {
  const isAuto = newVal?.hasAutomaticStation === "Y" ? "1" : "0";
  nextTick(() => {
    form.value = {
      ...newVal,
      statYear: (newVal?.statYear || "") + "",
      isAutosite: newVal?.hasAutomaticStation ? isAuto : "",
      isInit: true,
    };
  });
}

async function getInitData() {
  const res = await proxy.useBusDict(
    "water_quality_index",
    "gas_quality_index"
  );
  monitorParams.value = [
    ...res?.water_quality_index.value,
    ...res?.gas_quality_index.value,
  ];
  water_quality_index.value = res?.water_quality_index.value;
  gas_quality_index.value = res?.gas_quality_index.value;
}
function getSiteTypeList() {
  getSiteType().then((response) => {
    siteTypeOptions.value = response.data;
  });
}

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}

// 处理监测参数逻辑
watch(
  [() => water_quality_index.value, () => gas_quality_index.value],
  ([newVal, newVal2]) => {
    if (
      monitorParams.value.length === 0 &&
      newVal.length > 0 &&
      newVal2.length > 0
    ) {
      monitorParams.value = [...newVal, ...newVal2];
    }
  }
);

// 处理表单联动逻辑
watch(
  [
    () => form.value.businessType,
    () => form.value.siteType,
    () => form.value.cityCode,
    () => form.value.provinceCode,
  ],
  (
    [newBusinessType, newSiteType, newCityCode, newProvinceCode],
    [oldBusinessType, oldSiteType, oldCityCode, oldProvinceCode]
  ) => {
    if (form.value.isInit) {
      form.value.isInit = false;
      return;
    }
    if (newBusinessType !== oldBusinessType) {
      form.value.monitIndex = "";
      form.value.siteType = undefined;

      if (newBusinessType !== oldBusinessType) {
        if (!newBusinessType) {
          monitorParams.value = [
            ...water_quality_index.value,
            ...gas_quality_index.value,
          ];
        } else {
          monitorParams.value =
            newBusinessType === "water"
              ? water_quality_index.value
              : gas_quality_index.value;
        }
      }
    }
    if (
      newCityCode != oldCityCode ||
      newProvinceCode != oldProvinceCode ||
      newSiteType != oldSiteType
    ) {
      if (newProvinceCode !== oldProvinceCode) {
        form.value.cityCode = undefined;
      }
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
      }
      form.value.siteId = undefined;
    }
  },
  { deep: true }
);

/** 取消按钮 */
function cancel() {
  emit("update:open", false);
  reset();
}

function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    monitIndex: undefined,
    activitySubtype: undefined,
    qualityTarget: undefined,
    year: undefined,
  };

  proxy.resetForm("siteRef");
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtn.value = true;

      // 如果是水断面，则isAutosite为0,如果选则了站点，则根据站点的类型判断
      const isAutosite =
        form.value.siteType === -100 ? "0" : form.value.isAutosite;

      const requestObj = {
        ...form.value,
        siteType: form.value?.siteTypeAlias,
      };
      // 业务类型为水才会传isAutosite
      if (form.value.businessType === "water") {
        requestObj.isAutosite = isAutosite;
      }
      updateQualityTarget(requestObj).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        emit("update:open", false);
        emit("formSubmitted");
        loadingBtn.value = false;
      });
    }
  });
}

initData(props.editRecord);
getInitData();
getSiteTypeList();
getBusinessTypeList();
</script>
