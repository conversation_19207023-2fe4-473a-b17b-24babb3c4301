<template>
  <div class="app-container">
    <div v-if="openFiles">
      <div class="head-container">
        <span>年度数量目标文件</span>
        <el-button @click="handleCloseFiles">关闭</el-button>
      </div>
      <year-quantity-goal
        v-model="openFiles"
        :businessTypeOptions="businessTypeOptions"
      />
    </div>
    <el-row v-else :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16">
          <el-col style="height: 100%">
            <el-tabs
              v-model="treeType"
              @tab-click="handleClick"
              class="custom-tabs"
              type="border-card"
            >
              <el-tab-pane label="行政区域" name="area"
                ><div class="head-container" style="height: 100%">
                  <area-tree
                    ref="areaTreeRef"
                    @handleCheck="handleCheckChange"
                    :customTreeList="showAreaList"
                  ></area-tree></div
              ></el-tab-pane>
              <el-tab-pane label="片区" nonce="package">
                <div style="padding: 0 10px">
                  <el-input
                    v-model="deptName"
                    placeholder="请输入"
                    clearable
                    prefix-icon="Search"
                    style="margin-bottom: 20px"
                  />
                </div>
                <el-tree
                  :data="
                    showType === '1' ? simplePackageTreeList : packageTreeList
                  "
                  show-checkbox
                  :props="{
                    label: 'name',
                    children: 'children',
                    value: 'id',
                  }"
                  :expand-on-click-node="false"
                  :filter-node-method="filterNode"
                  ref="packageTreeRef"
                  node-key="id"
                  highlight-current
                  default-expand-all
                  @node-click="handleNodeClick"
                  @check="handleCheckChange"
                  style="width: 100%"
              /></el-tab-pane>
            </el-tabs>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane class="table-container" size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="100px"
              class="query-form"
            >
              <common-form-search
                :needActivity="true"
                v-model:queryParams="queryParams"
                :isRegular="true"
              />
              <el-form-item label="年度" prop="statYear">
                <el-date-picker
                  v-model="queryParams.statYear"
                  type="year"
                  placeholder="选择年度"
                  value-format="YYYY"
                  style="width: 240px"
                ></el-date-picker>
              </el-form-item>
              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row
              :gutter="24"
              style="display: flex; justify-content: space-between"
              class="mb8 table-header"
            >
              <el-col :span="12">
                <div style="width: 100%" class="table-title">
                  <el-tabs
                    @tab-click="handleTabClick"
                    v-model="showType"
                    class="custom-tabs-noborder"
                  >
                    ">
                    <el-tab-pane label="区域数量目标列表" name="1">
                    </el-tab-pane>
                    <el-tab-pane
                      label="点位数量目标列表"
                      name="2"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button
                  type="primary"
                  icon="Plus"
                  v-if="showType === '2'"
                  @click="handleAdd"
                  v-hasPermi="['system:user:add']"
                  >新增数量目标</el-button
                >
                <el-button
                  type="primary"
                  icon="Search"
                  @click="handleFiles"
                  v-hasPermi="['system:user:edit']"
                  >年度数量目标文件</el-button
                >
              </el-col>
            </el-row>

            <el-table
              v-loading="loading"
              :data="dataList"
              @selection-change="handleSelectionChange"
              max-height="calc(100vh - 460px)"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
                fixed
              />
              <el-table-column
                label="年度"
                align="center"
                key="statYear"
                prop="statYear"
                width="120"
              />
              <el-table-column
                label="省"
                align="center"
                key="provinceName"
                prop="provinceName"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="市"
                align="center"
                key="cityName"
                prop="cityName"
                v-if="showType === '2'"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="业务分类"
                align="center"
                key="businessTypeName"
                prop="businessTypeName"
                width="120"
              />
              <el-table-column label="站点类型" align="center" width="160">
                <template #default="scope">
                  <span>
                    {{
                      scope.row.siteType === "01" &&
                      scope.row.businessType === "water" &&
                      scope.row.hasAutomaticStation === "N"
                        ? "水断面"
                        : scope.row.siteTypeName
                    }}
                    {{
                      scope.row.hasAutomaticStation === "Y" &&
                      scope.row.businessType === "water"
                        ? "(自动站)"
                        : ""
                    }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column
                label="站点名称"
                align="center"
                key="siteName"
                prop="siteName"
                v-if="showType === '2'"
                :show-overflow-tooltip="true"
                width="120"
              />

              <el-table-column
                label="监测活动大类"
                align="center"
                key="activityTypeName"
                prop="activityTypeName"
                minWidth="160"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="监测活动小类"
                align="center"
                key="activitySubtypeName"
                prop="activitySubtypeName"
                minWidth="160"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="目标值"
                align="center"
                key="targetValue"
                prop="targetValue"
                width="120"
              />
              <el-table-column
                label="已完成"
                align="center"
                key="completeNum"
                prop="completeNum"
                width="120"
              />
              <template v-if="showType === '2'">
                <el-table-column
                  label="创建人"
                  align="center"
                  key="createBy"
                  prop="createBy"
                  width="120"
                />
                <el-table-column
                  label="创建时间"
                  align="center"
                  key="createTime"
                  prop="createTime"
                  width="160"
                />
                <el-table-column
                  label="修改时间"
                  align="center"
                  key="updateTime"
                  prop="updateTime"
                  width="160"
                />
                <el-table-column
                  label="操作"
                  align="center"
                  width="150"
                  class-name="small-padding fixed-width"
                  fixed="right"
                >
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      @click="handleUpdate(scope.row)"
                      >编辑</el-button
                    >
                    <el-button
                      link
                      type="primary"
                      @click="handleDelete(scope.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <add-form
      v-if="visible"
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
    ></add-form>
  </div>
</template>

<script setup name="Quantitygoalmgmt">
import useAppStore from "@/store/modules/app";
import yearQuantityGoal from "./yearQuantityGoal.vue";
import addForm from "./addForm.vue";
import {
  qryNumTargetList,
  delNumTarget,
  qryCollectNumTargetList,
} from "@/api/smartschedu/target";
import commonFormSearch from "@/views/smartschedu/component/commonFormSearch";

import {
  getAreaTreeInfo,
  getPackageTreeInfo,
  getRegionProvinceTreeInfo,
  getPackageProvinceTreeInfo,
} from "@/api/smartschedu/common";
import { Splitpanes, Pane } from "splitpanes";
import areaTree from "../../component/areaTree.vue";
import "splitpanes/dist/splitpanes.css";
import { watch } from "vue";
import { ref } from "vue";

const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const openFiles = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const deptName = ref("");
const areaTreeList = ref(undefined);
const packageTreeList = ref(undefined);
const editRecord = ref(null);
const checkedKeyList = ref([]);
const siteIds = ref([]);
const packageIds = ref([]);
const areaTreeRef = ref(null);
const packageTreeRef = ref(null);
const showType = ref("1");
const treeType = ref("area");
const showAreaList = ref([]);
const simpleAreaTreeList = ref([]);
const simplePackageTreeList = ref([]);
const provinceIds = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    activitySubtype: [
      {
        required: true,
        message: "请选择监测活动小类",
        trigger: ["blur", "change"],
      },
    ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

const handleCheckChange = (obj, checkedKeys) => {
  const cityCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "city")
    ?.map((item) => item.id);
  const siteId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "site")
    ?.map((item) => item.id);
  const packageId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "package")
    ?.map((item) => item.id);
  const provinceCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "province" || item.type === "root")
    ?.map((item) => item.id);
  siteIds.value = siteId;
  packageIds.value = packageId;
  checkedKeyList.value = cityCode;
  provinceIds.value = provinceCode;
  getList();
};

function handleClick(tab) {
  areaTreeRef.value?.areaTreeRef.setCheckedKeys([]);
  packageTreeRef.value.setCheckedKeys([]);
  checkedKeyList.value = [];
  provinceIds.value = [];
  siteIds.value = [];
  packageIds.value = [];
  getList();
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.areaName.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  proxy.$refs["areaTreeRef"].filter(val);
});

function handleTabClick(tab) {
  showAreaList.value = tab?.props?.name === "1" ? simpleAreaTreeList.value : [];
}

/** 查询用户列表 */
function getList() {
  loading.value = true;
  const req = {
    ...queryParams.value,
    cityCode: checkedKeyList.value?.join(","),
    siteId: siteIds.value?.join(","),
    packageId: packageIds.value?.join(","),
  };
  if (showType.value === "1") {
    req.provinceCode = provinceIds.value?.join(",");
  }
  (showType.value === "1" ? qryCollectNumTargetList : qryNumTargetList)(
    req
  ).then((res) => {
    loading.value = false;
    dataList.value = res.rows;
    total.value = res.total;
  });
}

function getAreaTreeList() {
  getAreaTreeInfo().then((response) => {
    areaTreeList.value = [response.data];
  });
}
function getPackageTreeList() {
  getPackageTreeInfo().then((response) => {
    packageTreeList.value = response.data;
  });
}

function getSimpleAreaTreeList() {
  getRegionProvinceTreeInfo().then((response) => {
    showAreaList.value = [response.data];
    simpleAreaTreeList.value = [response.data];
  });
}

function getSimplePackageTreeList() {
  getPackageProvinceTreeInfo().then((response) => {
    simplePackageTreeList.value = response.data;
  });
}

function handleCloseFiles() {
  openFiles.value = false;
}
/** 节点单击事件 */
async function handleNodeClick(data) {
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除该条数据项？")
    .then(function () {
      return delNumTarget(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  editRecord.value = {};
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  visible.value = true;
  title.value = "新增数量目标";
}

/** 新增按钮操作 */
function handleFiles() {
  reset();
  openFiles.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  editRecord.value = { ...row };
  title.value = "修改数量目标";
  visible.value = true;
}

getList();
getAreaTreeList();
getPackageTreeList();
getSimpleAreaTreeList();
getSimplePackageTreeList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  height: calc(100% - 52px);
  .el-tabs__content {
    padding: 15px 0;
  }
  .el-tab-pane {
    height: 100%;
  }
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
:deep(.custom-tabs-noborder) {
  height: 43px;
  --el-tabs-header-height: 30px;
  .el-tabs__nav-wrap:after {
    height: 0;
  }
}
</style>
