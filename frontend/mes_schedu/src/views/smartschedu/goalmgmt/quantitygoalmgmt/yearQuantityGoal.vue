<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
          class="query-form"
        >
          <el-form-item label="业务分类" prop="businessType">
            <el-select
              v-model="queryParams.businessType"
              placeholder="业务分类"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in businessTypeOptions"
                :key="dict.dictCode"
                :label="dict.dictValue"
                :value="dict.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="form-btn">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="10"
          style="display: flex; justify-content: flex-end"
          class="mb8 table-header"
        >
          <el-col style="margin-right: 6px" :span="1.5">
            <el-button
              type="primary"
              icon="Upload"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
              >文件上传</el-button
            >
          </el-col>
          <!-- <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
            :columns="columns"
          ></right-toolbar> -->
        </el-row>

        <el-table
          v-loading="loading"
          :data="fileList"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          v-el-table-infinite-scroll="load"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
            v-if="columns[0].visible"
          />
          <el-table-column
            label="业务分类"
            align="center"
            key="businessTypeName"
            prop="businessTypeName"
            v-if="columns[5].visible"
          />
          <el-table-column
            label="年度"
            align="center"
            key="statYear"
            prop="statYear"
            v-if="columns[5].visible"
          />
          <el-table-column
            label="文件名称"
            align="center"
            key="fileName"
            prop="fileName"
            v-if="columns[5].visible"
            width="350"
          />
          <el-table-column
            label="创建人"
            align="left"
            key="createBy"
            prop="createBy"
            v-if="columns[5].visible"
          />
          <el-table-column
            label="创建时间"
            align="center"
            key="createTime"
            prop="createTime"
            v-if="columns[5].visible"
          />
          <el-table-column
            label="操作"
            align="center"
            width="150"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)"
                >预览</el-button
              >
              <el-button link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加文件对话框 -->
    <el-dialog
      title="文件上传"
      v-model="open"
      align-center
      width="700px"
      append-to-body
    >
      <el-form
        :model="form"
        :disabled="form.id"
        :rules="rules"
        ref="uploadRef"
        label-width="110px"
      >
        <el-row gut="20">
          <el-col :span="20">
            <el-form-item label="业务分类" prop="businessType">
              <el-select
                v-model="form.businessType"
                placeholder="业务分类"
                clearable
              >
                <el-option
                  v-for="dict in businessTypeOptions"
                  :key="dict.dictCode"
                  :label="dict.dictValue"
                  :value="dict.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-model="form.fileName" placeholder="请输入文件名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="年度" prop="statYear">
              <!-- <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              ></el-date-picker> -->
              <el-select v-model="form.statYear" placeholder="请选择" clearable>
                <el-option
                  v-for="dict in yearOptions"
                  :key="dict"
                  :label="dic"
                  :value="dict"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="上传附件" prop="storageAddress">
              <file-upload
                action="/file/upload"
                ref="upload"
                v-model="form.storageAddress"
                v-model:uploadFile="uploadFile"
                :limit="1"
                :file-type="['pdf']"
                :file-size="5"
                :disabled="form.id"
                @change="handleChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!form.id"
            :loading="loadingBtn"
            type="primary"
            @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="文件预览"
      v-model="previewOpen"
      align-center
      width="1024px"
      append-to-body
    >
      <div class="preview-container">
        <div class="preview-content">
          <doc-viewer :fileUrl="previewDocuments" />
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelView">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="QuantityGoal">
import {
  qryNumTargetFileList,
  saveNumTargetFile,
  delNumTargetFile,
} from "@/api/smartschedu/target";

import "splitpanes/dist/splitpanes.css";
import DocViewer from "@/components/DocViewer/index.vue";
import { getBusinessType } from "@/api/smartschedu/common";
import { ref } from "vue";
import dayjs from "dayjs";
const { proxy } = getCurrentInstance();

const businessTypeOptions = ref([]);
const currentYear = new Date().getFullYear();

const fileList = ref([]);
const open = ref(false);
const loading = ref(true);
const loadingBtn = ref(false);
const previewOpen = ref(false);
const showSearch = ref(true);
const total = ref(0);
const previewDocuments = ref("");
const dateRange = ref([]);
const uploadFile = ref(undefined);
const yearOptions = ref([
  currentYear - 1, // 去年
  currentYear, // 今年
  currentYear + 1, // 明年
]); // 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `用户名称`, visible: true },
  { key: 2, label: `用户昵称`, visible: true },
  { key: 3, label: `部门`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `创建时间`, visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined,
  },
  rules: {
    businessType: [
      { required: true, message: "请选择业务类型", trigger: "blur" },
    ],
    year: [{ required: true, message: "请选择年度", trigger: "blur" }],
    fileName: [{ required: true, message: "文件名不能为空", trigger: "blur" }],
    storageAddress: [
      { required: true, message: "请上传附件", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}
/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryNumTargetFileList({
    businessType: queryParams.value.businessType,
    startTime: proxy.parseTime(dateRange.value[0]),
    endTime: dateRange.value[1]
      ? proxy.parseTime(dayjs(dateRange.value[1]).endOf("day").toDate())
      : "",
  }).then((res) => {
    loading.value = false;
    fileList.value = res.data.data;
    total.value = res.data?.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 重置操作表单 */
function reset() {
  form.value = {
    businessType: "water",
    year: undefined,
    fileName: undefined,
    storageAddress: undefined,
    id: undefined,
  };
  proxy.resetForm("uploadRef");
}

function handleAdd() {
  reset();
  open.value = true;
}

function handleDelete(row) {
  const userIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除该条数据项？')
    .then(function () {
      return delNumTargetFile(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleView(row) {
  reset();
  previewDocuments.value = row.id;
  previewOpen.value = true;
}

function cancel() {
  open.value = false;
}

function cancelView() {
  previewOpen.value = false;
}

function submitForm() {
  proxy.$refs["uploadRef"].validate((valid) => {
    if (valid) {
      loadingBtn.value = true;
      saveNumTargetFile({
        ...form.value,
        attachName: uploadFile.value.name,
        attachType: "01",
        attachSize: uploadFile.value.size,
        attachPath: form.value.storageAddress,
      }).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
        loadingBtn.value = false;
      });
    }
  });
}

getBusinessTypeList();
getList();
</script>
<style scoped>
.top-right-btn {
  margin-left: unset;
}
.app-container {
  height: calc(100vh - 204px);
}
</style>
