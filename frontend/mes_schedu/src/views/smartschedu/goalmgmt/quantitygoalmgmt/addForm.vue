<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="800px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="siteRef"
      class="el-form--inline"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="年度" prop="statYear">
        <el-date-picker
          v-model="form.statYear"
          type="year"
          placeholder="选择年份"
          value-format="YYYY"
          style="width: 240px"
          :disabled="editRecord.id"
        ></el-date-picker>
      </el-form-item>
      <common-form
        v-model:form="form"
        :editRecord="editRecord"
        :disabled="editRecord.id"
      ></common-form>

      <el-form-item label="监测活动大类" prop="activityType">
        <el-select
          v-model="form.activityType"
          placeholder="请选择监测活动大类"
          :disabled="editRecord.id"
        >
          <el-option
            v-for="dict in activityParentTypeOptions"
            :key="dict.activityTypeCode + dict.businessType"
            :label="dict.activityTypeName"
            :value="dict.activityTypeCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="监测活动小类" prop="activitySubtype">
        <el-select
          v-model="form.activitySubtype"
          placeholder="请选择监测活动小类"
          clearable
          style="width: 240px"
          :disabled="editRecord.id"
        >
          <el-option
            v-for="dict in activityTypeOptions"
            :key="dict.activitySubtypeCode"
            :label="dict.activitySubtypeName"
            :value="dict.activitySubtypeCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="目标值" prop="targetValue">
        <el-input
          v-model="form.targetValue"
          placeholder="请输入目标值"
          maxlength="30"
          :disabled="false"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loadingBtn" @click="submitForm"
          >确 定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import commonForm from "../../component/commonForm.vue";
import { saveNumTarget } from "@/api/smartschedu/target";
import {
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { nextTick, watch } from "vue";
const { proxy } = getCurrentInstance();

const emit = defineEmits();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtn = ref(false);
const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);

const data = reactive({
  form: {},
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    activitySubtype: [
      {
        required: true,
        message: "请选择监测活动小类",
        trigger: ["blur", "change"],
      },
    ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
    statYear: [
      {
        required: true,
        message: "请选择年份",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      initData(newVal);
    }
  },
  { deep: true }
);

watch(
  [
    () => form.value.businessType,
    () => form.value.siteType,
    () => form.value.activityType,
    () => form.value.cityCode,
    () => form.value.provinceCode,
    () => form.value.isAutosite,
  ],
  (
    [
      newBusinessType,
      newSiteType,
      newActivityType,
      newCityCode,
      newProvinceCode,
      newIsAutosite,
    ],
    [
      oldBusinessType,
      oldSiteType,
      oldActivityType,
      oldCityCode,
      oldProvinceCode,
      oldIsAutosite,
    ]
  ) => {
    if (form.value.isInit) {
      form.value.isInit = false;
      return;
    }
    if (newBusinessType !== oldBusinessType || newSiteType !== oldSiteType) {
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
        if (newIsAutosite !== oldIsAutosite) {
          return;
        }
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;
      } else {
        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
        if (newIsAutosite !== oldIsAutosite) {
          return;
        }
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;
      }
    }
    if (
      newBusinessType !== oldBusinessType ||
      newSiteType !== oldSiteType ||
      newActivityType !== oldActivityType
    ) {
      // 根据活动大类设置是否自动站逻辑
      if (newActivityType !== oldActivityType) {
        const selectedActivityType = activityParentTypeOptions.value?.find(
          (item) => item.activityTypeCode === newActivityType
        );
        // 0不是自动站1是自动站2都行
        if (selectedActivityType?.isAutosite) {
          if (
            form.value.isAutositeActivity !== selectedActivityType?.isAutosite
          ) {
            form.value.siteId = undefined;
          }
          form.value.isAutositeActivity = selectedActivityType?.isAutosite;
        }
      }
      form.value.activitySubtype = undefined;
      getActivityList(
        newActivityType,
        newBusinessType,
        form.value?.siteTypeAlias
      );
    }
    if (
      newCityCode !== oldCityCode ||
      newProvinceCode !== oldProvinceCode ||
      newSiteType !== oldSiteType
    ) {
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
      }
      if (newProvinceCode !== oldProvinceCode) {
        form.value.cityCode = undefined;
      }
      form.value.siteId = undefined;
    }
  },
  { deep: true }
);

function initData(newVal) {
  nextTick(() => {
    const isAuto = newVal?.hasAutomaticStation === "Y" ? 1 : 0;
    form.value = {
      ...newVal,
      statYear: (newVal?.statYear || "") + "",
      isAutosite: newVal?.hasAutomaticStation ? isAuto : "",
      isInit: true,
    };
  });
  getActivityList(newVal.activityType, newVal.businessType, newVal.siteType);
  getActivityParentTypeList(newVal.businessType, newVal.siteType);
}

function getActivityList(activityTypeCode, businessType, siteType) {
  getActivityType({
    activityTypeCode,
    businessType,
    siteType,
    isRegular: 1,
  }).then((response) => {
    activityTypeOptions.value = response.data;
  });
}
function getActivityParentTypeList(businessType, siteType, isAutosite) {
  const obj = {
    businessType,
    siteType,
    isRegular: 1,
  };
  if (businessType === "water") {
    obj.isAutosite = isAutosite;
  }
  getActivityParentType(obj).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

/** 取消按钮 */
function cancel() {
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtn.value = true;
      // 如果是水断面，则isAutosite为0,如果选则了站点，则根据站点的类型判断，如果没有则根据选中的活动大类判断
      const isAutosite =
        form.value.siteType === -100
          ? "0"
          : form.value.isAutosite || form.value.isAutositeActivity;

      const requestObj = {
        ...form.value,
        siteType: form.value?.siteTypeAlias,
      };
      // 业务类型为水才会传isAutosite
      if (form.value.businessType === "water") {
        requestObj.isAutosite = isAutosite;
      }
      saveNumTarget(requestObj)
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          props?.getList();
          loadingBtn.value = false;
        })
        .catch(() => {
          loadingBtn.value = false;
        });
    }
  });
}

initData(props.editRecord);
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
