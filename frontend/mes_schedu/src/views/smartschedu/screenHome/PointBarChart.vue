<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 15:35:09
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-23 09:38:08
 * @FilePath     :  / src / views / resourcemgr / screenHome / PointBarChart.vue
-->

<template>
  <div class="point-bar-chart-container">
    <!-- ECharts 容器，设置宽高 -->
    <div ref="chart" class="chart" style="width: 100%; height: 100%;"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const chart = ref(null);

onMounted(() => {
  // 初始化 ECharts 实例
  const myChart = echarts.init(chart.value);

  // 环形饼图配置项，对应图中数据和样式
  const option = {
    grid: {
      top: '8%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        fontSize: 14,
        color: '#999999',
        formatter: function (value) {
          // 将字符串拆分为数组，每个字符后添加换行符
          return value.split('').join('\n');
        }
      },
      data: ['启用', '短时停运', '临时停运', '短期停运', '长期停运', '点位搬迁', '撤销', '历史', '禁用']
    },
    yAxis: {
      axisLabel: {
        show: false,      // 强制显示纵坐标标签
      },
      splitLine: {
        show: true,      // 显示网格线
        lineStyle: {
          type: 'dashed',// 网格线样式
          opacity: 0.8   // 网格线透明度
        }
      },
      type: 'value'
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        label: {
          show: true,
          position: 'top',
          color: '#1472FF',
          fontWeight: 'bold',
        },
        data: [176, 23, 46, 69, 45, 34, 37, 31, 16],
        itemStyle: {
          color: '#5793f3'
        }
      }
    ]
  };

  // 设置配置项，渲染图表
  myChart.setOption(option);

  // 监听窗口 resize 事件，让图表自适应
  window.addEventListener('resize', () => {
    myChart.resize();
  });
});
</script>

<style scoped>
.point-bar-chart-container {
  width: 100%;
  height: calc(100% - 220px);
}
</style>