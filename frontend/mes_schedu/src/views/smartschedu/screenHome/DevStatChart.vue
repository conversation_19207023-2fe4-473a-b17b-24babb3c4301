<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 15:12:07
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-23 10:04:43
 * @FilePath     :  / src / views / resourcemgr / screenHome / DevStatChart.vue
-->
<template>
  <div class="dev-stat-chart-container">
    <!-- ECharts 容器，设置宽高 -->
    <div ref="chart" class="chart" style="width: 100%; height: 100%;"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const chart = ref(null);

onMounted(() => {
  // 初始化 ECharts 实例
  const myChart = echarts.init(chart.value);

  // 环形饼图配置项，对应图中数据和样式
  const option = {
    tooltip: {
      trigger: 'item', // 触发类型，饼图用 'item'
      formatter: '{a} <br/>{b}: {c} ({d}%)' // 提示框格式，显示名称、数值、占比
    },
    legend: {
      icon: 'circle',
      orient: 'vertical', // 图例排列方向
      left: '45%',
      top: 5,
      bottom: 5,
      data: [
        '常规五参数水质在线自动监测仪',
        '高锰酸盐指数水质在线自动监测仪',
        '氨氮水质在线自动监测仪',
        '总磷水质在线自动监测仪',
        '总氮水质在线自动监测仪'
      ]
    },
    series: [
      {
        name: '水质监测仪类型占比', // 系列名称，用于 tooltip 等
        type: 'pie', // 图表类型为饼图
        radius: '70%', // 饼图半径
        center: ['20%', '50%'],
        data: [
          { value: 37, name: '常规五参数水质在线自动监测仪' },
          { value: 48, name: '高锰酸盐指数水质在线自动监测仪' },
          { value: 13, name: '氨氮水质在线自动监测仪' },
          { value: 2, name: '总磷水质在线自动监测仪' },
          { value: 0, name: '总氮水质在线自动监测仪' } // 图中未显示数值，可根据实际调整
        ],
        label: {
          show: true, // 显示标签（名称和占比等）
          position: 'inside',
          formatter: '{d}%'
        },
        labelLine: {
          show: true // 显示标签连接线
        }
      }
    ]
  };

  // 设置配置项，渲染图表
  myChart.setOption(option);

  // 监听窗口 resize 事件，让图表自适应
  window.addEventListener('resize', () => {
    myChart.resize();
  });
});
</script>

<style scoped>
.dev-stat-chart-container {
  width: 100%;
  height: calc(calc(100% - 320px) / 3);
  margin-bottom: 16px;
}
</style>