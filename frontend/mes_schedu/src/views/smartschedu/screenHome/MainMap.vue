<template>
  <div class="point-cluster-container">
    <div ref="mapContainer" class="map"></div>
    <!-- <div class="controls">
      <el-button @click="reloadData">刷新数据</el-button>
      <el-button @click="changeClusterOptions">修改聚合设置</el-button>
      <el-select v-model="clusterRadius" placeholder="聚合半径">
        <el-option label="20px" value="20"></el-option>
        <el-option label="40px" value="40"></el-option>
        <el-option label="60px" value="60"></el-option>
        <el-option label="80px" value="80"></el-option>
      </el-select>
    </div> -->
    <div class="fullscreen-container" v-if="$slots.fullscreen">
      <slot name="fullscreen"></slot>
    </div>

    <change-map-type :map="map" />
  </div>
  <PopupModal
    v-if="dialogVisible"
    v-model:dialogVisible="dialogVisible"
    :detailRecord="detailRecord"
  />
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, watch } from "vue";
import L from "leaflet";
import { TiledMapLayer, WMTSLayer } from "@supermapgis/iclient-leaflet";
import { provinces } from "../common/optionsData";
import "leaflet.markercluster"; // 必须导入JS才能注册到L对象
import mapSites from "@/assets/images/home/<USER>";
import PopupModal from "./PopupModal.vue";
import { getSiteInfo } from "@/api/smartschedu/common";
import changeMapType from "./changeMapType.vue";
// 地图相关

// 引用地图容器
const mapContainer = ref(null);
const map = ref(null);
const clusterGroup = ref(null);
const clusterRadius = ref(40); // 聚合半径
const dialogVisible = ref(false);
const detailRecord = ref({});
const provinceMap = ref({});

provinces.forEach((item) => {
  provinceMap.value[item.code] = item;
});

// 生成全国各省的名称以及行政编码，并将各省地图的中心点位置，json格式数组

// 模拟点位数据（实际应用中应从API获取）
const pointData = ref([]);

const props = defineProps({
  businessType: String,
  selectProvince: String,
  selectSiteInfo: Object,
});

watch(
  () => pointData.value,
  (newVal) => {
    addPointsToCluster();
  }
);

watch(
  () => props.selectProvince,
  (newVal) => {
    if (!newVal || !map.value || !provinceMap.value[newVal]) return;

    // 定位到当前选择的省份
    nextTick(() => {
      map.value.setView(
        [
          provinceMap.value[newVal].lat &&
            Number(provinceMap.value[newVal].lat),
          provinceMap.value[newVal].lng &&
            Number(provinceMap.value[newVal].lng),
        ],
        7
      );
    });
  }
);
watch(
  () => props.selectSiteInfo,
  (newVal) => {
    if (!newVal || !map.value) return;

    // 定位到当前选择的省份
    nextTick(() => {
      map.value.setView(
        [
          props?.selectSiteInfo.officialLatitude &&
            Number(props?.selectSiteInfo.officialLatitude),
          props?.selectSiteInfo.officialLongitude &&
            Number(props?.selectSiteInfo.officialLongitude),
        ],
        10
      );
    });
  }
);

function getSiteList() {
  getSiteInfo({ businessType: props?.businessType }).then((response) => {
    pointData.value = response.data;
  });
}

watch(
  () => props?.businessType,
  (newVal) => {
    getSiteList();
  }
);

// 初始化地图
onMounted(() => {
  getSiteList();
  if (!mapContainer.value) return;

  // 初始化Leaflet地图
  map.value = L.map(mapContainer.value, {
    crs: L.CRS.EPSG4326,
    center: [40.0406009, 116.4173759],
    zoom: 14,
    maxZoom: 20,
  });

  // 添加SuperMap底图
  // addBaseLayer();

  // 初始化聚合图层
  initClusterLayer();
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (map.value) {
    map.value.remove();
    map.value = null;
  }
  if (clusterGroup.value) {
    clusterGroup.value.clearLayers();
    clusterGroup.value = null;
  }
});

// 添加SuperMap底图

// 初始化聚合图层
function initClusterLayer() {
  // 销毁旧的聚合图层
  if (clusterGroup.value) {
    map.value.removeLayer(clusterGroup.value);
  }

  // 创建聚合组
  clusterGroup.value = L.markerClusterGroup({
    showCoverageOnHover: false,
    maxClusterRadius: clusterRadius.value, // 聚合半径
    spiderfyOnMaxZoom: true, // 最大缩放级别时展开聚合
    removeOutsideVisibleBounds: true, // 超出视野时移除标记
  });

  // 添加点位到聚合图层
  addPointsToCluster();

  // 将聚合图层添加到地图
  map.value.addLayer(clusterGroup.value);
}

// 添加点位到聚合图层
function addPointsToCluster() {
  if (!clusterGroup.value) return;

  // 清空现有点位
  clusterGroup.value.clearLayers();

  // 添加新点位
  pointData.value.forEach((point) => {
    if (!point.longitude || !point.longitude) {
      return;
    }
    const marker = L.marker([Number(point.latitude), Number(point.longitude)], {
      icon: L.icon({
        iconUrl: mapSites,
      }),
      title: point.siteName,
    });

    // 添加点击事件
    marker.on("click", function () {
      detailRecord.value = point;
      dialogVisible.value = true;
      "showPointInfo"(point);
    });

    clusterGroup.value.addLayer(marker);
  });
}

// 监听聚合半径变化
watch(clusterRadius, () => {
  initClusterLayer();
});

// 添加到<script setup>中
function refreshMap() {
  if (map.value) {
    // 延迟执行以确保DOM已经更新
    setTimeout(() => {
      map.value.invalidateSize(false);
      // 如果需要，也可以重新调整视图
      // map.value.fitBounds(clusterGroup.value.getBounds());
    }, 50);
  }
}

// 暴露给父组件使用
defineExpose({
  refreshMap,
});
</script>

<style scoped>
.point-cluster-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map {
  width: 100%;
  height: 100%;
}

.controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  gap: 10px;
  align-items: center;
}

.fullscreen-container {
  position: absolute;
  bottom: 20px;
  right: 10px;
  z-index: 1000;
  cursor: pointer;
  color: aliceblue;
}

/* 自定义聚合标记样式 */
.custom-cluster-icon .cluster-icon {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  text-align: center;
  color: white;
  font-weight: bold;
}

.custom-cluster-icon.small {
  background-color: #3182ce;
}

.custom-cluster-icon.medium {
  background-color: #f6ad55;
}

.custom-cluster-icon.large {
  background-color: #e53e3e;
}

.point-popup {
  font-family: "Microsoft YaHei", sans-serif;
  padding: 10px;
  max-width: 250px;
}

:deep(.map) {
  .leaflet-control-container {
    display: none;
  }
}
</style>
