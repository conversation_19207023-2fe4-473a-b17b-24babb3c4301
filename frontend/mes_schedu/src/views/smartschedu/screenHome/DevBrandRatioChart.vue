<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 15:14:23
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-23 10:05:03
 * @FilePath     :  / src / views / resourcemgr / screenHome / DevBrandRatioChart.vue
-->

<template>
  <div class="dev-brand-ratio-chart-container">
    <!-- ECharts 容器，设置宽高 -->
    <div ref="chart" class="chart" style="width: 100%; height: 100%;"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const chart = ref(null);

let myChart = null;

onMounted(() => {
  const data = [
    { value: 57, name: '默飞世尔' },
    { value: 15, name: '河北先河' },
    { value: 8, name: 'API' },
    { value: 6, name: '武汉天虹' },
    { value: 5, name: '安徽蓝盾' },
    { value: 9, name: '其他' }
  ];
  // 初始化 ECharts 实例
  myChart = echarts.init(chart.value);

  // 环形饼图配置项，对应图中数据和样式
  const option = {
    tooltip: {
      trigger: 'item', // 触发类型，饼图用 'item'
      formatter: '{a} <br/>{b}: {c} ({d}%)' // 提示框格式，显示名称、数值、占比
    },
    legend: {
      icon: 'circle',
      orient: 'vertical', // 图例排列方向
      left: '45%',
      top: 5,
      bottom: 5,
      data: [
        '默飞世尔',
        '河北先河',
        'API',
        '武汉天虹',
        '安徽蓝盾',
        '其他'
      ]
    },
    series: [
      {
        name: '设备品牌占比', // 系列名称，用于 tooltip 等
        type: 'pie', // 图表类型为饼图
        radius: ['40%', '70%'], // 环形内外半径，形成环形效果
        avoidLabelOverlap: false,
        center: ['20%', '50%'],
        data: data,
        label: {
          show: false, // 显示标签（名称和占比等）
        },
        labelLine: {
          show: true // 显示标签连接线
        }
      }
    ]
  };

  // 设置配置项，渲染图表
  myChart.setOption(option);

  // 监听窗口 resize 事件，让图表自适应
  window.addEventListener('resize', resizeHandler);
});

const resizeHandler = () => {
  if (myChart) myChart.resize();
};

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener('resize', resizeHandler);
});
</script>

<style scoped>
.dev-brand-ratio-chart-container {
  width: 100%;
  height: calc(calc(100% - 320px) / 3);
  margin-bottom: 16px;
}
</style>