<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 15:39:44
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-27 11:01:31
 * @FilePath     :  / src / views / resourcemgr / screenHome / NoticeListBox.vue
-->

<template>
  <div class="notice-list-box-container">
    <div v-for="item in 4" :key="item">
      <el-row justify="space-between">
        <el-col :span="1.5">
          <img src="@/assets/images/home/<USER>" alt="">
        </el-col>
        <el-col :span="19" class="row-item">
          <div><span class="font-1">通知标题</span><span class="font-1"> 06/04 12:02 </span></div>
          <div title="dadad"><span class="font-2">
            这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通知内容这里是通
          </span></div>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain round>去查看</el-button>
        </el-col>
      </el-row>
      <el-divider class="card-divider"/>
    </div>
   
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

</script>

<style scoped lang="scss">
.notice-list-box-container {
  width: 100%;
  height: calc(100% - 30px);
  overflow-y: auto;
  .card-divider{
    margin: 7.5px 0 7.5px 0;
    border-top-style: dashed;
  }
  .row-item {
    // margin-left: 10px;
    // margin-right: 20px;
    div{
      display: flex;
      justify-content: space-between;
      
      span{
        white-space: nowrap;      /* 禁止文本换行 */
        overflow: hidden;         /* 超出部分隐藏 */
        text-overflow: ellipsis;  /* 超出部分显示省略号 */
      }
    }
  }
  .font-1 {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 500;
  }
  .font-2 {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #999999;
    letter-spacing: 0;
    font-weight: 400;
  }
}
</style>