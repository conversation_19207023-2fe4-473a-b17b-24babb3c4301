:deep(.el-calendar) {
  padding-left: 12px;
  .current,
  .prev,
  .next {
    border: none;
    width: 36px;
    color: var(--el-text-color-placeholder);
  }
  .el-calendar-table thead th {
    text-align: left;
    padding-left: 11px;
  }
  .el-calendar-day {
    height: 36px;
    width: 36px;
    padding: 0;
  }
  .el-calendar-table td.is-selected {
    background-color: unset;
  }
  .date-cell {
    width: 100%;
    height: 100%;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
  }
  --el-calendar-cell-width: 40px;
  .el-calendar__header {
    padding-top: 0;
    padding-bottom: 4px;
    display: none;
  }
  .el-calendar__body {
    padding: 0;
  }
}

:deep(.table-carousel) {
  .el-carousel__container {
    height: calc(100% - 40px);
  }
}

:deep(.custom-table-no-header) {
  .el-table__header {
    display: none;
  }
  .el-table__cell {
    padding: 6px 0;
  }
}

:deep(.map-dialog) {
  width: calc(100% - 24px);
  height: 100%;
  position: absolute;
  padding: 10px;
  top: 0;
  left: 0;
  background-color: white;
  border-radius: 8px;
  z-index: 99999;
  .map-header {
    height: 30px;
    text-align: right;
  }
  .map-content {
    position: relative;
    height: calc(100% - 30px);
    width: 100%;
  }
}

:deep(.small-table) {
  .el-table__row {
    .cell {
      padding: 0 !important;
    }
  }
}

:deep(.custom-tree-select-popper.el-popper) {
  min-width: 160px !important;
  width: 160px !important;

  .el-tree {
    width: 160px !important;
  }
}

.header-types {
  width: 180px;
  height: 28px;
  background: white;
  border-radius: 14px;
  padding: 0 16px;
  display: flex;
  gap: 32px;
  justify-content: space-between;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  margin-right: 24px;
  position: absolute;
  top: 24px;
  right: 0;
  z-index: 999;
  &_item {
    cursor: pointer;
    &_active {
      color: #023a93;
    }
  }

  &_item:not(:last-child)::after {
    content: " ";
    position: absolute;
    right: 55%; /* 等于gap值的一半 */
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: #999999;
    z-index: 1000;
  }
}

.resource-mgmt-home-container {
  width: calc(100% - 24px);
  height: calc(100% - 30px);
  position: absolute;
  display: flex;

  .el-carousel__container {
    height: calc(100% - 24px);
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
  }
  .flex-evenly {
    justify-content: space-evenly;
    column-gap: 12px;
  }

  .flex-column {
    flex-direction: column;
    justify-content: center;
  }

  .flex-avg {
    flex: 1;
  }

  .box-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    column-gap: 12px;
    .box-item {
      background: rgba(49, 103, 213, 0.05);
      height: 78px;
      border-radius: 4px;
      padding: 8px;
    }
  }

  :deep(.el-divider) {
    margin: 0 0 16px 0;
  }
  .el-row {
    width: 100%;
    height: 100%;
  }
  .el-col {
    height: 100%;
  }

  .card-box-swap {
    width: 100%;
    background: #fff;
    border-radius: 16px;
    padding: 16px;

    .card-item-1,
    .card-item-2 {
      width: 100%;
      background: rgba(49, 103, 213, 0.05);
      border-radius: 3px;
      margin-bottom: 16px;
      padding: 8px 16px;
      .card-divider {
        margin: 7.5px 0 7.5px 0;
        border-top-style: dashed;
      }
      .card-row-1 {
        .el-col {
          flex: 1;
          max-width: max-content;
          display: flex;
          align-items: center;
        }
      }
      .card-row-2 {
        .el-col {
          flex: 1;
          max-width: max-content;
        }
      }
    }

    .card-item-2 {
      width: 100%;
      background: rgba(49, 103, 213, 0.05);
      border-radius: 3px;
      padding: 8px;
      margin-bottom: 0;
    }

    :deep(.el-carousel__indicator.is-active button) {
      background-image: linear-gradient(-45deg, #00baff 0%, #1b7cec 100%);
      border-radius: 4px;
      width: 24px;
    }
    :deep(.el-carousel__button) {
      background: #d8d8d8;
      border-radius: 4px;
      height: 4px;
      width: 4px;
    }
  }

  .pl0 {
    padding-left: 0 !important;
  }
  .pr0 {
    padding-right: 0 !important;
  }

  .mb24 {
    margin-bottom: 24px;
  }
  .mb16 {
    margin-bottom: 16px;
  }
  .mr8 {
    margin-right: 8px;
  }
  .mr18 {
    margin-right: 18px;
  }
  .pr11 {
    padding-right: 11px;
  }
  .pl28 {
    padding-left: 28px;
  }
  .pb0 {
    padding-bottom: 0px;
  }
  .font-1 {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #333333;
    font-weight: 500;
  }
  .font-2 {
    font-family: DINAlternate-Bold;
    font-size: 18px;
    color: #1472ff;
    line-height: 30px;
    font-weight: 700;
  }
  .font-black {
    color: black;
  }
  .font-3 {
    font-family: PingFangSC-Medium;
    font-size: 12px;
    color: #999999;
    font-weight: 500;
  }
  .flex-row {
    align-items: center;
  }
  .block-text {
    // display: inline-block;
    width: 66px;
  }

  .container {
    width: 100%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    flex-direction: column;
    height: calc(100% - 40px);
  }

  .header {
    color: var(--el-color-primary);
    text-align: center;
    background: url(@/assets/images/home/<USER>
    height: 60px;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100%;
  }
  .stats-container {
    display: flex;
    justify-content: space-evenly;
    gap: 40px;
  }
  .stat-card {
    background: rgba(255, 255, 255, 0.15);
    width: 15%;
    transition: all 0.1s;
    cursor: pointer;
    padding-bottom: 3px;
  }
  .stat-card:hover {
    background: rgba(255, 255, 255, 0.25);
  }
  .stat-card.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    border-bottom: 2px solid var(--el-color-primary);
  }
  .stat-number {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .stat-label {
    font-size: 14px;
    opacity: 0.9;
  }
  .table-container {
    padding: 20px;
    padding-bottom: 0;
    padding-top: 6px;
    height: calc(100% - 98px);
    overflow-y: auto;
  }
  .pagination-container {
    padding: 0 20px;
    background-color: #f5f7fa;
    display: flex;
    justify-content: center;
    margin-top: 6px;
  }
  .blue-circle {
    display: inline-block;
    width: 22px;
    height: 22px;
    background-color: #2563eb;
    color: white;
    line-height: 22px;
    text-align: center;
    font-size: 14px;
  }
  .custom-header {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 600;
  }

  .category-container {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 0 16px;
    padding-top: 6px;
    background: white;
    font-family: "Segoe UI", system-ui, sans-serif;
    border-top: 1px solid var(--el-border-color-light);
  }

  .category-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .color-chip {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    flex-shrink: 0;
  }

  .category-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .icon-wrapper {
    margin-left: auto;
  }

  .chart-icon {
    width: 24px;
    height: 24px;
    color: #666;
  }

  /* 颜色定义 */
  .bg-gray {
    background-color: #e0e0e0;
  }
  .bg-beige {
    background-color: #f5e8d5;
  }
  .bg-light-orange {
    background-color: #ffcc80;
  }
  .bg-orange {
    background-color: #ff9800;
  }

  .home-select-box {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1001;
    width: 430px;
  }

  .percent {
    display: inline-block;
    min-width: 35px;
    font-size: 12px;
    &.left {
      text-align: left;
    }
    &.right {
      text-align: right;
    }
  }
}
