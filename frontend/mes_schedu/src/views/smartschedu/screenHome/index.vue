<!--
 * @Description  :
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 09:32:19
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-27 15:33:56
 * @FilePath     :  / src / views / resourcemgr / screenHome / index.vue
-->
<!-- 首页 -->
<template>
  <ScaleScreen
    :width="1920"
    :height="960"
    class="scale-wrap"
    :selfAdaption="true"
    @resetOther="resizeMap"
  >
    <div
      class="resource-mgmt-home-container"
      ref="containerRef"
      :style="{ transform: `scale(${scale})`, transformOrigin: 'top left' }"
    >
      <el-row :gutter="24">
        <el-col style="padding: 0" :span="6">
          <div class="card-box-swap mb16" style="height: 340px">
            <Title titleName="数量目标" />
            <div class="card-item-1">
              <el-row justify="space-between" align="middle" class="card-row-1">
                <el-col :span="8">
                  <img
                    src="@/assets/images/home/<USER>"
                    alt=""
                    class="mr18"
                  />
                  <span class="font-1"
                    >{{
                      businessType === "water" ? "水" : "气"
                    }}上月(进度/目标)</span
                  >
                </el-col>
                <el-col :span="8">
                  <span class="font-2 font-black">{{
                    quantityTargetPerformance.lastMonthMonthlyPerformance +
                    quantityTargetPerformance.lastMonthWeeklyPerformance
                  }}</span>
                  <span class="mr8">/</span>
                  <span class="font-2">{{
                    quantityTargetPerformance.lastMonthMonthlyTarget +
                    quantityTargetPerformance.lastMonthWeeklyTarget
                  }}</span>
                </el-col>
              </el-row>
              <el-divider class="card-divider" />
              <el-row justify="space-between" align="middle" class="card-row-2">
                <el-col :span="12">
                  <span class="font-3">周质控(进度/目标)</span>
                  <el-row class="flex-row">
                    <span class="font-2 font-black">{{
                      quantityTargetPerformance.lastMonthWeeklyPerformance
                    }}</span>
                    <span class="mr8">/</span>
                    <span class="font-2">
                      {{
                        quantityTargetPerformance.lastMonthWeeklyTarget
                      }}</span
                    >
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <span class="font-3">月质控(进度/目标)</span>
                  <el-row class="flex-row">
                    <span class="font-2 font-black">{{
                      quantityTargetPerformance.lastMonthMonthlyPerformance
                    }}</span>
                    <span class="mr8">/</span>
                    <span class="font-2">
                      {{
                        quantityTargetPerformance.lastMonthMonthlyTarget
                      }}</span
                    >
                  </el-row>
                </el-col>
              </el-row>
            </div>

            <div class="card-item-1">
              <el-row justify="space-between" align="middle" class="card-row-1">
                <el-col :span="8">
                  <img
                    src="@/assets/images/home/<USER>"
                    alt=""
                    class="mr18"
                  />
                  <span class="font-1"
                    >{{
                      businessType === "water" ? "水" : "气"
                    }}本月(进度/目标)</span
                  >
                </el-col>
                <el-col :span="8">
                  <span class="font-2 font-black">{{
                    quantityTargetPerformance.curMonthWeeklyPerformance +
                    quantityTargetPerformance.curMonthMonthlyPerformance
                  }}</span>
                  <span class="mr8">/</span>
                  <span class="font-2">
                    {{
                      quantityTargetPerformance.curMonthWeeklyTarget +
                      quantityTargetPerformance.curMonthMonthlyTarget
                    }}</span
                  >
                </el-col>
              </el-row>
              <el-divider class="card-divider" />
              <el-row justify="space-between" align="middle" class="card-row-2">
                <el-col :span="12">
                  <span class="font-3">周质控(进度/目标)</span>
                  <el-row class="flex-row">
                    <span class="font-2 font-black">{{
                      quantityTargetPerformance.curMonthWeeklyPerformance
                    }}</span>
                    <span class="mr8">/</span>
                    <span class="font-2">
                      {{ quantityTargetPerformance.curMonthWeeklyTarget }}</span
                    >
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <span class="font-3">月质控(进度/目标)</span>
                  <el-row class="flex-row">
                    <span class="font-2 font-black">{{
                      quantityTargetPerformance.curMonthMonthlyPerformance
                    }}</span>
                    <span class="mr8">/</span>
                    <span class="font-2">
                      {{
                        quantityTargetPerformance.curMonthMonthlyTarget
                      }}</span
                    >
                  </el-row>
                </el-col>
              </el-row>
            </div>
          </div>

          <div
            class="card-box-swap pr11 pb0"
            style="height: calc(100% - 364px)"
          >
            <Title titleName="质量目标">
              <template #extra>
                <div>
                  <el-select
                    style="width: 120px; margin-right: 8px"
                    v-model="conditionType"
                    placeholder="请选择"
                    @change="changeConditionType"
                  >
                    <el-option label="行政区域" value="area"> </el-option>
                    <el-option label="运维公司" value="company"> </el-option>
                  </el-select>
                  <el-tree-select
                    style="width: 150px"
                    multiple
                    collapse-tags
                    show-checkbox
                    v-model="condition"
                    :render-after-expand="false"
                    node-key="areaCode"
                    popper-class="custom-tree-select-popper"
                    placeholder="请选择"
                    clearable
                    :data="conditionType === 'area' ? areaTree : companyList"
                    :props="{
                      value: 'areaCode',
                      label: 'areaName',
                      children: 'children',
                    }"
                  >
                  </el-tree-select>
                </div>
              </template>
            </Title>

            <el-table
              v-if="businessType === 'water'"
              :data="qualityTargetPerformance"
              style="width: 100%; height: calc(100% - 40px)"
            >
              <el-table-column
                prop="monitIndexName"
                label="水"
                align="center"
                width="130"
              ></el-table-column>

              <el-table-column
                label="质控合格率"
                align="center"
                key="quactrlPassRate"
                prop="quactrlPassRate"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.quactrlPassRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.quactrlPassRate) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="数据获取率"
                align="center"
                key="captureRate"
                prop="captureRate"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.captureRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.captureRate) }}
                  </div>
                </template></el-table-column
              >
            </el-table>
            <el-table
              v-else
              :data="qualityTargetPerformance"
              style="width: 100%; height: calc(100% - 40px)"
              class="small-table"
            >
              <el-table-column prop="monitIndexName" label="气" width="70" />

              <el-table-column label="准确度" align="center" width="90">
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.accuracyResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.accuracy) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="精密度" align="center" width="90">
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.precisionResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.precision) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="有效率"
                align="center"
                key="effectivenessRate"
                prop="effectivenessRate"
                width="90"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.effectivenessRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.effectivenessRate) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="获取率"
                align="center"
                key="captureRate"
                prop="captureRate"
                width="90"
              >
                <template #default="scope">
                  <div class="percent right">
                    {{ getPercent(scope.row.captureRateResult) }}
                  </div>
                  |
                  <div class="percent left">
                    {{ getPercent(scope.row.captureRate) }}
                  </div>
                </template></el-table-column
              >
            </el-table>
          </div>
        </el-col>

        <el-col :span="12">
          <div
            class="card-box-swap mb16"
            style="height: 60%; position: relative"
          >
            <MainMap
              ref="mapRef"
              :businessType="businessType"
              :selectProvince="selectProvince"
              :selectSiteInfo="selectSiteInfo"
            >
              <template #fullscreen>
                <el-icon @click="handleFullScreen"><FullScreen /></el-icon>
              </template>
            </MainMap>
            <div class="home-select-box">
              <el-select
                v-model="selectProvince"
                value-key="code"
                placeholder="请选择"
                check-strictly
                :disabled="disabled"
                style="width: 100px"
              >
                <el-option
                  v-for="dict in provinces"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
              <el-select
                v-model="siteName"
                filterable
                remote
                reserve-keyword
                remote-show-suffix
                placeholder="请输入站点名称"
                :remote-method="remoteMethod"
                :loading="loading"
                @change="handleChange"
                style="width: 240px; margin-left: 16px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.siteName"
                  :value="item.id"
                />
              </el-select>
            </div>
            <div class="header-types">
              <div
                class="header-types_item"
                :class="{
                  'header-types_item_active': item.dictCode === businessType,
                }"
                v-for="item in businessTypeOptions"
                :key="item.dictCode"
                @click="selectTypes(item)"
              >
                {{ item.dictValue }}
              </div>
            </div>
          </div>
          <div class="card-box-swap" style="height: calc(40% - 24px)">
            <Title titleName="通知与待办" />
            <NoticeListBox />
          </div>
        </el-col>

        <el-col style="padding: 0" :span="6">
          <div class="card-box-swap pr11 mb16" style="height: 150px">
            <Title titleName="运维人员" />

            <div class="box-container">
              <div style="width: 40%" class="box-item flex-between">
                <img src="@/assets/images/home/<USER>" alt="" />
                <div class="flex-between flex-column">
                  <span class="font-1">人员总数</span>
                  <div>
                    <span class="font-2 mr8">{{
                      personInfo?.totalCount || 0
                    }}</span>
                    <span class="font-3">个</span>
                  </div>
                </div>
              </div>
              <div
                style="width: 60%"
                class="box-item flex-between flex-avg flex-evenly"
              >
                <div class="flex-between flex-column">
                  <div class="flex-between">
                    <img
                      class="mr8"
                      src="@/assets/images/home/<USER>"
                      alt=""
                    />
                    <span class="font-1 block-text">在途状态</span>
                  </div>
                  <div class="pl28">
                    <span class="font-2 mr8">{{
                      personInfo?.onDutyCount || 0
                    }}</span>
                    <span class="font-3">个</span>
                  </div>
                </div>
                <div class="flex-between flex-column">
                  <div class="flex-between">
                    <img
                      class="mr8"
                      src="@/assets/images/home/<USER>"
                      alt=""
                    />
                    <span class="font-1 block-text">空闲状态</span>
                  </div>
                  <div class="pl28">
                    <span class="font-2 mr8">{{
                      personInfo?.leaveCount || 0
                    }}</span>
                    <span class="font-3">个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            style="height: calc(100% - 520px); padding-bottom: 8px"
            class="card-box-swap pr11 mb16"
          >
            <Title titleName="当日调度任务" />

            <div class="box-container container">
              <div class="header">
                <div class="stats-container">
                  <div
                    class="stat-card"
                    :class="{ active: activeTab === 'pending' }"
                    @click="filterTasks('pending')"
                  >
                    <div class="stat-number">
                      {{ taskInfo.notStartedTaskCount ?? 0 }}
                    </div>
                    <div class="stat-label">待执行</div>
                  </div>
                  <div
                    class="stat-card"
                    :class="{ active: activeTab === 'inProgress' }"
                    @click="filterTasks('inProgress')"
                  >
                    <div class="stat-number">
                      {{ taskInfo.inProgressTaskCount ?? 0 }}
                    </div>
                    <div class="stat-label">执行中</div>
                  </div>
                  <div
                    class="stat-card"
                    :class="{ active: activeTab === 'completed' }"
                    @click="filterTasks('completed')"
                  >
                    <div class="stat-number">
                      {{ taskInfo.completeTaskCount ?? 0 }}
                    </div>
                    <div class="stat-label">已完成</div>
                  </div>
                </div>
              </div>

              <div class="table-container">
                <el-table
                  class="custom-table-no-header"
                  :data="taskList"
                  style="width: 100%"
                >
                  <el-table-column label="序号" prop="index" width="80">
                    <template #default="scope">
                      <div class="blue-circle">{{ scope.$index + 1 }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="taskName"
                    label="调度任务编号"
                    :header-cell-class-name="'custom-header'"
                  >
                    <template #header>
                      <span style="color: #606266; font-weight: 600"
                        >调度任务编号</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="siteName"
                    label="站点名称"
                    :header-cell-class-name="'custom-header'"
                  >
                    <template #header>
                      <span style="color: #606266; font-weight: 600"
                        >站点名称</span
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="pagination-container">
                <el-pagination
                  layout="prev, pager, next"
                  :page-size="pageSize"
                  :total="taskTotal"
                  v-model:current-page="currentPage"
                  @current-change="handlePageChange"
                  :pager-count="5"
                >
                </el-pagination>
              </div>
            </div>
          </div>

          <div
            class="card-box-swap pr11 mb16"
            style="height: 340px; padding-bottom: 8px"
          >
            <Title titleName="调度任务日历" style="margin-bottom: 0">
              <template #extra>
                <el-date-picker
                  v-model="currentDate"
                  type="month"
                  placeholder="选择日期"
                  class="date-picker"
                  style="width: 120px"
                  :clearable="false"
                />
              </template>
            </Title>

            <div
              style="height: calc(100% - 36px)"
              class="box-container flex-column"
            >
              <el-calendar v-model="currentDate">
                <template #date-cell="{ data }">
                  <div
                    :class="getLevelMap(taskTrend?.[data.day]?.totalCount)"
                    class="date-cell"
                  >
                    <div class="date">
                      {{ data.day.split("-").slice(0)?.[2] }}
                    </div>
                  </div>
                </template>
              </el-calendar>
              <div class="category-container">
                <div
                  v-for="(item, index) in categories"
                  :key="index"
                  class="category-item"
                >
                  <span
                    class="color-chip"
                    :class="item.colorClass"
                    :aria-label="`${item.label}数量区间`"
                  ></span>
                  <span class="category-label">{{ item.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="map-dialog" v-if="isFullScreen">
        <div class="map-header">
          <el-icon style="cursor: pointer" @click="handleFullScreen"
            ><Close
          /></el-icon>
        </div>
        <div class="map-content">
          <MainMap
            :businessType="businessType"
            :selectProvince="selectProvince"
            :selectSiteInfo="selectSiteInfo"
          />
          <div class="home-select-box">
            <el-select
              v-model="selectProvince"
              value-key="code"
              placeholder="请选择"
              check-strictly
              :disabled="disabled"
              style="width: 100px"
            >
              <el-option
                v-for="dict in provinces"
                :key="dict.code"
                :label="dict.name"
                :value="dict.code"
              />
            </el-select>
            <el-select
              v-model="siteName"
              filterable
              remote
              reserve-keyword
              remote-show-suffix
              placeholder="请输入站点名称"
              :remote-method="remoteMethod"
              :loading="loading"
              @change="handleChange"
              style="width: 240px; margin-left: 16px"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.siteName"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="header-types">
            <div
              class="header-types_item"
              :class="{
                'header-types_item_active': item.dictCode === businessType,
              }"
              v-for="item in businessTypeOptions"
              :key="item.dictCode"
              @click="selectTypes(item)"
            >
              {{ item.dictValue }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </ScaleScreen>
</template>

<script setup name="HomeComponent">
import { ref, onMounted, watch } from "vue";
import Title from "@/components/Title";
import MainMap from "./MainMap";
import NoticeListBox from "./NoticeListBox";
import { getProvinceInfo, getBusinessType } from "@/api/smartschedu/common";
import { qryTaskTrend } from "@/api/smartschedu/task";
import ScaleScreen from "@/components/ScaleScreen";
import { provinces } from "../common/optionsData";
import { Search } from "@element-plus/icons-vue";

import {
  qryTodayTaskStatic,
  qryQuantityTargetPerformance,
  qryTodayTaskList,
  qryMaintainPersonStatic,
  qryQualityTargetPerformance,
  getMaintainUnitInfo,
} from "@/api/smartschedu/home";
import dayjs from "dayjs";
import { getCurrentInstance } from "vue";
import { getSiteInfo } from "@/api/smartschedu/common";

const { proxy } = getCurrentInstance();
const mapRef = ref(null);
const statusMap = {
  pending: "1,2",
  inProgress: "3",
  completed: "4",
};
const siteName = ref("");

const queryParams = ref({ pageNo: 1, pageSize: 10 });
const selectProvince = ref("");

const taskInfo = ref({});
const taskList = ref([]);
const quantityTargetPerformance = ref([]);
const qualityTargetPerformance = ref([]);
const taskTrend = ref({});

const businessTypeOptions = ref([]);
const currentDate = ref(new Date());
const containerRef = ref(null);
const scale = ref(1);
const isFullScreen = ref(false);
const taskTotal = ref(0);
const conditionType = ref("area");
const condition = ref([]);
const companyList = ref([]);

// 响应式分类数据
const categories = ref([
  { label: "0", colorClass: "bg-gray" },
  { label: "1-10", colorClass: "bg-beige" },
  { label: "11-100", colorClass: "bg-light-orange" },
  { label: "100+", colorClass: "bg-orange" },
]);

const personInfo = ref({});

// 分页相关变量
const currentPage = ref(1);
const pageSize = 5;
// 当前活跃标签
const activeTab = ref("pending");
const businessType = ref("water");
const areaTree = ref([]);
const options = ref([]);
const loading = ref(false);
const selectSiteInfo = ref({});

const remoteMethod = (query) => {
  if (query) {
    loading.value = true;
    getSiteInfo({ businessType: businessType.value, siteName: query }).then(
      (response) => {
        loading.value = false;

        options.value = response.data;
      }
    );
  }
};

function handleChange(value) {
  const selected = options.value.find((item) => item.siteId === value);
  selectSiteInfo.value = selected;
}

function handleFullScreen() {
  isFullScreen.value = !isFullScreen.value;
}

function changeConditionType() {
  condition.value = undefined;
}

function selectTypes(item) {
  businessType.value = item.dictCode;
}

function getPercent(value) {
  if (!value) return "0%";

  return value * 100 + "%";
}

function resizeMap() {
  mapRef.value.refreshMap();
}

// 处理标签切换
function filterTasks(status) {
  activeTab.value = status;
  currentPage.value = 1; // 切换标签时回到第一页
}

function getLevelMap(value) {
  if (value === 0) return "bg-gray";
  if (value > 0 && value <= 10) {
    return "bg-beige";
  }
  if (value > 10 && value <= 100) {
    return "bg-light-orange";
  }
  if (value > 100) {
    return "bg-orange";
  }
  return "";
}

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}
function getPersonInfo() {
  if (!businessType.value) {
    return;
  }
  qryMaintainPersonStatic({ businessType: businessType.value }).then(
    (response) => {
      personInfo.value = response.data;
    }
  );
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page;
}

function getTaskTrend() {
  qryTaskTrend({
    startDate:
      currentDate.value &&
      proxy.parseTime(dayjs(currentDate.value).startOf("month").toDate()),
    endDate:
      currentDate.value &&
      proxy.parseTime(dayjs(currentDate.value).endOf("month").toDate()),
  }).then((res) => {
    const temp = {};
    res?.data?.map((item) => {
      temp[item?.date] = item;
    });
    taskTrend.value = temp;
  });
}

function getProvinceInfoList() {
  getProvinceInfo().then((res) => {
    if (res?.code === 200) {
      areaTree.value = [
        {
          areaName: "全国",
          areaCode: "000000",
          parentCode: null,
          children: res?.data,
        },
      ];
    }
  });
}

function getData() {
  getCompanyList({ businessType: businessType.value });

  qryTodayTaskStatic({ businessType: businessType.value }).then((res) => {
    if (res?.code === 200) {
      taskInfo.value = res?.data;
    }
  });
  getPersonInfo();
  getTaskList();
  qryQuantityTargetPerformance({ businessType: businessType.value }).then(
    (res) => {
      if (res?.code === 200) {
        quantityTargetPerformance.value = res?.data;
      }
    }
  );
}

function getCompanyList(params) {
  getMaintainUnitInfo(params).then((res) => {
    if (res?.code === 200) {
      const list = [];
      res?.data.forEach((item) => {
        list.push({
          areaName: item.maintenanceUnitName,
          areaCode: item.maintenanceUnitId,
        });
      });
      companyList.value = list;
    }
  });
}

function getTaskList() {
  qryTodayTaskList({
    businessType: businessType.value,
    ...queryParams.value,
    taskStaus: statusMap[activeTab.value],
  }).then((res) => {
    if (res?.code === 200) {
      taskList.value = res?.rows;
      taskTotal.value = res?.total;
    }
  });
}

function getList() {
  let query = {
    businessType: businessType.value,
  };
  if (condition.value?.length > 0) {
    if (conditionType.value === "area") {
      query.provinceCode = condition.value?.join(",");
    } else {
      query.maintainUnitCode = condition.value?.join(",");
    }
    if (condition.value?.indexOf("000000") > -1) {
      query = {
        businessType: businessType.value,
      };
    }
  }

  qryQualityTargetPerformance(query).then((res) => {
    if (res?.code === 200) {
      qualityTargetPerformance.value = res?.data;
    }
  });
}

watch(
  () => businessType.value,
  () => {
    getData();
  }
);
watch(
  [() => businessType.value, () => condition.value],
  () => {
    getList();
  },
  { deep: true }
);

watch(
  [
    () => queryParams.value.pageNo,
    () => queryParams.value.pageSize,
    () => activeTab.value,
  ],
  () => {
    getTaskList();
  }
);

onMounted(() => {
  getProvinceInfoList();
  getBusinessTypeList();
  getTaskTrend();
  getPersonInfo();
  getList();
  getData();
});
</script>

<style scoped lang="scss">
@import "./index.scss";

:deep(.custom-tree-select-popper) {
  min-width: 160px !important;
  width: 160px !important;

  .el-tree {
    width: 160px !important;
  }
}
</style>
