<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 15:06:38
 * @LastEditors  : wnj
 * @LastEditTime : 2025-06-23 09:59:05
 * @FilePath     :  / src / views / resourcemgr / screenHome / FunnelChart.vue
-->
<template>
  <div class="funnel-chart-container">
    <!-- ECharts 容器，设置宽高 -->
    <div ref="chart" class="chart" style="width: 100%; height: 100%;"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const chart = ref(null);

onMounted(() => {
  // 初始化 ECharts 实例
  const myChart = echarts.init(chart.value);

  // 漏斗图配置项，还原设计图样式
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b} <br/>占比: {d}%' 
    },
    legend: {
      show: false // 隐藏默认图例，右侧用自定义标注
    },
    series: [
       // 右侧标注文本，用富文本 + 坐标定位实现
       {
        type: 'custom',
        coordinateSystem: 'none', // 禁用坐标系，使用绝对定位
        renderItem: (params, api) => {
          const dataIndex = params.dataIndex;
          const seriesData = option.series[1].data[dataIndex];
          const chartHeight = myChart.getHeight(); // 关键修改：使用图表高度
          const y = (1 - (dataIndex + 0.6) / option.series[0].data.length) * chartHeight;
          
          return {
            type: 'text',
            position: [100, y],
            style: {
              text: `----------------- ${seriesData.name}  ${seriesData.value}%`,
              fontSize: 14,
              fill: '#999999'
            }
          };
        },
        data: new Array(5).fill(0)
      },
      {
        name: '设备年限分布',
        type: 'funnel', 
        left: 5, // 调整漏斗图位置，留出右侧标注空间
        width: '40%', // 漏斗图宽度
        top: 5,
        bottom: 5,
        label: {
          show: false // 隐藏漏斗图上默认标签
        },
        data: [
          { value: 7, name: '0～2年设备数量' },
          { value: 17, name: '2～4年设备数量' },
          { value: 21, name: '4～6年设备数量' },
          { value: 13, name: '6～8年设备数量' },
          { value: 9, name: '8～10年设备数量' }
        ],
        funnelAlign: 'center', // 漏斗图居中对齐
        sort: 'ascending', // 数据从大到小排列，形成上窄下宽
        gap: 0 // 层与层之间无间距，紧密衔接
      },
     
    ]
  };

  // 设置配置项，渲染图表
  myChart.setOption(option);

  // 监听窗口 resize 事件，让图表自适应
  window.addEventListener('resize', () => {
    myChart.resize();
  });
});
</script>

<style scoped>
.funnel-chart-container {
  width: 100%;
  height: calc(calc(100% - 300px) / 3);
}
</style>