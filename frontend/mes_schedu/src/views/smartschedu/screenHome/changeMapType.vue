<template>
  <div
    ref="mapTypeWrapperRef"
    id="mapType-wrapper"
    @mouseenter="showMapType"
    @mouseleave="hideMapType"
  >
    <div id="mapType">
      <div
        class="mapTypeCard normal"
        :class="mapType == 1 ? 'active' : ''"
        @click="changeMapType(1)"
      >
        <span class="mapTypeCardTitle">地图</span>
      </div>
      <div
        class="mapTypeCard earth"
        :class="mapType == 2 ? 'active' : ''"
        @click="changeMapType(2)"
      >
        <span class="mapTypeCardTitle">影像</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch } from "vue";

const mapTypeWrapperRef = ref(null);

const props = defineProps({
  map: Object,
});
const mapType = ref(2);
let expandTimeout;

function showMapType() {
  clearTimeout(expandTimeout);
  //   mapTypeWrapper.classList.add("expand");
  mapTypeWrapperRef.value.classList.add("expand");
}

function hideMapType() {
  expandTimeout = setTimeout(() => {
    mapTypeWrapperRef.value.classList.remove("expand");
  }, 100); // Delay hiding by 100ms
}
function changeMapType(value) {
  mapType.value = value;
  addBaseLayer();
}

function addBaseLayer() {
  // 添加wmts底图
  const layer = new L.supermap.TiandituTileLayer({
    key: "1d109683f4d84198e37a38c442d68311",
  });
  const labelLayer = new L.supermap.TiandituTileLayer({
    isLabel: true,
    key: "1d109683f4d84198e37a38c442d68311",
  });

  const imageLayer = new L.supermap.TiandituTileLayer({
    layerType: "img",
    key: "97d79afc34aff139de9493b6f9773541",
  });
  const imageLabelLayer = new L.supermap.TiandituTileLayer({
    layerType: "img",
    isLabel: true,
    key: "97d79afc34aff139de9493b6f9773541",
  });

  if (mapType.value == 1) {
    props.map?.removeLayer(imageLayer);
    props.map?.removeLayer(imageLabelLayer);
    props.map?.removeLayer(layer);
    props.map?.removeLayer(labelLayer);
    props.map?.addLayer(layer);
    props.map?.addLayer(labelLayer);
  } else {
    props.map?.removeLayer(layer);
    props.map?.removeLayer(labelLayer);
    props.map?.removeLayer(imageLayer);
    props.map?.removeLayer(imageLabelLayer);
    props.map?.addLayer(imageLayer);
    props.map?.addLayer(imageLabelLayer);
  }
}

watch(
  () => props.map,
  (newMap) => {
    if (newMap) {
      addBaseLayer();
    }
  }
);
</script>

<style lang="scss" scoped>
/* 地图切换样式 */
#mapType-wrapper {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1000;
  bottom: 32px;
  * {
    box-sizing: border-box;
  }
}

#mapType {
  height: 80px;
  cursor: pointer;
  -webkit-transition-property: width, background-color;
  transition-property: width, background-color;
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
  width: 110px;
  background-color: rgba(255, 255, 255, 0);
}
#mapType-wrapper.expand {
  width: 198px;
  background-color: rgba(255, 255, 255, 0.8);
  right: 10px;
}
#mapType .mapTypeCard {
  height: 60px;
  width: 86px;
  position: absolute;
  border-radius: 2px;
  top: 10px;
  box-sizing: border-box;
  background: url(@/assets/images/map/shadow.png) no-repeat 0 0;
  background-size: 86px 240px;
  -webkit-transition-property: right, background-image;
  transition-property: right, background-image;
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
}

#mapType .normal {
  z-index: 1;
  background-position: 0 0;
  background-image: url(@/assets/images/map/maptype.png);

  right: 10px;
  &.active {
    z-index: 5;
  }
}

#mapType .earth {
  background-position: 0 -61px;
  background-image: url(@/assets/images/map/maptype.png);
  z-index: 2;
  right: 10px;
  &.active {
    z-index: 5;
  }
}

.expand {
  .normal {
    right: 104px !important;
  }
  .earth {
    right: 10px !important;
  }
}
.expand #mapType .mapTypeCard {
  border: 1px solid rgba(255, 255, 255, 0);
  background-image: url(@/assets/images/map/maptype.png);
}
.mapTypeCardTitle {
  position: absolute;
  bottom: 0;
  height: 26px;
  text-align: center;
  color: #fff;
  width: 100%;
  line-height: 26px;
  background-color: rgba(0, 0, 0, 0.3);
}
.active {
  .mapTypeCardTitle {
    background-color: #3385ff;
  }
  &.mapTypeCard {
    border: 2px solid #3385ff !important;
  }
}
</style>
