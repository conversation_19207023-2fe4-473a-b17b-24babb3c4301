<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="700px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
    >
      <el-form-item label="任务执行人" prop="taskTime">
        <el-input
          disabled
          placeholder="请选择任务执行人"
          v-model="form.taskTime"
        >
          <template #append>
            <el-button type="primary" @click="showPersonnelDialog"
              >选择</el-button
            >
          </template></el-input
        >
      </el-form-item>
      <el-form-item label="任务派发时间" prop="dispatchedTime">
        <el-date-picker
          v-model="form.dispatchedTime"
          type="datetime"
          placeholder="请选择任务派发时间"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="任务开始时间" prop="dispatchedTime">
        <el-date-picker
          v-model="form.dispatchedTime"
          type="datetime"
          placeholder="请选择任务开始时间"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="任务结束时间" prop="dispatchedTime">
        <el-date-picker
          v-model="form.dispatchedTime"
          type="datetime"
          placeholder="请选择任务结束时间"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="送样地点" prop="taskTime">
        <el-input
          disabled
          placeholder="请输入送样地点"
          v-model="form.taskTime"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm()"
          >提 交</el-button
        >
      </div>
    </template>
  </el-dialog>

  <select-person
    v-model:open="showAddPerson"
    v-if="showAddPerson"
    v-model:selectPersonList="selectPersonList"
    :editRecord="editRecord"
  />
</template>

<script setup name="User">
import { manualDispatchPlan } from "@/api/smartschedu/task";
import { nextTick, watch } from "vue";
import selectPerson from "./selectPerson.vue";

const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const showAddPerson = ref(false);
const selectPersonList = ref([]);
const data = reactive({
  form: {},
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          isInit: true,
        };
      });
    }
  }
);

function disabledDate(current) {
  return current && current.getTime() < Date.now() - 86400000;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
  emit("update:editRecord", null);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtnSave.value = true;
      manualDispatchPlan({
        ...editRecord,
        ...form.value,
        startTime: proxy.parseTime(form.value.taskTiem[0]),
        endTime: proxy.parseTime(form.value.taskTiem[1]),
      })
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          props?.getList();
          loadingBtnSave.value = false;
        })
        .catch(() => {
          loadingBtnSave.value = false;
        });
    }
  });
}

function showPersonnelDialog() {
  showAddPerson.value = true;
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-input-group__append button.el-button,
.el-input-group__append button.el-button:hover,
.el-input-group__append div.el-select .el-select__wrapper,
.el-input-group__append div.el-select:hover .el-select__wrapper,
.el-input-group__prepend button.el-button,
.el-input-group__prepend button.el-button:hover,
.el-input-group__prepend div.el-select .el-select__wrapper,
.el-input-group__prepend div.el-select:hover .el-select__wrapper {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
  color: var(--el-color-white) !important;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
