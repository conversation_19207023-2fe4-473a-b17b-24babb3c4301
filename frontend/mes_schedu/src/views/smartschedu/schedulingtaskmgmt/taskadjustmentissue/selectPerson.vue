<template>
  <el-dialog
    title="人员选择"
    :model-value="open"
    align-center
    width="900px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="queryParams"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
      :inline="true"
      class="el-form--inline"
      row-key="personId"
    >
      <el-form-item label="姓名" prop="ruleName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入姓名"
          maxlength="30"
          style="width: 302px"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>

      <el-form-item label="运维公司" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入运维公司"
          maxlength="30"
          style="width: 302px"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
      max-height="calc(100vh - 280px)"
      style="width: 100%"
      v-el-table-infinite-scroll="load"
    >
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column
        prop="personName"
        label="资源名称"
        align="center"
      ></el-table-column>
      <el-table-column label="资源分类" align="center">
        <template #default="scope">
          <span>人员</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="资源状态"
        align="center"
      ></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getResList"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm()"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import {
  manualDispatchPlan,
  qryManualDispatchResList,
} from "@/api/smartschedu/task";
import { nextTick, watch } from "vue";

const { proxy } = getCurrentInstance();
const selectable = (row) => row.approvalStatus !== "approved";

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  selectPersonList: {
    type: Array,
    default: [],
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const ids = ref([]);
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
});

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      getResList();
    }
  }
);

function disabledDate(current) {
  return current && current.getTime() < Date.now() - 86400000;
}

function handleQuery() {
  queryParams.value.pageNo = 1;
  getResList();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
}

/** 取消按钮 */
function cancel() {
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  emit("update:selectPersonList", ids);
}

function getResList() {
  loading.value = true;
  qryManualDispatchResList({
    ...queryParams.value,
    siteId: props?.editRecord?.siteId,
  }).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
