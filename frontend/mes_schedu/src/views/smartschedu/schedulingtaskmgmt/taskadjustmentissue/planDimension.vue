<template>
  <el-row :gutter="20" style="height: 100%">
    <el-col style="height: 100%">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px"
        class="query-form">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="queryParams.taskName" placeholder="请输入计划名称" clearable style="width: 240px"
            @keyup.enter="handleQuery" />
        </el-form-item>

        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="queryParams.taskStatus" placeholder="任务状态" clearable style="width: 240px">
            <el-option v-for="dict in taskStatus" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务开始时间" prop="planTime">
          <el-date-picker type="datetimerange" v-model="queryParams.planTime" placeholder="选择开始时间" style="width: 240px"
            start-placeholder="开始时间" end-placeholder="结束时间" :shortcuts="shortcuts" />
        </el-form-item>
        <el-form-item />
        <el-form-item class="form-btn">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="24" style="display: flex; justify-content: space-between" class="mb8 table-header">
        <el-col :span="12">
          <div style="width: 100%" class="table-title">任务列表</div>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button type="primary" icon="Pointer" @click="() => handleDispatch()"
            v-hasPermi="['system:user:add']">批量推送</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange"
        max-height="calc(100vh - 460px)" style="width: 100%" v-el-table-infinite-scroll="load"
        :span-method="objectSpanMethod" row-key="planId">
        <el-table-column type="selection" :selectable="selectable" width="55" />
        <el-table-column label="任务编码" align="center" key="taskCode" prop="taskCode" :show-overflow-tooltip="true"
          width="200" />
        <el-table-column label="任务名称" align="center" key="taskName" prop="taskName" :show-overflow-tooltip="true"
          width="160" />
        <el-table-column label="任务创建方式" align="center" key="creatMethod" prop="creatMethod"
          :show-overflow-tooltip="true" width="110">
          <template #default="scope">
            <span>{{ taskCreateMethodMap?.[scope.row.creatMethod]?.text || "" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务派发时间" align="center" key="dispatchedTime" prop="dispatchedTime"
          :show-overflow-tooltip="true" width="160" />
        <!-- <el-table-column
          label="任务执行人"
          align="center"
          key="taskName"
          prop="taskName"
          :show-overflow-tooltip="true"
          width="160"
        /> -->
        <el-table-column label="任务开始时间" align="center" key="startTime" prop="startTime" :show-overflow-tooltip="true"
          width="160" />
        <el-table-column label="任务状态" align="center" prop="taskStatus" key="taskStatus" :show-overflow-tooltip="true"
          width="120">
          <template #default="scope">
            <span :style="{
              color: taskStatusMap?.[scope.row.taskStatus]?.color,
            }">{{ taskStatusMap?.[scope.row.taskStatus]?.text || "" }}</span></template></el-table-column>
        <el-table-column label="计划编码" align="center" key="planCode" prop="planCode" :show-overflow-tooltip="true"
          width="160" />
        <el-table-column label="计划名称" align="center" key="planName" prop="planName" :show-overflow-tooltip="true"
          width="160" />

        <el-table-column label="计划来源" align="center" width="120">
          <template #default="scope">
            <span>{{ planSourceMap?.[scope.row.planSource]?.text }}</span>
          </template>
        </el-table-column>
        <el-table-column label="优先级" align="center" key="planPriority" prop="planPriority" width="160">
          <template #default="scope">
            <span :style="{
              color: planPriorityMap[scope.row.planPriority]?.color,
            }">{{ planPriorityMap[scope.row.planPriority]?.text || "" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划调度状态" align="center" prop="scheduStatus" key="scheduStatus"
          :show-overflow-tooltip="true" width="120">
          <template #default="scope">
            <span :style="{
              color: planStatusMap?.[scope.row.scheduStatus]?.color,
            }">{{ planStatusMap?.[scope.row.scheduStatus]?.text || "" }}</span></template></el-table-column>
        <el-table-column label="省" align="center" key="provinceName" prop="provinceName" :show-overflow-tooltip="true"
          width="120" />
        <el-table-column label="市" align="center" key="cityName" prop="cityName" :show-overflow-tooltip="true"
          width="120" />
        <el-table-column label="业务分类" align="center" key="businessTypeName" prop="businessTypeName" />
        <el-table-column label="站点类型" align="center" key="siteTypeName" prop="siteTypeName" width="160" />
        <el-table-column label="站点名称" align="center" key="siteName" prop="siteName" :show-overflow-tooltip="true"
          width="120" />

        <el-table-column label="监测活动大类" align="center" key="activityTypeName" prop="activityTypeName" width="180"
          :show-overflow-tooltip="true" />
        <el-table-column label="监测活动小类" align="center" key="activitySubtypeName" prop="activitySubtypeName" width="180"
          :show-overflow-tooltip="true" />
        <el-table-column label="任务计划生成时间" align="center" key="createTime" prop="createTime"
          :show-overflow-tooltip="true" width="160" />

        <el-table-column label="操作" align="center" width="170"
          class-name="small-padding fixed-width custom-action-column" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row, 'view')">详情</el-button>
            <el-button link type="primary" v-if="
              scope.row.approvalStatus === 'approved' &&
              scope.row.taskStatus !== '2'
            " @click="handleDispatch(scope.row, 'view')">推送</el-button>
            <el-button link type="primary" v-if="scope.row.taskStatus === '1'"
              @click="handleManuleDispatch(scope.row)">调整</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-col>
  </el-row>

  <!-- 添加或编辑用户配置对话框 -->
  <add-form v-if="visible" :getList="getList" :title="title" v-model:open="visible" v-model:editRecord="editRecord" :planInfos="editRecord?.planInfos"
    :type="openType"></add-form>

  <task-detail v-if="showDetail" :getList="getList" :title="title" v-model:open="showDetail"
    :detailRecord="detailRecord" :type="openType" :ids="ids" :handleManuleDispatch="handleManuleDispatch"
    :handleDispatch="handleDispatch"></task-detail>
</template>

<script setup name="User">
import { ElMessage, ElMessageBox } from "element-plus";
import addForm from "../taskallocation/addForm.vue";
import taskDetail from "./taskDetail.vue";
import { qryTaskList, pushActivity } from "@/api/smartschedu/task";
import {
  planStatusMap,
  taskStatusMap,
  planPriorityMap,
  taskStatus,
  planSourceMap,
  taskCreateMethodMap,
} from "../../common/optionsData";
import { ref, watch } from "vue";
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  ruleName: undefined,
  ruleCode: undefined,
  status: undefined,
  scheduStatus: undefined,
});
const { proxy } = getCurrentInstance();
const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const detailRecord = ref(null);
const openType = ref("");
const taskIdRowCountMap = ref(new Map());
const showDetail = ref(false);
const selectable = (row) =>
  row.taskStatus !== "2" && row.approvalStatus === "approved";
/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryTaskList({
    ...queryParams.value,
    taskStatus: queryParams.value.taskStatus || "1,2",
    startTime: proxy.parseTime(queryParams.value?.planTime?.[0]),
    endTime: proxy.parseTime(queryParams.value?.planTime?.[1]),
  }).then((res) => {
    loading.value = false;
    const tempData = [];
    res.data.data.forEach((item) => {
      if (item.planInfos?.length > 0) {
        item.planInfos.forEach((planItem) => {
          tempData.push({
            ...planItem,
            planId: planItem.id,
            ...item,
            // rowSpan: item.planInfos?.length,
          });
        });
      } else {
        tempData.push({
          ...item,
        });
      }
    });
    dataList.value = tempData;
    taskIdRowCountMap.value = calculateSpans(dataList.value);
    total.value = res.data.totalRecords;
  });
}

// 计算需要合并的单元格信息
const calculateSpans = (data) => {
  const map = new Map();
  let pos = 0; // 记录当前位置

  // 遍历数据计算合并信息
  data.forEach((item, index) => {
    if (index === 0) {
      // 第一行总是需要合并
      map.set(pos, 1);
      pos = 0;
    } else {
      const prev = data[index - 1];
      // 判断当前行是否与上一行ID相同
      if (item.id === prev.id) {
        // 相同则合并行数+1，当前位置标记0
        map.set(pos, map.get(pos) + 1);
        map.set(index, 0);
      } else {
        // 不同则新建合并项
        map.set(index, 1);
        pos = index;
      }
    }
  });

  return map;
};

const maxMergedColumnIndex = 6;

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex < maxMergedColumnIndex || columnIndex === 19) {
    const rowspan = taskIdRowCountMap.value.get(rowIndex);
    return {
      rowspan: rowspan || 0,
      colspan: rowspan ? 1 : 0,
    };
  }
  return {
    rowspan: 1,
    colspan: 1,
  };
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  selectRows.value = selection;
  ids.value = selection.map((item) => item.id);
}

// 调整
function handleManuleDispatch(row) {
  editRecord.value = { ...row };
  visible.value = true;
  openType.value = "edit";
  title.value = "调整";
}

function handleDetail(row) {
  detailRecord.value = { ...row };
  showDetail.value = true;
}
// 及时调用
function handleDispatch(row) {
  if (!row && !isSameSelectType()) {
    return;
  }
  ElMessageBox.confirm("确认是否推送到活动系统？", "操作确认", {
    confirmButtonText: "推送",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      pushActivity({ id: `${row?.id}` || ids.value?.join(",") }).then((res) => {
        if (res.code === 200) {
          ElMessage({
            message: "推送成功",
            type: "success",
          });
          getList();
        } else {
          ElMessage({
            message: res.message,
            type: "error",
          });
        }
      });
    })
    .catch(() => { });
}

// 判断是否同一类型
function isSameSelectType() {
  if (ids.value.length === 0) {
    ElMessage({
      message: "请选择要分配的计划",
      type: "warning",
    });
    return false;
  }
  if (ids.value.length > 1) {
    let isSome = true;
    selectRows.value.forEach((item) => {
      if (item.businessType !== selectRows.value[0].businessType) {
        isSome = false;
      }
    });
    if (!isSome) {
      ElMessage({
        message: "请选择同一业务类型的计划",
        type: "warning",
      });
      return false;
    }
  }
  return true;
}

/** 新增按钮操作 */
function handleAdd() {
  if (!isSameSelectType()) {
    return;
  }
  visible.value = true;
  openType.value = "add";
  title.value = "手工分配任务资源";
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-right-btn {
  margin-left: unset;
}

.pagination-container {
  right: 11px !important;
}
</style>
