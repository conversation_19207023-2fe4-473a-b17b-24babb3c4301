<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane style="height: 100%" label="计划维度" name="first">
        <plan-dimension />
      </el-tab-pane>
      <el-tab-pane style="height: 100%" label="任务维度" name="second">
        <task-dimension />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Taskadjustmentissue">
import planDimension from "./planDimension.vue";
import taskDimension from "./taskDimension.vue";
const activeName = ref("first");
const handleClick = (tab, event) => {
  console.log(tab, event);
};
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-right-btn {
  margin-left: unset;
}

</style>
