<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes class="default-theme">
        <!--部门数据-->
        <pane size="16">
          <area-tree @handleCheck="handleCheckChange"></area-tree>
        </pane>
        <pane class="table-container" size="84">
          <el-col>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px"
              class="query-form">
              <common-form-search :needActivity="true" v-model:queryParams="queryParams" />
              <el-form-item label="计划来源" prop="planSource">
                <el-select v-model="queryParams.planSource" placeholder="计划来源" clearable style="width: 240px">
                  <el-option v-for="dict in planSource" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="调度状态" prop="scheduStatus">
                <el-select v-model="queryParams.scheduStatus" placeholder="调度状态" clearable style="width: 240px">
                  <el-option v-for="dict in planStatus" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="计划名称" prop="planName">
                <el-input v-model="queryParams.planName" placeholder="请输入计划名称" clearable style="width: 240px"
                  @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="执行时间" prop="planTime">
                <el-date-picker type="datetimerange" v-model="queryParams.planTime" placeholder="选择开始时间"
                  style="width: 240px" start-placeholder="开始时间" end-placeholder="结束时间" :shortcuts="shortcuts" />
              </el-form-item>
              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="24" style="display: flex; justify-content: space-between" class="mb8 table-header">
              <el-col :span="12">
                <div style="width: 100%" class="table-title">
                  调度计划分配列表
                </div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button type="primary" icon="AlarmClock" @click="() => handleDispatch()"
                  v-hasPermi="['system:user:add']">及时调用算法分配</el-button>
                <el-button type="primary" icon="Pointer" @click="handleAdd"
                  v-hasPermi="['system:user:add']">手工分配任务资源</el-button>
              </el-col>
            </el-row>

            <div :style="{ height: 'calc(100vh - 513px)' }">
              <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" height="100%"
                style="width: 100%" v-el-table-infinite-scroll="load">
                <el-table-column type="selection" :selectable="selectable" width="55" />
                <el-table-column label="序号" align="center" type="index" width="50" fixed />
                <el-table-column label="计划编码" align="center" key="planCode" prop="planCode"
                  :show-overflow-tooltip="true" width="160" />
                <el-table-column label="计划名称" align="center" key="planName" prop="planName"
                  :show-overflow-tooltip="true" width="160" />

                <el-table-column label="计划来源" align="center" width="120">
                  <template #default="scope">
                    <span>{{
                      planSourceMap?.[scope.row.planSource]?.text
                      }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="优先级" align="center" key="planPriority" prop="planPriority" width="160">
                  <template #default="scope">
                    <span :style="{
                      color: planPriorityMap[scope.row.planPriority]?.color,
                    }">{{
                        planPriorityMap[scope.row.planPriority]?.text || ""
                      }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="计划调度状态" align="center" prop="scheduStatus" key="scheduStatus"
                  :show-overflow-tooltip="true" width="120">
                  <template #default="scope">
                    <span :style="{
                      color: planStatusMap?.[scope.row.scheduStatus].color,
                    }">{{
                        planStatusMap?.[scope.row.scheduStatus].text || ""
                      }}</span></template></el-table-column>
                <el-table-column label="省" align="center" key="provinceName" prop="provinceName"
                  :show-overflow-tooltip="true" width="120" />
                <el-table-column label="市" align="center" key="cityName" prop="cityName" :show-overflow-tooltip="true"
                  width="120" />
                <el-table-column label="业务分类" align="center" key="businessTypeName" prop="businessTypeName" />
                <el-table-column label="站点类型" align="center" key="siteTypeName" prop="siteTypeName" width="160" />
                <el-table-column label="站点名称" align="center" key="siteName" prop="siteName"
                  :show-overflow-tooltip="true" width="120" />

                <el-table-column label="监测活动大类" align="center" key="activityTypeName" prop="activityTypeName"
                  width="180" :show-overflow-tooltip="true" />
                <el-table-column label="监测活动小类" align="center" key="activitySubtypeName" prop="activitySubtypeName"
                  width="180" :show-overflow-tooltip="true" />
                <el-table-column label="任务计划生成时间" align="center" key="createTime" prop="createTime"
                  :show-overflow-tooltip="true" width="160" />

                <el-table-column label="操作" align="center" width="170"
                  class-name="small-padding fixed-width custom-action-column" fixed="right">
                  <template #default="scope">
                    <el-button link type="primary" v-if="scope.row.scheduStatus !== '3'"
                      @click="handleDispatch(scope.row, 'view')">及时调用</el-button>
                    <el-button link type="primary" v-if="scope.row.scheduStatus !== '3'"
                      @click="handleManuleDispatch(scope.row)">手工分配</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form v-if="visible" :getList="getList" :title="title" v-model:open="visible" v-model:editRecord="editRecord"
      :type="openType" :planInfos="selectRows"></add-form>
  </div>
</template>

<script setup name="Taskallocation">
import { ElMessage, ElMessageBox } from "element-plus";
import addForm from "./addForm.vue";
import areaTree from "@/views/smartschedu/component/areaTree.vue";
import commonFormSearch from "@/views/smartschedu/component/commonFormSearch";

import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import {
  qryDispatchtaskPlanList,
  callDispatchPlan,
} from "@/api/smartschedu/task";
import {
  planStatusMap,
  planSource,
  planPriorityMap,
  planStatus,
  planSourceMap,
} from "../../common/optionsData";
import { ref, watch } from "vue";
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  ruleName: undefined,
  ruleCode: undefined,
  status: undefined,
  scheduStatus: undefined,
});
const { proxy } = getCurrentInstance();
const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const checkedKeyList = ref([]);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const openType = ref("");
const siteIds = ref([]);

const selectable = (row) => row.scheduStatus !== "3";

const handleCheckChange = (obj, checkedKeys) => {
  const cityCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "city")
    ?.map((item) => item.id);
  const siteId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "site")
    ?.map((item) => item.id);
  siteIds.value = siteId;
  checkedKeyList.value = cityCode;
  getList();
};
/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryDispatchtaskPlanList({
    ...queryParams.value,
    cityCode: checkedKeyList.value.join(","),
    siteId: siteIds.value?.join(","),
    scheduStatus: queryParams.value.scheduStatus || "2,3",
    startExecuteTime: proxy.parseTime(queryParams.value?.planTime?.[0]),
    endExecuteTime: proxy.parseTime(queryParams.value?.planTime?.[1]),
  }).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value = {};
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  selectRows.value = selection;
  ids.value = selection.map((item) => item.id);
}

// 手工分配
function handleManuleDispatch(row) {
  editRecord.value = { ...row };
  visible.value = true;
  openType.value = "add";
  selectRows.value = [row];
  title.value = "手工分配任务资源";
}

// 及时调用
function handleDispatch(row) {
  if (!row && !isSameSelectType()) {
    return;
  }
  ElMessageBox.confirm("确认是否及时调用算法分配？", "操作确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      callDispatchPlan({ id: row?.id || ids.value?.join(",") }).then((res) => {
        if (res.code === 200) {
          ElMessage({
            message: "调用成功",
            type: "success",
          });
          getList();
        } else {
          ElMessage({
            message: res.message,
            type: "error",
          });
        }
      });
    })
    .catch(() => { });
}

// 判断是否同一类型
function isSameSelectType() {
  if (ids.value.length === 0) {
    ElMessage({
      message: "请选择要分配的计划",
      type: "warning",
    });
    return false;
  }
  if (ids.value.length > 1) {
    let isSome = true;
    selectRows.value.forEach((item) => {
      if (item.businessType !== selectRows.value[0].businessType) {
        isSome = false;
      }
    });
    if (!isSome) {
      ElMessage({
        message: "请选择同一业务类型的计划",
        type: "warning",
      });
      return false;
    }
  }
  return true;
}

/** 新增按钮操作 */
function handleAdd() {
  if (!isSameSelectType()) {
    return;
  }
  visible.value = true;
  openType.value = "add";
  title.value = "手工分配任务资源";
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-right-btn {
  margin-left: unset;
}
</style>
