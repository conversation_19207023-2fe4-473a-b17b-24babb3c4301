<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="900px"
    append-to-body
    @close="cancel"
  >
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
      :inline="true"
      class="el-form--inline"
    >
      <div class="table-title">任务基础配置</div>
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          :disabled="type === 'edit'"
          v-model="form.taskName"
          placeholder="请输入任务名称"
          maxlength="30"
          style="width: 302px"
        />
      </el-form-item>
      <el-form-item label="推送时间" prop="dispatchedTime">
        <el-date-picker
          v-model="form.dispatchedTime"
          type="datetime"
          placeholder="请选择任务推送时间"
          style="width: 302px"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          type="datetime"
          placeholder="请选择开始时间"
          style="width: 302px"
          :disabled-date="disabledDate"
        />
      </el-form-item>

      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          style="width: 302px"
          :disabled-date="disabledDate"
        />
      </el-form-item>

      <el-form-item label="交割地点" prop="laboratoryAddress">
        <el-input
          v-model="form.laboratoryAddress"
          placeholder="请输入交割地点"
          maxlength="30"
          style="width: 302px"
        />
      </el-form-item>
      <el-form-item label="送样地点" prop="deliveryAddress">
        <el-input
          v-model="form.deliveryAddress"
          placeholder="请输入送样地点"
          maxlength="30"
          style="width: 302px"
        />
      </el-form-item>
    </el-form>
    <el-form
      :model="queryParams"
      ref="siteRef"
      label-width="100px"
      :disabled="type === 'view'"
      :inline="true"
      class="el-form--inline"
    >
      <div class="table-title">任务资源配置</div>
      <el-form-item label="资源分类" prop="ruleName">
        <el-select
          v-model="queryParams.ruleName"
          placeholder="请选择资源分类"
          style="width: 156px"
        >
          <el-option :key="1" label="人员" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属运维公司" prop="ruleName">
        <el-select
          v-model="queryParams.ruleName"
          placeholder="请选择所属运维公司"
          style="width: 156px"
        >
          <el-option :key="1" label="人员" :value="1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="资源名称" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入资源名称"
          maxlength="30"
          style="width: 156px"
          @keyup.enter="handleQuery"
          @blur="handleQuery"
        />
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
      max-height="calc(100vh - 280px)"
      style="width: 100%"
      row-key="id"
      ref="multipleTableRef"
    >
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column
        prop="personName"
        label="资源名称"
        align="center"
      ></el-table-column>
      <el-table-column label="资源分类" align="center">
        <template #default="scope">
          <span>人员</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="phoneNumber"
        key="phoneNumber"
        label="联系方式"
        align="center"
      />
      <el-table-column
        prop="phoneNumber"
        key="phoneNumber"
        label="技能资质"
        align="center"
      />
      <el-table-column
        prop="deptName"
        key="deptName"
        label="所属运维公司"
        align="center"
      />
      <el-table-column
        prop="statusName"
        key="statusName"
        label="资源状态"
        align="center"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getResList"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm()"
          >提 交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import {
  updateTaskInfo,
  manualDispatchPlan,
  qryManualDispatchResList,
  qryTaskDetail,
} from "@/api/smartschedu/task";
import { nextTick, watch } from "vue";

const { proxy } = getCurrentInstance();
const selectable = (row) => row?.id;

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  planInfos: {
    type: Array,
    default: [],
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtnSave = ref(false);
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const ids = ref([]);
const selectResList = ref([]);
const detail = ref({});
const multipleTableRef = ref();

const toggleSelection = (rows) => {
  if (rows) {
    rows.forEach((row) => {
      const index = dataList.value.findIndex((item) => item.id === row.id);
      multipleTableRef.value.toggleRowSelection(
        dataList.value[index],
        true,
        false
      );
    });
  } else {
    multipleTableRef.value.clearSelection();
  }
};

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    personName: "",
  },
  rules: {
    taskName: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
    dispatchedTime: [
      { required: true, message: "请选择推送时间", trigger: "change" },
    ],
    startTime: [
      { required: true, message: "请选择开始时间", trigger: "change" },
    ],
    endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

watch(
  [() => props.editRecord, () => props.planInfos],
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          isInit: true,
        };
      });
    }
    getResList();
    getDetail();
  },
  {
    deep: true,
    immediate: true,
  }
);

async function getDetail() {
  if (!props.editRecord.id) return;
  const res = await qryTaskDetail({
    id: props.editRecord.id,
  });
  if (res.code === 200) {
    detail.value = res.data;
    ids.value = res.data?.extendInfos?.map((item) => item.executorId);
    const reverse = res.data?.extendInfos?.map((item) => ({
      id: item.executorId,
      personId: item.executorId,
      personName: item.executorName,
      phoneNumber: item?.phone,
      orgId: item?.maintainUnitCode,
      orgName: item?.maintainUnitName,
    }));
    nextTick(() => {
      form.value = {
        ...res.data,
        deliveryAddress: res.data?.extendInfos?.[0]?.deliveryAddress || "",
        laboratoryAddress: res.data?.extendInfos?.[0]?.laboratoryAddress || "",
      };
      toggleSelection(reverse);
    });
  }
}

function disabledDate(current) {
  return current && current.getTime() < Date.now() - 86400000;
}

function handleQuery() {
  queryParams.value.pageNo = 1;
  getResList();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  selectResList.value = [...selection];
  ids.value = selection.map((item) => item.personId);
}
/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
  emit("update:editRecord", null);
}

function isTaskUpdate() {
  if (
    detail?.value?.dispatchedTime !==
      proxy.parseTime(form.value.dispatchedTime) ||
    detail?.value?.startTime !== proxy.parseTime(form.value.startTime) ||
    detail?.value?.endTime !== proxy.parseTime(form.value.endTime)
  ) {
    return true;
  } else {
    return false;
  }
}

function isExtendUpdate() {
  const extendInfosIds = detail?.value?.extendInfos
    ?.map((item) => item.executorId)
    .sort((a, b) => a - b);
  const idsSorted = ids.value?.sort((a, b) => a - b);
  if (
    detail?.value?.extendInfos?.[0]?.deliveryAddress !==
      form.value.deliveryAddress ||
    detail?.value?.extendInfos?.[0]?.laboratoryAddress !==
      form.value.laboratoryAddress ||
    ids.value.length !== detail?.value?.extendInfos?.length ||
    extendInfosIds?.join(",") !== idsSorted?.join(",")
  ) {
    return true;
  } else {
    return false;
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtnSave.value = true;
      let extendUpdateFlag = 0;
      let taskUpdateFlag = 0;
      if (isTaskUpdate()) {
        taskUpdateFlag = 1;
      }
      if (isExtendUpdate()) {
        extendUpdateFlag = 1;
      }
      (props.type === "add" ? manualDispatchPlan : updateTaskInfo)({
        ...props?.editRecord,
        ...form.value,
        planInfos: props?.editRecord ? [props?.editRecord] : props?.planInfos,
        extendInfos: selectResList.value?.map((item) => ({
          ...item,
          executorId: item.personId,
          executorName: item.personName,
          phone: item?.phoneNumber,
          maintainUnitCode: item?.orgId,
          maintainUnitName: item?.orgName,
          laboratoryAddress: form.value.laboratoryAddress,
          deliveryAddress: form.value.deliveryAddress,
        })),
        extendUpdateFlag,
        taskUpdateFlag,
        dispatchedTime: proxy.parseTime(form.value.dispatchedTime),
        startTime: proxy.parseTime(form.value.startTime),
        endTime: proxy.parseTime(form.value.endTime),
      })
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          emit("update:editRecord", null);
          props?.getList();
          loadingBtnSave.value = false;
        })
        .catch(() => {
          loadingBtnSave.value = false;
        });
    }
  });
}

function getResList() {
  if (!props?.editRecord?.siteId && props?.planInfos?.length < 0) return;
  loading.value = true;
  qryManualDispatchResList({
    ...queryParams.value,
    siteId:
      props?.editRecord?.siteId ||
      props?.planInfos?.map((item) => item.siteId)?.join(","),
  }).then((res) => {
    loading.value = false;
    const list = res.data.data?.map((item) => ({
      id: item.personId,
      personId: item.personId,
      personName: item.personName,
      phoneNumber: item?.phoneNumber,
      orgId: item?.deptId,
      orgName: item?.deptName,
      statusName: item?.statusName,
    }));
    dataList.value = list;
    total.value = res.data.totalRecords;
  });
}

// getResList();
// getDetail();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
