<template>
  <div class="detail-container">
    <div class="content-container" style="background: white">
      <div class="head-container">
        任务跟踪详情
        <el-button @click="emit('update:open', false)">返回</el-button>
      </div>
      <div class="sub-content">
        <div class="table-title">基础信息</div>
        <el-row v-if="detailRecord.id" :gutter="24" class="detail-row">
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">任务编码：</label>
              <span class="detail-value"
                ><el-tooltip
                  :content="detailRecord.taskCode || '-'"
                  placement="top"
                  >{{ detailRecord.taskCode || "-" }}</el-tooltip
                ></span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">任务名称：</label>
              <span class="detail-value"
                ><el-tooltip
                  :content="detailRecord.taskName || '-'"
                  placement="top"
                  >{{ detailRecord.taskName || "-" }}</el-tooltip
                ></span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">任务状态：</label>
              <span
                class="detail-value"
                :style="{
                  color: taskStatusMap?.[detailRecord.taskStatus]?.color,
                }"
                >{{
                  taskStatusMap?.[detailRecord.taskStatus]?.text || "-"
                }}</span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">包含计划数：</label>
              <span class="detail-value"
                ><el-tooltip
                  :content="detailRecord.planInfos?.length || '-'"
                  placement="top"
                  >{{ detailRecord.planInfos?.length || "-" }}</el-tooltip
                ></span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">当前执行计划：</label>
              <span class="detail-value"
                ><el-tooltip
                  :content="detailRecord.taskName || '-'"
                  placement="top"
                  >{{ detailRecord.taskName || "-" }}</el-tooltip
                ></span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label class="detail-label">任务执行环节：</label>
              <span class="detail-value"
                ><el-tooltip
                  :content="detailRecord.taskName || '-'"
                  placement="top"
                  >{{ detailRecord.taskName || "-" }}</el-tooltip
                ></span
              >
            </div>
          </el-col>
        </el-row>
        <el-divider />
        <div class="table-title">打卡记录</div>

        <el-timeline
          style="max-width: 800px; padding-left: 100px"
          class="custom-timeline"
        >
          <el-timeline-item
            timestamp="溶解氧（计划2）"
            :color="taskStatusMap?.[detailRecord.taskStatus]?.color"
            placement="top"
          >
            <div class="header-title">xxx 计划</div>
            <el-card
              body-style="padding: 8px 0 !important;"
              class="custom-card"
            >
              <el-row class="detail-row">
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">当前执行计划：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">任务状态：</label>
                    <span
                      class="detail-value"
                      :style="{
                        color: `${
                          taskStatusMap?.[detailRecord.taskStatus]?.color
                        } !important`,
                      }"
                      >{{
                        taskStatusMap?.[detailRecord.taskStatus]?.text || "-"
                      }}</span
                    >
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">打卡说明：</label>
                    <span class="detail-value"
                      ><el-tooltip
                        :content="detailRecord.taskName || '-'"
                        placement="top"
                        >{{ detailRecord.taskName || "-" }}</el-tooltip
                      ></span
                    >
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">活动开始时间：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">活动结束时间：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">附件：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </el-timeline-item>
          <el-timeline-item
            timestamp="溶解氧（计划2）"
            :color="taskStatusMap?.[detailRecord.taskStatus]?.color"
            placement="top"
          >
            <div class="header-title">xxx 计划</div>
            <el-card
              body-style="padding: 8px 0 !important;"
              class="custom-card"
            >
              <el-row class="detail-row">
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">当前执行计划：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">任务状态：</label>
                    <span
                      class="detail-value"
                      :style="{
                        color: `${
                          taskStatusMap?.[detailRecord.taskStatus]?.color
                        } !important`,
                      }"
                      >{{
                        taskStatusMap?.[detailRecord.taskStatus]?.text || "-"
                      }}</span
                    >
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">打卡说明：</label>
                    <span class="detail-value"
                      ><el-tooltip
                        :content="detailRecord.taskName || '-'"
                        placement="top"
                        >{{ detailRecord.taskName || "-" }}</el-tooltip
                      ></span
                    >
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">活动开始时间：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">活动结束时间：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="detail-item">
                    <label class="detail-label">附件：</label>
                    <span class="detail-value">{{
                      detailRecord.taskName || "-"
                    }}</span>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup name="User">
import { taskStatusMap } from "../../common/optionsData";

const props = defineProps({
  detailRecord: {
    type: Object,
    default: () => ({}),
  },
  open: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  padding: 24px;
  .content-container {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    padding: 20px;
    :deep(.sub-content) {
      padding: 16px;
      overflow-y: auto;
      height: calc(100% - 40px);
      .el-timeline-item__timestamp {
        left: -72px;
        position: absolute;
        width: 60px;
        text-align: center;
        line-height: 20px;
        top: -6px;
        color: black;
      }
    }
  }
}
.detail-item {
  margin-bottom: 6px !important;
  font-size: 12px !important;
}
.detail-value {
  color: rgba(102, 102, 102, 0.7) !important;
}
.top-right-btn {
  margin-left: unset;
}

:deep(.custom-timeline) {
  --el-timeline-node-size-normal: 8px !important;
  .el-timeline-item__tail {
    border-left: 1px dashed var(--el-color-primary) !important;
  }
  .el-timeline-item__node--normal {
    left: 0 !important;
  }
}

.custom-card {
  --el-card-bg-color: #f5f7fa !important;
}
.header-title {
  margin-bottom: 8px;
}
</style>
