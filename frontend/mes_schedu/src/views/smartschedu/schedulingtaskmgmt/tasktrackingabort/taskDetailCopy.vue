<template>
  <el-dialog
    title="任务回退处置"
    :model-value="open"
    align-center
    width="710px"
    append-to-body
    @close="cancel"
  >
    <div class="detail-container">
      <div v-if="detailRecord.id" class="table-title">任务信息</div>
      <el-row v-if="detailRecord.id" :gutter="20" class="detail-row">
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务名称：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.taskName || '-'"
                placement="top"
                >{{ detailRecord.taskName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务编码：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.taskCode || '-'"
                placement="top"
                >{{ detailRecord.taskCode || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划名称：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord?.planName || '-'"
                placement="top"
                >{{ detailRecord?.planName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划编码：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord?.planCode || '-'"
                placement="top"
                >{{ detailRecord?.planCode || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">省份：</label>
            <span class="detail-value">{{
              detailRecord.provinceName || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">地市：</label>
            <span class="detail-value">{{ detailRecord.cityName || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划优先级：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="
                  planPriorityMap[detailRecord?.planPriority]?.text || '-'
                "
                placement="top"
                >{{
                  planPriorityMap[detailRecord?.planPriority]?.text || "-"
                }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">站点类型：</label>
            <span class="detail-value">{{
              detailRecord?.siteTypeName || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">监测活动大类：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord?.activityTypeName || '-'"
                placement="top"
                >{{ detailRecord?.activityTypeName || "-" }}</el-tooltip
              >
            </span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">站点名称：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord?.siteName || '-'"
                placement="top"
                >{{ detailRecord?.siteName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">监测活动小类：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord?.activitySubtypeName || '-'"
                placement="top"
                >{{ detailRecord?.activitySubtypeName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">处置人：</label>
            <span class="detail-value">{{
              detailRecord.siteTypeName || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务派发时间：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.siteName || '-'"
                placement="top"
                >{{ detailRecord.siteName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">所属单位：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.activitySubtypeName || '-'"
                placement="top"
                >{{ detailRecord.activitySubtypeName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>

        <el-col :span="24">
          <div class="detail-item">
            <label class="detail-label">处置描述：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.companionRule || '-'"
                placement="topLeft"
                >{{ detailRecord.companionRule || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loadingBtn" @click="submitForm"
          >任务调整</el-button
        >
        <el-button type="primary" @click="dispatchActivity">重新分配</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import { planSourceMap, planPriorityMap } from "../../common/optionsData";
import {
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { nextTick, watch } from "vue";
const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  handleManuleDispatch: {
    type: Function,
    default: () => {},
  },
  handleDispatch: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  detailRecord: {
    type: Object,
    default: () => {},
  },
  ids: {
    type: Array,
    default: () => [],
  },
});
const loadingBtn = ref(false);

const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);
const siteOptions = ref(undefined);
const initFlag = ref(true);

const data = reactive({
  form: {
    approvalStatus: "approved",
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  rules: {
    approvalStatus: [
      {
        required: true,
        message: "请选择审批状态",
        trigger: "change",
      },
    ],
    approvalOpinion: [
      {
        required: false,
        message: "请输入审批意见",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.detailRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          approvalStatus: "approved",
          isInit: true,
        };
      });
      getActivityList(
        newVal.activityType,
        newVal.businessType,
        newVal.siteType
      );
      getActivityParentTypeList(newVal.businessType, newVal.siteType);
    }
  }
);

function getActivityList(activityTypeCode, businessType, siteType) {
  getActivityType({ activityTypeCode, businessType, siteType }).then(
    (response) => {
      activityTypeOptions.value = response.data;
    }
  );
}
function getActivityParentTypeList(businessType, siteType) {
  getActivityParentType({ businessType, siteType }).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  loadingBtn.value = false;
  initFlag.value = true;
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  props?.handleManuleDispatch(props?.detailRecord);
}

/** 提交按钮 */
function dispatchActivity() {
  props?.handleDispatch(props?.detailRecord);
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
:deep(.custom-descriptions) {
  .el-descriptions__label {
    text-align: right !important;
  }
}

.detail-container {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}
</style>
