<template>
  <div class="app-container">
    <div class="work-container">
      <div class="table-title">站点工作日历</div>
      <el-row :gutter="20" class="detail-row summary-info">
        <el-col :span="8">
          <div class="detail-item">
            <label class="detail-label">站点名称：</label>
            <span class="detail-value">{{ siteName || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="2">
          <div class="detail-item">
            <label class="detail-label">工单总数：</label>
            <span class="detail-value" style="color: var(--el-color-primary)">{{
              sumaryInfo?.taskCount || 0
            }}</span>
          </div>
        </el-col>
        <el-col :span="2">
          <div class="detail-item">
            <label class="detail-label">已完成：</label>
            <span style="color: var(--el-color-success)" class="detail-value">{{
              sumaryInfo?.completeTaskCount || 0
            }}</span>
          </div>
        </el-col>
        <el-col :span="2">
          <div class="detail-item">
            <label class="detail-label">处置中：</label>
            <span class="detail-value" style="color: var(--el-color-warning)">{{
              sumaryInfo?.unCompleteTaskCount || 0
            }}</span>
          </div>
        </el-col>
      </el-row>
      <div class="calendar-container">
        <el-calendar v-model="calendarValue" @change="handleChange">
          <template #date-cell="{ data }">
            <div class="date-cell">
              <div class="date">
                {{ data.day.split("-").slice(0)?.[2] }}
              </div>
              <div v-if="taskTrend?.[data.day]" class="date-content detail-row">
                <div class="detail-item">
                  <label class="detail-label">工单总数：</label>
                  <span
                    class="detail-value"
                    style="color: var(--el-color-primary)"
                    >{{ taskTrend?.[data.day]?.totalCount || 0 }}</span
                  >
                </div>
                <div class="detail-item">
                  <label class="detail-label">已完成：</label>
                  <span
                    style="color: var(--el-color-success)"
                    class="detail-value"
                    >{{ taskTrend?.[data.day]?.completeTaskCount || 0 }}</span
                  >
                </div>
                <div class="detail-item">
                  <label class="detail-label">处置中：</label>
                  <span
                    class="detail-value"
                    style="color: var(--el-color-warning)"
                    >{{ taskTrend?.[data.day]?.unCompleteTaskCount || 0 }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </div>
  </div>
</template>

<script setup name="SiteWorkCalendar">
import { qryTaskTrend } from "@/api/smartschedu/task";
import { qryPeriodTaskStaticBySite } from "@/api/smartschedu/home";
import { onMounted, watch } from "vue";
import dayjs from "dayjs";

const route = useRoute();

const { proxy } = getCurrentInstance();
const taskTrend = ref({});
const sumaryInfo = ref({});
const siteName = ref("");
const calendarValue = ref(dayjs().startOf("month").toDate());

watch(calendarValue, () => {
  getTaskTrend();
  getSumaryInfo();
});

function getTaskTrend() {
  qryTaskTrend({
    siteId: route.params.tableId,
    startDate:
      calendarValue.value &&
      proxy.parseTime(dayjs(calendarValue.value).startOf("month").toDate()),
    endDate:
      calendarValue.value &&
      proxy.parseTime(dayjs(calendarValue.value).endOf("month").toDate()),
  }).then((res) => {
    const temp = {};
    res?.data?.map((item) => {
      temp[item?.date] = item;
    });
    taskTrend.value = temp;
  });
}
function getSumaryInfo() {
  // 获取search 参数
  qryPeriodTaskStaticBySite({
    siteId: route.params.tableId,
    startDate:
      calendarValue.value &&
      proxy.parseTime(dayjs(calendarValue.value).startOf("month").toDate()),
    endDate:
      calendarValue.value &&
      proxy.parseTime(dayjs(calendarValue.value).endOf("month").toDate()),
  }).then((res) => {
    sumaryInfo.value = res?.data;
  });
}

onMounted(() => {
  const searchParams = new URLSearchParams(location.search);
  siteName.value = searchParams.get("siteName");
});

getTaskTrend();
getSumaryInfo();
</script>

<style lang="scss" scoped>
.summary-info {
  height: 40px;
  width: 100%;
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  line-height: 40px;
}
:deep(.calendar-container) {
  height: calc(100% - 100px);
  overflow-y: auto;
  --el-fill-color-blank: rgba(var(--el-color-info-rgb), 0.1);
  .date-cell {
    width: 100%;
    height: 100%;
    .date {
      font-weight: 400;
      text-align: right;
    }
    .date-content {
      height: calc(100% - 18px);
      width: 100%;
      background-color: rgba(var(--el-color-primary-rgb), 0.1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .detail-item {
        margin-bottom: 0;
        font-size: 10px;
        .detail-label {
          width: 80px;
          text-align: left;
        }
        .detail-value {
          display: inline;
          flex: unset;
          width: auto;
        }
      }
    }
  }

  .el-calendar-day {
    --el-calendar-cell-width: 100px;
  }
}
</style>
