<template>
  <div class="point-cluster-container">
    <div ref="mapContainer" class="map"></div>
    <div class="controls">
      <el-button @click="reloadData">刷新数据</el-button>
      <el-button @click="changeClusterOptions">修改聚合设置</el-button>
      <el-select v-model="clusterRadius" placeholder="聚合半径">
        <el-option label="20px" value="20"></el-option>
        <el-option label="40px" value="40"></el-option>
        <el-option label="60px" value="60"></el-option>
        <el-option label="80px" value="80"></el-option>
      </el-select>
    </div>
    <PopupModal v-model:dialogVisible="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, watch } from "vue";
import L from "leaflet";
import { TiledMapLayer } from "@supermapgis/iclient-leaflet";
import "leaflet.markercluster"; // 必须导入JS才能注册到L对象
import mapSites from "@/assets/images/map/mapSite.png";
import PopupModal from "./PopupModal.vue";
// 引用地图容器
const mapContainer = ref(null);
const map = ref(null);
const clusterGroup = ref(null);
const clusterRadius = ref(40); // 聚合半径
const dialogVisible = ref(false);
// 模拟点位数据（实际应用中应从API获取）
const pointData = ref([
  {
    id: 1,
    lat: 39.9042,
    lng: 116.4074,
    name: "北京天安门",
    info: "中国首都标志性建筑",
  },
  {
    id: 2,
    lat: 39.9528,
    lng: 116.4399,
    name: "北京故宫",
    info: "明清皇家宫殿",
  },
  {
    id: 3,
    lat: 31.2304,
    lng: 121.4737,
    name: "上海外滩",
    info: "上海标志性景点",
  },
  {
    id: 4,
    lat: 31.1997,
    lng: 121.5431,
    name: "上海东方明珠",
    info: "上海地标建筑",
  },
  {
    id: 5,
    lat: 22.5431,
    lng: 114.0579,
    name: "深圳平安金融中心",
    info: "深圳第一高楼",
  },
  // 生成更多测试数据
  ...generateRandomPoints(50),
]);

// 生成随机测试点位
function generateRandomPoints(count) {
  const points = [];
  for (let i = 0; i < count; i++) {
    // 在中国范围内生成随机坐标
    const lat = 20 + Math.random() * 20;
    const lng = 105 + Math.random() * 25;
    points.push({
      id: i + 6,
      lat,
      lng,
      name: `随机站点位${i + 1}`,
      info: `随机站点位信息${i + 1}`,
    });
  }
  return points;
}

// 初始化地图
onMounted(() => {
  if (!mapContainer.value) return;

  // 初始化Leaflet地图
  map.value = L.map(mapContainer.value, {
    crs: L.CRS.EPSG4326,
    center: [30, 110],
    zoom: 4,
    maxZoom: 18,
  });

  // 添加SuperMap底图
  addBaseLayer();

  // 初始化聚合图层
  initClusterLayer();
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (map.value) {
    map.value.remove();
    map.value = null;
  }
  if (clusterGroup.value) {
    clusterGroup.value.clearLayers();
    clusterGroup.value = null;
  }
});

// 添加SuperMap底图
function addBaseLayer() {
  const baseLayer = new TiledMapLayer(
    "https://iserver.supermap.io/iserver/services/map-world/rest/maps/World",
    {
      opacity: 0.9,
      attribution: "SuperMap iServer",
    }
  );
  baseLayer.addTo(map.value);
}

// 初始化聚合图层
function initClusterLayer() {
  // 销毁旧的聚合图层
  if (clusterGroup.value) {
    map.value.removeLayer(clusterGroup.value);
  }

  // 创建聚合组
  clusterGroup.value = L.markerClusterGroup({
    showCoverageOnHover: false,
    maxClusterRadius: clusterRadius.value, // 聚合半径
    spiderfyOnMaxZoom: true, // 最大缩放级别时展开聚合
    removeOutsideVisibleBounds: true, // 超出视野时移除标记
    // 自定义聚合样式
    // iconCreateFunction: function(cluster) {
    //   const count = cluster.getChildCount()
    //   let size = 'small'
    //   if (count > 10) size = 'large'
    //   else if (count > 5) size = 'medium'

    //   return L.divIcon({
    //     html: `<div class="cluster-icon ${size}">${count}</div>`,
    //     className: 'custom-cluster-icon',
    //     iconSize: L.point(40, 40),
    //     iconAnchor: L.point(20, 20)
    //   })
    // }
  });

  // 添加点位到聚合图层
  addPointsToCluster();

  // 将聚合图层添加到地图
  map.value.addLayer(clusterGroup.value);
}

// 添加点位到聚合图层
function addPointsToCluster() {
  if (!clusterGroup.value) return;

  // 清空现有点位
  clusterGroup.value.clearLayers();

  // 添加新点位
  pointData.value.forEach((point) => {
    const marker = L.marker([point.lat, point.lng], {
      icon: L.icon({
        iconUrl: mapSites,
      }),
      title: point.name,
    });

    // 添加点击事件
    marker.on("click", function () {
      dialogVisible.value = true;
      showPointInfo(point);
    });

    clusterGroup.value.addLayer(marker);
  });
}

// 显示点位信息
function showPointInfo(point) {
  L.popup()
    .setLatLng([point.lat, point.lng])
    .setContent(
      `
      <div class="point-popup">
        <h3>${point.name}</h3>
        <p>${point.info}</p>
        <p>坐标: (${point.lat.toFixed(4)}, ${point.lng.toFixed(4)})</p>
      </div>
    `
    )
    .openOn(map.value);
}

// 刷新数据
function reloadData() {
  // 模拟重新加载数据
  pointData.value = [
    ...pointData.value.slice(0, 5),
    ...generateRandomPoints(50),
  ];
  addPointsToCluster();
}

// 修改聚合设置
function changeClusterOptions() {
  initClusterLayer();
}

// 监听聚合半径变化
watch(clusterRadius, () => {
  initClusterLayer();
});
</script>

<style scoped>
.point-cluster-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map {
  width: 100%;
  height: 100%;
}

.controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 自定义聚合标记样式 */
.custom-cluster-icon .cluster-icon {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  text-align: center;
  color: white;
  font-weight: bold;
}

.custom-cluster-icon.small {
  background-color: #3182ce;
}

.custom-cluster-icon.medium {
  background-color: #f6ad55;
}

.custom-cluster-icon.large {
  background-color: #e53e3e;
}

.point-popup {
  font-family: "Microsoft YaHei", sans-serif;
  padding: 10px;
  max-width: 250px;
}
</style>
