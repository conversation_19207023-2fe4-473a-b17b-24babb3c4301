<template>
  <el-dialog
    title="点位详情"
    :model-value="dialogVisible"
    width="850px"
    @close="cancel"
    :modal="false"
    :destroy-on-close="true"
    :draggable="true"
    class="map-popup"
    :custom-style="{ top: '100px', left: '200px', transform: 'none' }"
  >
    <div class="table-title">基本信息</div>
    <el-row :gutter="20" class="detail-row">
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">区域：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">点位类型：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">本年任务总数：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">本月任务总数：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">本日任务总数：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">进行中任务：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">未开始任务：</label>
          <span class="detail-value">{{ detailRecord?.taskName || "-" }}</span>
        </div>
      </el-col>
    </el-row>
    <div class="table-title flex-content">
      进行中和未开始任务明细
      <div>
        <el-button
          type="primary"
          size="small"
          @click="handleJump('siteWorkCalendar')"
          >站点工作日历</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handleJump('companyWorkCalendar')"
          >运维公司工作日历</el-button
        >
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      max-height="200px"
      style="width: 100%"
    >
      <el-table-column
        label="任务名称"
        align="center"
        key="siteName"
        prop="siteName"
        :show-overflow-tooltip="true"
        min-width="140"
      />
      <el-table-column
        label="监测活动种类"
        align="center"
        key="siteTypeName"
        prop="siteTypeName"
        width="160"
      />
      <el-table-column
        label="任务生成时间"
        align="center"
        key="targetValue"
        prop="targetValue"
        min-width="120"
      />

      <el-table-column
        label="任务结束时间"
        align="center"
        key="endTime"
        prop="endTime"
        min-width="120"
      />
      <el-table-column
        label="任务执行人"
        align="center"
        key="endTime"
        prop="endTime"
        min-width="140"
      />
      <el-table-column
        label="运维公司"
        align="center"
        key="createBy"
        prop="createBy"
        min-width="120"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      style="margin-bottom: 8px"
    />
  </el-dialog>
</template>
<script setup>
const emit = defineEmits();
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  detailRecord: {
    type: Object,
    default: () => {},
  },
});
const router = useRouter();

const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});
const dataList = ref([]);
const loading = ref(false);

function cancel() {
  emit("update:dialogVisible", false);
}

function handleJump(type) {
  router.push({
    path: `/smartschedu/schedulingtaskmgmt/schedulingEfficiencyEvalMap/${type}/${
      props?.detailRecord?.id || "12"
    }`,
  });
}
function getList() {}
</script>

<style lang="scss" scoped>
:deep(.map-popup) {
  .el-dialog {
    position: fixed !important;
    top: 100px !important;
    left: 200px !important;
    margin: 0 !important;
  }
}
.flex-content {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
