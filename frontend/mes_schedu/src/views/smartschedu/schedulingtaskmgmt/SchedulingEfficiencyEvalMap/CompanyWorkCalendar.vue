<template>
  <div class="app-container">
    <div>
      <div class="table-title">运维公司工作日历</div>
      <div class="chart-container">
        <div class="gantt-chart">
          <div>
            <div class="date-labels" style="display: flex">
              <div class="date-label gantt-header">公司</div>
              <div class="date-label gantt-header">人员</div>
              <div
                v-for="(day, index) in dateHeaders"
                :key="index"
                class="date-label gantt-header"
              >
                <div>{{ day.month }}月{{ day.dateNum }}日</div>
              </div>
            </div>
          </div>
          <div>
            <div
              v-for="(group, index) in filteredGroups"
              :key="index"
              class="gantt-row"
              style="display: flex"
            >
              <div class="date-label">
                <div>{{ group.maintainUnitName }}</div>
              </div>
              <div class="date-label">
                <div>{{ group.executorName }}</div>
              </div>
              <div
                v-for="(day, index) in dateHeaders"
                :key="index"
                class="date-label"
              ></div>
              <!-- 任务条 -->
              <div
                v-for="task in group.taskList"
                :key="task.id"
                class="gantt-task-block"
                :style="taskBlockStyle(task)"
                @click="showTaskDetail(task, $event)"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="legend">
        <div
          class="legend-item"
          v-for="taskType in taskTypes"
          :key="taskType.name"
        >
          <div
            class="legend-color"
            :style="{ backgroundColor: taskType.color }"
          ></div>
          <div>{{ taskType.name }}</div>
        </div>
      </div> -->

      <!-- 任务详情弹出层 -->
    </div>
    <el-dialog
      v-model="open"
      v-if="open"
      title="任务详情"
      apped-to-body
      width="700px"
      align-center
    >
      <el-row :gutter="20" class="detail-row">
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务名称：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="activeTask.taskName || '-'"
                placement="top"
                >{{ activeTask.taskName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">监测活动大类：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="activeTask.taskCode || '-'"
                placement="top"
                >{{ activeTask.taskCode || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务生成时间：</label>
            <span class="detail-value">{{ activeTask.startTime || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务结束时间：</label>
            <span class="detail-value">{{ activeTask.endTime || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务执行人：</label>
            <span class="detail-value">{{
              activeTask.executorName || "-"
            }}</span>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">运维公司：</label>
            <span class="detail-value">
              <el-tooltip
                :content="activeTask.maintainUnitName || '-'"
                placement="top"
                >{{ activeTask.maintainUnitName || "-" }}</el-tooltip
              >
            </span>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script setup>
import { qryCompanyWorkGanttData } from "@/api/smartschedu/home";
import dayjs from "dayjs";
const route = useRoute();

// 当前日期时间
const currentDateTime = ref("");
const currentDate = ref("");
const activeTask = ref({});

// 筛选状态
const filterStatus = ref("all");
const filterCompany = ref("all");

const open = ref(false);
const taskDetailPosition = ref({
  top: "0px",
  left: "0px",
});
const dateRange = ref([
  dayjs().startOf("month").toISOString().slice(0, 10),
  dayjs().endOf("month").toISOString().slice(0, 10),
]);
const updateDateTime = () => {
  const now = new Date();
  currentDateTime.value = now.toLocaleString("zh-CN");
  currentDate.value = now.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
};

onMounted(() => {
  updateDateTime();
  setInterval(updateDateTime, 1000);
});

// 初始化日期列表
const dateHeaders = computed(() => {
  if (!dateRange.value || dateRange.value.length < 2) return [];

  const start = new Date(dateRange.value[0]);
  const end = new Date(dateRange.value[1]);
  const dates = [];

  // 添加前一天作为起始点
  start.setDate(start.getDate() - 1);

  for (
    let date = new Date(start);
    date <= end;
    date.setDate(date.getDate() + 1)
  ) {
    const dateStr = date.toISOString().slice(0, 10);
    const day = date.getDay();

    dates.push({
      date: dateStr,
      day: ["日", "一", "二", "三", "四", "五", "六"][day],
      dateNum: date.getDate(),
      month: date.getMonth() + 1,
    });
  }
  return dates;
});

// 模拟运维数据
const generateTasks = () => {
  return [
    {
      company: "云峰科技",
      person: "张明",
      tasks: [
        {
          id: 1,
          title: "服务器安装",
          start: "2025-07-01",
          end: "2025-07-03",
          progress: 100,
          type: "设备安装",
          description:
            "数据中心A区安装新服务器集群，已完成全部物理安装和初始化",
        },
        {
          id: 2,
          title: "数据库迁移",
          start: "2025-07-05",
          end: "2025-07-08",
          progress: 60,
          type: "数据备份",
          description: "将数据库从旧平台迁移到新服务器，已完成数据迁移工作",
        },
      ],
    },
    {
      company: "云峰科技",
      person: "李华",
      tasks: [
        {
          id: 3,
          title: "网络升级",
          start: "2025-07-02",
          end: "2025-07-05",
          progress: 75,
          type: "网络巡检",
          description: "升级核心交换机硬件和固件，目前正在测试阶段",
        },
        {
          id: 4,
          title: "防火墙配置",
          start: "2025-07-08",
          end: "2025-07-10",
          progress: 20,
          type: "系统维护",
          description: "配置新防火墙策略，项目刚开始，等待网络拓扑图",
        },
      ],
    },
    {
      company: "创智信息",
      person: "王强",
      tasks: [
        {
          id: 5,
          title: "机房巡检",
          start: "2025-07-01",
          end: "2025-07-04",
          progress: 100,
          type: "系统维护",
          description: "完成对数据中心机房的全面检查，发现2处问题已解决",
        },
        {
          id: 77,
          title: "路由器故障",
          start: "2025-07-07",
          end: "2025-07-09",
          progress: 0,
          type: "故障处理",
          description: "核心路由器端口故障，等待备件到达后更换",
        },
        {
          id: 6,
          title: "路由器故障",
          start: "2025-07-15",
          end: "2025-07-20",
          progress: 0,
          type: "故障处理",
          description: "核心路由器端口故障，等待备件到达后更换",
        },
        {
          id: 99,
          title: "路由器故障",
          start: "2025-07-29",
          end: "2025-07-30",
          progress: 0,
          type: "故障处理",
          description: "核心路由器端口故障，等待备件到达后更换",
        },
      ],
    },
    {
      company: "创智信息",
      person: "陈芳",
      tasks: [
        {
          id: 7,
          title: "备份系统升级",
          start: "2025-07-03",
          end: "2025-07-05",
          progress: 100,
          type: "数据备份",
          description: "备份系统软件已升级至最新版本，并通过测试",
        },
        {
          id: 8,
          title: "服务器迁移",
          start: "2025-07-06",
          end: "2025-07-08",
          progress: 50,
          type: "设备安装",
          description: "将旧服务器迁移至虚拟化平台，已完成一半工作负载迁移",
        },
      ],
    },
    {
      company: "迅捷科技",
      person: "赵伟",
      tasks: [
        {
          id: 9,
          title: "安全审计",
          start: "2025-07-02",
          end: "2025-07-06",
          progress: 25,
          type: "安全审计",
          description: "网络安全合规审计，正在评估防火墙和入侵检测系统",
        },
      ],
    },
    {
      company: "迅捷科技",
      person: "郑秀",
      tasks: [
        {
          id: 10,
          title: "数据库优化",
          start: "2025-07-01",
          end: "2025-07-03",
          progress: 100,
          type: "系统维护",
          description: "优化生产数据库性能，已完成所有指标测试",
        },
        {
          id: 11,
          title: "灾备测试",
          start: "2025-07-07",
          end: "2025-07-10",
          progress: 0,
          type: "数据备份",
          description: "灾难恢复计划测试，计划安排在本周末",
        },
      ],
    },
  ];
};

const groups = ref(generateTasks());

// 为每个任务添加公司、人员信息
groups.value.forEach((group) => {
  group.tasks.forEach((task) => {
    task.maintainUnitName = group.maintainUnitName;
    task.executorName = group.executorName;
    task.days =
      (new Date(task.endDate) - new Date(task.startDate)) /
        (1000 * 60 * 60 * 24) +
      1;
  });
});

function getData() {
  const searchParams = new URLSearchParams(location.search);

  qryCompanyWorkGanttData({
    maintainUnitCode: searchParams.get("operationUnit"),
    startDate: dateRange.value[0],
    endDate: dateRange.value[1],
  }).then((res) => {
    groups.value = res.data;
  });
}

// 筛选数据
const filteredGroups = computed(() => {
  let result = groups.value;

  // 按公司筛选
  if (filterCompany.value !== "all") {
    result = result.filter((g) => g.company === filterCompany.value);
  }

  // 按任务状态筛选
  if (filterStatus.value !== "all") {
    result = result
      .map((group) => {
        const tasks = group.tasks.filter((task) => {
          if (filterStatus.value === "ongoing")
            return task.progress > 0 && task.progress < 100;
          if (filterStatus.value === "completed") return task.progress === 100;
          if (filterStatus.value === "planned") return task.progress === 0;
          return true;
        });
        return { ...group, tasks };
      })
      .filter((group) => group.tasks.length > 0);
  }

  return result;
});

// 计算任务起始位置和宽度
const taskBlockStyle = (task) => {
  const startDate = new Date(task.startDate);
  const endDate = new Date(task.endDate);
  const rangeStartDate = new Date(dateRange.value[0]);
  // 计算任务在时间线上的起始位置（天数）
  const startOffset = (startDate - rangeStartDate) / (24 * 60 * 60 * 1000) + 1;
  // 计算时间持续天数 (天数)
  const duration = (endDate - startDate) / (24 * 60 * 60 * 1000) + 1;

  return {
    backgroundColor: "#409EFF",
    left: `${160 + startOffset * 80}px`,
    width: `${duration * 80}px`,
    height: "16px",
  };
};

// 显示任务详情
const showTaskDetail = (task, event) => {
  activeTask.value = task;
  open.value = true;
  taskDetailPosition.value = {
    top: `${event.clientY + 10}px`,
    left: `${event.clientX + 10}px`,
  };

  // 添加点击关闭事件
  setTimeout(() => {
    const closeDetail = (e) => {
      if (activeTask.value && !event.target.closest(".task-detail")) {
        activeTask.value = null;
        document.removeEventListener("click", closeDetail);
      }
    };
    document.addEventListener("click", closeDetail);
  }, 0);
};

getData();
</script>

<style lang="scss" scoped>
.chart-container {
  overflow-x: auto;
  padding: 0 10px;
}
.fixed-column {
  position: sticky;
  left: 0;
  z-index: 10;
  background: #f8f9fa;
  font-weight: 500;
}
.fixed-column.person {
  left: 120px;
  background: #e6f7ff;
}
.gantt-container {
  position: relative;
  height: 40px;
  //   width: calc(100% - 160px);
}
.gantt-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 30px;
  transform: translateY(-50%);
  display: flex;
}
.gantt-task-block {
  height: 100%;
  display: block;
  font-size: 12px;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: absolute;
  z-index: 99;
  top: 10px;
}
.gantt-task-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.gantt-task-text {
  position: relative;
  z-index: 2;
  padding: 0 8px;
}
.legend {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 15px;
  flex-wrap: wrap;
  background: #f9fbfd;
  border-top: 1px solid #eaeff4;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 12px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}
.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.date-label {
  position: sticky;
  top: 0;
  border-bottom: 1px solid #dfe4ea;
  min-width: 80px;
  text-align: center;
  padding: 8px;
  z-index: 10;
  font-size: 14px;
  color: rgb(96, 98, 102);
}
.timeline-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  z-index: 0;
  color: #dfe4ea;
}
.grid-cell {
  flex: 1;
  border-right: 1px solid #e0e3e6;
}

.gantt-row {
  position: relative;
  //   border-bottom: 1px solid #646668;
}
.gantt-header {
  background-color: #f8f8f9 !important;
  height: 40px;
  color: #515a6e;
  font-size: 13px;
  font-weight: 600;
}
</style>
