<template>
  <el-dialog
    :title="type === 'edit' ? '任务审批' : '任务详情'"
    :model-value="open"
    align-center
    width="710px"
    append-to-body
    @close="cancel"
  >
    <div class="detail-container">
      <div v-if="detailRecord.id" class="table-title">任务信息</div>
      <el-row v-if="detailRecord.id" :gutter="20" class="detail-row">
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务名称：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.taskName || '-'"
                placement="top"
                >{{ detailRecord.taskName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务编码：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.taskCode || '-'"
                placement="top"
                >{{ detailRecord.taskCode || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">省份：</label>
            <span class="detail-value">{{
              detailRecord.provinceName || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">地市：</label>
            <span class="detail-value">{{ detailRecord.cityName || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务执行人：</label>
            <span class="detail-value">{{
              detailRecord.siteTypeName || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务派发时间：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="detailRecord.siteName || '-'"
                placement="top"
                >{{ detailRecord.siteName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">联系方式：</label>
            <span class="detail-value">{{
              detailRecord.businessTypeName || "-"
            }}</span>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务开始时间：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.activityTypeName || '-'"
                placement="top"
                >{{ detailRecord.activityTypeName || "-" }}</el-tooltip
              >
            </span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">运维单位：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.activitySubtypeName || '-'"
                placement="top"
                >{{ detailRecord.activitySubtypeName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">任务结束时间：</label>
            <span class="detail-value">{{
              detailRecord.isCompanion || "-"
            }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">交割地址：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.companionRule || '-'"
                placement="topLeft"
                >{{ detailRecord.companionRule || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">送样地点：</label>
            <span class="detail-value">
              <el-tooltip
                :content="detailRecord.companionRule || '-'"
                placement="topLeft"
                >{{ detailRecord.companionRule || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
      </el-row>
      <div v-if="detailRecord.id" class="table-title">计划信息</div>
      <el-row
        v-for="(item, index) in detailRecord?.planInfos"
        :key="index"
        :gutter="20"
        class="detail-row"
      >
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划名称：</label>
            <span class="detail-value"
              ><el-tooltip :content="item?.planName || '-'" placement="top">{{
                item?.planName || "-"
              }}</el-tooltip></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划编码：</label>
            <span class="detail-value"
              ><el-tooltip :content="item?.planCode || '-'" placement="top">{{
                item?.planCode || "-"
              }}</el-tooltip></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划优先级：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="planPriorityMap[item?.planPriority]?.text || '-'"
                placement="top"
                >{{
                  planPriorityMap[item?.planPriority]?.text || "-"
                }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">计划来源：</label>
            <span class="detail-value"
              ><el-tooltip
                :content="planSourceMap[item?.planSource]?.text || '-'"
                placement="top"
                >{{ planSourceMap[item?.planSource]?.text || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">站点类型：</label>
            <span class="detail-value">{{ item?.siteTypeName || "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">监测活动大类：</label>
            <span class="detail-value">
              <el-tooltip
                :content="item?.activityTypeName || '-'"
                placement="top"
                >{{ item?.activityTypeName || "-" }}</el-tooltip
              >
            </span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">站点名称：</label>
            <span class="detail-value"
              ><el-tooltip :content="item?.siteName || '-'" placement="top">{{
                item?.siteName || "-"
              }}</el-tooltip></span
            >
          </div>
        </el-col>
        <!-- <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">业务类型：</label>
          <span class="detail-value">{{ item?.businessTypeName || "-" }}</span>
        </div>
      </el-col> -->

        <el-col :span="12">
          <div class="detail-item">
            <label class="detail-label">监测活动小类：</label>
            <span class="detail-value">
              <el-tooltip
                :content="item?.activitySubtypeName || '-'"
                placement="top"
                >{{ item?.activitySubtypeName || "-" }}</el-tooltip
              ></span
            >
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-if="type === 'edit'" class="table-title">计划审批</div>

    <el-form
      v-if="type === 'edit'"
      :model="form"
      :rules="rules"
      ref="siteRef"
      label-width="110px"
    >
      <el-form-item label="审批结果" prop="approvalStatus">
        <el-radio-group v-model="form.approvalStatus">
          <el-radio value="approved">通过</el-radio>
          <el-radio value="rejected">不通过</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="审批意见" prop="approvalOpinion">
        <el-input
          type="textarea"
          v-model="form.approvalOpinion"
          placeholder="请输入审批意见"
          clearable
          :rows="4"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="type === 'edit'" type="primary" @click="submitForm"
          >提交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import { planSourceMap, planPriorityMap } from "../../common/optionsData";
import { nextTick, watch } from "vue";
const { proxy } = getCurrentInstance();
import { verifyTaskInfo } from "@/api/smartschedu/task";

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  detailRecord: {
    type: Object,
    default: () => {},
  },
  ids: {
    type: Array,
    default: () => [],
  },
});
const loadingBtn = ref(false);

const initFlag = ref(true);

const data = reactive({
  form: {
    approvalStatus: "approved",
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  rules: {
    approvalStatus: [
      {
        required: true,
        message: "请选择审批状态",
        trigger: "change",
      },
    ],
    approvalOpinion: [
      {
        required: false,
        message: "请输入审批意见",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.detailRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          approvalStatus: "approved",
          isInit: true,
        };
      });
    }
  }
);

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  loadingBtn.value = false;
  initFlag.value = true;
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtn.value = true;
      verifyTaskInfo({
        ...form.value,
        id: props?.ids?.join(",") || props?.detailRecord?.id + "",
      }).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        emit("update:open", false);
        props?.getList();
        loadingBtn.value = false;
      });
    }
  });
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
:deep(.custom-descriptions) {
  .el-descriptions__label {
    text-align: right !important;
  }
}

.detail-container {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}
</style>
