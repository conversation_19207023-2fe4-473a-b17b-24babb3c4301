export const basinListMock = {
  code: 200,
  data: [
    { id: 1, name: "河道1" },
    { id: 2, name: "河道2" },
  ],
};

export const stationTypeListMock = {
  code: 200,
  data: [
    { id: 1, name: "站点类型1" },
    { id: 2, name: "站点类型2" },
  ],
};

export const dataTypeListMock = {
  code: 200,
  data: [
    { id: 1, name: "数据类型1" },
    { id: 2, name: "数据类型2" },
  ],
};

export const moniParamListMock = {
  code: 200,
  data: [
    { id: 1, name: "参数1", unit: "单位1", range: "范围1" },
    { id: 2, name: "参数2", unit: "单位2", range: "范围2" },
    { id: 3, name: "参数3", unit: "单位3", range: "范围3" },
    { id: 4, name: "参数4", unit: "单位4", range: "范围4" },
    { id: 5, name: "参数5", unit: "单位5", range: "范围5" },
    { id: 6, name: "参数6", unit: "单位6", range: "范围6" }
  ],
};

export const cityNameListMock = {
  code: 200,
  data: [
    { id: 1, name: "城市1" },
    { id: 2, name: "城市2" },
  ],
};

export const tableDataMock = {
  code: 200,
  data: {
    list: [
      { id: 1, basinName: '河道1', cityName: '大兴安岭地区', stationName: '加格达奇上', stationCode: 'A-734362', onlineStatus: '在线', moniTime: '2021-08-01 10:00:00', waterType: 'Ⅲ类', waterTemp: 20.1, ph: 7, do: 6.9, ec: 200, tds: 3.2, nh3n: 4.2, tp: 0.67, no3: 0.32, tn: 0.66 },
      { id: 2, basinName: '河道2', cityName: 'xx地区', stationName: 'xxxx', stationCode: 'A-223113', onlineStatus: '在线', moniTime: '2024-08-01 10:00:00', waterType: 'Ⅲ类', waterTemp: 10.1, ph: 8, do: 8.9, ec: 100, tds: 2.2, nh3n: 3.2, tp: 0.57, no3: 0.22, tn: 0.56 },
      { id: 3, basinName: '河道3', cityName: '大兴安岭地区', stationName: '加格达奇上', stationCode: 'A-734362', onlineStatus: '在线', moniTime: '2021-08-01 10:00:00', waterType: 'Ⅲ类', waterTemp: 20.1, ph: 7, do: 6.9, ec: 200, tds: 3.2, nh3n: 4.2, tp: 0.67, no3: 0.32, tn: 0.66 }
    ],
    total: 3
  }
}

export const topChartDataMock = [
  { dt: '2021-08-01 10时', waterTemp: 20.1, ph: 7, do: 6.9, ec: 200, tds: 3.2, nh3n: 4.2, tp: 0.67, no3: 0.32, tn: 0.66 },
  { dt: '2021-08-01 11时', waterTemp: 19.8, ph: 7.2, do: 7.1, ec: 195, tds: 3.1, nh3n: 4.0, tp: 0.65, no3: 0.31, tn: 0.65 },
  { dt: '2021-08-01 12时', waterTemp: 21.0, ph: 6.8, do: 7.0, ec: 205, tds: 3.3, nh3n: 4.3, tp: 0.68, no3: 0.33, tn: 0.67 },
  { dt: '2021-08-01 13时', waterTemp: 20.5, ph: 7.1, do: 6.8, ec: 202, tds: 3.25, nh3n: 4.1, tp: 0.665, no3: 0.315, tn: 0.665 },
  { dt: '2021-08-01 14时', waterTemp: 19.9, ph: 7.0, do: 7.2, ec: 198, tds: 3.15, nh3n: 4.15, tp: 0.675, no3: 0.325, tn: 0.67 },
  { dt: '2021-08-01 15时', waterTemp: 20.2, ph: 7.3, do: 7.3, ec: 201, tds: 3.22, nh3n: 4.22, tp: 0.672, no3: 0.322, tn: 0.668 },
  { dt: '2021-08-01 16时', waterTemp: 20.0, ph: 6.9, do: 7.0, ec: 200, tds: 3.20, nh3n: 4.20, tp: 0.670, no3: 0.320, tn: 0.666 },
  { dt: '2021-08-01 17时', waterTemp: 19.7, ph: 7.1, do: 6.9, ec: 197, tds: 3.18, nh3n: 4.18, tp: 0.668, no3: 0.322, tn: 0.667 },
  { dt: '2021-08-01 18时', waterTemp: 20.3, ph: 7.2, do: 7.2, ec: 203, tds: 3.26, nh3n: 4.26, tp: 0.676, no3: 0.326, tn: 0.671 },
  { dt: '2021-08-01 19时', waterTemp: 19.6, ph: 7.0, do: 7.3, ec: 196, tds: 3.17, nh3n: 4.17, tp: 0.673, no3: 0.323, tn: 0.669 },
]

export const bottomChartDataMock = [
  { dt: '2021-08-01 10时', tds1: 3.2, tds2: 4.2},
  { dt: '2021-08-01 11时', tds1: 3.1, tds2: 4.1},
  { dt: '2021-08-01 12时', tds1: 3.3, tds2: 4.3},
  { dt: '2021-08-01 13时', tds1: 3.25, tds2: 4.25},
  { dt: '2021-08-01 14时', tds1: 3.15, tds2: 4.15},
  { dt: '2021-08-01 15时', tds1: 3.22, tds2: 4.22},
  { dt: '2021-08-01 16时', tds1: 3.20, tds2: 4.20},
  { dt: '2021-08-01 17时', tds1: 3.18, tds2: 4.18},
  { dt: '2021-08-01 18时', tds1: 3.26, tds2: 4.26},
  { dt: '2021-08-01 19时', tds1: 3.17, tds2: 4.17},
];
