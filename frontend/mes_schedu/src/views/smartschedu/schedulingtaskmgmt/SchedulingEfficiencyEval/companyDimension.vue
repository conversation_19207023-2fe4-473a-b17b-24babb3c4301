<template>
  <el-row class="tabs-body-container" :gutter="20">
    <splitpanes
      :horizontal="appStore.device === 'mobile'"
      class="default-theme"
    >
      <!--部门数据-->
      <pane size="16">
        <area-tree
          ref="areaTreeRef"
          :isSelect="true"
          :onlySelectLeaf="true"
          :customTreeList="areaList"
          :customProps="{ label: 'name', children: 'children', value: 'code' }"
          @filter-node="filterNode"
          @check-change="handleCheckChange"
        />
      </pane>

      <!--用户数据-->
      <pane class="table-container" size="84">
        <el-col>
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            label-width="100px"
            class="query-form"
          >
            <el-form-item label="业务分类" prop="businessType">
              <el-select
                v-model="queryParams.businessType"
                placeholder="业务分类"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in businessTypeOptions"
                  :key="dict.dictCode"
                  :label="dict.dictValue"
                  :value="dict.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="任务执行时间" prop="planTime">
              <el-date-picker
                type="datetimerange"
                v-model="queryParams.planTime"
                placeholder="选择开始时间"
                style="width: 240px"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :shortcuts="shortcuts"
              />
            </el-form-item>
            <el-form-item class="form-btn">
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <div style="width: 100%" class="table-title">任务完成统计</div>
          <el-row :gutter="24">
            <el-col
              v-for="(item, index) in summaryList"
              :key="index"
              class="summay-container"
              :span="6"
            >
              <img class="top-btn" src="@/assets/images/icon_dizuo.png" />
              <el-card class="custom-card-small">
                <el-row>
                  <el-col :span="12" class="text-left">
                    <div class="text">{{ item.title }}</div>
                  </el-col>
                  <el-col :span="12" class="text-right">
                    <el-statistic
                      class="custom-statistic"
                      title=""
                      :value="item.value || 0"
                    >
                      <template v-if="item.unit" #suffix>
                        {{ item.unit }}
                      </template>
                    </el-statistic>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-card>
                <SubTitle titleName="任务完成和未完成趋势图"></SubTitle>
                <div style="width: 100%"><line-chats :data="taskTrend" /></div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <SubTitle titleName="超时未完成任务原因分布"></SubTitle>
                <pie-charts />
              </el-card>
            </el-col>
          </el-row>
          <div style="width: 100%" class="table-title">计划类型统计</div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-card>
                <SubTitle titleName="计划类型统计"></SubTitle>
                <div style="width: 100%">
                  <bar-charts :data="planTypeStatic" />
                </div>
              </el-card>
            </el-col>
          </el-row>
          <div style="width: 100%" class="table-title">
            运维人员任务完成情况统计
          </div>
          <el-card style="position: relative; padding-bottom: 60px">
            <SubTitle titleName="运维人员任务完成情况统计列表"></SubTitle>
            <el-row :gutter="24">
              <el-col class="summay-container" :span="12">
                <img class="top-btn" src="@/assets/images/icon_dizuo.png" />

                <el-card class="custom-card" body-style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col :span="12">
                      <div class="text-left">运维人员数量</div>
                    </el-col>
                    <el-col :span="12" class="text-right">
                      <el-statistic
                        class="custom-statistic"
                        title=""
                        :value="
                          maintenanceStatic?.totalMaintenancePersonCount || 0
                        "
                      >
                      </el-statistic>
                    </el-col>
                  </el-row>
                </el-card>
              </el-col>
              <el-col class="summay-container" :span="12">
                <img class="top-btn" src="@/assets/images/icon_dizuo.png" />

                <el-card class="custom-card" body-style="width: 100%">
                  <el-row>
                    <el-col :span="12" class="text-left">
                      <div class="text">任务覆盖人员数量</div>
                    </el-col>
                    <el-col :span="12" class="text-right">
                      <el-statistic
                        class="custom-statistic"
                        title=""
                        :value="
                          maintenanceStatic?.taskMaintenancePersonCount || 0
                        "
                      >
                      </el-statistic>
                    </el-col>
                  </el-row>
                </el-card>
              </el-col>
            </el-row>
            <el-table
              v-loading="loading"
              :data="maintenanceList"
              @selection-change="handleSelectionChange"
              max-height="200px"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
            >
              <el-table-column
                label="账号"
                align="center"
                key="personCode"
                prop="personCode"
                :show-overflow-tooltip="true"
                min-width="160"
              />
              <el-table-column
                label="姓名"
                align="center"
                key="personName"
                prop="personName"
                width="160"
              />
              <el-table-column
                label="分配任务数量"
                align="center"
                key="totalCount"
                prop="totalCount"
                min-width="120"
              />

              <el-table-column
                label="已完成任务数量"
                align="center"
                key="completeTaskCount"
                prop="completeTaskCount"
                min-width="120"
              />
              <el-table-column
                label="未完成任务数量"
                align="center"
                key="unCompleteTaskCount"
                prop="unCompleteTaskCount"
                min-width="120"
              />
              <el-table-column
                label="任务平均时长（小时）"
                align="center"
                key="averageTime"
                prop="averageTime"
                min-width="180"
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
              style="margin-bottom: 8px"
            />
          </el-card>
        </el-col>
      </pane>
    </splitpanes>
  </el-row>
</template>

<script setup>
import useAppStore from "@/store/modules/app";
import {
  qryMaintenanceList,
  qryMaintenanceStatic,
  qryPlanTypeStatic,
  qryTaskStatic,
  qryTaskTrend,
  getRegionMaintainUnitTreeInfo,
} from "@/api/smartschedu/task";
import { getBusinessType } from "@/api/smartschedu/common";
import { Splitpanes, Pane } from "splitpanes";
import areaTree from "@/views/smartschedu/component/areaTree.vue";
import SubTitle from "@/components/SubTitle";
import LineChats from "./lineChats.vue";
import BarCharts from "./barCharts.vue";
import PieCharts from "./pieCharts.vue";
import "splitpanes/dist/splitpanes.css";
import { onMounted, onUpdated, ref, watch } from "vue";

const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const maintenanceList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);
const businessTypeOptions = ref(undefined);
const checkedKeyList = ref([]);
const taskTrend = ref([]);
const taskStatic = ref([]);
const planTypeStatic = ref([]);
const areaList = ref([]);
const maintenanceStatic = ref({
  taskMaintenancePersonCount: 0,
  totalMaintenancePersonCount: 0,
});
const areaTreeRef = ref(null);

const summaryList = ref([
  {
    title: "任务总量",
    value: 0,
    key: "taskCount",
  },
  {
    title: "任务工单变更率",
    value: 0,
    key: "completeTaskCount",
  },
  {
    title: "任务工单退回率",
    value: 0,
    key: "unCompleteTaskCount",
  },
  {
    title: "平均任务完成时长",
    value: 0,
    unit: "小时",
    key: "averageTaskTime",
  },
]);

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  maintainUnitCode: undefined,
  startDate: undefined,
  endDate: undefined,
  approvalStatus: undefined,
  businessType: "water",
});

watch(
  queryParams.value,
  () => {
    initData();
  },
  { deep: true }
);

const handleCheckChange = (data) => {
  checkedKeyList.value = [data.areaCode];
  initData();
};

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}
function getCompanyListTree() {
  getRegionMaintainUnitTreeInfo().then((response) => {
    areaList.value = [response.data];
    // 找到第一个叶子节点
    const firstLeafNode = response.data.find((item) => item.isLeaf);

    areaTreeRef.value?.areaTreeRef?.setCurrentKey("110101", true);
    handleCheckChange({ areaCode: "110101" });
  });
}

/** 查询用户列表 */
function getMaintenanceList() {
  loading.value = true;
  qryMaintenanceList(
    proxy.addDateRange(
      {
        ...queryParams.value,
        approvalStatus: queryParams.value.approvalStatus
          ? queryParams.value.approvalStatus
          : "pending,rejected,approved",
        maintainUnitCode: checkedKeyList.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    maintenanceList.value = res.data;
    loading.value = false;
  });
}

function getTaskTrend() {
  qryTaskTrend(
    proxy.addDateRange(
      {
        ...queryParams.value,
        maintainUnitCode: checkedKeyList.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    taskTrend.value = res.data;
  });
}

function getPlanTypeStatic() {
  qryPlanTypeStatic(
    proxy.addDateRange(
      {
        ...queryParams.value,
        maintainUnitCode: checkedKeyList.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    planTypeStatic.value = res.data;
  });
}

function getTaskStatic() {
  qryTaskStatic(
    proxy.addDateRange(
      {
        ...queryParams.value,
        maintainUnitCode: checkedKeyList.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    const temp = [...summaryList.value];

    temp?.map((item) => {
      item.value = res.data?.[item.key] || 0;
    });

    summaryList.value = temp;
    taskStatic.value = res.data;
  });
}

function getMaintenanceStatic() {
  qryMaintenanceStatic(
    proxy.addDateRange(
      {
        ...queryParams.value,
        maintainUnitCode: checkedKeyList.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    maintenanceStatic.value = res.data;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  
  handleQuery();
}

function initData() {
  getMaintenanceStatic();
  getMaintenanceList();
  getTaskTrend();
  getPlanTypeStatic();
  getTaskStatic();
}

getBusinessTypeList();
getCompanyListTree();
initData();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
.pagination-container {
  right: 11px !important;
  bottom: 0;
}
.table-container {
  overflow-y: auto;
  height: 100%;
}
.custom-statistic {
  --el-statistic-content-color: var(--el-color-primary);
}
.custom-card {
  height: 80px;
  margin-top: 40px;
  margin-bottom: 20px;
  background: url(@/assets/images/bg_596.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 120px;
}
.custom-card-small {
  height: 120px;
  padding-top: 60px;
  margin-top: 40px;
  margin-bottom: 20px;
  background: url(@/assets/images/bg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  box-shadow: none;
}
.tabs-body-container {
  overflow-y: auto;
  height: 100%;
}
.summay-container {
  position: relative;
  .top-btn {
    position: absolute;
    top: 0;
    left: 30px;
  }
}
.table-title {
  margin-top: 20px;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
</style>
