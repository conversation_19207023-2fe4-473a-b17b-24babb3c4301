<template>
  <div class="totalAlarm-chart" ref="alarmHandleInfoChartRef"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { init } from "echarts";

const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
});

const alarmHandleInfoChartRef = ref(null);
let topChartIns = null;

const removeChart = () => {
  if (topChartIns) {
    topChartIns.dispose();
    topChartIns = null;
  }
};

const initTopChart = async () => {
  // 添加容器尺寸检查
  if (
    !alarmHandleInfoChartRef.value ||
    alarmHandleInfoChartRef.value.clientWidth === 0
  ) {
    setTimeout(initTopChart, 100); // 延迟重试
    return;
  }

  const xData = [];
  const totalData = [];
  const completeData = [];
  const unCompleteTaskCount = [];
  console.log("props?.data", props?.data);

  props?.data?.map((item) => {
    xData.push(item.date);
    totalData.push(item.totalCount);
    completeData.push(item.completeTaskCount);
    unCompleteTaskCount.push(item.unCompleteTaskCount);
  });
  topChartIns = init(alarmHandleInfoChartRef.value);
  const option = {
    xAxis: {
      type: "category",
      data: xData,
    },
    grid: {
      left: "10%",
      right: "5%",
      bottom: "10%",
      top: "15%",
    },
    yAxis: {
      type: "value",
      name: "单位：个",
    },
    legend: {
      data: ["任务总量", "已完成任务数", "未完成任务数"],
    },
    tooltip: {
      trigger: "axis",
    },
    series: [
      {
        name: "任务总量",
        type: "line",
        smooth: true,
        data: totalData,
      },
      {
        name: "已完成任务数",
        type: "line",
        smooth: true,
        data: completeData,
      },
      {
        name: "未完成任务数",
        type: "line",
        smooth: true,
        data: unCompleteTaskCount,
      },
    ],
  };
  topChartIns.setOption(option);
  topChartIns.resize();
};

let resizeObserver = null;

onMounted(() => {
  if (alarmHandleInfoChartRef.value) {
    resizeObserver = new ResizeObserver(() => {
      if (topChartIns) {
        topChartIns.resize();
      }
    });
    resizeObserver.observe(alarmHandleInfoChartRef.value);

    setTimeout(() => {
      initTopChart();
    }, 50);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.unobserve(alarmHandleInfoChartRef.value);
  }
  removeChart();
});

watch(
  () => props.data,
  () => {
    removeChart();
    initTopChart();
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
.totalAlarm-chart {
  width: 100%;
  height: 300px;
}
</style>
