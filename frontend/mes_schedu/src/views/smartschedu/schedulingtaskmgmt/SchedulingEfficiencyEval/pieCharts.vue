<template>
  <div class="totalAlarm-chart" ref="alarmHandleInfoChartRef"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { topChartDataMock } from "./mockData";
import { color, init } from "echarts";

const props = defineProps({
  dataId: {
    type: String,
    default: "",
  },
});

const alarmHandleInfoChartRef = ref(null);
let topChartIns = null;

const removeChart = () => {
  if (topChartIns) {
    topChartIns.dispose();
    topChartIns = null;
  }
};

const initTopChart = async () => {
  // 添加容器尺寸检查
  if (
    !alarmHandleInfoChartRef.value ||
    alarmHandleInfoChartRef.value.clientWidth === 0
  ) {
    setTimeout(initTopChart, 100); // 延迟重试
    return;
  }

  topChartIns = init(alarmHandleInfoChartRef.value);
  const option = {
    legend: {
      top: "bottom",
    },
    toolbox: {
      show: true,
      feature: {
        mark: { show: false },
        dataView: { show: false, readOnly: false },
        restore: { show: false },
        saveAsImage: { show: false },
      },
    },
    series: [
      {
        name: "Nightingale Chart",
        type: "pie",
        radius: [20, 90],
        center: ["50%", "50%"],
        roseType: "area",
        itemStyle: {
          borderRadius: 8,
        },
        label: {
          formatter: "{b|{b}}\n  {c|{c}}  {per|{d}%}  ",
          // backgroundColor: "#F6F8FC",
          // borderColor: "#8C8D8E",
          borderWidth: 1,
          borderRadius: 4,
          rich: {
            a: {
              color: "#6E7079",
              lineHeight: 22,
              align: "center",
            },
            hr: {
              borderColor: "#8C8D8E",
              width: "100%",
              borderWidth: 1,
              height: 0,
            },
            b: {
              color: "gray",
              fontSize: 14,
              fontWeight: "bold",
              lineHeight: 33,
            },
            c: {
              color: "#1472FF",
              fontSize: 14,
            },
            per: {
              color: "#1472FF",
              fontSize: 14,

              // backgroundColor: "#4C5058",
              // padding: [3, 4],
              // borderRadius: 4,
            },
          },
        },
        data: [
          { name: "资源不足", value: 35 },
          { name: "人员短缺", value: 20 },
          { name: "设备故障", value: 15 },
          { name: "计划冲突", value: 10 },
          { name: "天气影响", value: 8 },
          { name: "材料延迟", value: 7 },
          { name: "其他", value: 5 },
        ],
      },
    ],
  };
  topChartIns.setOption(option);
};

let resizeObserver = null;

onMounted(() => {
  if (alarmHandleInfoChartRef.value) {
    resizeObserver = new ResizeObserver(() => {
      if (topChartIns) {
        topChartIns.resize();
      }
    });
    resizeObserver.observe(alarmHandleInfoChartRef.value);

    setTimeout(() => {
      initChart();
    }, 50);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.unobserve(alarmHandleInfoChartRef.value);
  }
  removeChart();
});

const initChart = (dataId) => {
  initTopChart();
};

onMounted(() => {
  initChart();
});
</script>
<style lang="scss" scoped>
.totalAlarm-chart {
  width: 100%;
  height: 300px;
}
</style>
