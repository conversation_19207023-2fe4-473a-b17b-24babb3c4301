<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane style="height: 100%" label="监测总站维度" name="first">
        <summary-dimension v-if="activeName === 'first'" />
      </el-tab-pane>
      <el-tab-pane style="height: 100%" label="运维公司维度" name="second">
        <company-dimension v-if="activeName === 'second'" />
      </el-tab-pane>
    </el-tabs>
    <el-button class="export-btn" type="primary" @click="handleClick"
      >下载调度效率评估报告</el-button
    >
  </div>
</template>

<script setup name="Schedulingefficiencyeval">
import companyDimension from "./companyDimension.vue";
import summaryDimension from "./summaryDimension.vue";
const activeName = ref("first");
const handleClick = (tab, event) => {
  console.log(tab, event);
};
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-right-btn {
  margin-left: unset;
}
.export-btn {
  position: absolute;
  right: 44px;
  top: 38px;
}
</style>
