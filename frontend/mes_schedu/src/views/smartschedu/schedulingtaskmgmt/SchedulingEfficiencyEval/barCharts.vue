<template>
  <div class="totalAlarm-chart" ref="alarmHandleInfoChartRef"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { topChartDataMock } from "./mockData";
import { init } from "echarts";

const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
});

const alarmHandleInfoChartRef = ref(null);
let topChartIns = null;

const removeChart = () => {
  if (topChartIns) {
    topChartIns.dispose();
    topChartIns = null;
  }
};

const posList = [
  "left",
  "right",
  "top",
  "bottom",
  "inside",
  "insideTop",
  "insideLeft",
  "insideRight",
  "insideBottom",
  "insideTopLeft",
  "insideTopRight",
  "insideBottomLeft",
  "insideBottomRight",
];
app.configParameters = {
  rotate: {
    min: -90,
    max: 90,
  },
  align: {
    options: {
      left: "left",
      center: "center",
      right: "right",
    },
  },
  verticalAlign: {
    options: {
      top: "top",
      middle: "middle",
      bottom: "bottom",
    },
  },
  position: {
    options: posList.reduce(function (map, pos) {
      map[pos] = pos;
      return map;
    }, {}),
  },
  distance: {
    min: 0,
    max: 100,
  },
};
app.config = {
  rotate: 90,
  align: "left",
  verticalAlign: "middle",
  position: "insideBottom",
  distance: 15,
  onChange: function () {
    const labelOption = {
      rotate: app.config.rotate,
      align: app.config.align,
      verticalAlign: app.config.verticalAlign,
      position: app.config.position,
      distance: app.config.distance,
    };
    myChart.setOption({
      series: [
        {
          label: labelOption,
        },
        {
          label: labelOption,
        },
        {
          label: labelOption,
        },
        {
          label: labelOption,
        },
      ],
    });
  },
};
const labelOption = {
  show: false,
  position: app.config.position,
  distance: app.config.distance,
  align: app.config.align,
  verticalAlign: app.config.verticalAlign,
  rotate: app.config.rotate,
  formatter: "{c}  {name|{a}}",
  fontSize: 16,
  rich: {
    name: {},
  },
};

const initTopChart = async () => {
  // 添加容器尺寸检查
  if (
    !alarmHandleInfoChartRef.value ||
    alarmHandleInfoChartRef.value.clientWidth === 0
  ) {
    setTimeout(initTopChart, 100); // 延迟重试
    return;
  }

  const xData = [];
  const totalData = [];
  const completeData = [];
  const exceptionData = [];

  props?.data?.map((item) => {
    xData.push(item?.activityTypeName || "");
    totalData.push(item?.totalCount);
    completeData.push(item?.completeCount);
    exceptionData.push(item?.exceptionCount);
  });

  topChartIns = init(alarmHandleInfoChartRef.value);
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["工单总数", "完成数", "异常数"],
    },
    toolbox: {
      show: false,
      orient: "vertical",
      left: "right",
      top: "center",
    },
    xAxis: [
      {
        type: "category",
        axisTick: { show: false },
        data: xData,
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "单位：个",
      },
    ],
    series: [
      {
        name: "工单总数",
        type: "bar",
        barGap: 0,
        barWidth: 20,
        label: labelOption,
        emphasis: {
          focus: "series",
        },
        data: totalData,
      },
      {
        name: "完成数",
        type: "bar",
        label: labelOption,
        barWidth: 20,

        emphasis: {
          focus: "series",
        },
        data: completeData,
      },
      {
        name: "异常数",
        type: "bar",
        label: labelOption,
        emphasis: {
          focus: "series",
        },
        data: exceptionData,
        barWidth: 20,
      },
    ],
  };
  topChartIns.setOption(option);
};

let resizeObserver = null;

onMounted(() => {
  if (alarmHandleInfoChartRef.value) {
    resizeObserver = new ResizeObserver(() => {
      if (topChartIns) {
        topChartIns.resize();
      }
    });
    resizeObserver.observe(alarmHandleInfoChartRef.value);

    setTimeout(() => {
      initTopChart();
    }, 50);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.unobserve(alarmHandleInfoChartRef.value);
  }
  removeChart();
});

onMounted(() => {
  initTopChart();
});

watch(
  () => props.data,
  () => {
    removeChart();
    initTopChart();
  }
);
</script>
<style lang="scss" scoped>
.totalAlarm-chart {
  width: 100%;
  height: 300px;
}
</style>
