<template>
  <el-col style="height: 100%">
    <div class="head-container">
      <el-input
        v-model="deptName"
        placeholder="请输入"
        clearable
        prefix-icon="Search"
        style="margin-bottom: 20px"
      />
    </div>
    <el-tree
      :show-checkbox="!isSelect"
      :props="{
        label: customProps?.label || 'name',
        children: customProps?.children || 'children',
        value: customProps?.value || 'code',
      }"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :data="treeList"
      ref="areaTreeRef"
      :node-key="customProps?.value || 'code'"
      :auto-expand-parent="true"
      highlight-current
      :default-expanded-keys="['000000']"
      @check="handleCheckChange"
      @node-click="handleNodeClickChange"
      style="width: 100%"
      v-bind="props"
      class="custom-tree-container"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <span>{{ node.label }}</span>
          <span v-if="data.num" style="margin-left: 16px"
            >({{ data.num }})</span
          >
        </div>
      </template>
    </el-tree>
  </el-col>
</template>

<script setup>
import { getRegionTreeInfo, getAreaTreeInfo } from "@/api/smartschedu/common";
import { nextTick, ref, watch } from "vue";

const props = defineProps({
  isSelect: {
    type: Boolean,
    default: false,
  },
  onlySelectLeaf: {
    type: Boolean,
    default: false,
  },
  customTreeList: {
    type: Array,
    default: () => [],
  },
  customProps: {
    type: Object,
    default: () => ({}),
  },
  needSite: {
    type: Boolean,
    default: true,
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["handleCheck", "handleNodeClick"]);

const areaTreeList = ref([]);
const areaTreeRef = ref(null);
const lastKey = ref(null);
const deptName = ref("");
const treeList = ref([]);
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data?.[props?.customProps?.label || "name"].indexOf(value) !== -1;
};

watch(
  () => props.customTreeList,
  () => {
    nextTick(() => {
      treeList.value =
        props?.customTreeList?.length > 0
          ? [...props.customTreeList]
          : areaTreeList.value;
    });
  },
  { deep: true, immediate: true }
);

/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  proxy.$refs["areaTreeRef"].filter(val);
});
function getAreaTreeList() {
  (props?.needSite ? getRegionTreeInfo : getAreaTreeInfo)().then((response) => {
    areaTreeList.value = [response.data];
    if (treeList.value?.length === 0) {
      treeList.value = [response.data];
    }
  });
}

// 部门树选中事件
const handleCheckChange = (obj, checkedKeys) => {
  emit("handleCheck", obj, checkedKeys);
};

const handleNodeClickChange = (data) => {
  if (props?.onlySelectLeaf) {
    if (!data.children || data.children.length === 0) {
      lastKey.value = data?.[props?.customProps?.value || "code"];
      emit("handleNodeClick", { ...data, code: data.id });
    } else {
      areaTreeRef.value.setCurrentKey(lastKey.value); // 清除当前选中状态
      return;
    }
  } else {
    emit("handleNodeClick", { ...data, code: data.id });
  }
};

defineExpose({ areaTreeRef });

getAreaTreeList();
</script>

<style scoped>
.el-tree-node.is-current:not(.is-leaf) .el-tree-node__content {
  background-color: transparent !important;
}
.custom-tree-container {
  height: calc(100% - 49px);
  overflow-y: auto;
}
</style>
