<template>
  <el-form-item label="业务分类" prop="businessType">
    <el-select
      v-model="form.businessType"
      value-key="id"
      placeholder="请选择业务分类"
      check-strictly
      :disabled="disabled"
    >
      <el-option
        v-for="dict in businessTypeOptions"
        :key="dict.dictCode"
        :label="dict.dictValue"
        :value="dict.dictCode"
      />
    </el-select>
  </el-form-item>
  <el-form-item label="省" prop="provinceCode">
    <el-select
      v-model="form.provinceCode"
      value-key="id"
      placeholder="请选择省"
      :disabled="disabled"
    >
      <el-option
        v-for="dict in provinceOptions"
        :key="dict.areaCode"
        :label="dict.areaName"
        :value="dict.areaCode"
    /></el-select>
  </el-form-item>
  <el-form-item label="站点类型" prop="siteType">
    <el-select
      v-model="form.siteType"
      placeholder="请选择站点类型"
      :disabled="disabled"
      @change="handleSiteTypeChange"
    >
      <el-option
        v-for="dict in siteTypeOptions"
        :key="dict.id"
        :label="dict.dictValue"
        :value="dict.id"
      />
    </el-select>
  </el-form-item>

  <el-form-item label="市" prop="cityCode">
    <el-select
      v-model="form.cityCode"
      value-key="id"
      placeholder="请选择市"
      :disabled="disabled"
    >
      <el-option
        v-for="dict in cityOptions"
        :key="dict.areaCode"
        :label="dict.areaName"
        :value="dict.areaCode"
    /></el-select>
  </el-form-item>
  <el-form-item label="站点名称" prop="siteId">
    <el-select
      v-model="form.siteId"
      placeholder="请选择站点名称"
      clearable
      :disabled="disabled"
    >
      <el-option
        v-for="dict in siteOptions"
        :key="dict.siteId"
        :label="dict.siteName"
        :value="Number(dict.siteId)"
      />
    </el-select>
  </el-form-item>
</template>

<script setup>
import {
  getSiteType,
  getBusinessType,
  getCityInfo,
  getProvinceInfo,
  getSiteInfo,
} from "@/api/smartschedu/common";
import { watch } from "vue";
const props = defineProps({
  form: Object,
  editRecord: Object,
  isSiteNameSingle: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
});
const businessTypeOptions = ref([]);
const siteTypeOptions = ref([]);
const cityOptions = ref([]);
const siteOptions = ref([]);
const provinceOptions = ref([]);

watch(
  [
    () => props.form.provinceCode,
    () => props.form.cityCode,
    () => props.form.siteType,
    () => props.form.businessType,
    () => props.form.isAutositeActivity,
  ],
  (
    [
      newProvinceCode,
      newCityCode,
      newSiteType,
      newBusinessType,
      newIsAutositeActivity,
    ],
    [
      oldProvinceCode,
      oldCityCode,
      oldSiteType,
      oldBusinessType,
      oldIsAutositeActivity,
    ]
  ) => {
    if (props?.form?.isInit) {
      return;
    }
    if (newProvinceCode !== oldProvinceCode) {
      getCityList(newProvinceCode);
    }

    if (newBusinessType !== oldBusinessType) {
      // props.form.siteType = undefined;
      getSiteTypeList(newBusinessType);
    }

    if (
      newSiteType !== oldSiteType ||
      newCityCode !== oldCityCode ||
      newProvinceCode !== oldProvinceCode ||
      newBusinessType !== oldBusinessType ||
      newIsAutositeActivity !== oldIsAutositeActivity
    ) {
      getSiteList(
        newProvinceCode,
        newCityCode,
        props.form.siteTypeAlias,
        newBusinessType,
        newIsAutositeActivity
      );
      // if (newIsAutositeActivity !== oldIsAutositeActivity) {
      //   props.form.siteId = undefined;
      // }
      // 获取省份名称
      const selectedProvince = provinceOptions.value?.find(
        (item) => item.areaCode === newProvinceCode
      );
      props.form.provinceName =
        selectedProvince?.areaName || props.form.provinceName || "";

      // 获取城市名称
      const selectedCity = cityOptions.value?.find(
        (item) => item.areaCode === newCityCode
      );
      props.form.cityName = selectedCity?.areaName || props.form.cityName || "";
    }
  },
  { deep: true }
);

watch(
  () => props.form.siteId,
  (newSiteId) => {
    const selectedSite = siteOptions.value?.find(
      (item) => item.siteId == newSiteId
    );
    props.form.siteName = selectedSite?.siteName || props.form.siteName || "";

    const isAuto =
      selectedSite?.hasAutomaticStation || props.form.isAutosite || "";
    if (isAuto) {
      props.form.isAutosite = isAuto === "Y" || isAuto === "1" ? "1" : "0";
    }
  }
);

function handleSiteTypeChange(value) {
  const temp = siteTypeOptions.value?.find((item) => item.id === value);
  props.form.siteTypeAlias = temp?.dictCode;
  // 水断面为非自动站时，国控站为自动站
  if (temp?.id === -100) {
    props.form.isAutosite = "0";
    props.form.isAutositeActivity = "0";
  } else {
    props.form.isAutosite = "1";
    props.form.isAutositeActivity = "1";
  }
}

function getSiteTypeList(parentDictCode) {
  getSiteType({ parentDictCode }).then((response) => {
    let tempList = response.data;
    if (parentDictCode !== "air") {
      tempList = [
        {
          dictValue: "水断面",
          parentDictCode: "water",
          parentClassCode: "monitoring_element",
          id: -100,
          dictCode: "01",
        },
        ...response.data,
      ];
    }
    siteTypeOptions.value = tempList;
    if (typeof props.form.siteType === "string") {
      const temp = tempList?.find((item) => {
        if (props.editRecord.businessType !== "air") {
          if (props.editRecord.hasAutomaticStation === "N") {
            return item.dictCode === props.form.siteType && item.id === -100;
          }
        }
        return item.dictCode === props.form.siteType && item.id !== -100;
      });
      props.form.siteType = temp?.id;
      props.form.siteTypeAlias = temp?.dictCode;
      // 水断面为非自动站时，国控站为自动站
      if (temp?.id === -100) {
        props.form.isAutosite = "0";
        props.form.isAutositeActivity = "0";
      } else {
        props.form.isAutosite = "1";
        props.form.isAutositeActivity = "1";
      }
    }
  });
}

function getSiteList(province, city, siteType, businessType, isAutosite) {
  const obj = { province, city, siteType, businessType };
  if (isAutosite !== "2" && businessType === "water") {
    obj.isAutosite = isAutosite;
  }
  siteOptions.value = [];
  getSiteInfo({ ...obj }).then((response) => {
    siteOptions.value = response.data;
  });
}

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}

function getCityList(parentCode) {
  getCityInfo({ parentCode }).then((response) => {
    cityOptions.value = response.data;
  });
}
function getProvinceList() {
  getProvinceInfo().then((response) => {
    provinceOptions.value = response.data;
  });
}

getBusinessTypeList();
getSiteTypeList();
getProvinceList();
getSiteList();
</script>
