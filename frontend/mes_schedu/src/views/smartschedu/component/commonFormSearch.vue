<template>
  <el-form-item label="业务分类" prop="businessType">
    <el-select
      v-model="queryParams.businessType"
      placeholder="业务分类"
      style="width: 240px"
    >
      <el-option
        v-for="dict in businessTypeOptions"
        :key="dict.dictCode"
        :label="dict.dictValue"
        :value="dict.dictCode"
      />
    </el-select>
  </el-form-item>
  <el-form-item label="站点类型" prop="siteTypeTemp">
    <el-select
      v-model="queryParams.siteTypeTemp"
      placeholder="站点类型"
      clearable
      style="width: 240px"
      @change="handleSiteTypeChange"
    >
      <el-option
        v-for="dict in siteTypeOptions"
        :key="dict.id"
        :label="dict.dictValue"
        :value="dict.id"
      />
    </el-select>
  </el-form-item>

  <el-form-item v-if="needActivity" label="监测活动大类" prop="activityType">
    <el-select
      v-model="queryParams.activityType"
      placeholder="监测活动大类"
      clearable
      style="width: 240px"
    >
      <el-option
        v-for="dict in activityParentTypeOptions"
        :key="dict.activityTypeCode"
        :label="dict.activityTypeName"
        :value="dict.activityTypeCode"
      />
    </el-select>
  </el-form-item>
  <el-form-item v-if="needActivity" label="监测活动小类" prop="activitySubtype">
    <el-select
      v-model="queryParams.activitySubtype"
      placeholder="监测活动小类"
      clearable
      style="width: 240px"
    >
      <el-option
        v-for="dict in activityTypeOptions"
        :key="dict.activitySubtypeCode"
        :label="dict.activitySubtypeName"
        :value="dict.activitySubtypeCode"
      />
    </el-select>
  </el-form-item>
</template>

<script setup>
import {
  getSiteType,
  getBusinessType,
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { onMounted, watch, nextTick } from "vue";
const props = defineProps({
  queryParams: Object,
  needActivity: {
    type: Boolean,
    default: true,
  },
  isRegular: {
    type: Boolean,
    default: false,
  },
});
const businessTypeOptions = ref([]);
const siteTypeOptions = ref(undefined);
const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);
function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}

function handleSiteTypeChange(value) {
  const temp = siteTypeOptions.value?.find((item) => {
    return item.id === value;
  });
  props.queryParams.siteType = temp?.dictCode;

  if (props.queryParams.businessType === "air") {
    delete props.queryParams.isAutosite;
    return;
  }

  // 水断面为非自动站时，国控站为自动站
  if (temp?.id === -100) {
    props.queryParams.isAutosite = "0";
  } else {
    props.queryParams.isAutosite = "1";
  }
}

function getSiteTypeList(parentDictCode) {
  getSiteType({ parentDictCode }).then((response) => {
    let tempList = response.data;
    if (parentDictCode !== "air") {
      tempList = [
        {
          dictValue: "水断面",
          parentDictCode: "water",
          parentClassCode: "monitoring_element",
          id: -100,
          dictCode: "01",
        },
        ...response.data,
      ];
    }
    siteTypeOptions.value = tempList;
  });
}

function getActivityList(activityTypeCode, businessType, siteType) {
  const obj = { activityTypeCode, businessType, siteType };
  if (props?.isRegular) {
    obj.isRegular = 1;
  }
  getActivityType(obj).then((response) => {
    activityTypeOptions.value = response.data;
  });
}
function getActivityParentTypeList(businessType, siteType, isAutosite) {
  const obj = {
    businessType,
    siteType,
  };
  if (businessType === "water") {
    obj.isAutosite = isAutosite;
  }
  if (props?.isRegular) {
    obj.isRegular = 1;
  }
  getActivityParentType(obj).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

watch(
  [
    () => props.queryParams.businessType,
    () => props.queryParams.siteTypeTemp,
    () => props.queryParams.activityType,
    () => props.queryParams.isAutosite,
  ],
  (
    [newBusinessType, newSiteTypeTemp, newActivityType, newIsAutosite],
    [oldBusinessType, oldSiteTypeTemp, oldActivityType, oldIsAutosite]
  ) => {
    if (
      newBusinessType != oldBusinessType ||
      newSiteTypeTemp != oldSiteTypeTemp ||
      newIsAutosite != oldIsAutosite
    ) {
      props.queryParams.activityType = "";
      props.queryParams.activitySubtype = "";
      if (newBusinessType !== oldBusinessType) {
        props.queryParams.siteType = "";
        props.queryParams.siteTypeTemp = "";
        if (props.queryParams.businessType === "air") {
          delete props.queryParams.isAutosite;
        }
        getSiteTypeList(newBusinessType);
      }

      getActivityParentTypeList(
        newBusinessType,
        props.queryParams.siteType,
        newIsAutosite
      );
    }
    if (
      newBusinessType != oldBusinessType ||
      newSiteTypeTemp != oldSiteTypeTemp ||
      newActivityType != oldActivityType
    ) {
      props.queryParams.activitySubtype = "";

      getActivityList(
        newActivityType,
        newBusinessType,
        props.queryParams.siteType
      );
    }
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    props.queryParams.businessType = "water";
    getSiteTypeList("water");
    getActivityParentTypeList("water", null);
    getActivityList(null, "water", props.queryParams.siteType);
  });
});

getBusinessTypeList();
getActivityList();
getActivityParentTypeList();
getSiteTypeList();
</script>
