<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16">
          <area-tree @handleCheck="handleCheckChange"></area-tree>
        </pane>
        <!--用户数据-->
        <pane class="table-container" size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="100px"
              class="query-form"
            >
              <el-form-item label="业务分类" prop="businessType">
                <el-select
                  v-model="queryParams.businessType"
                  value-key="id"
                  placeholder="请选择业务分类"
                  check-strictly
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in businessTypeOptions"
                    :key="dict.dictCode"
                    :label="dict.dictValue"
                    :value="dict.dictCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设备编码" prop="algruleCode">
                <el-input
                  v-model="queryParams.algruleCode"
                  placeholder="请输入设备编码"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="设备名称" prop="algruleName">
                <el-input
                  v-model="queryParams.algruleName"
                  placeholder="请输入设备名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item style="width: 340px" />
              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row
              :gutter="24"
              style="display: flex; justify-content: space-between"
              class="mb8 table-header"
            >
              <el-col :span="12">
                <div style="width: 100%" class="table-title">设备列表</div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button
                  type="primary"
                  icon="Download"
                  @click="exportList"
                  v-hasPermi="['system:user:add']"
                  >导 出</el-button
                >
                <el-button
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['system:user:add']"
                  >新增设备</el-button
                >
              </el-col>
            </el-row>

            <el-table
              v-loading="loading"
              :data="dataList"
              @selection-change="handleSelectionChange"
              max-height="calc(100vh - 460px)"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
                fixed
              />
              <el-table-column
                label="设备名称"
                align="center"
                key="algruleName"
                prop="algruleName"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="设备编码"
                align="center"
                key="algruleCode"
                prop="algruleCode"
                v-if="columns[4].visible"
                width="160"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                label="设备大类"
                align="center"
                key="algruleTypeName"
                prop="algruleTypeName"
                width="160"
              />
              <el-table-column
                label="设备小类"
                align="center"
                key="algruleSubtypeName"
                prop="algruleSubtypeName"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column
                label="关联算法"
                align="center"
                key="algorithmName"
                prop="algorithmName"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column
                label="生效地区"
                align="center"
                key="effectiveareaName"
                prop="effectiveareaName"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column label="启用状态" align="center">
                <template #default="scope">
                  <el-switch
                    :model-value="scope.row.algruleStatus"
                    :active-value="1"
                    :inactive-value="0"
                    @change="(value) => handleStatusChange(value, scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="创建时间"
                align="center"
                key="createTime"
                prop="createTime"
                v-if="columns[11].visible"
                width="180"
              />
              <el-table-column
                label="操作"
                align="center"
                width="150"
                class-name="small-padding fixed-width custom-action-column"
                fixed="right"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    v-if="!scope.row.approvalStatus"
                    @click="handleUpdate(scope.row, 'edit')"
                    >编辑</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    @click="handleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
      :type="openType"
    ></add-form>
  </div>
</template>

<script setup name="EquipmentReverseControlCfg">
import useAppStore from "@/store/modules/app";
import addForm from "./addForm.vue";
import {
  qryAlgorithmLibrary,
  delAlgorithmLibrary,
  exportAlgorithmLibrary,
  saveAlgorithmLibrary,
} from "@/api/smartschedu/algorithm";
import { downloadFile } from "@/utils/index";
import { Splitpanes, Pane } from "splitpanes";
import areaTree from "@/views/smartschedu/component/areaTree.vue";
import { getBusinessType } from "@/api/smartschedu/common";

import "splitpanes/dist/splitpanes.css";
import { ref } from "vue";

const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const openType = ref("");
const checkedKeyList = ref([]);
const algSubtype = ref([]);
const businessTypeOptions = ref([]);

// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `省`, visible: true },
  { key: 2, label: `市`, visible: true },
  { key: 3, label: `站点名称`, visible: true },
  { key: 4, label: `站点类型`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `业务分类`, visible: true },
  { key: 7, label: `设备大类`, visible: true },
  { key: 8, label: `设备小类`, visible: true },
  { key: 9, label: `目标值`, visible: true },
  { key: 10, label: `已完成`, visible: true },
  { key: 11, label: `创建人`, visible: true },
  { key: 12, label: `创建时间`, visible: true },
  { key: 13, label: `编辑时间`, visible: true },
]);

const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  algruleName: undefined,
  algruleCode: undefined,
  siteType: undefined,
  activitySubtype: undefined,
  algruleType: undefined,
});

watch(
  () => queryParams.value.algruleType,
  async (val) => {
    queryParams.value.algruleSubtype = undefined;
    const res = await proxy.useBusDict(val);
    nextTick(() => {
      algSubtype.value = res?.[val]?.value;
    });
  },
  { immediate: true, deep: true }
);

function getBusinessTypeList() {
  getBusinessType().then((response) => {
    businessTypeOptions.value = response.data;
  });
}
const handleCheckChange = (obj, checkedKeys) => {
  checkedKeyList.value = checkedKeys.checkedKeys;
  getList();
};

/** 导出按钮操作 */
function exportList() {
  exportAlgorithmLibrary({
    algorithmCode: queryParams.value.algorithmCode,
    algorithmName: queryParams.value.algorithmName,
    invokeDate: queryParams.value.invokeDate,
  }).then((res) => {
    if (res) {
      // 获取请求返回的 headers
      downloadFile(res, "算法设备配置列表.xlsx");
    }
  });
}

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryAlgorithmLibrary({
    ...queryParams.value,
    startTime: proxy.parseTime(queryParams.value?.planTime?.[0]),
    endTime: proxy.parseTime(queryParams.value?.planTime?.[1]),
    effectiveRegion: checkedKeyList.value.join(","),
  })
    .then((res) => {
      loading.value = false;
      dataList.value = res.data.data;
      total.value = res.data.totalRecords;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除该条数据项？")
    .then(function () {
      return delAlgorithmLibrary({ id: userIds });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleStatusChange(value, row) {
  if (!("algruleStatus" in row) || value === row.algruleStatus) {
    return;
  }
  saveAlgorithmLibrary({
    ...row,
    algruleStatus: value ? 1 : 0,
  }).then((response) => {
    getList();
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  visible.value = true;
  openType.value = "add";
  title.value = "新增设备配置";
}

/** 编辑按钮操作 */
function handleUpdate(row, type) {
  openType.value = type;
  editRecord.value = { ...row };
  title.value = `${type === "view" ? "查看" : "编辑"}设备配置`;
  visible.value = true;
}

getList();
getBusinessTypeList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
