<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
          class="query-form"
        >
          <el-form-item label="设备编码" prop="algorithmName">
            <el-input
              v-model="queryParams.algorithmName"
              placeholder="请输入设备编码"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="设备名称" prop="algorithmCode">
            <el-input
              v-model="queryParams.algorithmCode"
              placeholder="请输入设备名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="反控指令" prop="algorithmCode">
            <el-input
              v-model="queryParams.algorithmCode"
              placeholder="请输入反控指令"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="站点名称" prop="algorithmCode">
            <el-input
              v-model="queryParams.algorithmCode"
              placeholder="请输入站点名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="下发时间" prop="invokeDate">
            <el-date-picker
              type="datetimerange"
              v-model="queryParams.invokeDate"
              placeholder="选择下发时间"
              style="width: 240px"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :shortcuts="shortcuts"
            />
          </el-form-item>
          <el-form-item class="form-btn">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row
          :gutter="24"
          style="display: flex; justify-content: space-between"
          class="mb8 table-header"
        >
          <el-col :span="12">
            <div style="width: 100%" class="table-title">设备反控记录列表</div>
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="dataList"
          @selection-change="handleSelectionChange"
          max-height="calc(100vh - 400px)"
          style="width: 100%"
          v-el-table-infinite-scroll="load"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
            fixed
          />
          <el-table-column
            label="设备编码"
            align="center"
            key="algorithmName"
            prop="algorithmName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="设备名称"
            align="center"
            key="algorithmCode"
            prop="algorithmCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="省"
            align="center"
            key="provinceName"
            prop="provinceName"
            :show-overflow-tooltip="true"
            width="180"
          />
          <el-table-column
            label="市"
            align="center"
            key="cityName"
            prop="cityName"
            :show-overflow-tooltip="true"
            width="180"
          />
          <el-table-column
            label="站点类型"
            align="center"
            key="siteTypeName"
            prop="siteTypeName"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="站点名称"
            align="center"
            key="siteName"
            prop="siteName"
          />
          <el-table-column
            label="反控指令"
            align="center"
            key="taskCount"
            prop="taskCount"
          />
          <el-table-column
            label="指令编码"
            align="center"
            key="taskCount"
            prop="taskCount"
          />
          <el-table-column
            label="状态"
            align="center"
            key="ruleStatus"
            prop="ruleStatus"
          >
            <template #default="scope">
              <span>{{ statusMap?.[scope.row.ruleStatus] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="下发时间"
            align="center"
            key="taskCount"
            prop="taskCount"
          />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="EquipmentReverseControlList">
import {
  qryAlgorithmCallList,
  exportAlgorithmCall,
} from "@/api/smartschedu/algorithm";
import { algorithmStatus } from "../../common/optionsData";
import { downloadFile } from "@/utils/index";
import { ref } from "vue";
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  algorithmName: undefined,
  algorithmCode: undefined,
  status: undefined,
});
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const editRecord = ref(null);
const openType = ref("");

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryAlgorithmCallList({ ...queryParams.value }).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 导出按钮操作 */
function exportList() {
  exportAlgorithmCall({
    algorithmCode: queryParams.value.algorithmCode,
    algorithmName: queryParams.value.algorithmName,
    invokeDate: queryParams.value.invokeDate,
  }).then((res) => {
    if (res) {
      // 获取请求返回的 headers
      downloadFile(res, "设备调用执行记录.xlsx");
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  //   reset();
  //   visible.value = true;
  //   openType.value = "add";
  //   title.value = "新增规则配置";
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-right-btn {
  margin-left: unset;
}
</style>
