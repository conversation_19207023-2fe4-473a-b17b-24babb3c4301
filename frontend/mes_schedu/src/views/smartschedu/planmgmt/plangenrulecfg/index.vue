<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16">
          <area-tree @handleCheck="handleCheckChange"></area-tree>
        </pane>
        <!--用户数据-->
        <pane class="table-container" size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="100px"
              class="query-form"
            >
              <common-form-search
                :needActivity="true"
                v-model:queryParams="queryParams"
                :isRegular="true"
              />
              <el-form-item label="规则名称" prop="ruleName">
                <el-input
                  v-model="queryParams.ruleName"
                  placeholder="请输入规则名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="规则编码" prop="ruleCode">
                <el-input
                  v-model="queryParams.ruleCode"
                  placeholder="请输入规则编码"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item style="width: 340px" />

              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row
              :gutter="24"
              style="display: flex; justify-content: space-between"
              class="mb8 table-header"
            >
              <el-col :span="12">
                <div style="width: 100%" class="table-title">规则配置列表</div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['system:user:add']"
                  >新增规则配置</el-button
                >
              </el-col>
            </el-row>

            <el-table
              v-loading="loading"
              :data="dataList"
              @selection-change="handleSelectionChange"
              max-height="calc(100vh - 460px)"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
            >
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
                v-if="columns[0].visible"
                fixed
              />
              <el-table-column
                label="规则名称"
                align="center"
                key="ruleName"
                prop="ruleName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="规则编码"
                align="center"
                key="ruleCode"
                prop="ruleCode"
                v-if="columns[4].visible"
                width="160"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="省"
                align="center"
                key="provinceName"
                prop="provinceName"
                v-if="columns[1].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="市"
                align="center"
                key="cityName"
                prop="cityName"
                v-if="columns[2].visible"
                :show-overflow-tooltip="true"
                width="120"
              />

              <el-table-column
                label="业务分类"
                align="center"
                key="businessTypeName"
                prop="businessTypeName"
                v-if="columns[5].visible"
              />
              <el-table-column
                label="监测活动大类"
                align="center"
                key="activityTypeName"
                prop="activityTypeName"
                v-if="columns[6].visible"
                width="160"
              />
              <el-table-column
                label="监测活动小类"
                align="center"
                key="activitySubtypeName"
                prop="activitySubtypeName"
                v-if="columns[7].visible"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column
                label="站点名称"
                align="center"
                key="siteName"
                prop="siteName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="180"
              />
              <el-table-column
                label="站点类型"
                align="center"
                key="siteTypeName"
                prop="siteTypeName"
                v-if="columns[4].visible"
                width="160"
              >
                <template #default="scope">
                  <span>
                    {{
                      scope.row.siteType === "01" &&
                      scope.row.businessType === "water" &&
                      scope.row.hasAutomaticStation === "N"
                        ? "水断面"
                        : scope.row.siteTypeName
                    }}
                    {{
                      scope.row.hasAutomaticStation === "Y" &&
                      scope.row.businessType === "water"
                        ? "(自动站)"
                        : ""
                    }}
                  </span>
                </template>
              </el-table-column>
              <!-- <el-table-column
                label="状态"
                align="center"
                key="ruleStatus"
                prop="ruleStatus"
                v-if="columns[8].visible"
                width="120"
              >
                <template #default="scope">
                  <span>{{ statusMap?.[scope.row.ruleStatus] }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="启用状态" align="center">
                <template #default="scope">
                  <el-switch
                    :model-value="scope.row.ruleStatus"
                    :active-value="1"
                    :inactive-value="0"
                    @change="(value) => handleStatusChange(value, scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="审批状态"
                align="center"
                v-if="columns[9].visible"
                width="120"
              >
                <template #default="scope">
                  <el-tag
                    v-if="approvalStatusMap?.[scope.row.approvalStatus]?.type"
                    :type="approvalStatusMap?.[scope.row.approvalStatus]?.type"
                    >{{
                      approvalStatusMap?.[scope.row.approvalStatus]?.text
                    }}</el-tag
                  >
                </template>
              </el-table-column>

              <el-table-column
                label="审批意见"
                align="center"
                key="approvalOpinion"
                prop="approvalOpinion"
                v-if="columns[9].visible"
                width="120"
              />
              <el-table-column
                label="审批人"
                align="center"
                key="approver"
                prop="approver"
                v-if="columns[9].visible"
                width="120"
              />
              <el-table-column
                label="创建人"
                align="center"
                key="createBy"
                prop="createBy"
                v-if="columns[10].visible"
                width="120"
              />
              <el-table-column
                label="创建时间"
                align="center"
                key="createTime"
                prop="createTime"
                v-if="columns[11].visible"
                width="180"
              />
              <el-table-column
                label="操作"
                align="center"
                width="150"
                class-name="small-padding fixed-width custom-action-column"
                fixed="right"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handleUpdate(scope.row, 'view')"
                    >查看</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    v-if="
                      !scope.row.approvalStatus ||
                      scope.row.approvalStatus === 'rejected'
                    "
                    @click="handleUpdate(scope.row, 'edit')"
                    >编辑</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    @click="handleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form
      v-if="visible"
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
      :type="openType"
    ></add-form>
  </div>
</template>

<script setup name="Plangenrulecfg">
import useAppStore from "@/store/modules/app";
import addForm from "./addForm.vue";
import { qryPlanRule, delPlanRule, savePlanRule } from "@/api/smartschedu/plan";
import { approvalStatusMap, statusMap } from "../../common/optionsData";
import commonFormSearch from "@/views/smartschedu/component/commonFormSearch";

import { Splitpanes, Pane } from "splitpanes";
import areaTree from "@/views/smartschedu/component/areaTree.vue";

import "splitpanes/dist/splitpanes.css";
import { ref, watch } from "vue";

const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const openType = ref("");
const checkedKeyList = ref([]);
const siteIds = ref([]);

// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `省`, visible: true },
  { key: 2, label: `市`, visible: true },
  { key: 3, label: `站点名称`, visible: true },
  { key: 4, label: `站点类型`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `业务分类`, visible: true },
  { key: 7, label: `监测活动大类`, visible: true },
  { key: 8, label: `监测活动小类`, visible: true },
  { key: 9, label: `目标值`, visible: true },
  { key: 10, label: `已完成`, visible: true },
  { key: 11, label: `创建人`, visible: true },
  { key: 12, label: `创建时间`, visible: true },
  { key: 13, label: `编辑时间`, visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityType: undefined,
  },
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    activitySubtype: [
      {
        required: true,
        message: "请选择监测活动小类",
        trigger: ["blur", "change"],
      },
    ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

const handleCheckChange = (obj, checkedKeys) => {
  const cityCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "city")
    ?.map((item) => item.id);
  const siteId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "site")
    ?.map((item) => item.id);
  siteIds.value = siteId;
  checkedKeyList.value = cityCode;
  getList();
};

/** 查询用户列表 */
function getList() {
  loading.value = true;
  qryPlanRule(
    proxy.addDateRange(
      {
        ...queryParams.value,
        cityCode: checkedKeyList.value?.join(","),
        siteId: siteIds.value?.join(","),
      },
      dateRange.value
    )
  ).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除该条数据项？")
    .then(function () {
      return delPlanRule(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleStatusChange(value, row) {
  if (!("ruleStatus" in row) || value === row.ruleStatus) {
    return;
  }
  savePlanRule({
    ...row,
    ruleStatus: value ? 1 : 0,
  }).then((response) => {
    getList();
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  editRecord.value = {};
  proxy.resetForm("siteRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  visible.value = true;
  openType.value = "add";
  title.value = "新增规则配置";
}

/** 编辑按钮操作 */
function handleUpdate(row, type) {
  reset();
  openType.value = type;
  editRecord.value = { ...row };
  title.value = `${type === "view" ? "查看" : "编辑"}规则配置`;
  visible.value = true;
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
