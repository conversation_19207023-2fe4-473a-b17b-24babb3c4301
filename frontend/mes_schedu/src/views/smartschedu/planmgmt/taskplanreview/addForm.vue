<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="600px"
    append-to-body
    @close="cancel"
  >
    <div v-if="editRecord.id" class="table-title">计划详情</div>
    <el-row v-if="editRecord.id" :gutter="20" class="detail-row">
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">计划编码：</label>
          <el-tooltip :content="form.planCode || '-'" placement="top">
            <span class="detail-value">{{ form.planCode || "-" }}</span>
          </el-tooltip>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">计划名称：</label>
          <el-tooltip :content="form.planName || '-'" placement="top">
            <span class="detail-value">{{ form.planName || "-" }}</span>
          </el-tooltip>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">计划来源：</label>
          <span class="detail-value">{{
            planSourceMap?.[form.planSource]?.text || "-"
          }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">执行时间：</label>
          <span class="detail-value">{{ form.planTime || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">优先级：</label>
          <span
            class="detail-value"
            :style="{
              color: planPriorityMap[form.planPriority]?.color,
            }"
            >{{ planPriorityMap?.[form.planPriority]?.text || "-" }}</span
          >
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">业务类型：</label>
          <span class="detail-value">{{ form.businessTypeName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">省份：</label>
          <span class="detail-value">{{ form.provinceName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">地市：</label>
          <span class="detail-value">{{ form.cityName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">站点类型：</label>
          <span class="detail-value">{{ form.siteTypeName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">站点名称：</label>
          <el-tooltip :content="form.siteName || '-'" placement="top">
            <span class="detail-value">{{ form.siteName || "-" }}</span>
          </el-tooltip>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">监测活动大类：</label>
          <span class="detail-value">{{ form.activityTypeName || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">监测活动小类：</label>
          <span class="detail-value">{{
            form.activitySubtypeName || "-"
          }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">填报人：</label>
          <span class="detail-value">{{ form.createBy || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="detail-item">
          <label class="detail-label">创建时间：</label>
          <span class="detail-value">{{ form.createTime || "-" }}</span>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="detail-item">
          <label class="detail-label">填报说明：</label>
          <span class="detail-value">{{ form.remark || "-" }}</span>
        </div>
      </el-col>
    </el-row>
    <div v-if="editRecord.id" class="table-title">计划审批</div>
    <el-form :model="form" :rules="plans" ref="siteRef" label-width="110px">
      <el-form-item label="审批结果" prop="approvalStatus">
        <el-radio-group v-model="form.approvalStatus">
          <el-radio value="approved">通过</el-radio>
          <el-radio value="rejected">不通过</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="审批意见" prop="approvalOpinion">
        <el-input
          type="textarea"
          v-model="form.approvalOpinion"
          placeholder="请输入审批意见"
          clearable
          :rows="4"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loadingBtn" @click="submitForm"
          >确 定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import { verifyPlanInfo } from "@/api/smartschedu/plan";
import {
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { planPriorityMap, planSourceMap } from "../../common/optionsData";
import { nextTick, watch } from "vue";
const { proxy } = getCurrentInstance();

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
  ids: {
    type: Array,
    default: () => [],
  },
});
const loadingBtn = ref(false);

const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);
const initFlag = ref(true);

const data = reactive({
  form: {
    approvalStatus: "approved",
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  plans: {
    approvalStatus: [
      {
        required: true,
        message: "请选择审批状态",
        trigger: "change",
      },
    ],
    approvalOpinion: [
      {
        required: false,
        message: "请输入审批意见",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, plans } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        form.value = {
          ...newVal,
          approvalStatus: "approved",
          isInit: true,
        };
      });
      getActivityList(
        newVal.activityType,
        newVal.businessType,
        newVal.siteType
      );
      getActivityParentTypeList(newVal.businessType, newVal.siteType);
    }
  }
);

function getActivityList(activityTypeCode, businessType, siteType) {
  getActivityType({ activityTypeCode, businessType, siteType }).then(
    (response) => {
      activityTypeOptions.value = response.data;
    }
  );
}
function getActivityParentTypeList(businessType, siteType) {
  getActivityParentType({ businessType, siteType }).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
  };
  loadingBtn.value = false;
  initFlag.value = true;
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      loadingBtn.value = true;
      verifyPlanInfo({
        ...form.value,
        id: props?.ids?.join(",") || props?.editRecord?.id + "",
      }).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        emit("update:open", false);
        props?.getList();
        loadingBtn.value = false;
      });
    }
  });
}
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
:deep(.custom-descriptions) {
  .el-descriptions__label {
    text-align: right !important;
  }
}
</style>
