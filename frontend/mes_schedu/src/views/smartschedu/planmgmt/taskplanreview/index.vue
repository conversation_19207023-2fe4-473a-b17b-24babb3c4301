<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16">
          <area-tree @handleCheck="handleCheckChange"></area-tree>
        </pane>
        <!--用户数据-->
        <pane class="table-container" size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="100px"
              class="query-form"
            >
              <common-form-search
                :needActivity="true"
                v-model:queryParams="queryParams"
              />
              <el-form-item label="计划来源" prop="planSource">
                <el-select
                  v-model="queryParams.planSource"
                  placeholder="审批状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in planSource"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="审批状态" prop="approvalStatus">
                <el-select
                  v-model="queryParams.approvalStatus"
                  placeholder="审批状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in approvalStatus"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="计划名称" prop="planName">
                <el-input
                  v-model="queryParams.planName"
                  placeholder="请输入站点名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="生成时间" prop="planTime">
                <el-date-picker
                  type="datetimerange"
                  v-model="queryParams.planTime"
                  placeholder="选择开始时间"
                  style="width: 240px"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :shortcuts="shortcuts"
                />
              </el-form-item>

              <el-form-item class="form-btn">
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row
              :gutter="24"
              style="display: flex; justify-content: space-between"
              class="mb8 table-header"
            >
              <el-col :span="12">
                <div style="width: 100%" class="table-title">
                  调度计划审核列表
                </div>
              </el-col>
              <el-col :span="12" style="text-align: right">
                <el-button
                  type="primary"
                  :disabled="ids.length === 0"
                  @click="handleAdd"
                  >批量审批</el-button
                >
                <span
                  style="color: #606266; font-size: 14px; margin-left: 12px"
                  v-if="ids.length > 0"
                >
                  已勾选 <span style="color: #1472ff">{{ ids.length }}</span> 条
                </span>
              </el-col>
            </el-row>

            <el-table
              v-loading="loading"
              :data="dataList"
              @selection-change="handleSelectionChange"
              max-height="calc(100vh - 500px)"
              style="width: 100%"
              v-el-table-infinite-scroll="load"
            >
              <el-table-column
                type="selection"
                :selectable="selectable"
                width="55"
              />
              <el-table-column
                label="序号"
                align="center"
                type="index"
                width="50"
                v-if="columns[0].visible"
                fixed
              />
              <el-table-column
                label="计划编码"
                align="center"
                key="planCode"
                prop="planCode"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="160"
              />
              <el-table-column
                label="计划名称"
                align="center"
                key="planName"
                prop="planName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="160"
              />
              <el-table-column
                label="优先级"
                align="center"
                key="planPriority"
                prop="planPriority"
                v-if="columns[4].visible"
                width="160"
              >
                <template #default="scope">
                  <span
                    :style="{
                      color: planPriorityMap[scope.row.planPriority]?.color,
                    }"
                    >{{
                      planPriorityMap[scope.row.planPriority]?.text || ""
                    }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="计划来源"
                align="center"
                v-if="columns[9].visible"
                width="120"
              >
                <template #default="scope">
                  <span>{{ planSourceMap?.[scope.row.planSource]?.text }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="省"
                align="center"
                key="provinceName"
                prop="provinceName"
                v-if="columns[1].visible"
                :show-overflow-tooltip="true"
                width="120"
              />
              <el-table-column
                label="市"
                align="center"
                key="cityName"
                prop="cityName"
                v-if="columns[2].visible"
                :show-overflow-tooltip="true"
                width="120"
              />

              <el-table-column
                label="业务分类"
                align="center"
                key="businessTypeName"
                prop="businessTypeName"
                v-if="columns[5].visible"
              />
              <el-table-column
                label="监测活动大类"
                align="center"
                key="activityTypeName"
                prop="activityTypeName"
                v-if="columns[6].visible"
                width="160"
                show-overflow-tooltip
              />
              <el-table-column
                label="监测活动小类"
                align="center"
                key="activitySubtypeName"
                prop="activitySubtypeName"
                v-if="columns[7].visible"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column
                label="站点名称"
                align="center"
                key="siteName"
                prop="siteName"
                v-if="columns[3].visible"
                :show-overflow-tooltip="true"
                width="180"
              />
              <el-table-column
                label="站点类型"
                align="center"
                key="siteTypeName"
                prop="siteTypeName"
                v-if="columns[4].visible"
                width="160"
              >
                <template #default="scope">
                  <span>
                    {{
                      scope.row.siteType === "01" &&
                      scope.row.businessType === "water" &&
                      scope.row.hasAutomaticStation === "N"
                        ? "水断面"
                        : scope.row.siteTypeName
                    }}
                    {{
                      scope.row.hasAutomaticStation === "Y" &&
                      scope.row.businessType === "water"
                        ? "(自动站)"
                        : ""
                    }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                label="审批状态"
                align="center"
                v-if="columns[9].visible"
                width="120"
              >
                <template #default="scope">
                  <el-tag
                    v-if="approvalStatusMap?.[scope.row.approvalStatus]?.type"
                    :type="approvalStatusMap?.[scope.row.approvalStatus]?.type"
                    >{{
                      approvalStatusMap?.[scope.row.approvalStatus]?.text
                    }}</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="审批人"
                align="center"
                key="approver"
                prop="approver"
                v-if="columns[9].visible"
                width="120"
              />
              <el-table-column
                label="生成时间"
                align="center"
                key="createTime"
                prop="createTime"
                v-if="columns[11].visible"
                width="160"
              />

              <el-table-column
                label="操作"
                align="center"
                width="150"
                class-name="small-padding fixed-width custom-action-column"
                fixed="right"
              >
                <template #default="scope">
                  <el-button
                    v-if="
                      scope.row.approvalStatus !== 'approved' &&
                      scope.row.approvalStatus !== 'rejected'
                    "
                    link
                    type="primary"
                    @click="handleUpdate(scope.row, 'view')"
                    >审批</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或编辑用户配置对话框 -->
    <add-form
      :getList="getList"
      :title="title"
      v-model:open="visible"
      :editRecord="editRecord"
      :type="openType"
      :ids="ids"
    ></add-form>
  </div>
</template>

<script setup name="Taskplanreview">
import useAppStore from "@/store/modules/app";
import addForm from "./addForm.vue";
import { qryPlanApprovalList } from "@/api/smartschedu/plan";
import { Splitpanes, Pane } from "splitpanes";
import areaTree from "@/views/smartschedu/component/areaTree.vue";
import {
  approvalStatus,
  planSource,
  planPriorityMap,
  approvalStatusMap,
  planSourceMap,
} from "../../common/optionsData";
import commonFormSearch from "@/views/smartschedu/component/commonFormSearch";

import "splitpanes/dist/splitpanes.css";
import { ref, watch } from "vue";

const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const dataList = ref([]);
const visible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const editRecord = ref(null);
const openType = ref("");
const checkedKeyList = ref([]);
const siteIds = ref([]);

const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() - 7));
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() - 30));
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() - 90));
      return [start, end];
    },
  },
];
// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `省`, visible: true },
  { key: 2, label: `市`, visible: true },
  { key: 3, label: `站点名称`, visible: true },
  { key: 4, label: `站点类型`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `业务分类`, visible: true },
  { key: 7, label: `监测活动大类`, visible: true },
  { key: 8, label: `监测活动小类`, visible: true },
  { key: 9, label: `目标值`, visible: true },
  { key: 10, label: `已完成`, visible: true },
  { key: 11, label: `创建人`, visible: true },
  { key: 12, label: `创建时间`, visible: true },
  { key: 13, label: `编辑时间`, visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityType: undefined,
  },
  rules: {
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    activitySubtype: [
      {
        required: true,
        message: "请选择监测活动小类",
        trigger: ["blur", "change"],
      },
    ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);
const selectable = (row) => row.approvalStatus !== "approved";

const handleCheckChange = (obj, checkedKeys) => {
  const cityCode = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "city")
    ?.map((item) => item.id);
  const siteId = checkedKeys.checkedNodes
    ?.filter((item) => item.type === "site")
    ?.map((item) => item.id);
  siteIds.value = siteId;
  checkedKeyList.value = cityCode;
  getList();
};

/** 查询用户列表 */
function getList() {
  loading.value = true;
  const requestObj = {
    ...queryParams.value,
    approvalStatus: queryParams.value.approvalStatus
      ? queryParams.value.approvalStatus
      : "pending,rejected,approved",
    cityCode: checkedKeyList.value?.join(","),
    siteId: siteIds.value?.join(","),
    startTime: proxy.parseTime(queryParams.value?.planTime?.[0]),
    endTime: proxy.parseTime(queryParams.value?.planTime?.[1]),
  };
  qryPlanApprovalList(requestObj).then((res) => {
    loading.value = false;
    dataList.value = res.data.data;
    total.value = res.data.totalRecords;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");

  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
    planName: undefined,
  };
  editRecord.value = {};
  openType.value = "add";
  proxy.resetForm("siteRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  openType.value = "add";
  visible.value = true;
  title.value = "批量调度计划审批";
}

/** 编辑按钮操作 */
function handleUpdate(row, type) {
  reset();
  openType.value = type;
  editRecord.value = { ...row };
  title.value = "调度计划审批";
  visible.value = true;
}

getList();
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
