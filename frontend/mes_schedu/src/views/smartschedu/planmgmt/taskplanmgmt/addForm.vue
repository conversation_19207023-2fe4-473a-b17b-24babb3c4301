<template>
  <el-dialog
    :title="title"
    :model-value="open"
    align-center
    width="800px"
    append-to-body
    @close="cancel"
  >
    <!-- <div class="table-title">基础信息</div> -->
    <el-form
      :model="form"
      :rules="type === 'view' ? {} : rules"
      ref="siteRef"
      label-width="110px"
      class="el-form--inline"
      :inline="true"
      :disabled="type === 'view'"
    >
      <template v-if="type === 'view'">
        <el-form-item label="计划编码" prop="planCode">
          <el-input
            v-model="form.planName"
            placeholder="请输入计划编码"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="计划名称" prop="planName">
          <el-input
            v-model="form.planName"
            placeholder="请输入计划名称"
            maxlength="30"
          />
        </el-form-item>
      </template>

      <el-form-item label="计划来源" prop="planSource">
        <el-select
          disabled
          v-model="form.planSource"
          placeholder="计划来源"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in planSource"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="调度优先级" prop="planPriority">
        <el-select
          v-model="form.planPriority"
          placeholder="请选择调度优先级"
          clearable
        >
          <el-option
            v-for="dict in planPriority"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        style="width: 100%; padding-right: 36px"
        label="计划执行时间"
        prop="planTime"
      >
        <el-date-picker
          type="datetimerange"
          v-model="form.planTime"
          placeholder="选择开始时间"
          style="width: 100%"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :shortcuts="shortcuts"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item
        style="width: 100%; padding-right: 36px"
        label="填报说明"
        prop="remark"
      >
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入填报说明"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item v-if="type !== 'view'" label="计划名称" prop="planName">
        <el-input
          v-model="form.planName"
          placeholder="请输入计划名称"
          maxlength="30"
        />
      </el-form-item>
      <common-form
        v-model:form="form"
        :editRecord="editRecord"
        :isSiteNameSingle="true"
      ></common-form>
      <el-form-item label="监测活动大类" prop="activityType">
        <el-select v-model="form.activityType" placeholder="请选择监测活动大类">
          <el-option
            v-for="dict in activityParentTypeOptions"
            :key="dict.activityTypeCode"
            :label="dict.activityTypeName"
            :value="dict.activityTypeCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="监测活动小类" prop="activitySubtype">
        <el-select
          v-model="form.activitySubtype"
          placeholder="请选择监测活动小类"
          clearable
        >
          <el-option
            v-for="dict in activityTypeOptions"
            :key="dict.activitySubtypeCode"
            :label="dict.activitySubtypeName"
            :value="dict.activitySubtypeCode"
          />
        </el-select>
      </el-form-item>
      <template v-if="type === 'view'">
        <el-form-item
          v-if="form.activitySubtype === 1"
          label="填报人"
          prop="planCode"
        >
          <el-input v-model="form.planName" placeholder="-" maxlength="30" />
        </el-form-item>
        <el-form-item
          v-if="form.activitySubtype === 2"
          label="外部系统详情查看"
          prop="planCode"
        >
          <el-input v-model="form.planName" placeholder="-" maxlength="30" />
        </el-form-item>
        <el-form-item
          v-if="form.activitySubtype === 3"
          label="计划规则"
          prop="planCode"
        >
          <el-input v-model="form.planName" placeholder="-" maxlength="30" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-input
            v-model="form.createTime"
            placeholder="请输入计划名称"
            maxlength="30"
          />
        </el-form-item>

        <template v-if="form?.deviceInfo?.id">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model="form.deviceName"
              placeholder="-"
              maxlength="30"
            />
          </el-form-item>
          <el-form-item label="设备编码" prop="deviceCode">
            <el-input
              v-model="form.deviceCode"
              placeholder="-"
              maxlength="30"
            />
          </el-form-item>
        </template>
        <template v-if="form?.extendInfo?.id">
          <el-form-item label="风险等级" prop="riskLevel">
            <el-input v-model="form.riskLevel" placeholder="-" maxlength="30" />
          </el-form-item>
          <el-form-item label="风险参数" prop="riskPara">
            <el-input v-model="form.riskPara" placeholder="-" maxlength="30" />
          </el-form-item>
          <el-form-item label="采样参数" prop="collectPara">
            <el-input
              v-model="form.collectPara"
              placeholder="-"
              maxlength="30"
            />
          </el-form-item>
          <el-form-item label="是否质控" prop="isQualityCtl">
            <el-input
              v-model="form.isQualityCtl"
              placeholder="-"
              maxlength="30"
            />
          </el-form-item>
          <el-form-item label="是否盲样" prop="isBlind">
            <el-input v-model="form.isBlind" placeholder="-" maxlength="30" />
          </el-form-item>
          <el-form-item label="是否加标" prop="isMark">
            <el-input v-model="form.isMark" placeholder="-" maxlength="30" />
          </el-form-item>
        </template>
      </template>
      <div v-if="type === 'view'">
        <el-divider />
        <el-form-item label="审批状态" prop="statusName">
          <el-input v-model="form.statusName" placeholder="-" maxlength="30" />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="-" maxlength="30" />
        </el-form-item>
        <el-form-item
          label="审批意见"
          style="width: 100%; padding-right: 36px"
          prop="approvalOpinion"
        >
          <el-input
            v-model="form.approvalOpinion"
            type="textarea"
            placeholder="-"
            maxlength="30"
            style="width: 100%"
          />
        </el-form-item>
      </div>
      <!-- <div v-if="form.activityType === '01'">
        <div class="table-title">计划扩展信息</div>
        <el-form-item label="设备选择" prop="devcModel">
          <el-select
            v-model="form.devcModel"
            placeholder="请选择监测活动小类"
            clearable
          >
            <el-option
              v-for="dict in deviceList"
              :key="dict.activitySubtypeCode"
              :label="dict.activitySubtypeName"
              :value="dict.activitySubtypeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="activitySubtype">
          <el-input v-model="form.activitySubtype" disabled />
        </el-form-item>
        <el-form-item label="设备编号" prop="activitySubtype">
          <el-input v-model="form.activitySubtype" disabled />
        </el-form-item>
        <el-form-item label="设备名称" prop="activitySubtype">
          <el-input v-model="form.activitySubtype" disabled />
        </el-form-item>
        <el-form-item
          style="width: 100%; padding-right: 36px"
          label="设备故障说明"
          prop="activitySubtype"
        >
          <el-input
            type="textarea"
            v-model="form.activitySubtype"
            disabled
            style="width: 100%"
          />
        </el-form-item>
      </div> -->
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSave"
          @click="submitForm(0)"
          >保 存</el-button
        >
        <el-button
          type="primary"
          v-if="type !== 'view'"
          :loading="loadingBtnSubmit"
          @click="submitForm(1)"
          >提 交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="User">
import {
  savePlanInfo,
  qrySiteLinkDevice,
  qryPlanDetail,
} from "@/api/smartschedu/plan";
import {
  getActivityParentType,
  getActivityType,
} from "@/api/smartschedu/common";
import { nextTick, watch } from "vue";
import {
  planPriority,
  planSource,
  approvalStatusMap,
} from "../../common/optionsData";

import commonForm from "../../component/commonForm.vue";
const { proxy } = getCurrentInstance();
const shortcuts = [
  {
    text: "2小时",
    value: [new Date(), new Date().setHours(new Date().getHours() + 2)],
  },
  {
    text: "1个工作日",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() + 1));
      return [start, end];
    },
  },
  {
    text: "2个工作日",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() + 2));
      return [start, end];
    },
  },
  {
    text: "3个工作日",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() + 3));

      return [start, end];
    },
  },
  {
    text: "5个工作日",
    value: () => {
      const start = new Date();
      const end = new Date(new Date().setDate(new Date().getDate() + 5));

      return [start, end];
    },
  },
];

const emit = defineEmits(["update:open"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  getList: {
    type: Function,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  editRecord: {
    type: Object,
    default: () => {},
  },
});
const loadingBtn = ref(false);

const activityTypeOptions = ref(undefined);
const activityParentTypeOptions = ref(undefined);
const initFlag = ref(true);
const loadingBtnSave = ref(false);
const loadingBtnSubmit = ref(false);
const deviceList = ref([]);

const data = reactive({
  form: {
    planSource: "7",
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    cityCode: undefined,
    siteName: undefined,
    siteId: undefined,
    businessType: "water",
    siteType: undefined,
    activitySubtype: undefined,
    activityMajorType: undefined,
  },
  rules: {
    planSource: [
      {
        required: true,
        message: "请选择计划来源",
        trigger: ["blur", "change"],
      },
    ],
    planTime: [
      {
        required: true,
        message: "请选择计划时间",
        trigger: ["blur", "change"],
      },
    ],
    siteType: [
      {
        required: true,
        message: "请选择站点类型",
        trigger: ["blur", "change"],
      },
    ],
    businessType: [
      {
        required: true,
        message: "请选择业务类型",
        trigger: ["blur", "change"],
      },
    ],
    activityType: [
      {
        required: true,
        message: "请选择监测活动大类",
        trigger: ["blur", "change"],
      },
    ],
    activitySubtype: [
      {
        required: true,
        message: "请选择监测活动小类",
        trigger: ["blur", "change"],
      },
    ],
    targetValue: [{ required: true, message: "请输入目标值", trigger: "blur" }],
    provinceCode: [
      {
        required: true,
        message: "请选择省份",
        trigger: ["blur", "change"],
      },
    ],
    cityCode: [
      {
        required: true,
        message: "请选择市",
        trigger: ["blur", "change"],
      },
    ],
    siteId: [
      {
        required: true,
        message: "请选择站点名称",
        trigger: ["blur", "change"],
      },
    ],
    planPriority: [
      {
        required: true,
        message: "请选择调度优先级",
        trigger: ["blur", "change"],
      },
    ],
    planName: [
      {
        required: true,
        message: "请输入计划名称",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { form, rules } = toRefs(data);

watch(
  () => props.editRecord,
  (newVal, oldVal) => {
    if (newVal) {
      initData(newVal);
    }
  }
);

watch(
  [
    () => form.value.businessType,
    () => form.value.siteType,
    () => form.value.activityType,
    () => form.value.cityCode,
    () => form.value.provinceCode,
    () => form.value.isAutosite,
  ],
  (
    [
      newBusinessType,
      newSiteType,
      newActivityType,
      newCityCode,
      newProvinceCode,
    ],
    [
      oldBusinessType,
      oldSiteType,
      oldActivityType,
      oldCityCode,
      oldProvinceCode,
    ]
  ) => {
    if (form.value.isInit) {
      form.value.isInit = false;
      return;
    }
    if (newBusinessType !== oldBusinessType || newSiteType !== oldSiteType) {
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;

        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
      } else {
        form.value.activityType = undefined;
        form.value.activitySubtype = undefined;

        getActivityParentTypeList(
          newBusinessType,
          form.value?.siteTypeAlias,
          form.value?.isAutosite
        );
      }
    }
    if (
      newBusinessType !== oldBusinessType ||
      newSiteType !== oldSiteType ||
      newActivityType !== oldActivityType
    ) {
      form.value.activitySubtype = undefined;

      getActivityList(
        newActivityType,
        newBusinessType,
        form.value?.siteTypeAlias
      );
    }
    if (
      newCityCode !== oldCityCode ||
      newProvinceCode !== oldProvinceCode ||
      newSiteType !== oldSiteType
    ) {
      if (newProvinceCode !== oldProvinceCode) {
        form.value.cityCode = undefined;
      }
      if (newSiteType !== oldSiteType) {
        // 站点类型 水断面  -100 和 国控站点 01 为同一个, 如果新的为数字，老的为字符串，则认为是初始化过程，无需重置
        if (
          (newSiteType === -100 && oldSiteType === "01") ||
          (newSiteType === "01" && oldSiteType === -100) ||
          (typeof newSiteType === "number" && typeof oldSiteType === "string")
        ) {
          return;
        }
      }
      form.value.siteId = undefined;
    }
  },
  { deep: true }
);

watch(
  () => form.value.siteId,
  (newVal, oldVal) => {
    if (newVal) {
      getDeviceList(newVal);
    }
  }
);
watch(
  [() => form.value.activitySubtype, () => activityTypeOptions.value],
  (newVal, oldVal) => {
    if (newVal && activityTypeOptions.value?.length > 0) {
      const activitySubtype = activityTypeOptions.value?.find(
        (item) => item.activitySubtypeCode === newVal
      );
      form.value.testItems = activitySubtype?.testItems;
    }
  }
);

function initData(newVal) {
  nextTick(() => {
    form.value = {
      ...newVal,
      planSource: "7",
      planTime: [newVal.planStartTime, newVal.planEndTime],
      isInit: true,
    };
  });
  if (props?.type === "view") {
    getDetail(newVal.id);
  }
  getActivityList(newVal.activityType, newVal.businessType, newVal.siteType);
  getActivityParentTypeList(newVal.businessType, newVal.siteType);
}

function disabledDate(current) {
  return current.getTime() < Date.now() - 8.64e7;
}

function getDetail(id) {
  if (!id) {
    return;
  }
  qryPlanDetail(id).then((response) => {
    const data = response.data;
    form.value = {
      isInit: true,
      ...props?.editRecord,
      ...data,
      ...data?.extendInfo,
      ...data?.deviceInfo,
      statusName: approvalStatusMap?.[form.value.approvalStatus].text,
      planTime: [data.planStartTime, data.planEndTime],
    };
  });
}
function getActivityList(activityTypeCode, businessType, siteType) {
  getActivityType({ activityTypeCode, businessType, siteType }).then(
    (response) => {
      activityTypeOptions.value = response.data;
    }
  );
}
function getActivityParentTypeList(businessType, siteType, isAutosite) {
  const obj = {
    businessType,
    siteType,
  };
  if (businessType === "water") {
    obj.isAutosite = isAutosite;
  }
  getActivityParentType(obj).then((response) => {
    activityParentTypeOptions.value = response.data;
  });
}

function getDeviceList(id) {
  qrySiteLinkDevice(id).then((response) => {
    deviceList.value = response.data;
  });
}

/** 重置操作表单 */
function reset() {
  form.value = {
    provinceCode: undefined,
    provinceName: undefined,
    cityCode: undefined,
    cityName: undefined,
    siteName: undefined,
    siteId: undefined,
    siteType: undefined,
    businessType: "water",
    activityType: undefined,
    activitySubtype: undefined,
    targetValue: undefined,
    planSource: "7",
  };
  initFlag.value = true;
  proxy.resetForm("siteRef");
}

/** 取消按钮 */
function cancel() {
  reset();
  emit("update:open", false);
}

/** 提交按钮 */
function submitForm(submitFlag) {
  proxy.$refs["siteRef"].validate((valid) => {
    if (valid) {
      if (submitFlag === 1) {
        loadingBtnSubmit.value = true;
      } else {
        loadingBtnSave.value = true;
      }

      // 如果是水断面，则isAutosite为0,如果选则了站点，则根据站点的类型判断，如果没有则根据选中的活动大类判断
      const isAutosite =
        form.value.siteType === -100
          ? "0"
          : form.value.isAutosite || form.value.isAutositeActivity;

      const requestObj = {
        ...form.value,
        siteType: form.value?.siteTypeAlias,
        planStartTime: form.value?.planTime?.[0]
          ? proxy.parseTime(form.value.planTime[0])
          : "",
        planEndTime: form.value?.planTime?.[1]
          ? proxy.parseTime(form.value.planTime[1])
          : "",
        isCompanion: 0,
        submitFlag,
      };
      // 业务类型为水才会传isAutosite
      if (form.value.businessType === "water") {
        requestObj.isAutosite = isAutosite;
      }
      savePlanInfo(requestObj)
        .then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          emit("update:open", false);
          props?.getList();
          if (submitFlag === 1) {
            loadingBtnSubmit.value = false;
          } else {
            loadingBtnSave.value = false;
          }
        })
        .catch(() => {
          if (submitFlag === 1) {
            loadingBtnSubmit.value = false;
          } else {
            loadingBtnSave.value = false;
          }
        });
    }
  });
}

initData(props.editRecord);
</script>

<style scoped lang="scss">
.head-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.custom-tabs) {
  .el-tabs__header {
    display: flex;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
  }
  .el-tabs__nav {
    width: 100%;
  }
}
.top-right-btn {
  margin-left: unset;
}
</style>
