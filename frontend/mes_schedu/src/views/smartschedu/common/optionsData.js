/**
 * approved-已批准, rejected-已驳回, pending-待审批
 */
export const approvalStatus = [
  { value: "approved", label: "已批准" },
  { value: "rejected", label: "已驳回" },
  { value: "pending", label: "待审批" },
];

export const approvalStatusMap = {
  approved: {
    text: "已批准",
    type: "primary",
    color: "var(--el-color-primary)",
  },
  rejected: { text: "已驳回", type: "danger", color: "var(--el-color-danger)" },
  pending: {
    text: "待审批",
    type: "warning",
    color: "var(--el-color-warning)",
  },
};

/**
 * 计划来源：1规则生成，2监测预警，3数据审核，4资源管理，5质量管理，6监测活动，7人工填报
 */
export const planSource = [
  { value: "1", label: "规则生成" },
  { value: "2", label: "监测预警" },
  { value: "3", label: "数据审核" },
  { value: "4", label: "资源管理" },
  { value: "5", label: "质量管理" },
  { value: "6", label: "监测活动" },
  { value: "7", label: "人工填报" },
];

export const planSourceMap = {
  1: { text: "规则生成" },
  2: { text: "监测预警" },
  3: { text: "数据审核" },
  4: { text: "资源管理" },
  5: { text: "质量管理" },
  6: { text: "监测活动" },
  7: { text: "人工填报" },
};

/**
 * 计划优先级：1紧急，2重要，3中等，4一般
 */
export const planPriority = [
  { value: "1", label: "紧急" },
  { value: "2", label: "重要" },
  { value: "3", label: "中等" },
  { value: "4", label: "一般" },
];

export const planPriorityMap = {
  1: { text: "紧急", color: "var(--el-color-danger)" },
  2: { text: "重要", color: "var(--el-color-warning)" },
  3: { text: "中等", color: "" },
  4: { text: "一般", color: "" },
};

/**
 * 是否options 1是，0否
 */
export const yesOrNo = [
  { value: "1", label: "是" },
  { value: "0", label: "否" },
];

/**
 * transport_sample,水样运输
    blink_check,盲样考核
    supervision,现场监督
 */
export const sampleType = [
  { value: "water_sample", label: "水样运输" },
  { value: "blink_check", label: "盲样考核" },
  { value: "supervision", label: "现场监督" },
];

/**
 * 1-启动 0-停用
 */
export const statusMap = {
  1: "启用",
  0: "停用",
};

/**
 * 算法调用状态 1-启用 0-停用
 */
export const algorithmStatus = [
  { value: "1", label: "启用" },
  { value: "0", label: "停用" },
];

/**
 * 动态规则,algrule_subtype_dyn
基础规则,algrule_subtype_bas
 */
export const algruleType = [
  { value: "algrule_subtype_dyn", label: "动态规则" },
  { value: "algrule_subtype_bas", label: "基础规则" },
];

/**
 * 【调度计划分配】查询计划列表），计划调度状态筛选只有两个枚举（2-待调度、3-已调度）
 */
export const planStatus = [
  { value: "2", label: "待调度" },
  { value: "3", label: "已调度" },
];

/**
 * 颜色 var(--el-color-danger)   blue
 * 颜色 var(--el-color-warning)  orange
 * 颜色 var(--el-color-primary)  blue
 * 颜色 var(--el-color-success) green
 * 颜色 var(--el-color-info) gray
 */
export const planStatusMap = {
  2: { text: "待调度", color: "var(--el-color-warning)" },
  3: { text: "已调度", color: "var(--el-color-primary)" },
};

/**
 * 参数taskStatus 任务状态（1-待推送，2-已推送，3-进行中，4-已完成，5-已回退，6-已中止）
 */
export const taskStatusMap = {
  1: { text: "待推送", color: "var(--el-color-warning)" },
  2: { text: "已推送", color: "var(--el-color-primary)" },
  3: { text: "进行中", color: "var(--el-color-success)" },
  4: { text: "已完成", color: "var(--el-color-success)" },
  5: { text: "已回退", color: "var(--el-color-info)" },
  6: { text: "已中止", color: "var(--el-color-info)" },
};

/**
 * 任务创建方式 1-定时调度，2-即时调度，3-人工分配
 */
export const taskCreateMethodMap = {
  1: { text: "定时调度" },
  2: { text: "即时调度" },
  3: { text: "人工分配" }
};

// 下拉框使用暂时只有两种
export const taskStatus = [
  { value: "1", label: "待推送" },
  { value: "2", label: "已推送" },
];

// 省信息增
export const provinces = [
  { name: "北京", code: "110000", lat: "39.9042", lng: "116.4074" },
  { name: "天津", code: "120000", lat: "39.0842", lng: "117.2008" },
  { name: "河北", code: "130000", lat: "38.0359", lng: "114.4698" },
  { name: "山西", code: "140000", lat: "37.8737", lng: "112.5492" },
  { name: "内蒙古", code: "150000", lat: "40.8172", lng: "111.7510" },

  { name: "辽宁", code: "210000", lat: "41.8354", lng: "123.4294" },
  { name: "吉林", code: "220000", lat: "43.8962", lng: "125.3255" },
  { name: "黑龙江", code: "230000", lat: "45.7423", lng: "126.6620" },

  { name: "上海", code: "310000", lat: "31.2304", lng: "121.4737" },
  { name: "江苏", code: "320000", lat: "32.0603", lng: "118.7969" },
  { name: "浙江", code: "330000", lat: "30.2650", lng: "120.1528" },
  { name: "安徽", code: "340000", lat: "31.8206", lng: "117.2272" },
  { name: "福建", code: "350000", lat: "26.0996", lng: "119.2965" },
  { name: "江西", code: "360000", lat: "28.6765", lng: "115.9090" },
  { name: "山东", code: "370000", lat: "36.6702", lng: "117.0203" },

  { name: "河南", code: "410000", lat: "34.7657", lng: "113.7536" },
  { name: "湖北", code: "420000", lat: "30.5960", lng: "114.2734" },
  { name: "湖南", code: "430000", lat: "28.1124", lng: "112.9837" },
  { name: "广东", code: "440000", lat: "23.3790", lng: "113.7633" },
  { name: "广西", code: "450000", lat: "22.8152", lng: "108.3275" },
  { name: "海南", code: "460000", lat: "19.1805", lng: "109.7353" },

  { name: "重庆", code: "500000", lat: "29.5630", lng: "106.5516" },
  { name: "四川", code: "510000", lat: "30.5723", lng: "104.0758" },
  { name: "贵州", code: "520000", lat: "26.5992", lng: "106.7072" },
  { name: "云南", code: "530000", lat: "25.0453", lng: "102.7097" },
  { name: "西藏", code: "540000", lat: "29.6474", lng: "91.1175" },

  { name: "陕西", code: "610000", lat: "34.3416", lng: "108.9398" },
  { name: "甘肃", code: "620000", lat: "36.0611", lng: "103.8343" },
  { name: "青海", code: "630000", lat: "36.6232", lng: "101.7806" },
  { name: "宁夏", code: "640000", lat: "38.4872", lng: "106.2325" },
  { name: "新疆", code: "650000", lat: "43.7953", lng: "87.6168" },

  { name: "台湾", code: "710000", lat: "25.0320", lng: "121.5654" },
  { name: "香港", code: "810000", lat: "22.3193", lng: "114.1694" },
  { name: "澳门", code: "820000", lat: "22.1987", lng: "113.5439" },
];

// 任务跟踪的任务状态下拉框
export const taskTrackTaskStatus = [
  { value: "2", label: "已推送" },
  { value: "3", label: "进行中" },
  { value: "4", label: "已完成" },
  { value: "5", label: "已退回" },
  { value: "6", label: "已中止" },
];

// 一些特殊的监测活动子类
export const activityInfoSubtypeEnums = {
  // 全采
  water_collect_sample_all: "all_index_monitor",
  // 半采
  water_collect_sample_half: "9+X_index_monitor",
  // 样品运输
  sample_transport: "sample_transport",
  // 高锰酸盐指数实际水样比对（含采样）
  permanganate_index_sample_comparison: "permanganate_index_sample_comparison",
  // 氨氮实际水样比对（含采样）
  NH3_N_sample_comparison: "NH3-N_sample_comparison",
  // 总氮实际水样比对（含采样）
  TN_sample_comparison: "TN_sample_comparison",
  // 总磷实际水样比对（含采样）
  TP_sample_comparison: "TP_sample_comparison",
  // 比对水样运输
  water_sample_transport: "water_sample_transport"
}

// 是否在表单中展示交割地点
export const getShowLaboratoryAddressFlag = (activitySubtypes = []) => {
  const targetTypes = [
    activityInfoSubtypeEnums.water_collect_sample_all,
    activityInfoSubtypeEnums.water_collect_sample_half,
    activityInfoSubtypeEnums.sample_transport,
    activityInfoSubtypeEnums.permanganate_index_sample_comparison,
    activityInfoSubtypeEnums.NH3_N_sample_comparison,
    activityInfoSubtypeEnums.TN_sample_comparison,
    activityInfoSubtypeEnums.TP_sample_comparison,
    activityInfoSubtypeEnums.water_sample_transport
  ];
  
  return activitySubtypes.some(item => targetTypes.includes(item));
}

// 是否在表单中展示送样地点
export const getShowDeliveryAddressFlag = (activitySubtypes = []) => {
  const targetTypes = [
    activityInfoSubtypeEnums.sample_transport,
    activityInfoSubtypeEnums.water_sample_transport
  ];
  
  return activitySubtypes.some(item => targetTypes.includes(item));
}