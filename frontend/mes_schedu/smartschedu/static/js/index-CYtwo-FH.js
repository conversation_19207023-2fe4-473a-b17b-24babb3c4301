import{$ as C,B as ye,d as ve,r as g,N as be,T as Ve,e as a,Q as z,c as I,o as d,R as _,f as l,S as L,l as t,i as n,m as j,G as M,H as A,j as y,P as q,n as s,h as G,t as H}from"./index-BZGe1FpZ.js";function we(m){return C({url:"/system/config/list",method:"get",params:m})}function he(m){return C({url:"/system/config/"+m,method:"get"})}function Ce(m){return C({url:"/system/config",method:"post",data:m})}function ke(m){return C({url:"/system/config",method:"put",data:m})}function xe(m){return C({url:"/system/config/"+m,method:"delete"})}function Ne(){return C({url:"/system/config/refreshCache",method:"delete"})}const Se={class:"app-container"},Te={class:"dialog-footer"},Ue=ye({name:"Config"}),Re=Object.assign(Ue,{setup(m){const{proxy:f}=ve(),{sys_yes_no:T}=f.useDict("sys_yes_no"),P=g([]),b=g(!1),U=g(!0),N=g(!0),K=g([]),B=g(!0),Y=g(!0),R=g(0),D=g(""),k=g([]),O=be({form:{},queryParams:{pageNum:1,pageSize:10,configName:void 0,configKey:void 0,configType:void 0},rules:{configName:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],configKey:[{required:!0,message:"参数键名不能为空",trigger:"blur"}],configValue:[{required:!0,message:"参数键值不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:J}=Ve(O);function w(){U.value=!0,we(f.addDateRange(u.value,k.value)).then(r=>{P.value=r.rows,R.value=r.total,U.value=!1})}function W(){b.value=!1,$()}function $(){i.value={configId:void 0,configName:void 0,configKey:void 0,configValue:void 0,configType:"Y",remark:void 0},f.resetForm("configRef")}function S(){u.value.pageNum=1,w()}function X(){k.value=[],f.resetForm("queryRef"),S()}function Z(r){K.value=r.map(e=>e.configId),B.value=r.length!=1,Y.value=!r.length}function ee(){$(),b.value=!0,D.value="添加参数"}function E(r){$();const e=r.configId||K.value;he(e).then(V=>{i.value=V.data,b.value=!0,D.value="修改参数"})}function le(){f.$refs.configRef.validate(r=>{r&&(i.value.configId!=null?ke(i.value).then(e=>{f.$modal.msgSuccess("修改成功"),b.value=!1,w()}):Ce(i.value).then(e=>{f.$modal.msgSuccess("新增成功"),b.value=!1,w()}))})}function F(r){const e=r.configId||K.value;f.$modal.confirm('是否确认删除参数编号为"'+e+'"的数据项？').then(function(){return xe(e)}).then(()=>{w(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){f.download("system/config/export",{...u.value},`config_${new Date().getTime()}.xlsx`)}function ne(){Ne().then(()=>{f.$modal.msgSuccess("刷新缓存成功")})}return w(),(r,e)=>{const V=a("el-input"),c=a("el-form-item"),te=a("el-option"),ae=a("el-select"),ie=a("el-date-picker"),p=a("el-button"),Q=a("el-form"),x=a("el-col"),ue=a("right-toolbar"),re=a("el-row"),v=a("el-table-column"),de=a("dict-tag"),se=a("el-table"),fe=a("pagination"),pe=a("el-radio"),me=a("el-radio-group"),ce=a("el-dialog"),h=z("hasPermi"),ge=z("loading");return d(),I("div",Se,[_(l(Q,{model:t(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[l(c,{label:"参数名称",prop:"configName"},{default:n(()=>[l(V,{modelValue:t(u).configName,"onUpdate:modelValue":e[0]||(e[0]=o=>t(u).configName=o),placeholder:"请输入参数名称",clearable:"",style:{width:"240px"},onKeyup:j(S,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"参数键名",prop:"configKey"},{default:n(()=>[l(V,{modelValue:t(u).configKey,"onUpdate:modelValue":e[1]||(e[1]=o=>t(u).configKey=o),placeholder:"请输入参数键名",clearable:"",style:{width:"240px"},onKeyup:j(S,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"系统内置",prop:"configType"},{default:n(()=>[l(ae,{modelValue:t(u).configType,"onUpdate:modelValue":e[2]||(e[2]=o=>t(u).configType=o),placeholder:"系统内置",clearable:"",style:{width:"240px"}},{default:n(()=>[(d(!0),I(M,null,A(t(T),o=>(d(),y(te,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"创建时间",style:{width:"308px"}},{default:n(()=>[l(ie,{modelValue:t(k),"onUpdate:modelValue":e[3]||(e[3]=o=>q(k)?k.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),l(c,null,{default:n(()=>[l(p,{type:"primary",icon:"Search",onClick:S},{default:n(()=>e[13]||(e[13]=[s("搜索")])),_:1,__:[13]}),l(p,{icon:"Refresh",onClick:X},{default:n(()=>e[14]||(e[14]=[s("重置")])),_:1,__:[14]})]),_:1})]),_:1},8,["model"]),[[L,t(N)]]),l(re,{gutter:10,class:"mb8"},{default:n(()=>[l(x,{span:1.5},{default:n(()=>[_((d(),y(p,{type:"primary",plain:"",icon:"Plus",onClick:ee},{default:n(()=>e[15]||(e[15]=[s("新增")])),_:1,__:[15]})),[[h,["system:config:add"]]])]),_:1}),l(x,{span:1.5},{default:n(()=>[_((d(),y(p,{type:"success",plain:"",icon:"Edit",disabled:t(B),onClick:E},{default:n(()=>e[16]||(e[16]=[s("修改")])),_:1,__:[16]},8,["disabled"])),[[h,["system:config:edit"]]])]),_:1}),l(x,{span:1.5},{default:n(()=>[_((d(),y(p,{type:"danger",plain:"",icon:"Delete",disabled:t(Y),onClick:F},{default:n(()=>e[17]||(e[17]=[s("删除")])),_:1,__:[17]},8,["disabled"])),[[h,["system:config:remove"]]])]),_:1}),l(x,{span:1.5},{default:n(()=>[_((d(),y(p,{type:"warning",plain:"",icon:"Download",onClick:oe},{default:n(()=>e[18]||(e[18]=[s("导出")])),_:1,__:[18]})),[[h,["system:config:export"]]])]),_:1}),l(x,{span:1.5},{default:n(()=>[_((d(),y(p,{type:"danger",plain:"",icon:"Refresh",onClick:ne},{default:n(()=>e[19]||(e[19]=[s("刷新缓存")])),_:1,__:[19]})),[[h,["system:config:remove"]]])]),_:1}),l(ue,{showSearch:t(N),"onUpdate:showSearch":e[4]||(e[4]=o=>q(N)?N.value=o:null),onQueryTable:w},null,8,["showSearch"])]),_:1}),_((d(),y(se,{data:t(P),onSelectionChange:Z},{default:n(()=>[l(v,{type:"selection",width:"55",align:"center"}),l(v,{label:"参数主键",align:"center",prop:"configId"}),l(v,{label:"参数名称",align:"center",prop:"configName","show-overflow-tooltip":!0}),l(v,{label:"参数键名",align:"center",prop:"configKey","show-overflow-tooltip":!0}),l(v,{label:"参数键值",align:"center",prop:"configValue","show-overflow-tooltip":!0}),l(v,{label:"系统内置",align:"center",prop:"configType"},{default:n(o=>[l(de,{options:t(T),value:o.row.configType},null,8,["options","value"])]),_:1}),l(v,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),l(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:n(o=>[G("span",null,H(r.parseTime(o.row.createTime)),1)]),_:1}),l(v,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:n(o=>[_((d(),y(p,{link:"",type:"primary",icon:"Edit",onClick:_e=>E(o.row)},{default:n(()=>e[20]||(e[20]=[s("修改")])),_:2,__:[20]},1032,["onClick"])),[[h,["system:config:edit"]]]),_((d(),y(p,{link:"",type:"primary",icon:"Delete",onClick:_e=>F(o.row)},{default:n(()=>e[21]||(e[21]=[s("删除")])),_:2,__:[21]},1032,["onClick"])),[[h,["system:config:remove"]]])]),_:1})]),_:1},8,["data"])),[[ge,t(U)]]),_(l(fe,{total:t(R),page:t(u).pageNum,"onUpdate:page":e[5]||(e[5]=o=>t(u).pageNum=o),limit:t(u).pageSize,"onUpdate:limit":e[6]||(e[6]=o=>t(u).pageSize=o),onPagination:w},null,8,["total","page","limit"]),[[L,t(R)>0]]),l(ce,{title:t(D),modelValue:t(b),"onUpdate:modelValue":e[12]||(e[12]=o=>q(b)?b.value=o:null),width:"500px","append-to-body":""},{footer:n(()=>[G("div",Te,[l(p,{type:"primary",onClick:le},{default:n(()=>e[22]||(e[22]=[s("确 定")])),_:1,__:[22]}),l(p,{onClick:W},{default:n(()=>e[23]||(e[23]=[s("取 消")])),_:1,__:[23]})])]),default:n(()=>[l(Q,{ref:"configRef",model:t(i),rules:t(J),"label-width":"80px"},{default:n(()=>[l(c,{label:"参数名称",prop:"configName"},{default:n(()=>[l(V,{modelValue:t(i).configName,"onUpdate:modelValue":e[7]||(e[7]=o=>t(i).configName=o),placeholder:"请输入参数名称"},null,8,["modelValue"])]),_:1}),l(c,{label:"参数键名",prop:"configKey"},{default:n(()=>[l(V,{modelValue:t(i).configKey,"onUpdate:modelValue":e[8]||(e[8]=o=>t(i).configKey=o),placeholder:"请输入参数键名"},null,8,["modelValue"])]),_:1}),l(c,{label:"参数键值",prop:"configValue"},{default:n(()=>[l(V,{modelValue:t(i).configValue,"onUpdate:modelValue":e[9]||(e[9]=o=>t(i).configValue=o),type:"textarea",placeholder:"请输入参数键值"},null,8,["modelValue"])]),_:1}),l(c,{label:"系统内置",prop:"configType"},{default:n(()=>[l(me,{modelValue:t(i).configType,"onUpdate:modelValue":e[10]||(e[10]=o=>t(i).configType=o)},{default:n(()=>[(d(!0),I(M,null,A(t(T),o=>(d(),y(pe,{key:o.value,value:o.value},{default:n(()=>[s(H(o.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"备注",prop:"remark"},{default:n(()=>[l(V,{modelValue:t(i).remark,"onUpdate:modelValue":e[11]||(e[11]=o=>t(i).remark=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Re as default};
