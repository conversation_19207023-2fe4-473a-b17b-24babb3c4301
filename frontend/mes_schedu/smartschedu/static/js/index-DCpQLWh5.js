import{B as Je,a as Ge,a2 as We,d as Xe,r as f,N as oe,a7 as Ze,T as el,w as ll,e as r,Q as ne,c as S,o as u,f as l,i as a,l as o,h,P as q,R as k,m as se,G as T,H as D,j as d,n as g,S as ue,k as y,t as re}from"./index-BZGe1FpZ.js";import{d as tl,l as al,b as de,c as ol,e as nl,r as sl,f as ul,h as rl}from"./user-DzON5jmK.js";import{M as dl,g as ie}from"./splitpanes.es-nC04_w-q.js";/* empty css                   */const il={class:"app-container"},pl={class:"head-container"},ml={class:"head-container"},fl={class:"dialog-footer"},cl={class:"el-upload__tip text-center"},_l={class:"el-upload__tip"},vl={class:"dialog-footer"},bl=Je({name:"User"}),wl=Object.assign(bl,{setup(gl){const pe=Ge(),me=We(),{proxy:i}=Xe(),{sys_normal_disable:j,sys_user_sex:fe}=i.useDict("sys_normal_disable","sys_user_sex"),H=f([]),x=f(!1),L=f(!0),P=f(!0),M=f([]),J=f(!0),G=f(!0),E=f(0),K=f(""),$=f([]),z=f(""),W=f(void 0),X=f(void 0),ce=f(void 0),A=f([]),Q=f([]),v=oe({open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Ze()},url:"/prod-api/system/user/importData"}),I=f([{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}]),_e=oe({form:{},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:c,form:s,rules:ve}=el(_e),be=(n,e)=>n?e.label.indexOf(n)!==-1:!0;ll(z,n=>{i.$refs.deptTreeRef.filter(n)});function U(){L.value=!0,al(i.addDateRange(c.value,$.value)).then(n=>{L.value=!1,H.value=n.rows,E.value=n.total})}function ge(){tl().then(n=>{W.value=n.data,X.value=Z(JSON.parse(JSON.stringify(n.data)))})}function Z(n){return n.filter(e=>e.disabled?!1:(e.children&&e.children.length&&(e.children=Z(e.children)),!0))}function ye(n){c.value.deptId=n.id,R()}function R(){c.value.pageNum=1,U()}function ke(){$.value=[],i.resetForm("queryRef"),c.value.deptId=void 0,i.$refs.deptTreeRef.setCurrentKey(null),R()}function ee(n){const e=n.userId||M.value;i.$modal.confirm('是否确认删除用户编号为"'+e+'"的数据项？').then(function(){return ol(e)}).then(()=>{U(),i.$modal.msgSuccess("删除成功")}).catch(()=>{})}function he(){i.download("system/user/export",{...c.value},`user_${new Date().getTime()}.xlsx`)}function Ve(n){let e=n.status==="0"?"启用":"停用";i.$modal.confirm('确认要"'+e+'""'+n.userName+'"用户吗?').then(function(){return nl(n.userId,n.status)}).then(()=>{i.$modal.msgSuccess(e+"成功")}).catch(function(){n.status=n.status==="0"?"1":"0"})}function we(n){const e=n.userId;pe.push("/system/user-auth/role/"+e)}function xe(n){i.$prompt('请输入"'+n.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:e=>{if(/<|>|"|'|\||\\/.test(e))return`不能包含非法字符：< > " ' \\ |`}}).then(({value:e})=>{sl(n.userId,e).then(p=>{i.$modal.msgSuccess("修改成功，新密码是："+e)})}).catch(()=>{})}function Ie(n){M.value=n.map(e=>e.userId),J.value=n.length!=1,G.value=!n.length}function Ue(){v.title="用户导入",v.open=!0}function Ne(){i.download("system/user/importTemplate",{},`user_template_${new Date().getTime()}.xlsx`)}const Ce=(n,e,p)=>{v.isUploading=!0},Se=(n,e,p)=>{v.open=!1,v.isUploading=!1,i.$refs.uploadRef.handleRemove(e),i.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+n.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),U()};function $e(){i.$refs.uploadRef.submit()}function Y(){s.value={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[]},i.resetForm("userRef")}function Re(){x.value=!1,Y()}function Te(){Y(),de().then(n=>{A.value=n.posts,Q.value=n.roles,x.value=!0,K.value="添加用户",s.value.password=ce.value})}function le(n){Y();const e=n.userId||M.value;de(e).then(p=>{s.value=p.data,A.value=p.posts,Q.value=p.roles,s.value.postIds=p.postIds,s.value.roleIds=p.roleIds,x.value=!0,K.value="修改用户",s.password=""})}function De(){i.$refs.userRef.validate(n=>{n&&(s.value.userId!=null?ul(s.value).then(e=>{i.$modal.msgSuccess("修改成功"),x.value=!1,U()}):rl(s.value).then(e=>{i.$modal.msgSuccess("新增成功"),x.value=!1,U()}))})}return ge(),U(),(n,e)=>{const p=r("el-input"),Pe=r("el-tree"),m=r("el-col"),_=r("el-form-item"),O=r("el-option"),B=r("el-select"),ze=r("el-date-picker"),b=r("el-button"),te=r("el-form"),Oe=r("right-toolbar"),N=r("el-row"),V=r("el-table-column"),Be=r("el-switch"),F=r("el-tooltip"),Fe=r("el-table"),qe=r("pagination"),Le=r("el-tree-select"),Me=r("el-radio"),Ee=r("el-radio-group"),ae=r("el-dialog"),Ke=r("upload-filled"),Ae=r("el-icon"),Qe=r("el-checkbox"),Ye=r("el-link"),je=r("el-upload"),w=ne("hasPermi"),He=ne("loading");return u(),S("div",il,[l(N,{gutter:20},{default:a(()=>[l(o(dl),{horizontal:o(me).device==="mobile",class:"default-theme"},{default:a(()=>[l(o(ie),{size:"16"},{default:a(()=>[l(m,null,{default:a(()=>[h("div",pl,[l(p,{modelValue:o(z),"onUpdate:modelValue":e[0]||(e[0]=t=>q(z)?z.value=t:null),placeholder:"请输入部门名称",clearable:"","prefix-icon":"Search",style:{"margin-bottom":"20px"}},null,8,["modelValue"])]),h("div",ml,[l(Pe,{data:o(W),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":be,ref:"deptTreeRef","node-key":"id","highlight-current":"","default-expand-all":"",onNodeClick:ye},null,8,["data"])])]),_:1})]),_:1}),l(o(ie),{class:"table-container",size:"84"},{default:a(()=>[l(m,null,{default:a(()=>[k(l(te,{model:o(c),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[l(_,{label:"用户名称",prop:"userName"},{default:a(()=>[l(p,{modelValue:o(c).userName,"onUpdate:modelValue":e[1]||(e[1]=t=>o(c).userName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:se(R,["enter"])},null,8,["modelValue"])]),_:1}),l(_,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[l(p,{modelValue:o(c).phonenumber,"onUpdate:modelValue":e[2]||(e[2]=t=>o(c).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:se(R,["enter"])},null,8,["modelValue"])]),_:1}),l(_,{label:"状态",prop:"status"},{default:a(()=>[l(B,{modelValue:o(c).status,"onUpdate:modelValue":e[3]||(e[3]=t=>o(c).status=t),placeholder:"用户状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(u(!0),S(T,null,D(o(j),t=>(u(),d(O,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"创建时间",style:{width:"308px"}},{default:a(()=>[l(ze,{modelValue:o($),"onUpdate:modelValue":e[4]||(e[4]=t=>q($)?$.value=t:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),l(_,null,{default:a(()=>[l(b,{type:"primary",icon:"Search",onClick:R},{default:a(()=>e[23]||(e[23]=[g("搜索")])),_:1,__:[23]}),l(b,{icon:"Refresh",onClick:ke},{default:a(()=>e[24]||(e[24]=[g("重置")])),_:1,__:[24]})]),_:1})]),_:1},8,["model"]),[[ue,o(P)]]),l(N,{gutter:10,class:"mb8"},{default:a(()=>[l(m,{span:1.5},{default:a(()=>[k((u(),d(b,{type:"primary",plain:"",icon:"Plus",onClick:Te},{default:a(()=>e[25]||(e[25]=[g("新增")])),_:1,__:[25]})),[[w,["system:user:add"]]])]),_:1}),l(m,{span:1.5},{default:a(()=>[k((u(),d(b,{type:"success",plain:"",icon:"Edit",disabled:o(J),onClick:le},{default:a(()=>e[26]||(e[26]=[g("修改")])),_:1,__:[26]},8,["disabled"])),[[w,["system:user:edit"]]])]),_:1}),l(m,{span:1.5},{default:a(()=>[k((u(),d(b,{type:"danger",plain:"",icon:"Delete",disabled:o(G),onClick:ee},{default:a(()=>e[27]||(e[27]=[g("删除")])),_:1,__:[27]},8,["disabled"])),[[w,["system:user:remove"]]])]),_:1}),l(m,{span:1.5},{default:a(()=>[k((u(),d(b,{type:"info",plain:"",icon:"Upload",onClick:Ue},{default:a(()=>e[28]||(e[28]=[g("导入")])),_:1,__:[28]})),[[w,["system:user:import"]]])]),_:1}),l(m,{span:1.5},{default:a(()=>[k((u(),d(b,{type:"warning",plain:"",icon:"Download",onClick:he},{default:a(()=>e[29]||(e[29]=[g("导出")])),_:1,__:[29]})),[[w,["system:user:export"]]])]),_:1}),l(Oe,{showSearch:o(P),"onUpdate:showSearch":e[5]||(e[5]=t=>q(P)?P.value=t:null),onQueryTable:U,columns:o(I)},null,8,["showSearch","columns"])]),_:1}),k((u(),d(Fe,{data:o(H),onSelectionChange:Ie},{default:a(()=>[l(V,{type:"selection",width:"50",align:"center"}),o(I)[0].visible?(u(),d(V,{label:"用户编号",align:"center",key:"userId",prop:"userId"})):y("",!0),o(I)[1].visible?(u(),d(V,{label:"用户名称",align:"center",key:"userName",prop:"userName","show-overflow-tooltip":!0})):y("",!0),o(I)[2].visible?(u(),d(V,{label:"用户昵称",align:"center",key:"nickName",prop:"nickName","show-overflow-tooltip":!0})):y("",!0),o(I)[3].visible?(u(),d(V,{label:"部门",align:"center",key:"deptName",prop:"dept.deptName","show-overflow-tooltip":!0})):y("",!0),o(I)[4].visible?(u(),d(V,{label:"手机号码",align:"center",key:"phonenumber",prop:"phonenumber",width:"120"})):y("",!0),o(I)[5].visible?(u(),d(V,{label:"状态",align:"center",key:"status"},{default:a(t=>[l(Be,{modelValue:t.row.status,"onUpdate:modelValue":C=>t.row.status=C,"active-value":"0","inactive-value":"1",onChange:C=>Ve(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):y("",!0),o(I)[6].visible?(u(),d(V,{key:6,label:"创建时间",align:"center",prop:"createTime",width:"160"},{default:a(t=>[h("span",null,re(n.parseTime(t.row.createTime)),1)]),_:1})):y("",!0),l(V,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:a(t=>[t.row.userId!==1?(u(),d(F,{key:0,content:"修改",placement:"top"},{default:a(()=>[k(l(b,{link:"",type:"primary",icon:"Edit",onClick:C=>le(t.row)},null,8,["onClick"]),[[w,["system:user:edit"]]])]),_:2},1024)):y("",!0),t.row.userId!==1?(u(),d(F,{key:1,content:"删除",placement:"top"},{default:a(()=>[k(l(b,{link:"",type:"primary",icon:"Delete",onClick:C=>ee(t.row)},null,8,["onClick"]),[[w,["system:user:remove"]]])]),_:2},1024)):y("",!0),t.row.userId!==1?(u(),d(F,{key:2,content:"重置密码",placement:"top"},{default:a(()=>[k(l(b,{link:"",type:"primary",icon:"Key",onClick:C=>xe(t.row)},null,8,["onClick"]),[[w,["system:user:resetPwd"]]])]),_:2},1024)):y("",!0),t.row.userId!==1?(u(),d(F,{key:3,content:"分配角色",placement:"top"},{default:a(()=>[k(l(b,{link:"",type:"primary",icon:"CircleCheck",onClick:C=>we(t.row)},null,8,["onClick"]),[[w,["system:user:edit"]]])]),_:2},1024)):y("",!0)]),_:1})]),_:1},8,["data"])),[[He,o(L)]]),k(l(qe,{total:o(E),page:o(c).pageNum,"onUpdate:page":e[6]||(e[6]=t=>o(c).pageNum=t),limit:o(c).pageSize,"onUpdate:limit":e[7]||(e[7]=t=>o(c).pageSize=t),onPagination:U},null,8,["total","page","limit"]),[[ue,o(E)>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),l(ae,{title:o(K),modelValue:o(x),"onUpdate:modelValue":e[19]||(e[19]=t=>q(x)?x.value=t:null),width:"600px","append-to-body":""},{footer:a(()=>[h("div",fl,[l(b,{type:"primary",onClick:De},{default:a(()=>e[30]||(e[30]=[g("确 定")])),_:1,__:[30]}),l(b,{onClick:Re},{default:a(()=>e[31]||(e[31]=[g("取 消")])),_:1,__:[31]})])]),default:a(()=>[l(te,{model:o(s),rules:o(ve),ref:"userRef","label-width":"80px"},{default:a(()=>[l(N,null,{default:a(()=>[l(m,{span:12},{default:a(()=>[l(_,{label:"用户昵称",prop:"nickName"},{default:a(()=>[l(p,{modelValue:o(s).nickName,"onUpdate:modelValue":e[8]||(e[8]=t=>o(s).nickName=t),placeholder:"请输入用户昵称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),l(m,{span:12},{default:a(()=>[l(_,{label:"归属部门",prop:"deptId"},{default:a(()=>[l(Le,{modelValue:o(s).deptId,"onUpdate:modelValue":e[9]||(e[9]=t=>o(s).deptId=t),data:o(X),props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择归属部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1}),l(N,null,{default:a(()=>[l(m,{span:12},{default:a(()=>[l(_,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[l(p,{modelValue:o(s).phonenumber,"onUpdate:modelValue":e[10]||(e[10]=t=>o(s).phonenumber=t),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),l(m,{span:12},{default:a(()=>[l(_,{label:"邮箱",prop:"email"},{default:a(()=>[l(p,{modelValue:o(s).email,"onUpdate:modelValue":e[11]||(e[11]=t=>o(s).email=t),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(N,null,{default:a(()=>[l(m,{span:12},{default:a(()=>[o(s).userId==null?(u(),d(_,{key:0,label:"用户名称",prop:"userName"},{default:a(()=>[l(p,{modelValue:o(s).userName,"onUpdate:modelValue":e[12]||(e[12]=t=>o(s).userName=t),placeholder:"请输入用户名称",maxlength:"30"},null,8,["modelValue"])]),_:1})):y("",!0)]),_:1}),l(m,{span:12},{default:a(()=>[o(s).userId==null?(u(),d(_,{key:0,label:"用户密码",prop:"password"},{default:a(()=>[l(p,{modelValue:o(s).password,"onUpdate:modelValue":e[13]||(e[13]=t=>o(s).password=t),placeholder:"请输入用户密码",type:"password",maxlength:"20","show-password":""},null,8,["modelValue"])]),_:1})):y("",!0)]),_:1})]),_:1}),l(N,null,{default:a(()=>[l(m,{span:12},{default:a(()=>[l(_,{label:"用户性别"},{default:a(()=>[l(B,{modelValue:o(s).sex,"onUpdate:modelValue":e[14]||(e[14]=t=>o(s).sex=t),placeholder:"请选择"},{default:a(()=>[(u(!0),S(T,null,D(o(fe),t=>(u(),d(O,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(m,{span:12},{default:a(()=>[l(_,{label:"状态"},{default:a(()=>[l(Ee,{modelValue:o(s).status,"onUpdate:modelValue":e[15]||(e[15]=t=>o(s).status=t)},{default:a(()=>[(u(!0),S(T,null,D(o(j),t=>(u(),d(Me,{key:t.value,value:t.value},{default:a(()=>[g(re(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(N,null,{default:a(()=>[l(m,{span:12},{default:a(()=>[l(_,{label:"岗位"},{default:a(()=>[l(B,{modelValue:o(s).postIds,"onUpdate:modelValue":e[16]||(e[16]=t=>o(s).postIds=t),multiple:"",placeholder:"请选择"},{default:a(()=>[(u(!0),S(T,null,D(o(A),t=>(u(),d(O,{key:t.postId,label:t.postName,value:t.postId,disabled:t.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(m,{span:12},{default:a(()=>[l(_,{label:"角色"},{default:a(()=>[l(B,{modelValue:o(s).roleIds,"onUpdate:modelValue":e[17]||(e[17]=t=>o(s).roleIds=t),multiple:"",placeholder:"请选择"},{default:a(()=>[(u(!0),S(T,null,D(o(Q),t=>(u(),d(O,{key:t.roleId,label:t.roleName,value:t.roleId,disabled:t.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(N,null,{default:a(()=>[l(m,{span:24},{default:a(()=>[l(_,{label:"备注"},{default:a(()=>[l(p,{modelValue:o(s).remark,"onUpdate:modelValue":e[18]||(e[18]=t=>o(s).remark=t),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ae,{title:o(v).title,modelValue:o(v).open,"onUpdate:modelValue":e[22]||(e[22]=t=>o(v).open=t),width:"400px","append-to-body":""},{footer:a(()=>[h("div",vl,[l(b,{type:"primary",onClick:$e},{default:a(()=>e[36]||(e[36]=[g("确 定")])),_:1,__:[36]}),l(b,{onClick:e[21]||(e[21]=t=>o(v).open=!1)},{default:a(()=>e[37]||(e[37]=[g("取 消")])),_:1,__:[37]})])]),default:a(()=>[l(je,{ref:"uploadRef",limit:1,accept:".xlsx, .xls",headers:o(v).headers,action:o(v).url+"?updateSupport="+o(v).updateSupport,disabled:o(v).isUploading,"on-progress":Ce,"on-success":Se,"auto-upload":!1,drag:""},{tip:a(()=>[h("div",cl,[h("div",_l,[l(Qe,{modelValue:o(v).updateSupport,"onUpdate:modelValue":e[20]||(e[20]=t=>o(v).updateSupport=t)},null,8,["modelValue"]),e[32]||(e[32]=g("是否更新已经存在的用户数据 "))]),e[34]||(e[34]=h("span",null,"仅允许导入xls、xlsx格式文件。",-1)),l(Ye,{type:"primary",underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},onClick:Ne},{default:a(()=>e[33]||(e[33]=[g("下载模板")])),_:1,__:[33]})])]),default:a(()=>[l(Ae,{class:"el-icon--upload"},{default:a(()=>[l(Ke)]),_:1}),e[35]||(e[35]=h("div",{class:"el-upload__text"},[g("将文件拖到此处，或"),h("em",null,"点击上传")],-1))]),_:1,__:[35]},8,["headers","action","disabled"])]),_:1},8,["title","modelValue"])])}}});export{wl as default};
