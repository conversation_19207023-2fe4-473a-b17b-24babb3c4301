import{$ as a}from"./index-BZGe1FpZ.js";function e(t){return a({url:"/smartdispatch/dispatchtaskmgr/qryTaskStaticBySite",method:"get",params:t})}function s(t){return a({url:"/smartdispatch/dispatchtaskmgr/qryTaskListBySite",method:"get",params:t})}function i(t){return a({url:"/smartdispatch/dispatchtaskmgr/qryPeriodTaskStaticBySite",method:"get",params:t})}function n(t){return a({url:"/smartdispatch/dispatchtaskmgr/qryCompanyWorkGanttData",method:"get",params:t})}function m(t){return a({url:"/smartdispatch/homepage/qryTodayTaskStatic",method:"get",params:t})}function o(t){return a({url:"/smartdispatch/homepage/qryTodayTaskList",method:"get",params:t})}function c(t){return a({url:"/smartdispatch/homepage/qryQuantityTargetPerformance",method:"get",params:t})}function u(t){return a({url:"/smartdispatch/homepage/qryMaintainPersonStatic",method:"get",params:t})}function y(t){return a({url:"/smartdispatch/homepage/qryQualityTargetPerformance",method:"get",params:t})}function p(t){return a({url:"/smartdispatch/genericmgr/getMaintainUnitInfo",method:"get",params:t})}export{c as a,y as b,o as c,u as d,i as e,n as f,p as g,e as h,s as i,m as q};
