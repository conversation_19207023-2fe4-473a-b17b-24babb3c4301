import{_ as ye,B as me,a2 as be,d as fe,r as d,N as ge,T as _e,e as c,Q as J,c as U,o as a,f as l,j as r,k as n,i,l as o,R as z,P as we,m as W,G as ke,H as he,n as g,S as X,h as P,t as B}from"./index-BZGe1FpZ.js";import Se from"./addForm-BkKdIg-z.js";import{a as Ne}from"./plan-KEsYJfaJ.js";import Ce from"./commonFormSearch-L73kV2iA.js";import{b as xe,s as Te,c as I}from"./optionsData-BhtT3Fxt.js";import{M as qe,g as Y}from"./splitpanes.es-nC04_w-q.js";import Ve from"./areaTree-BxzGSRqT.js";/* empty css                   */import"./common-BS8XB_Gq.js";const Re={class:"app-container"},je={key:0,style:{color:"#606266","font-size":"14px","margin-left":"12px"}},Ue={style:{color:"#1472ff"}},ze=me({name:"User"}),Pe=Object.assign(ze,{setup(Be){const Z=be(),{proxy:L}=fe(),ee=v=>v.approvalStatus!=="approved"&&v.approvalStatus!=="rejected",D=d([]),w=d(!1),T=d(!0),te=d(!0),k=d([]),q=d(0),V=d(""),A=d([]),R=d(null),j=d(""),F=d([]),K=d([]),p=d([{key:0,label:"用户编号",visible:!0},{key:1,label:"省",visible:!0},{key:2,label:"市",visible:!0},{key:3,label:"站点名称",visible:!0},{key:4,label:"站点类型",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"业务分类",visible:!0},{key:7,label:"监测活动大类",visible:!0},{key:8,label:"监测活动小类",visible:!0},{key:9,label:"目标值",visible:!0},{key:10,label:"已完成",visible:!0},{key:11,label:"创建人",visible:!0},{key:12,label:"创建时间",visible:!0},{key:13,label:"编辑时间",visible:!0}]),le=ge({form:{},queryParams:{pageNo:1,pageSize:10,cityCode:void 0,siteName:void 0,siteId:void 0,businessType:"water",siteType:void 0,activitySubtype:void 0,activityType:void 0},rules:{siteType:[{required:!0,message:"请选择站点类型",trigger:["blur","change"]}],businessType:[{required:!0,message:"请选择业务类型",trigger:["blur","change"]}],activityType:[{required:!0,message:"请选择监测活动大类",trigger:["blur","change"]}],activitySubtype:[{required:!0,message:"请选择监测活动小类",trigger:["blur","change"]}],targetValue:[{required:!0,message:"请输入目标值",trigger:"blur"}],provinceCode:[{required:!0,message:"请选择省份",trigger:["blur","change"]}]}}),{queryParams:u,form:ae,rules:Ie}=_e(le),ie=(v,e)=>{var N,C,f,x;const m=(C=(N=e.checkedNodes)==null?void 0:N.filter(y=>y.type==="city"))==null?void 0:C.map(y=>y.id),b=(x=(f=e.checkedNodes)==null?void 0:f.filter(y=>y.type==="site"))==null?void 0:x.map(y=>y.id);K.value=b,F.value=m,h()};function h(){var v,e;T.value=!0,Ne(L.addDateRange({...u.value,approvalStatus:u.value.approvalStatus?u.value.approvalStatus:"pending,rejected,approved",cityCode:(v=F.value)==null?void 0:v.join(","),siteId:(e=K.value)==null?void 0:e.join(",")},A.value)).then(m=>{T.value=!1,D.value=m.data.data,q.value=m.data.totalRecords})}function S(){u.value.pageNo=1,h()}function oe(){A.value=[],L.resetForm("queryRef"),S()}function re(v){k.value=v.map(e=>e.id)}function M(){ae.value={provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,siteName:void 0,siteId:void 0,siteType:void 0,businessType:"water",activityType:void 0,activitySubtype:void 0,targetValue:void 0},R.value={}}function ne(){M(),w.value=!0,j.value="add",V.value="规则配置审批"}function ue(v,e){M(),j.value=e,R.value={...v},V.value="规则配置审批",w.value=!0}return h(),(v,e)=>{const m=c("el-input"),b=c("el-form-item"),N=c("el-option"),C=c("el-select"),f=c("el-button"),x=c("el-form"),y=c("el-col"),O=c("el-row"),s=c("el-table-column"),se=c("el-tag"),pe=c("el-table"),de=c("pagination"),ve=J("loading"),ce=J("el-table-infinite-scroll");return a(),U("div",Re,[l(O,{gutter:20},{default:i(()=>[l(o(qe),{horizontal:o(Z).device==="mobile",class:"default-theme"},{default:i(()=>[l(o(Y),{size:"16"},{default:i(()=>[l(Ve,{onHandleCheck:ie})]),_:1}),l(o(Y),{class:"table-container",size:"84"},{default:i(()=>[l(y,null,{default:i(()=>[z(l(x,{model:o(u),ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:i(()=>[l(o(Ce),{needActivity:!0,queryParams:o(u),"onUpdate:queryParams":e[0]||(e[0]=t=>we(u)?u.value=t:null)},null,8,["queryParams"]),l(b,{label:"规则名称",prop:"ruleName"},{default:i(()=>[l(m,{modelValue:o(u).ruleName,"onUpdate:modelValue":e[1]||(e[1]=t=>o(u).ruleName=t),placeholder:"请输入规则名称",clearable:"",style:{width:"240px"},onKeyup:W(S,["enter"])},null,8,["modelValue"])]),_:1}),l(b,{label:"规则编码",prop:"ruleCode"},{default:i(()=>[l(m,{modelValue:o(u).ruleCode,"onUpdate:modelValue":e[2]||(e[2]=t=>o(u).ruleCode=t),placeholder:"请输入规则编码",clearable:"",style:{width:"240px"},onKeyup:W(S,["enter"])},null,8,["modelValue"])]),_:1}),l(b,{label:"审批状态",prop:"approvalStatus"},{default:i(()=>[l(C,{modelValue:o(u).approvalStatus,"onUpdate:modelValue":e[3]||(e[3]=t=>o(u).approvalStatus=t),placeholder:"审批状态",clearable:"",style:{width:"240px"}},{default:i(()=>[(a(!0),U(ke,null,he(o(xe),t=>(a(),r(N,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(b,{style:{width:"340px"}}),l(b,{class:"form-btn"},{default:i(()=>[l(f,{type:"primary",icon:"Search",onClick:S},{default:i(()=>e[7]||(e[7]=[g("搜索")])),_:1,__:[7]}),l(f,{icon:"Refresh",onClick:oe},{default:i(()=>e[8]||(e[8]=[g("重置")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"]),[[X,te.value]]),l(O,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:i(()=>[l(y,{span:12},{default:i(()=>e[9]||(e[9]=[P("div",{style:{width:"100%"},class:"table-title"}," 规则配置审批列表 ",-1)])),_:1,__:[9]}),l(y,{span:12,style:{"text-align":"right"}},{default:i(()=>[l(f,{type:"primary",disabled:k.value.length===0,onClick:ne},{default:i(()=>e[10]||(e[10]=[g("批量审批")])),_:1,__:[10]},8,["disabled"]),k.value.length>0?(a(),U("span",je,[e[11]||(e[11]=g(" 已勾选 ")),P("span",Ue,B(k.value.length),1),e[12]||(e[12]=g(" 条 "))])):n("",!0)]),_:1})]),_:1}),z((a(),r(pe,{data:D.value,onSelectionChange:re,"max-height":"calc(100vh - 460px)",style:{width:"100%"}},{default:i(()=>[l(s,{type:"selection",selectable:ee,width:"55"}),p.value[0].visible?(a(),r(s,{key:0,label:"序号",align:"center",type:"index",width:"50",fixed:""})):n("",!0),p.value[1].visible?(a(),r(s,{label:"省",align:"center",key:"provinceName",prop:"provinceName","show-overflow-tooltip":!0,width:"120"})):n("",!0),p.value[2].visible?(a(),r(s,{label:"市",align:"center",key:"cityName",prop:"cityName","show-overflow-tooltip":!0,width:"120"})):n("",!0),p.value[3].visible?(a(),r(s,{label:"规则名称",align:"center",key:"ruleName",prop:"ruleName","show-overflow-tooltip":!0,width:"120"})):n("",!0),p.value[4].visible?(a(),r(s,{label:"规则编码",align:"center",key:"ruleCode",prop:"ruleCode",width:"160"})):n("",!0),p.value[5].visible?(a(),r(s,{label:"业务分类",align:"center",key:"businessTypeName",prop:"businessTypeName"})):n("",!0),p.value[6].visible?(a(),r(s,{label:"监测活动大类",align:"center",key:"activityTypeName",prop:"activityTypeName",width:"160"})):n("",!0),p.value[7].visible?(a(),r(s,{label:"监测活动小类",align:"center",key:"activitySubtypeName",prop:"activitySubtypeName",width:"180","show-overflow-tooltip":""})):n("",!0),p.value[3].visible?(a(),r(s,{label:"站点名称",align:"center",key:"siteName",prop:"siteName","show-overflow-tooltip":!0,width:"160"})):n("",!0),p.value[4].visible?(a(),r(s,{label:"站点类型",align:"center",key:"siteTypeName",prop:"siteTypeName",width:"160"})):n("",!0),p.value[8].visible?(a(),r(s,{label:"状态",align:"center",key:"ruleStatus",prop:"ruleStatus",width:"120"},{default:i(t=>{var _;return[P("span",null,B((_=o(Te))==null?void 0:_[t.row.ruleStatus]),1)]}),_:1})):n("",!0),p.value[9].visible?(a(),r(s,{key:11,label:"审批状态",align:"center",width:"120"},{default:i(t=>{var _,Q,H,$;return[(Q=(_=o(I))==null?void 0:_[t.row.approvalStatus])!=null&&Q.type?(a(),r(se,{key:0,type:($=(H=o(I))==null?void 0:H[t.row.approvalStatus])==null?void 0:$.type},{default:i(()=>{var E,G;return[g(B((G=(E=o(I))==null?void 0:E[t.row.approvalStatus])==null?void 0:G.text),1)]}),_:2},1032,["type"])):n("",!0)]}),_:1})):n("",!0),p.value[9].visible?(a(),r(s,{label:"审批意见",align:"center",key:"approvalOpinion",prop:"approvalOpinion",width:"120"})):n("",!0),p.value[9].visible?(a(),r(s,{label:"审批人",align:"center",key:"approver",prop:"approver",width:"120"})):n("",!0),p.value[10].visible?(a(),r(s,{label:"创建人",align:"center",key:"createBy",prop:"createBy",width:"120"})):n("",!0),p.value[11].visible?(a(),r(s,{label:"创建时间",align:"center",key:"createTime",prop:"createTime",width:"120"})):n("",!0),l(s,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width",fixed:"right"},{default:i(t=>[t.row.approvalStatus!=="approved"&&t.row.approvalStatus!=="rejected"?(a(),r(f,{key:0,link:"",type:"primary",onClick:_=>ue(t.row,"view")},{default:i(()=>e[13]||(e[13]=[g("审批")])),_:2,__:[13]},1032,["onClick"])):n("",!0)]),_:1})]),_:1},8,["data"])),[[ve,T.value],[ce,v.load]]),z(l(de,{total:q.value,page:o(u).pageNo,"onUpdate:page":e[4]||(e[4]=t=>o(u).pageNo=t),limit:o(u).pageSize,"onUpdate:limit":e[5]||(e[5]=t=>o(u).pageSize=t),onPagination:h},null,8,["total","page","limit"]),[[X,q.value>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),w.value?(a(),r(Se,{key:0,getList:h,title:V.value,open:w.value,"onUpdate:open":e[6]||(e[6]=t=>w.value=t),editRecord:R.value,type:j.value,ids:k.value},null,8,["title","open","editRecord","type","ids"])):n("",!0)])}}}),$e=ye(Pe,[["__scopeId","data-v-1902ef56"]]);export{$e as default};
