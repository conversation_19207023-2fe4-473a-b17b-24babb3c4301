import{g as O}from"./cache-Crrih8In.js";import{i as g}from"./index-DOTAK0kg.js";import{B as z,r as b,d as B,e as _,c as a,o as i,f as d,i as c,h as e,k as n,l as t,t as o,n as v}from"./index-BZGe1FpZ.js";const F={class:"app-container"},I={class:"el-table el-table--enable-row-hover el-table--medium"},N={cellspacing:"0",style:{width:"100%"}},S={class:"el-table__cell is-leaf"},V={key:0,class:"cell"},D={class:"el-table__cell is-leaf"},E={key:0,class:"cell"},L={class:"el-table__cell is-leaf"},P={key:0,class:"cell"},M={class:"el-table__cell is-leaf"},R={key:0,class:"cell"},T={class:"el-table__cell is-leaf"},$={key:0,class:"cell"},j={class:"el-table__cell is-leaf"},A={key:0,class:"cell"},K={class:"el-table__cell is-leaf"},U={key:0,class:"cell"},q={class:"el-table__cell is-leaf"},G={key:0,class:"cell"},H={class:"el-table__cell is-leaf"},J={key:0,class:"cell"},Q={class:"el-table__cell is-leaf"},W={key:0,class:"cell"},X={class:"el-table__cell is-leaf"},Y={key:0,class:"cell"},Z={class:"el-table__cell is-leaf"},ee={key:0,class:"cell"},le={class:"el-table el-table--enable-row-hover el-table--medium"},se={class:"el-table el-table--enable-row-hover el-table--medium"},te=z({name:"Cache"}),de=Object.assign(te,{setup(ae){const s=b([]),p=b(null),y=b(null),{proxy:h}=B();function k(){h.$modal.loading("正在加载缓存监控数据，请稍候！"),O().then(m=>{h.$modal.closeLoading(),s.value=m.data;const l=g(p.value,"macarons");l.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},series:[{name:"命令",type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:m.data.commandStats,animationEasing:"cubicInOut",animationDuration:1e3}]});const r=g(y.value,"macarons");r.setOption({tooltip:{formatter:"{b} <br/>{a} : "+s.value.info.used_memory_human},series:[{name:"峰值",type:"gauge",min:0,max:1e3,detail:{formatter:s.value.info.used_memory_human},data:[{value:parseFloat(s.value.info.used_memory_human),name:"内存消耗"}]}]}),window.addEventListener("resize",()=>{l.resize(),r.resize()})})}return k(),(m,l)=>{const r=_("Monitor"),u=_("el-card"),f=_("el-col"),x=_("PieChart"),w=_("Odometer"),C=_("el-row");return i(),a("div",F,[d(C,{gutter:10},{default:c(()=>[d(f,{span:24,class:"card-box"},{default:c(()=>[d(u,null,{header:c(()=>[d(r,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),l[0]||(l[0]=v()),l[1]||(l[1]=e("span",{style:{"vertical-align":"middle"}},"基本信息",-1))]),default:c(()=>[e("div",I,[e("table",N,[e("tbody",null,[e("tr",null,[l[2]||(l[2]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Redis版本")],-1)),e("td",S,[t(s).info?(i(),a("div",V,o(t(s).info.redis_version),1)):n("",!0)]),l[3]||(l[3]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行模式")],-1)),e("td",D,[t(s).info?(i(),a("div",E,o(t(s).info.redis_mode=="standalone"?"单机":"集群"),1)):n("",!0)]),l[4]||(l[4]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"端口")],-1)),e("td",L,[t(s).info?(i(),a("div",P,o(t(s).info.tcp_port),1)):n("",!0)]),l[5]||(l[5]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"客户端数")],-1)),e("td",M,[t(s).info?(i(),a("div",R,o(t(s).info.connected_clients),1)):n("",!0)])]),e("tr",null,[l[6]||(l[6]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"运行时间(天)")],-1)),e("td",T,[t(s).info?(i(),a("div",$,o(t(s).info.uptime_in_days),1)):n("",!0)]),l[7]||(l[7]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用内存")],-1)),e("td",j,[t(s).info?(i(),a("div",A,o(t(s).info.used_memory_human),1)):n("",!0)]),l[8]||(l[8]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"使用CPU")],-1)),e("td",K,[t(s).info?(i(),a("div",U,o(parseFloat(t(s).info.used_cpu_user_children).toFixed(2)),1)):n("",!0)]),l[9]||(l[9]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"内存配置")],-1)),e("td",q,[t(s).info?(i(),a("div",G,o(t(s).info.maxmemory_human),1)):n("",!0)])]),e("tr",null,[l[10]||(l[10]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"AOF是否开启")],-1)),e("td",H,[t(s).info?(i(),a("div",J,o(t(s).info.aof_enabled=="0"?"否":"是"),1)):n("",!0)]),l[11]||(l[11]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"RDB是否成功")],-1)),e("td",Q,[t(s).info?(i(),a("div",W,o(t(s).info.rdb_last_bgsave_status),1)):n("",!0)]),l[12]||(l[12]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"Key数量")],-1)),e("td",X,[t(s).dbSize?(i(),a("div",Y,o(t(s).dbSize),1)):n("",!0)]),l[13]||(l[13]=e("td",{class:"el-table__cell is-leaf"},[e("div",{class:"cell"},"网络入口/出口")],-1)),e("td",Z,[t(s).info?(i(),a("div",ee,o(t(s).info.instantaneous_input_kbps)+"kps/"+o(t(s).info.instantaneous_output_kbps)+"kps",1)):n("",!0)])])])])])]),_:1})]),_:1}),d(f,{span:12,class:"card-box"},{default:c(()=>[d(u,null,{header:c(()=>[d(x,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),l[14]||(l[14]=v()),l[15]||(l[15]=e("span",{style:{"vertical-align":"middle"}},"命令统计",-1))]),default:c(()=>[e("div",le,[e("div",{ref_key:"commandstats",ref:p,style:{height:"420px"}},null,512)])]),_:1})]),_:1}),d(f,{span:12,class:"card-box"},{default:c(()=>[d(u,null,{header:c(()=>[d(w,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),l[16]||(l[16]=v()),l[17]||(l[17]=e("span",{style:{"vertical-align":"middle"}},"内存信息",-1))]),default:c(()=>[e("div",se,[e("div",{ref_key:"usedmemory",ref:y,style:{height:"420px"}},null,512)])]),_:1})]),_:1})]),_:1})])}}});export{de as default};
