import m from"./planDimension-OFWBAG3U.js";import _ from"./taskDimension-DpitUih9.js";import{_ as p,B as d,r as f,e as l,c as u,o as b,f as e,i as o,l as h,P as k}from"./index-BZGe1FpZ.js";import"./addForm-C4OHZAJX.js";import"./task-Bw3yL6aD.js";import"./taskDetail-BUu-urFe.js";import"./optionsData-BhtT3Fxt.js";const v={class:"app-container"},x=d({name:"User"}),C=Object.assign(x,{setup(g){const a=f("first"),i=(s,t)=>{console.log(s,t)};return(s,t)=>{const n=l("el-tab-pane"),c=l("el-tabs");return b(),u("div",v,[e(c,{modelValue:h(a),"onUpdate:modelValue":t[0]||(t[0]=r=>k(a)?a.value=r:null),class:"demo-tabs",onTabClick:i},{default:o(()=>[e(n,{style:{height:"100%"},label:"计划维度",name:"first"},{default:o(()=>[e(m)]),_:1}),e(n,{style:{height:"100%"},label:"任务维度",name:"second"},{default:o(()=>[e(_)]),_:1})]),_:1},8,["modelValue"])])}}}),w=p(C,[["__scopeId","data-v-cafb2a8f"]]);export{w as default};
