import{_ as pe,B as me,a2 as ve,d as ce,r as u,w as ge,D as fe,e as r,Q as U,c as S,o as i,f as l,i as t,l as b,R as _,m as I,G as L,H as D,j as m,n as y,S as Q,h as be,k as z}from"./index-BZGe1FpZ.js";import ye from"./addForm-1SsXjm5p.js";import{q as _e,e as he,s as ke,d as we}from"./algorithm-Ex_U_8wD.js";import{h as Se,a as Ce}from"./optionsData-BhtT3Fxt.js";import{d as xe}from"./index-CUycEg5u.js";import{M as Ne,g as H}from"./splitpanes.es-nC04_w-q.js";import Te from"./areaTree-BxzGSRqT.js";/* empty css                   */import"./common-BS8XB_Gq.js";const Ve={class:"app-container"},Re=me({name:"User"}),Ue=Object.assign(Re,{setup(Le){const E=ve(),{proxy:c}=ce(),q=u([]),h=u(!1),k=u(!0),G=u(!0),A=u([]),M=u(!0),O=u(!0),C=u(0),x=u(""),J=u([]),B=u(null),N=u(""),P=u([]),j=u([]),K=u([{key:0,label:"用户编号",visible:!0},{key:1,label:"省",visible:!0},{key:2,label:"市",visible:!0},{key:3,label:"站点名称",visible:!0},{key:4,label:"站点类型",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"业务分类",visible:!0},{key:7,label:"规则大类",visible:!0},{key:8,label:"规则小类",visible:!0},{key:9,label:"目标值",visible:!0},{key:10,label:"已完成",visible:!0},{key:11,label:"创建人",visible:!0},{key:12,label:"创建时间",visible:!0},{key:13,label:"编辑时间",visible:!0}]),o=u({pageNo:1,pageSize:10,algruleName:void 0,algruleCode:void 0,siteType:void 0,activitySubtype:void 0,algruleType:void 0});ge(()=>o.value.algruleType,async n=>{o.value.algruleSubtype=void 0;const e=await c.useBusDict(n);fe(()=>{var p;j.value=(p=e==null?void 0:e[n])==null?void 0:p.value})},{immediate:!0,deep:!0});const W=(n,e)=>{P.value=e.checkedKeys,v()};function X(){he({algorithmCode:o.value.algorithmCode,algorithmName:o.value.algorithmName,invokeDate:o.value.invokeDate}).then(n=>{n&&xe(n,"算法规则配置列表.xlsx")})}function v(){var n,e,p,s;k.value=!0,_e({...o.value,startTime:c.parseTime((e=(n=o.value)==null?void 0:n.planTime)==null?void 0:e[0]),endTime:c.parseTime((s=(p=o.value)==null?void 0:p.planTime)==null?void 0:s[1]),effectiveRegion:P.value.join(",")}).then(g=>{k.value=!1,q.value=g.data.data,C.value=g.data.totalRecords}).catch(()=>{k.value=!1})}function w(){o.value.pageNo=1,v()}function Y(){J.value=[],c.resetForm("queryRef"),w()}function Z(n){const e=n.id||A.value;c.$modal.confirm("是否确认删除该条数据项？").then(function(){return we({id:e})}).then(()=>{v(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(n,e){!("algruleStatus"in e)||n===e.algruleStatus||ke({...e,algruleStatus:n?1:0}).then(p=>{v()})}function le(n){A.value=n.map(e=>e.userId),M.value=n.length!=1,O.value=!n.length}function ae(){h.value=!0,N.value="add",x.value="新增规则配置"}function te(n,e){N.value=e,B.value={...n},x.value="编辑规则配置",h.value=!0}return v(),(n,e)=>{const p=r("el-input"),s=r("el-form-item"),g=r("el-option"),T=r("el-select"),oe=r("el-date-picker"),f=r("el-button"),ne=r("el-form"),V=r("el-col"),$=r("el-row"),d=r("el-table-column"),ue=r("el-switch"),re=r("el-table"),ie=r("pagination"),F=U("hasPermi"),se=U("loading"),de=U("el-table-infinite-scroll");return i(),S("div",Ve,[l($,{gutter:20},{default:t(()=>[l(b(Ne),{horizontal:b(E).device==="mobile",class:"default-theme"},{default:t(()=>[l(b(H),{size:"16"},{default:t(()=>[l(Te,{needSite:!1,onHandleCheck:W,customProps:{label:"areaName",children:"children",value:"areaCode"}})]),_:1}),l(b(H),{class:"table-container",size:"84"},{default:t(()=>[l(V,null,{default:t(()=>[_(l(ne,{model:o.value,ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:t(()=>[l(s,{label:"规则编码",prop:"algruleCode"},{default:t(()=>[l(p,{modelValue:o.value.algruleCode,"onUpdate:modelValue":e[0]||(e[0]=a=>o.value.algruleCode=a),placeholder:"请输入规则编码",clearable:"",style:{width:"240px"},onKeyup:I(w,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"规则名称",prop:"algruleName"},{default:t(()=>[l(p,{modelValue:o.value.algruleName,"onUpdate:modelValue":e[1]||(e[1]=a=>o.value.algruleName=a),placeholder:"请输入规则名称",clearable:"",style:{width:"240px"},onKeyup:I(w,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"规则状态",prop:"algruleStatus"},{default:t(()=>[l(T,{modelValue:o.value.algruleStatus,"onUpdate:modelValue":e[2]||(e[2]=a=>o.value.algruleStatus=a),placeholder:"请选择规则状态",clearable:"",style:{width:"240px"}},{default:t(()=>[(i(!0),S(L,null,D(b(Se),a=>(i(),m(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"规则大类",prop:"algruleType"},{default:t(()=>[l(T,{modelValue:o.value.algruleType,"onUpdate:modelValue":e[3]||(e[3]=a=>o.value.algruleType=a),placeholder:"请选择规则大类",clearable:"",style:{width:"240px"}},{default:t(()=>[(i(!0),S(L,null,D(b(Ce),a=>(i(),m(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"规则小类",prop:"algruleSubtype"},{default:t(()=>[l(T,{modelValue:o.value.algruleSubtype,"onUpdate:modelValue":e[4]||(e[4]=a=>o.value.algruleSubtype=a),placeholder:"请选择规则小类",clearable:"",style:{width:"240px"}},{default:t(()=>[(i(!0),S(L,null,D(j.value,a=>(i(),m(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"调用日期",prop:"planTime"},{default:t(()=>[l(oe,{type:"daterange",modelValue:o.value.planTime,"onUpdate:modelValue":e[5]||(e[5]=a=>o.value.planTime=a),placeholder:"选择调用日期",style:{width:"240px"},"start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:n.shortcuts},null,8,["modelValue","shortcuts"])]),_:1}),l(s,{style:{width:"340px"}}),l(s,{class:"form-btn"},{default:t(()=>[l(f,{type:"primary",icon:"Search",onClick:w},{default:t(()=>e[9]||(e[9]=[y("搜索")])),_:1,__:[9]}),l(f,{icon:"Refresh",onClick:Y},{default:t(()=>e[10]||(e[10]=[y("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"]),[[Q,G.value]]),l($,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:t(()=>[l(V,{span:12},{default:t(()=>e[11]||(e[11]=[be("div",{style:{width:"100%"},class:"table-title"}," 算法规则配置列表 ",-1)])),_:1,__:[11]}),l(V,{span:12,style:{"text-align":"right"}},{default:t(()=>[_((i(),m(f,{type:"primary",icon:"Download",onClick:X},{default:t(()=>e[12]||(e[12]=[y("导 出")])),_:1,__:[12]})),[[F,["system:user:add"]]]),_((i(),m(f,{type:"primary",icon:"Plus",onClick:ae},{default:t(()=>e[13]||(e[13]=[y("新增规则")])),_:1,__:[13]})),[[F,["system:user:add"]]])]),_:1})]),_:1}),_((i(),m(re,{data:q.value,onSelectionChange:le,"max-height":"calc(100vh - 460px)",style:{width:"100%"}},{default:t(()=>[l(d,{label:"序号",align:"center",type:"index",width:"50",fixed:""}),l(d,{label:"规则名称",align:"center",key:"algruleName",prop:"algruleName","show-overflow-tooltip":!0,width:"120"}),K.value[4].visible?(i(),m(d,{label:"规则编码",align:"center",key:"algruleCode",prop:"algruleCode",width:"160","show-overflow-tooltip":!0})):z("",!0),l(d,{label:"规则大类",align:"center",key:"algruleTypeName",prop:"algruleTypeName",width:"160"}),l(d,{label:"规则小类",align:"center",key:"algruleSubtypeName",prop:"algruleSubtypeName",width:"180","show-overflow-tooltip":""}),l(d,{label:"关联算法",align:"center",key:"algorithmName",prop:"algorithmName",width:"180","show-overflow-tooltip":""}),l(d,{label:"生效地区",align:"center",key:"effectiveRegionName",prop:"effectiveRegionName",width:"180","show-overflow-tooltip":""}),l(d,{label:"启用状态",align:"center"},{default:t(a=>[l(ue,{"model-value":a.row.algruleStatus,"active-value":1,"inactive-value":0,onChange:R=>ee(R,a.row)},null,8,["model-value","onChange"])]),_:1}),K.value[11].visible?(i(),m(d,{label:"创建时间",align:"center",key:"createTime",prop:"createTime",width:"180"})):z("",!0),l(d,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width custom-action-column",fixed:"right"},{default:t(a=>[a.row.approvalStatus?z("",!0):(i(),m(f,{key:0,link:"",type:"primary",onClick:R=>te(a.row,"edit")},{default:t(()=>e[14]||(e[14]=[y("编辑")])),_:2,__:[14]},1032,["onClick"])),l(f,{link:"",type:"primary",onClick:R=>Z(a.row)},{default:t(()=>e[15]||(e[15]=[y("删除")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[se,k.value],[de,n.load]]),_(l(ie,{total:C.value,page:o.value.pageNo,"onUpdate:page":e[6]||(e[6]=a=>o.value.pageNo=a),limit:o.value.pageSize,"onUpdate:limit":e[7]||(e[7]=a=>o.value.pageSize=a),onPagination:v},null,8,["total","page","limit"]),[[Q,C.value>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),l(ye,{getList:v,title:x.value,open:h.value,"onUpdate:open":e[8]||(e[8]=a=>h.value=a),editRecord:B.value,type:N.value},null,8,["title","open","editRecord","type"])])}}}),Fe=pe(Ue,[["__scopeId","data-v-0ce603ce"]]);export{Fe as default};
