import{r as n,x as l,C as u,Q as d,R as m,l as e,c as h,o as p,h as _,J as g}from"./index-BZGe1FpZ.js";const f=["src"],w={__name:"index",props:{src:{type:String,required:!0}},setup(s){const r=s,t=n(document.documentElement.clientHeight-94.5+"px;"),o=n(!0),c=l(()=>r.src);return u(()=>{setTimeout(()=>{o.value=!1},300),window.onresize=function(){t.value=document.documentElement.clientHeight-94.5+"px;"}}),(i,v)=>{const a=d("loading");return m((p(),h("div",{style:g("height:"+e(t))},[_("iframe",{src:e(c),frameborder:"no",style:{width:"100%",height:"100%"},scrolling:"auto"},null,8,f)],4)),[[a,e(o)]])}}};export{w as _};
