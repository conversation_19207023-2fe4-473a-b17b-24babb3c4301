import{$ as n}from"./index-BZGe1FpZ.js";function e(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qryPlanRule",method:"get",params:r})}function t(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qryPlanApprovalList",method:"get",params:r})}function o(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qryPlanList",method:"get",params:r})}function l(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/savePlanRule",method:"post",data:r})}function u(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/savePlanInfo",method:"post",data:r})}function i(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/verifyPlanInfo",method:"post",data:r})}function s(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/verifyPlanRule",method:"post",data:r})}function m(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/delPlanRule",method:"get",params:{id:r}})}function c(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/delPlanInfo",method:"get",params:{id:r}})}function p(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qryAssoPlanRule",method:"get",params:{mainRuleId:r}})}function d(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qrySiteLinkDevice",method:"get",params:{siteId:r}})}function h(r){return n({url:"/monitorwarn/rocketapi/schedu/planmgr/qryPlanDetail",method:"get",params:{id:r}})}function f(r){return n({url:"/monitorwarn/rocketapi/schedu/targetmgr/qryTargetFileAttachInfo",method:"get",params:{id:r}})}export{e as a,p as b,h as c,m as d,d as e,u as f,o as g,c as h,i,t as j,f as q,l as s,s as v};
