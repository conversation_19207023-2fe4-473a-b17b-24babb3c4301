import b from"./planDimension-BSjJ-FyW.js";import k from"./taskDimension-DBOiEd1Q.js";import v from"./taskTrackDetail-Bu9qgkgO.js";import{_ as D,B as R,r as i,e as c,c as T,o as u,f as s,j as w,k as U,i as d,P as n,l as a}from"./index-BZGe1FpZ.js";import"./addForm-C4OHZAJX.js";import"./task-Bw3yL6aD.js";import"./taskDetail-BN4zICCM.js";import"./optionsData-BhtT3Fxt.js";/* empty css                                                                        */const C={class:"app-container"},x=R({name:"User"}),V=Object.assign(x,{setup(g){const r=i("first"),l=i({}),o=i(!1),f=(p,e)=>{console.log(p,e)};return(p,e)=>{const m=c("el-tab-pane"),_=c("el-tabs");return u(),T("div",C,[s(_,{modelValue:a(r),"onUpdate:modelValue":e[4]||(e[4]=t=>n(r)?r.value=t:null),class:"demo-tabs",onTabClick:f},{default:d(()=>[s(m,{style:{height:"100%"},label:"计划维度",name:"first"},{default:d(()=>[s(b,{detailRecord:a(l),"onUpdate:detailRecord":e[0]||(e[0]=t=>n(l)?l.value=t:null),showTractDetail:a(o),"onUpdate:showTractDetail":e[1]||(e[1]=t=>n(o)?o.value=t:null)},null,8,["detailRecord","showTractDetail"])]),_:1}),s(m,{style:{height:"100%"},label:"任务维度",name:"second"},{default:d(()=>[s(k,{detailRecord:a(l),"onUpdate:detailRecord":e[2]||(e[2]=t=>n(l)?l.value=t:null),showTractDetail:a(o),"onUpdate:showTractDetail":e[3]||(e[3]=t=>n(o)?o.value=t:null)},null,8,["detailRecord","showTractDetail"])]),_:1})]),_:1},8,["modelValue"]),a(o)?(u(),w(v,{key:0,open:a(o),"onUpdate:open":e[5]||(e[5]=t=>n(o)?o.value=t:null),detailRecord:a(l)},null,8,["open","detailRecord"])):U("",!0)])}}}),q=D(V,[["__scopeId","data-v-b4f5bc42"]]);export{q as default};
