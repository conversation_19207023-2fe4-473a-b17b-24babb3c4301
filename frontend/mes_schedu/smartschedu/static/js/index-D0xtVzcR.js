import{B as ge,d as ye,r as c,N as ve,T as be,e as n,Q as K,c as I,o as u,R as f,f as t,S as L,l as o,i as a,m as j,G as M,H as A,j as _,P as $,n as r,h as P,t as q,W as we}from"./index-BZGe1FpZ.js";import{l as ke,g as Ve,d as he,r as Te,u as Ce,a as xe}from"./type-CJ0iIHPa.js";const Ne={class:"app-container"},Se={class:"dialog-footer"},De=ge({name:"Dict"}),$e=Object.assign(De,{setup(Re){const{proxy:p}=ye(),{sys_normal_disable:x}=p.useDict("sys_normal_disable"),B=c([]),y=c(!1),N=c(!0),T=c(!0),S=c([]),E=c(!0),F=c(!0),D=c(0),R=c(""),V=c([]),G=ve({form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0},rules:{dictName:[{required:!0,message:"字典名称不能为空",trigger:"blur"}],dictType:[{required:!0,message:"字典类型不能为空",trigger:"blur"}]}}),{queryParams:d,form:i,rules:H}=be(G);function b(){N.value=!0,ke(p.addDateRange(d.value,V.value)).then(s=>{B.value=s.rows,D.value=s.total,N.value=!1})}function O(){y.value=!1,U()}function U(){i.value={dictId:void 0,dictName:void 0,dictType:void 0,status:"0",remark:void 0},p.resetForm("dictRef")}function C(){d.value.pageNum=1,b()}function W(){V.value=[],p.resetForm("queryRef"),C()}function J(){U(),y.value=!0,R.value="添加字典类型"}function X(s){S.value=s.map(e=>e.dictId),E.value=s.length!=1,F.value=!s.length}function Q(s){U();const e=s.dictId||S.value;Ve(e).then(w=>{i.value=w.data,y.value=!0,R.value="修改字典类型"})}function Z(){p.$refs.dictRef.validate(s=>{s&&(i.value.dictId!=null?Ce(i.value).then(e=>{p.$modal.msgSuccess("修改成功"),y.value=!1,b()}):xe(i.value).then(e=>{p.$modal.msgSuccess("新增成功"),y.value=!1,b()}))})}function Y(s){const e=s.dictId||S.value;p.$modal.confirm('是否确认删除字典编号为"'+e+'"的数据项？').then(function(){return he(e)}).then(()=>{b(),p.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){p.download("system/dict/type/export",{...d.value},`dict_${new Date().getTime()}.xlsx`)}function te(){Te().then(()=>{p.$modal.msgSuccess("刷新成功"),we().cleanDict()})}return b(),(s,e)=>{const w=n("el-input"),g=n("el-form-item"),le=n("el-option"),ae=n("el-select"),oe=n("el-date-picker"),m=n("el-button"),z=n("el-form"),h=n("el-col"),ne=n("right-toolbar"),de=n("el-row"),v=n("el-table-column"),ie=n("router-link"),se=n("dict-tag"),ue=n("el-table"),re=n("pagination"),pe=n("el-radio"),me=n("el-radio-group"),ce=n("el-dialog"),k=K("hasPermi"),fe=K("loading");return u(),I("div",Ne,[f(t(z,{model:o(d),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[t(g,{label:"字典名称",prop:"dictName"},{default:a(()=>[t(w,{modelValue:o(d).dictName,"onUpdate:modelValue":e[0]||(e[0]=l=>o(d).dictName=l),placeholder:"请输入字典名称",clearable:"",style:{width:"240px"},onKeyup:j(C,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"字典类型",prop:"dictType"},{default:a(()=>[t(w,{modelValue:o(d).dictType,"onUpdate:modelValue":e[1]||(e[1]=l=>o(d).dictType=l),placeholder:"请输入字典类型",clearable:"",style:{width:"240px"},onKeyup:j(C,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"状态",prop:"status"},{default:a(()=>[t(ae,{modelValue:o(d).status,"onUpdate:modelValue":e[2]||(e[2]=l=>o(d).status=l),placeholder:"字典状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(u(!0),I(M,null,A(o(x),l=>(u(),_(le,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"创建时间",style:{width:"308px"}},{default:a(()=>[t(oe,{modelValue:o(V),"onUpdate:modelValue":e[3]||(e[3]=l=>$(V)?V.value=l:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(g,null,{default:a(()=>[t(m,{type:"primary",icon:"Search",onClick:C},{default:a(()=>e[12]||(e[12]=[r("搜索")])),_:1,__:[12]}),t(m,{icon:"Refresh",onClick:W},{default:a(()=>e[13]||(e[13]=[r("重置")])),_:1,__:[13]})]),_:1})]),_:1},8,["model"]),[[L,o(T)]]),t(de,{gutter:10,class:"mb8"},{default:a(()=>[t(h,{span:1.5},{default:a(()=>[f((u(),_(m,{type:"primary",plain:"",icon:"Plus",onClick:J},{default:a(()=>e[14]||(e[14]=[r("新增")])),_:1,__:[14]})),[[k,["system:dict:add"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[f((u(),_(m,{type:"success",plain:"",icon:"Edit",disabled:o(E),onClick:Q},{default:a(()=>e[15]||(e[15]=[r("修改")])),_:1,__:[15]},8,["disabled"])),[[k,["system:dict:edit"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[f((u(),_(m,{type:"danger",plain:"",icon:"Delete",disabled:o(F),onClick:Y},{default:a(()=>e[16]||(e[16]=[r("删除")])),_:1,__:[16]},8,["disabled"])),[[k,["system:dict:remove"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[f((u(),_(m,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:a(()=>e[17]||(e[17]=[r("导出")])),_:1,__:[17]})),[[k,["system:dict:export"]]])]),_:1}),t(h,{span:1.5},{default:a(()=>[f((u(),_(m,{type:"danger",plain:"",icon:"Refresh",onClick:te},{default:a(()=>e[18]||(e[18]=[r("刷新缓存")])),_:1,__:[18]})),[[k,["system:dict:remove"]]])]),_:1}),t(ne,{showSearch:o(T),"onUpdate:showSearch":e[4]||(e[4]=l=>$(T)?T.value=l:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),f((u(),_(ue,{data:o(B),onSelectionChange:X},{default:a(()=>[t(v,{type:"selection",width:"55",align:"center"}),t(v,{label:"字典编号",align:"center",prop:"dictId"}),t(v,{label:"字典名称",align:"center",prop:"dictName","show-overflow-tooltip":!0}),t(v,{label:"字典类型",align:"center","show-overflow-tooltip":!0},{default:a(l=>[t(ie,{to:"/system/dict-data/index/"+l.row.dictId,class:"link-type"},{default:a(()=>[P("span",null,q(l.row.dictType),1)]),_:2},1032,["to"])]),_:1}),t(v,{label:"状态",align:"center",prop:"status"},{default:a(l=>[t(se,{options:o(x),value:l.row.status},null,8,["options","value"])]),_:1}),t(v,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),t(v,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(l=>[P("span",null,q(s.parseTime(l.row.createTime)),1)]),_:1}),t(v,{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},{default:a(l=>[f((u(),_(m,{link:"",type:"primary",icon:"Edit",onClick:_e=>Q(l.row)},{default:a(()=>e[19]||(e[19]=[r("修改")])),_:2,__:[19]},1032,["onClick"])),[[k,["system:dict:edit"]]]),f((u(),_(m,{link:"",type:"primary",icon:"Delete",onClick:_e=>Y(l.row)},{default:a(()=>e[20]||(e[20]=[r("删除")])),_:2,__:[20]},1032,["onClick"])),[[k,["system:dict:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,o(N)]]),f(t(re,{total:o(D),page:o(d).pageNum,"onUpdate:page":e[5]||(e[5]=l=>o(d).pageNum=l),limit:o(d).pageSize,"onUpdate:limit":e[6]||(e[6]=l=>o(d).pageSize=l),onPagination:b},null,8,["total","page","limit"]),[[L,o(D)>0]]),t(ce,{title:o(R),modelValue:o(y),"onUpdate:modelValue":e[11]||(e[11]=l=>$(y)?y.value=l:null),width:"500px","append-to-body":""},{footer:a(()=>[P("div",Se,[t(m,{type:"primary",onClick:Z},{default:a(()=>e[21]||(e[21]=[r("确 定")])),_:1,__:[21]}),t(m,{onClick:O},{default:a(()=>e[22]||(e[22]=[r("取 消")])),_:1,__:[22]})])]),default:a(()=>[t(z,{ref:"dictRef",model:o(i),rules:o(H),"label-width":"80px"},{default:a(()=>[t(g,{label:"字典名称",prop:"dictName"},{default:a(()=>[t(w,{modelValue:o(i).dictName,"onUpdate:modelValue":e[7]||(e[7]=l=>o(i).dictName=l),placeholder:"请输入字典名称"},null,8,["modelValue"])]),_:1}),t(g,{label:"字典类型",prop:"dictType"},{default:a(()=>[t(w,{modelValue:o(i).dictType,"onUpdate:modelValue":e[8]||(e[8]=l=>o(i).dictType=l),placeholder:"请输入字典类型"},null,8,["modelValue"])]),_:1}),t(g,{label:"状态",prop:"status"},{default:a(()=>[t(me,{modelValue:o(i).status,"onUpdate:modelValue":e[9]||(e[9]=l=>o(i).status=l)},{default:a(()=>[(u(!0),I(M,null,A(o(x),l=>(u(),_(pe,{key:l.value,value:l.value},{default:a(()=>[r(q(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"备注",prop:"remark"},{default:a(()=>[t(w,{modelValue:o(i).remark,"onUpdate:modelValue":e[10]||(e[10]=l=>o(i).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{$e as default};
