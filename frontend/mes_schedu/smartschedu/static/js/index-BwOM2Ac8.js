import{_ as me,B as fe,r as s,d as ve,e as r,Q as I,c as M,o as p,f as t,j as _,k as z,i as a,l as d,R as S,h as N,G,H as J,m as ye,n as b,S as W,t as L,J as X,E as _e,O as V}from"./index-BZGe1FpZ.js";import he from"./addForm-C4OHZAJX.js";import ge from"./areaTree-BxzGSRqT.js";import we from"./commonFormSearch-L73kV2iA.js";import{M as be,g as Y}from"./splitpanes.es-nC04_w-q.js";/* empty css                   */import{j as ke,k as Se}from"./task-Bw3yL6aD.js";import{p as Ne,k as xe,g as Te,f as Z,j as ee}from"./optionsData-BhtT3Fxt.js";import"./common-BS8XB_Gq.js";const Ce={class:"app-container"},Ve={style:{height:"calc(100vh - 513px)"}},Pe=fe({name:"User"}),Re=Object.assign(Pe,{setup(Ue){const o=s({pageNo:1,pageSize:10,ruleName:void 0,ruleCode:void 0,status:void 0,scheduStatus:void 0}),{proxy:P}=ve(),A=s([]),k=s(!1),R=s(!0),te=s(!0),x=s([]),T=s([]),F=s([]),U=s(0),j=s(""),le=s([]),q=s(null),B=s(""),Q=s([]),ae=n=>n.scheduStatus!=="3",oe=(n,e)=>{var u,g,C,f;const c=(g=(u=e.checkedNodes)==null?void 0:u.filter(v=>v.type==="city"))==null?void 0:g.map(v=>v.id),h=(f=(C=e.checkedNodes)==null?void 0:C.filter(v=>v.type==="site"))==null?void 0:f.map(v=>v.id);Q.value=h,F.value=c,w()};function w(){var n,e,c,h,u;R.value=!0,ke({...o.value,cityCode:F.value.join(","),siteId:(n=Q.value)==null?void 0:n.join(","),scheduStatus:o.value.scheduStatus||"2,3",startExecuteTime:P.parseTime((c=(e=o.value)==null?void 0:e.planTime)==null?void 0:c[0]),endExecuteTime:P.parseTime((u=(h=o.value)==null?void 0:h.planTime)==null?void 0:u[1])}).then(g=>{R.value=!1,A.value=g.data.data,U.value=g.data.totalRecords})}function D(){o.value.pageNo=1,w()}function ne(){le.value=[],o.value={},P.resetForm("queryRef"),D()}function ie(n){T.value=n,x.value=n.map(e=>e.id)}function se(n){q.value={...n},k.value=!0,B.value="add",j.value="手工分配任务资源"}function $(n){!n&&!H()||_e.confirm("确认是否及时调用算法分配？","操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var e;Se({id:(n==null?void 0:n.id)||((e=x.value)==null?void 0:e.join(","))}).then(c=>{c.code===200?(V({message:"调用成功",type:"success"}),w()):V({message:c.message,type:"error"})})}).catch(()=>{})}function H(){if(x.value.length===0)return V({message:"请选择要分配的计划",type:"warning"}),!1;if(x.value.length>1){let n=!0;if(T.value.forEach(e=>{e.businessType!==T.value[0].businessType&&(n=!1)}),!n)return V({message:"请选择同一业务类型的计划",type:"warning"}),!1}return!0}function re(){H()&&(k.value=!0,B.value="add",j.value="手工分配任务资源")}return w(),(n,e)=>{const c=r("el-option"),h=r("el-select"),u=r("el-form-item"),g=r("el-input"),C=r("el-date-picker"),f=r("el-button"),v=r("el-form"),E=r("el-col"),K=r("el-row"),i=r("el-table-column"),ue=r("el-table"),pe=r("pagination"),O=I("hasPermi"),de=I("loading"),ce=I("el-table-infinite-scroll");return p(),M("div",Ce,[t(K,{gutter:20},{default:a(()=>[t(d(be),{class:"default-theme"},{default:a(()=>[t(d(Y),{size:"16"},{default:a(()=>[t(ge,{onHandleCheck:oe})]),_:1}),t(d(Y),{class:"table-container",size:"84"},{default:a(()=>[t(E,null,{default:a(()=>[S(t(v,{model:o.value,ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:a(()=>[t(d(we),{needActivity:!0,queryParams:o.value,"onUpdate:queryParams":e[0]||(e[0]=l=>o.value=l)},null,8,["queryParams"]),t(u,{label:"计划来源",prop:"planSource"},{default:a(()=>[t(h,{modelValue:o.value.planSource,"onUpdate:modelValue":e[1]||(e[1]=l=>o.value.planSource=l),placeholder:"计划来源",clearable:"",style:{width:"240px"}},{default:a(()=>[(p(!0),M(G,null,J(d(Ne),l=>(p(),_(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"调度状态",prop:"scheduStatus"},{default:a(()=>[t(h,{modelValue:o.value.scheduStatus,"onUpdate:modelValue":e[2]||(e[2]=l=>o.value.scheduStatus=l),placeholder:"调度状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(p(!0),M(G,null,J(d(xe),l=>(p(),_(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"计划名称",prop:"planName"},{default:a(()=>[t(g,{modelValue:o.value.planName,"onUpdate:modelValue":e[3]||(e[3]=l=>o.value.planName=l),placeholder:"请输入计划名称",clearable:"",style:{width:"240px"},onKeyup:ye(D,["enter"])},null,8,["modelValue"])]),_:1}),t(u,{label:"执行时间",prop:"planTime"},{default:a(()=>[t(C,{type:"datetimerange",modelValue:o.value.planTime,"onUpdate:modelValue":e[4]||(e[4]=l=>o.value.planTime=l),placeholder:"选择开始时间",style:{width:"240px"},"start-placeholder":"开始时间","end-placeholder":"结束时间",shortcuts:n.shortcuts},null,8,["modelValue","shortcuts"])]),_:1}),t(u,{class:"form-btn"},{default:a(()=>[t(f,{type:"primary",icon:"Search",onClick:D},{default:a(()=>e[10]||(e[10]=[b("搜索")])),_:1,__:[10]}),t(f,{icon:"Refresh",onClick:ne},{default:a(()=>e[11]||(e[11]=[b("重置")])),_:1,__:[11]})]),_:1})]),_:1},8,["model"]),[[W,te.value]]),t(K,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:a(()=>[t(E,{span:12},{default:a(()=>e[12]||(e[12]=[N("div",{style:{width:"100%"},class:"table-title"}," 调度计划分配列表 ",-1)])),_:1,__:[12]}),t(E,{span:12,style:{"text-align":"right"}},{default:a(()=>[S((p(),_(f,{type:"primary",icon:"AlarmClock",onClick:e[5]||(e[5]=()=>$())},{default:a(()=>e[13]||(e[13]=[b("及时调用算法分配")])),_:1,__:[13]})),[[O,["system:user:add"]]]),S((p(),_(f,{type:"primary",icon:"Pointer",onClick:re},{default:a(()=>e[14]||(e[14]=[b("手工分配任务资源")])),_:1,__:[14]})),[[O,["system:user:add"]]])]),_:1})]),_:1}),N("div",Ve,[S((p(),_(ue,{data:A.value,onSelectionChange:ie,height:"100%",style:{width:"100%"}},{default:a(()=>[t(i,{type:"selection",selectable:ae,width:"55"}),t(i,{label:"序号",align:"center",type:"index",width:"50",fixed:""}),t(i,{label:"计划编码",align:"center",key:"planCode",prop:"planCode","show-overflow-tooltip":!0,width:"160"}),t(i,{label:"计划名称",align:"center",key:"planName",prop:"planName","show-overflow-tooltip":!0,width:"160"}),t(i,{label:"计划来源",align:"center",width:"120"},{default:a(l=>{var m,y;return[N("span",null,L((y=(m=d(Te))==null?void 0:m[l.row.planSource])==null?void 0:y.text),1)]}),_:1}),t(i,{label:"优先级",align:"center",key:"planPriority",prop:"planPriority",width:"160"},{default:a(l=>{var m,y;return[N("span",{style:X({color:(m=d(Z)[l.row.planPriority])==null?void 0:m.color})},L(((y=d(Z)[l.row.planPriority])==null?void 0:y.text)||""),5)]}),_:1}),t(i,{label:"计划调度状态",align:"center",prop:"scheduStatus",key:"scheduStatus","show-overflow-tooltip":!0,width:"120"},{default:a(l=>{var m,y;return[N("span",{style:X({color:(m=d(ee))==null?void 0:m[l.row.scheduStatus].color})},L(((y=d(ee))==null?void 0:y[l.row.scheduStatus].text)||""),5)]}),_:1}),t(i,{label:"省",align:"center",key:"provinceName",prop:"provinceName","show-overflow-tooltip":!0,width:"120"}),t(i,{label:"市",align:"center",key:"cityName",prop:"cityName","show-overflow-tooltip":!0,width:"120"}),t(i,{label:"业务分类",align:"center",key:"businessTypeName",prop:"businessTypeName"}),t(i,{label:"站点类型",align:"center",key:"siteTypeName",prop:"siteTypeName",width:"160"}),t(i,{label:"站点名称",align:"center",key:"siteName",prop:"siteName","show-overflow-tooltip":!0,width:"120"}),t(i,{label:"监测活动大类",align:"center",key:"activityTypeName",prop:"activityTypeName",width:"180","show-overflow-tooltip":!0}),t(i,{label:"监测活动小类",align:"center",key:"activitySubtypeName",prop:"activitySubtypeName",width:"180","show-overflow-tooltip":!0}),t(i,{label:"任务计划生成时间",align:"center",key:"createTime",prop:"createTime","show-overflow-tooltip":!0,width:"160"}),t(i,{label:"操作",align:"center",width:"170","class-name":"small-padding fixed-width custom-action-column",fixed:"right"},{default:a(l=>[l.row.scheduStatus!=="3"?(p(),_(f,{key:0,link:"",type:"primary",onClick:m=>$(l.row,"view")},{default:a(()=>e[15]||(e[15]=[b("及时调用")])),_:2,__:[15]},1032,["onClick"])):z("",!0),l.row.scheduStatus!=="3"?(p(),_(f,{key:1,link:"",type:"primary",onClick:m=>se(l.row)},{default:a(()=>e[16]||(e[16]=[b("手工分配")])),_:2,__:[16]},1032,["onClick"])):z("",!0)]),_:1})]),_:1},8,["data"])),[[de,R.value],[ce,n.load]])]),S(t(pe,{total:U.value,page:o.value.pageNo,"onUpdate:page":e[6]||(e[6]=l=>o.value.pageNo=l),limit:o.value.pageSize,"onUpdate:limit":e[7]||(e[7]=l=>o.value.pageSize=l),onPagination:w},null,8,["total","page","limit"]),[[W,U.value>0]])]),_:1})]),_:1})]),_:1})]),_:1}),k.value?(p(),_(he,{key:0,getList:w,title:j.value,open:k.value,"onUpdate:open":e[8]||(e[8]=l=>k.value=l),editRecord:q.value,"onUpdate:editRecord":e[9]||(e[9]=l=>q.value=l),type:B.value,planInfos:T.value},null,8,["title","open","editRecord","type","planInfos"])):z("",!0)])}}}),Ae=me(Re,[["__scopeId","data-v-9bb47920"]]);export{Ae as default};
