import{$ as N,r as M,d as T,e as r,c,o as d,f as o,i as _,h as l,k as i,l as t,t as a,n as m,I as b,G as B,H as V}from"./index-BZGe1FpZ.js";function G(){return N({url:"/system/server",method:"get"})}const I={class:"app-container"},J={class:"el-table el-table--enable-row-hover el-table--medium"},L={cellspacing:"0",style:{width:"100%"}},$={class:"el-table__cell is-leaf"},A={key:0,class:"cell"},D={class:"el-table__cell is-leaf"},P={key:0,class:"cell"},S={class:"el-table__cell is-leaf"},z={key:0,class:"cell"},E={class:"el-table__cell is-leaf"},H={key:0,class:"cell"},U={class:"el-table el-table--enable-row-hover el-table--medium"},q={cellspacing:"0",style:{width:"100%"}},K={class:"el-table__cell is-leaf"},O={key:0,class:"cell"},Q={class:"el-table__cell is-leaf"},R={key:0,class:"cell"},W={class:"el-table__cell is-leaf"},X={key:0,class:"cell"},Y={class:"el-table__cell is-leaf"},Z={key:0,class:"cell"},F={class:"el-table__cell is-leaf"},ll={key:0,class:"cell"},el={class:"el-table__cell is-leaf"},sl={key:0,class:"cell"},tl={class:"el-table__cell is-leaf"},al={class:"el-table__cell is-leaf"},cl={class:"el-table el-table--enable-row-hover el-table--medium"},dl={cellspacing:"0",style:{width:"100%"}},il={class:"el-table__cell is-leaf"},ol={key:0,class:"cell"},_l={class:"el-table__cell is-leaf"},nl={key:0,class:"cell"},rl={class:"el-table__cell is-leaf"},ul={key:0,class:"cell"},vl={class:"el-table__cell is-leaf"},ml={key:0,class:"cell"},bl={class:"el-table el-table--enable-row-hover el-table--medium"},fl={cellspacing:"0",style:{width:"100%","table-layout":"fixed"}},pl={class:"el-table__cell is-leaf"},yl={key:0,class:"cell"},hl={class:"el-table__cell is-leaf"},gl={key:0,class:"cell"},kl={class:"el-table__cell is-leaf"},jl={key:0,class:"cell"},wl={class:"el-table__cell is-leaf"},xl={key:0,class:"cell"},Cl={colspan:"3",class:"el-table__cell is-leaf"},Nl={key:0,class:"cell"},Ml={colspan:"3",class:"el-table__cell is-leaf"},Tl={key:0,class:"cell"},Bl={colspan:"3",class:"el-table__cell is-leaf"},Vl={key:0,class:"cell"},Gl={class:"el-table el-table--enable-row-hover el-table--medium"},Il={cellspacing:"0",style:{width:"100%"}},Jl={key:0},Ll={class:"el-table__cell is-leaf"},$l={class:"cell"},Al={class:"el-table__cell is-leaf"},Dl={class:"cell"},Pl={class:"el-table__cell is-leaf"},Sl={class:"cell"},zl={class:"el-table__cell is-leaf"},El={class:"cell"},Hl={class:"el-table__cell is-leaf"},Ul={class:"cell"},ql={class:"el-table__cell is-leaf"},Kl={class:"cell"},Ol={class:"el-table__cell is-leaf"},Wl={__name:"index",setup(Ql){const s=M([]),{proxy:f}=T();function y(){f.$modal.loading("正在加载服务监控数据，请稍候！"),G().then(p=>{s.value=p.data,f.$modal.closeLoading()})}return y(),(p,e)=>{const h=r("Cpu"),u=r("el-card"),v=r("el-col"),g=r("Tickets"),k=r("Monitor"),j=r("CoffeeCup"),w=r("MessageBox"),x=r("el-row");return d(),c("div",I,[o(x,{gutter:10},{default:_(()=>[o(v,{span:12,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(h,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[0]||(e[0]=m()),e[1]||(e[1]=l("span",{style:{"vertical-align":"middle"}},"CPU",-1))]),default:_(()=>[l("div",J,[l("table",L,[e[6]||(e[6]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"值")])])],-1)),l("tbody",null,[l("tr",null,[e[2]||(e[2]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"核心数")],-1)),l("td",$,[t(s).cpu?(d(),c("div",A,a(t(s).cpu.cpuNum),1)):i("",!0)])]),l("tr",null,[e[3]||(e[3]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"用户使用率")],-1)),l("td",D,[t(s).cpu?(d(),c("div",P,a(t(s).cpu.used)+"%",1)):i("",!0)])]),l("tr",null,[e[4]||(e[4]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统使用率")],-1)),l("td",S,[t(s).cpu?(d(),c("div",z,a(t(s).cpu.sys)+"%",1)):i("",!0)])]),l("tr",null,[e[5]||(e[5]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"当前空闲率")],-1)),l("td",E,[t(s).cpu?(d(),c("div",H,a(t(s).cpu.free)+"%",1)):i("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:12,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(g,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[7]||(e[7]=m()),e[8]||(e[8]=l("span",{style:{"vertical-align":"middle"}},"内存",-1))]),default:_(()=>[l("div",U,[l("table",q,[e[13]||(e[13]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"内存")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"JVM")])])],-1)),l("tbody",null,[l("tr",null,[e[9]||(e[9]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总内存")],-1)),l("td",K,[t(s).mem?(d(),c("div",O,a(t(s).mem.total)+"G",1)):i("",!0)]),l("td",Q,[t(s).jvm?(d(),c("div",R,a(t(s).jvm.total)+"M",1)):i("",!0)])]),l("tr",null,[e[10]||(e[10]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用内存")],-1)),l("td",W,[t(s).mem?(d(),c("div",X,a(t(s).mem.used)+"G",1)):i("",!0)]),l("td",Y,[t(s).jvm?(d(),c("div",Z,a(t(s).jvm.used)+"M",1)):i("",!0)])]),l("tr",null,[e[11]||(e[11]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"剩余内存")],-1)),l("td",F,[t(s).mem?(d(),c("div",ll,a(t(s).mem.free)+"G",1)):i("",!0)]),l("td",el,[t(s).jvm?(d(),c("div",sl,a(t(s).jvm.free)+"M",1)):i("",!0)])]),l("tr",null,[e[12]||(e[12]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"使用率")],-1)),l("td",tl,[t(s).mem?(d(),c("div",{key:0,class:b(["cell",{"text-danger":t(s).mem.usage>80}])},a(t(s).mem.usage)+"%",3)):i("",!0)]),l("td",al,[t(s).jvm?(d(),c("div",{key:0,class:b(["cell",{"text-danger":t(s).jvm.usage>80}])},a(t(s).jvm.usage)+"%",3)):i("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(k,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[14]||(e[14]=m()),e[15]||(e[15]=l("span",{style:{"vertical-align":"middle"}},"服务器信息",-1))]),default:_(()=>[l("div",cl,[l("table",dl,[l("tbody",null,[l("tr",null,[e[16]||(e[16]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器名称")],-1)),l("td",il,[t(s).sys?(d(),c("div",ol,a(t(s).sys.computerName),1)):i("",!0)]),e[17]||(e[17]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"操作系统")],-1)),l("td",_l,[t(s).sys?(d(),c("div",nl,a(t(s).sys.osName),1)):i("",!0)])]),l("tr",null,[e[18]||(e[18]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器IP")],-1)),l("td",rl,[t(s).sys?(d(),c("div",ul,a(t(s).sys.computerIp),1)):i("",!0)]),e[19]||(e[19]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统架构")],-1)),l("td",vl,[t(s).sys?(d(),c("div",ml,a(t(s).sys.osArch),1)):i("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(j,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[20]||(e[20]=m()),e[21]||(e[21]=l("span",{style:{"vertical-align":"middle"}},"Java虚拟机信息",-1))]),default:_(()=>[l("div",bl,[l("table",fl,[l("tbody",null,[l("tr",null,[e[22]||(e[22]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java名称")],-1)),l("td",pl,[t(s).jvm?(d(),c("div",yl,a(t(s).jvm.name),1)):i("",!0)]),e[23]||(e[23]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java版本")],-1)),l("td",hl,[t(s).jvm?(d(),c("div",gl,a(t(s).jvm.version),1)):i("",!0)])]),l("tr",null,[e[24]||(e[24]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"启动时间")],-1)),l("td",kl,[t(s).jvm?(d(),c("div",jl,a(t(s).jvm.startTime),1)):i("",!0)]),e[25]||(e[25]=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行时长")],-1)),l("td",wl,[t(s).jvm?(d(),c("div",xl,a(t(s).jvm.runTime),1)):i("",!0)])]),l("tr",null,[e[26]||(e[26]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"安装路径")],-1)),l("td",Cl,[t(s).jvm?(d(),c("div",Nl,a(t(s).jvm.home),1)):i("",!0)])]),l("tr",null,[e[27]||(e[27]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"项目路径")],-1)),l("td",Ml,[t(s).sys?(d(),c("div",Tl,a(t(s).sys.userDir),1)):i("",!0)])]),l("tr",null,[e[28]||(e[28]=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行参数")],-1)),l("td",Bl,[t(s).jvm?(d(),c("div",Vl,a(t(s).jvm.inputArgs),1)):i("",!0)])])])])])]),_:1})]),_:1}),o(v,{span:24,class:"card-box"},{default:_(()=>[o(u,null,{header:_(()=>[o(w,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[29]||(e[29]=m()),e[30]||(e[30]=l("span",{style:{"vertical-align":"middle"}},"磁盘状态",-1))]),default:_(()=>[l("div",Gl,[l("table",Il,[e[31]||(e[31]=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符路径")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"文件系统")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符类型")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"可用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用百分比")])])],-1)),t(s).sysFiles?(d(),c("tbody",Jl,[(d(!0),c(B,null,V(t(s).sysFiles,(n,C)=>(d(),c("tr",{key:C},[l("td",Ll,[l("div",$l,a(n.dirName),1)]),l("td",Al,[l("div",Dl,a(n.sysTypeName),1)]),l("td",Pl,[l("div",Sl,a(n.typeName),1)]),l("td",zl,[l("div",El,a(n.total),1)]),l("td",Hl,[l("div",Ul,a(n.free),1)]),l("td",ql,[l("div",Kl,a(n.used),1)]),l("td",Ol,[l("div",{class:b(["cell",{"text-danger":n.usage>80}])},a(n.usage)+"%",3)])]))),128))])):i("",!0)])])]),_:1})]),_:1})]),_:1})])}}};export{Wl as default};
