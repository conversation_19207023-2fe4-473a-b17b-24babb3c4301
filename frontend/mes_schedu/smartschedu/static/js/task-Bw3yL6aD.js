import{$ as a}from"./index-BZGe1FpZ.js";function s(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/qryDispatchtaskPlanList",method:"get",params:t})}function n(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/manualDispatchPlan",method:"post",data:t})}function i(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/callDispatchPlan",method:"get",params:t})}function e(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/qryManualDispatchResList",method:"get",params:t})}function c(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/qryTaskList",method:"get",params:t})}function o(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/qryTaskDetail",method:"get",params:t})}function u(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/updateTaskInfo",method:"post",data:t})}function m(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/pushActivity",method:"post",data:t})}function p(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/qryImportantTaskList",method:"get",params:t})}function h(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/verifyTaskInfo",method:"post",data:t})}function d(t){return a({url:"/monitorwarn/rocketapi/schedu/dispatchtaskmgr/rebackPlanPool",method:"post",data:t})}function k(t){return a({url:"/smartdispatch/dispatchEfficiencymgr/qryTaskTrend",method:"get",params:t})}function l(t){return a({url:"/smartdispatch/dispatchEfficiencymgr/qryTaskStatic",method:"get",params:t})}function f(t){return a({url:"/smartdispatch/dispatchEfficiencymgr/qryPlanTypeStatic",method:"get",params:t})}function g(t){return a({url:"/smartdispatch/dispatchEfficiencymgr/qryMaintenanceStatic",method:"get",params:t})}function y(t){return a({url:"/smartdispatch/dispatchEfficiencymgr/qryMaintenanceList",method:"get",params:t})}function q(t){return a({url:"/smartdispatch/genericmgr/getRegionMaintainUnitTreeInfo",method:"get",params:t})}export{g as a,y as b,f as c,l as d,p as e,e as f,q as g,c as h,o as i,s as j,i as k,n as m,m as p,k as q,d as r,u,h as v};
