import{_ as o,B as e,r as t,c as a,o as n,f as r}from"./index-BZGe1FpZ.js";/* empty css                                                             */import s from"./index-DCpQLWh5.js";import"./user-DzON5jmK.js";import"./splitpanes.es-nC04_w-q.js";/* empty css                   */const c={class:"app-container home"},_=e({name:"Index"}),i=Object.assign(_,{setup(p){return t("3.8.9"),(m,f)=>(n(),a("div",c,[r(s)]))}}),B=o(i,[["__scopeId","data-v-c792ca29"]]);export{B as default};
