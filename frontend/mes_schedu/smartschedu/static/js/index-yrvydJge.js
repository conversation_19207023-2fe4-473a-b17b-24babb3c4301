import{aJ as e,_ as Ae,r as c,e as u,c as h,o as a,f as t,h as m,i as s,l as _,P as W,G as j,H as z,I as Le,t as K,B as Oe,d as ye,N as be,T as ke,Q as te,R as U,k as I,S as we,m as he,j as r,n as i,cf as oe,D as xe}from"./index-BZGe1FpZ.js";import{l as _e,g as Ce,d as Ne,u as Ue,a as Se}from"./menu-fzDPRgMj.js";let $=[];const qe=Object.assign({"../../assets/icons/svg/404.svg":()=>e(()=>import("./404-Dy3nURRX.js"),[]),"../../assets/icons/svg/QR_Code.svg":()=>e(()=>import("./QR_Code-BxVGTxby.js"),[]),"../../assets/icons/svg/Spotcheck.svg":()=>e(()=>import("./Spotcheck-BTKXgrDF.js"),[]),"../../assets/icons/svg/bug.svg":()=>e(()=>import("./bug-10dePVta.js"),[]),"../../assets/icons/svg/build.svg":()=>e(()=>import("./build-2jMyI6eP.js"),[]),"../../assets/icons/svg/button.svg":()=>e(()=>import("./button-BlSCM_GH.js"),[]),"../../assets/icons/svg/cascader.svg":()=>e(()=>import("./cascader-CXIOcY1C.js"),[]),"../../assets/icons/svg/chart.svg":()=>e(()=>import("./chart-BsLMrzXU.js"),[]),"../../assets/icons/svg/checkbox.svg":()=>e(()=>import("./checkbox-Bpiun3bf.js"),[]),"../../assets/icons/svg/client.svg":()=>e(()=>import("./client-BuZkq35_.js"),[]),"../../assets/icons/svg/clipboard.svg":()=>e(()=>import("./clipboard-DaV3cn7f.js"),[]),"../../assets/icons/svg/code.svg":()=>e(()=>import("./code-DgJ8cT4a.js"),[]),"../../assets/icons/svg/color.svg":()=>e(()=>import("./color-y1Sshoou.js"),[]),"../../assets/icons/svg/comparison.svg":()=>e(()=>import("./comparison-BsjxnU8r.js"),[]),"../../assets/icons/svg/component.svg":()=>e(()=>import("./component-Djp9s69L.js"),[]),"../../assets/icons/svg/dashboard.svg":()=>e(()=>import("./dashboard-Dy7qt_a2.js"),[]),"../../assets/icons/svg/date-range.svg":()=>e(()=>import("./date-range-B8MgYLb1.js"),[]),"../../assets/icons/svg/date.svg":()=>e(()=>import("./date-B1FSITvi.js"),[]),"../../assets/icons/svg/dict.svg":()=>e(()=>import("./dict-Bi_GqSXR.js"),[]),"../../assets/icons/svg/documentation.svg":()=>e(()=>import("./documentation-uH9BvL5U.js"),[]),"../../assets/icons/svg/download.svg":()=>e(()=>import("./download-DeIzgQWH.js"),[]),"../../assets/icons/svg/drag.svg":()=>e(()=>import("./drag-BG1_I1vT.js"),[]),"../../assets/icons/svg/druid.svg":()=>e(()=>import("./druid-BybW_S_B.js"),[]),"../../assets/icons/svg/edit.svg":()=>e(()=>import("./edit-D0DI9pAq.js"),[]),"../../assets/icons/svg/education.svg":()=>e(()=>import("./education-47KsSYIl.js"),[]),"../../assets/icons/svg/email.svg":()=>e(()=>import("./email-Dig28Vt2.js"),[]),"../../assets/icons/svg/engineering.svg":()=>e(()=>import("./engineering-DHHNMoNd.js"),[]),"../../assets/icons/svg/enter.svg":()=>e(()=>import("./enter-KOZ0bgqJ.js"),[]),"../../assets/icons/svg/erp.svg":()=>e(()=>import("./erp-DLVtuM4p.js"),[]),"../../assets/icons/svg/example.svg":()=>e(()=>import("./example-CnLLAFb9.js"),[]),"../../assets/icons/svg/excel.svg":()=>e(()=>import("./excel-D3hj5F35.js"),[]),"../../assets/icons/svg/exit-fullscreen.svg":()=>e(()=>import("./exit-fullscreen-dXhGKlQm.js"),[]),"../../assets/icons/svg/eye-open.svg":()=>e(()=>import("./eye-open-BxlshWqB.js"),[]),"../../assets/icons/svg/eye.svg":()=>e(()=>import("./eye-DqRz4sMZ.js"),[]),"../../assets/icons/svg/finance.svg":()=>e(()=>import("./finance-CJ3N6W_F.js"),[]),"../../assets/icons/svg/form.svg":()=>e(()=>import("./form-BDTA_i-I.js"),[]),"../../assets/icons/svg/fullscreen.svg":()=>e(()=>import("./fullscreen-0JHt5yWX.js"),[]),"../../assets/icons/svg/github.svg":()=>e(()=>import("./github-AJ0WQBa2.js"),[]),"../../assets/icons/svg/guide.svg":()=>e(()=>import("./guide-DZWUPi2j.js"),[]),"../../assets/icons/svg/icon.svg":()=>e(()=>import("./icon-BtMv6Od8.js"),[]),"../../assets/icons/svg/input.svg":()=>e(()=>import("./input-BJoPMnBW.js"),[]),"../../assets/icons/svg/international.svg":()=>e(()=>import("./international-CmzG1OHg.js"),[]),"../../assets/icons/svg/items.svg":()=>e(()=>import("./items-VAa6XZx3.js"),[]),"../../assets/icons/svg/job.svg":()=>e(()=>import("./job-BcmuINx7.js"),[]),"../../assets/icons/svg/language.svg":()=>e(()=>import("./language-CaW1LMEk.js"),[]),"../../assets/icons/svg/link.svg":()=>e(()=>import("./link-C93f4PgI.js"),[]),"../../assets/icons/svg/list.svg":()=>e(()=>import("./list-C7O8B4zW.js"),[]),"../../assets/icons/svg/lock.svg":()=>e(()=>import("./lock-Bexeb9hp.js"),[]),"../../assets/icons/svg/log.svg":()=>e(()=>import("./log-CF2F-nSs.js"),[]),"../../assets/icons/svg/logininfor.svg":()=>e(()=>import("./logininfor-Bm9ZYYR7.js"),[]),"../../assets/icons/svg/marketEngine.svg":()=>e(()=>import("./marketEngine-B1YrVngU.js"),[]),"../../assets/icons/svg/material.svg":()=>e(()=>import("./material-CXdcmnJU.js"),[]),"../../assets/icons/svg/message.svg":()=>e(()=>import("./message-UkR-VIBB.js"),[]),"../../assets/icons/svg/money.svg":()=>e(()=>import("./money-B1qqPuhn.js"),[]),"../../assets/icons/svg/monitor.svg":()=>e(()=>import("./monitor-gwnnVq4l.js"),[]),"../../assets/icons/svg/moon.svg":()=>e(()=>import("./moon-BOcjHwCq.js"),[]),"../../assets/icons/svg/more-up.svg":()=>e(()=>import("./more-up-u2qZwiNm.js"),[]),"../../assets/icons/svg/myprocess.svg":()=>e(()=>import("./myprocess-xK-X2O2Z.js"),[]),"../../assets/icons/svg/nacos.svg":()=>e(()=>import("./nacos-CmARyran.js"),[]),"../../assets/icons/svg/nested.svg":()=>e(()=>import("./nested-B4d5u3hW.js"),[]),"../../assets/icons/svg/number.svg":()=>e(()=>import("./number-D4hB_nHC.js"),[]),"../../assets/icons/svg/online.svg":()=>e(()=>import("./online-C2ZP8pdY.js"),[]),"../../assets/icons/svg/operationalReview.svg":()=>e(()=>import("./operationalReview-BLvrexHR.js"),[]),"../../assets/icons/svg/password.svg":()=>e(()=>import("./password-DfGvqQpB.js"),[]),"../../assets/icons/svg/pdf.svg":()=>e(()=>import("./pdf-CD9mOGjJ.js"),[]),"../../assets/icons/svg/people.svg":()=>e(()=>import("./people-CdGMHN63.js"),[]),"../../assets/icons/svg/peoples.svg":()=>e(()=>import("./peoples-BRYsIqmI.js"),[]),"../../assets/icons/svg/phone.svg":()=>e(()=>import("./phone-BpAUIz0g.js"),[]),"../../assets/icons/svg/plan.svg":()=>e(()=>import("./plan-DQ41X6Dk.js"),[]),"../../assets/icons/svg/post.svg":()=>e(()=>import("./post-DrLDyPY9.js"),[]),"../../assets/icons/svg/process-list.svg":()=>e(()=>import("./process-list-BQxM4EgI.js"),[]),"../../assets/icons/svg/process-ok.svg":()=>e(()=>import("./process-ok-DmTaDQP3.js"),[]),"../../assets/icons/svg/process-sent1.svg":()=>e(()=>import("./process-sent1-XBkmdDXy.js"),[]),"../../assets/icons/svg/process-sent2.svg":()=>e(()=>import("./process-sent2-C3bzEX1N.js"),[]),"../../assets/icons/svg/processdefinition.svg":()=>e(()=>import("./processdefinition-DHkp92I1.js"),[]),"../../assets/icons/svg/production.svg":()=>e(()=>import("./production-BYlHAkG_.js"),[]),"../../assets/icons/svg/productionPlan.svg":()=>e(()=>import("./productionPlan-Dp8BQu8F.js"),[]),"../../assets/icons/svg/purchase.svg":()=>e(()=>import("./purchase-0rpfCCGr.js"),[]),"../../assets/icons/svg/qq.svg":()=>e(()=>import("./qq-D8j4O83Y.js"),[]),"../../assets/icons/svg/quality.svg":()=>e(()=>import("./quality-pquB0hff.js"),[]),"../../assets/icons/svg/question.svg":()=>e(()=>import("./question-CvYWQbyW.js"),[]),"../../assets/icons/svg/radio.svg":()=>e(()=>import("./radio-B0t9wPBQ.js"),[]),"../../assets/icons/svg/rate.svg":()=>e(()=>import("./rate-CgnHQvKS.js"),[]),"../../assets/icons/svg/redis-list.svg":()=>e(()=>import("./redis-list-BtKGPnqO.js"),[]),"../../assets/icons/svg/redis.svg":()=>e(()=>import("./redis-D4ECyT6a.js"),[]),"../../assets/icons/svg/row.svg":()=>e(()=>import("./row-CRXKIHjm.js"),[]),"../../assets/icons/svg/sales.svg":()=>e(()=>import("./sales-CG2CrTav.js"),[]),"../../assets/icons/svg/search.svg":()=>e(()=>import("./search-CUfclCsR.js"),[]),"../../assets/icons/svg/secondaryterm.svg":()=>e(()=>import("./secondaryterm-CpEnA2Mk.js"),[]),"../../assets/icons/svg/select.svg":()=>e(()=>import("./select-DhuHHMxz.js"),[]),"../../assets/icons/svg/sentinel.svg":()=>e(()=>import("./sentinel-sIU9HpHS.js"),[]),"../../assets/icons/svg/server.svg":()=>e(()=>import("./server-unS7EyF7.js"),[]),"../../assets/icons/svg/shopping.svg":()=>e(()=>import("./shopping-CU1IRvxM.js"),[]),"../../assets/icons/svg/size.svg":()=>e(()=>import("./size-Cj9fB5Rp.js"),[]),"../../assets/icons/svg/skill.svg":()=>e(()=>import("./skill-B8f_I4m_.js"),[]),"../../assets/icons/svg/slider.svg":()=>e(()=>import("./slider-BGfehM6X.js"),[]),"../../assets/icons/svg/star.svg":()=>e(()=>import("./star-kST8a72V.js"),[]),"../../assets/icons/svg/sunny.svg":()=>e(()=>import("./sunny-DvkHW8g8.js"),[]),"../../assets/icons/svg/swagger.svg":()=>e(()=>import("./swagger-BHGXZ2Jt.js"),[]),"../../assets/icons/svg/switch.svg":()=>e(()=>import("./switch-CvaargRJ.js"),[]),"../../assets/icons/svg/system.svg":()=>e(()=>import("./system-DcNSH_Fq.js"),[]),"../../assets/icons/svg/tab.svg":()=>e(()=>import("./tab-nA3f0aBt.js"),[]),"../../assets/icons/svg/table.svg":()=>e(()=>import("./table-5PRh60AQ.js"),[]),"../../assets/icons/svg/taskmanger.svg":()=>e(()=>import("./taskmanger-CH3xDLS3.js"),[]),"../../assets/icons/svg/textarea.svg":()=>e(()=>import("./textarea-CJWXlgbJ.js"),[]),"../../assets/icons/svg/theme.svg":()=>e(()=>import("./theme-CyGq941x.js"),[]),"../../assets/icons/svg/time-range.svg":()=>e(()=>import("./time-range-D3dxgtLj.js"),[]),"../../assets/icons/svg/time.svg":()=>e(()=>import("./time-BVERp0sU.js"),[]),"../../assets/icons/svg/tool.svg":()=>e(()=>import("./tool-D8kXk1l-.js"),[]),"../../assets/icons/svg/tree-table.svg":()=>e(()=>import("./tree-table-CnOS99I9.js"),[]),"../../assets/icons/svg/tree.svg":()=>e(()=>import("./tree-BCtS3oPD.js"),[]),"../../assets/icons/svg/upload.svg":()=>e(()=>import("./upload-BueI-Il1.js"),[]),"../../assets/icons/svg/user.svg":()=>e(()=>import("./user-DqMuW5cU.js"),[]),"../../assets/icons/svg/validCode.svg":()=>e(()=>import("./validCode-COB1iLxa.js"),[]),"../../assets/icons/svg/wechat.svg":()=>e(()=>import("./wechat-lmQOcPZu.js"),[]),"../../assets/icons/svg/workflow.svg":()=>e(()=>import("./workflow-Dp7YgAzy.js"),[]),"../../assets/icons/svg/zip.svg":()=>e(()=>import("./zip-DIOSZc69.js"),[])});for(const M in qe){const g=M.split("assets/icons/svg/")[1].split(".svg")[0];$.push(g)}const Fe={class:"icon-body"},$e={class:"icon-list"},Me={class:"list-container"},Be=["onClick"],Qe={__name:"index",props:{activeIcon:{type:String}},emits:["selected"],setup(M,{expose:g,emit:G}){const E=c(""),w=c($),f=G;function x(){w.value=$,E.value&&(w.value=$.filter(A=>A.indexOf(E.value)!==-1))}function C(A){f("selected",A),document.body.click()}function S(){E.value="",w.value=$}return g({reset:S}),(A,L)=>{const q=u("el-input"),B=u("svg-icon");return a(),h("div",Fe,[t(q,{modelValue:_(E),"onUpdate:modelValue":L[0]||(L[0]=O=>W(E)?E.value=O:null),class:"icon-search",clearable:"",placeholder:"请输入图标名称",onClear:x,onInput:x},{suffix:s(()=>L[1]||(L[1]=[m("i",{class:"el-icon-search el-input__icon"},null,-1)])),_:1},8,["modelValue"]),m("div",$e,[m("div",Me,[(a(!0),h(j,null,z(_(w),(O,y)=>(a(),h("div",{class:"icon-item-wrapper",key:y,onClick:n=>C(O)},[m("div",{class:Le(["icon-item",{active:M.activeIcon===O}])},[t(B,{"icon-class":O,"class-name":"icon",style:{height:"25px",width:"16px"}},null,8,["icon-class"]),m("span",null,K(O),1)],2)],8,Be))),128))])])])}}},je=Ae(Qe,[["__scopeId","data-v-7ed7ecf0"]]),ze={class:"app-container"},Ke={class:"dialog-footer"},Ge=Oe({name:"Menu"}),We=Object.assign(Ge,{setup(M){const{proxy:g}=ye(),{sys_show_hide:G,sys_normal_disable:E}=g.useDict("sys_show_hide","sys_normal_disable"),w=c([]),f=c(!1),x=c(!0),C=c(!0),S=c(""),A=c([]),L=c(!1),q=c(!0),B=c(null),O=be({form:{},queryParams:{menuName:void 0,visible:void 0},rules:{menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}],path:[{required:!0,message:"路由地址不能为空",trigger:"blur"}]}}),{queryParams:y,form:n,rules:le}=ke(O);function N(){x.value=!0,_e(y.value).then(d=>{w.value=g.handleTree(d.data,"menuId"),x.value=!1})}function X(){A.value=[],_e().then(d=>{const o={menuId:0,menuName:"主类目",children:[]};o.children=g.handleTree(d.data,"menuId"),A.value.push(o)})}function ne(){f.value=!1,H()}function H(){n.value={menuId:void 0,parentId:0,menuName:void 0,icon:void 0,menuType:"M",orderNum:void 0,isFrame:"1",isCache:"0",visible:"0",status:"0"},g.resetForm("menuRef")}function ie(){B.value.reset()}function ae(d){n.value.icon=d}function J(){N()}function ue(){g.resetForm("queryRef"),J()}function Y(d){H(),X(),d!=null&&d.menuId?n.value.parentId=d.menuId:n.value.parentId=0,f.value=!0,S.value="添加菜单"}function re(){q.value=!1,L.value=!L.value,xe(()=>{q.value=!0})}async function de(d){H(),await X(),Ce(d.menuId).then(o=>{n.value=o.data,f.value=!0,S.value="修改菜单"})}function ve(){g.$refs.menuRef.validate(d=>{d&&(n.value.menuId!=null?Ue(n.value).then(o=>{g.$modal.msgSuccess("修改成功"),f.value=!1,N()}):Se(n.value).then(o=>{g.$modal.msgSuccess("新增成功"),f.value=!1,N()}))})}function pe(d){g.$modal.confirm('是否确认删除名称为"'+d.menuName+'"的数据项?').then(function(){return Ne(d.menuId)}).then(()=>{N(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}return N(),(d,o)=>{const b=u("el-input"),v=u("el-form-item"),me=u("el-option"),ge=u("el-select"),T=u("el-button"),Z=u("el-form"),p=u("el-col"),ce=u("right-toolbar"),ee=u("el-row"),k=u("el-table-column"),Ee=u("dict-tag"),fe=u("el-table"),Ve=u("el-tree-select"),R=u("el-radio"),F=u("el-radio-group"),Ie=u("search"),V=u("el-icon"),Te=u("el-popover"),Re=u("el-input-number"),P=u("question-filled"),D=u("el-tooltip"),Pe=u("el-dialog"),Q=te("hasPermi"),De=te("loading");return a(),h("div",ze,[U(t(Z,{model:_(y),ref:"queryRef",inline:!0},{default:s(()=>[t(v,{label:"菜单名称",prop:"menuName"},{default:s(()=>[t(b,{modelValue:_(y).menuName,"onUpdate:modelValue":o[0]||(o[0]=l=>_(y).menuName=l),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"},onKeyup:he(J,["enter"])},null,8,["modelValue"])]),_:1}),t(v,{label:"状态",prop:"status"},{default:s(()=>[t(ge,{modelValue:_(y).status,"onUpdate:modelValue":o[1]||(o[1]=l=>_(y).status=l),placeholder:"菜单状态",clearable:"",style:{width:"200px"}},{default:s(()=>[(a(!0),h(j,null,z(_(E),l=>(a(),r(me,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,null,{default:s(()=>[t(T,{type:"primary",icon:"Search",onClick:J},{default:s(()=>o[18]||(o[18]=[i("搜索")])),_:1,__:[18]}),t(T,{icon:"Refresh",onClick:ue},{default:s(()=>o[19]||(o[19]=[i("重置")])),_:1,__:[19]})]),_:1})]),_:1},8,["model"]),[[we,_(C)]]),t(ee,{gutter:10,class:"mb8"},{default:s(()=>[t(p,{span:1.5},{default:s(()=>[U((a(),r(T,{type:"primary",plain:"",icon:"Plus",onClick:Y},{default:s(()=>o[20]||(o[20]=[i("新增")])),_:1,__:[20]})),[[Q,["system:menu:add"]]])]),_:1}),t(p,{span:1.5},{default:s(()=>[t(T,{type:"info",plain:"",icon:"Sort",onClick:re},{default:s(()=>o[21]||(o[21]=[i("展开/折叠")])),_:1,__:[21]})]),_:1}),t(ce,{showSearch:_(C),"onUpdate:showSearch":o[2]||(o[2]=l=>W(C)?C.value=l:null),onQueryTable:N},null,8,["showSearch"])]),_:1}),_(q)?U((a(),r(fe,{key:0,data:_(w),"row-key":"menuId","default-expand-all":_(L),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:s(()=>[t(k,{prop:"menuName",label:"菜单名称","show-overflow-tooltip":!0,width:"160"}),t(k,{prop:"icon",label:"图标",align:"center",width:"100"},{default:s(l=>[t(_(oe),{"icon-class":l.row.icon},null,8,["icon-class"])]),_:1}),t(k,{prop:"orderNum",label:"排序",width:"60"}),t(k,{prop:"perms",label:"权限标识","show-overflow-tooltip":!0}),t(k,{prop:"component",label:"组件路径","show-overflow-tooltip":!0}),t(k,{prop:"status",label:"状态",width:"80"},{default:s(l=>[t(Ee,{options:_(E),value:l.row.status},null,8,["options","value"])]),_:1}),t(k,{label:"创建时间",align:"center",width:"160",prop:"createTime"},{default:s(l=>[m("span",null,K(d.parseTime(l.row.createTime)),1)]),_:1}),t(k,{label:"操作",align:"center",width:"210","class-name":"small-padding fixed-width"},{default:s(l=>[U((a(),r(T,{link:"",type:"primary",icon:"Edit",onClick:se=>de(l.row)},{default:s(()=>o[22]||(o[22]=[i("修改")])),_:2,__:[22]},1032,["onClick"])),[[Q,["system:menu:edit"]]]),U((a(),r(T,{link:"",type:"primary",icon:"Plus",onClick:se=>Y(l.row)},{default:s(()=>o[23]||(o[23]=[i("新增")])),_:2,__:[23]},1032,["onClick"])),[[Q,["system:menu:add"]]]),U((a(),r(T,{link:"",type:"primary",icon:"Delete",onClick:se=>pe(l.row)},{default:s(()=>o[24]||(o[24]=[i("删除")])),_:2,__:[24]},1032,["onClick"])),[[Q,["system:menu:remove"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[De,_(x)]]):I("",!0),t(Pe,{title:_(S),modelValue:_(f),"onUpdate:modelValue":o[17]||(o[17]=l=>W(f)?f.value=l:null),width:"680px","append-to-body":""},{footer:s(()=>[m("div",Ke,[t(T,{type:"primary",onClick:ve},{default:s(()=>o[41]||(o[41]=[i("确 定")])),_:1,__:[41]}),t(T,{onClick:ne},{default:s(()=>o[42]||(o[42]=[i("取 消")])),_:1,__:[42]})])]),default:s(()=>[t(Z,{ref:"menuRef",model:_(n),rules:_(le),"label-width":"100px"},{default:s(()=>[t(ee,null,{default:s(()=>[t(p,{span:24},{default:s(()=>[t(v,{label:"上级菜单"},{default:s(()=>[t(Ve,{modelValue:_(n).parentId,"onUpdate:modelValue":o[3]||(o[3]=l=>_(n).parentId=l),data:_(A),props:{value:"menuId",label:"menuName",children:"children"},"value-key":"menuId",placeholder:"选择上级菜单","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1}),t(p,{span:24},{default:s(()=>[t(v,{label:"菜单类型",prop:"menuType"},{default:s(()=>[t(F,{modelValue:_(n).menuType,"onUpdate:modelValue":o[4]||(o[4]=l=>_(n).menuType=l)},{default:s(()=>[t(R,{value:"M"},{default:s(()=>o[25]||(o[25]=[i("目录")])),_:1,__:[25]}),t(R,{value:"C"},{default:s(()=>o[26]||(o[26]=[i("菜单")])),_:1,__:[26]}),t(R,{value:"F"},{default:s(()=>o[27]||(o[27]=[i("按钮")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(n).menuType!="F"?(a(),r(p,{key:0,span:12},{default:s(()=>[t(v,{label:"菜单图标",prop:"icon"},{default:s(()=>[t(Te,{placement:"bottom-start",width:540,trigger:"click"},{reference:s(()=>[t(b,{modelValue:_(n).icon,"onUpdate:modelValue":o[5]||(o[5]=l=>_(n).icon=l),placeholder:"点击选择图标",onBlur:ie,readonly:""},{prefix:s(()=>[_(n).icon?(a(),r(_(oe),{key:0,"icon-class":_(n).icon,class:"el-input__icon",style:{height:"32px",width:"16px"}},null,8,["icon-class"])):(a(),r(V,{key:1,style:{height:"32px",width:"16px"}},{default:s(()=>[t(Ie)]),_:1}))]),_:1},8,["modelValue"])]),default:s(()=>[t(_(je),{ref_key:"iconSelectRef",ref:B,onSelected:ae,"active-icon":_(n).icon},null,8,["active-icon"])]),_:1})]),_:1})]),_:1})):I("",!0),t(p,{span:12},{default:s(()=>[t(v,{label:"显示排序",prop:"orderNum"},{default:s(()=>[t(Re,{modelValue:_(n).orderNum,"onUpdate:modelValue":o[6]||(o[6]=l=>_(n).orderNum=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),t(p,{span:12},{default:s(()=>[t(v,{label:"菜单名称",prop:"menuName"},{default:s(()=>[t(b,{modelValue:_(n).menuName,"onUpdate:modelValue":o[7]||(o[7]=l=>_(n).menuName=l),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1}),_(n).menuType=="C"?(a(),r(p,{key:1,span:12},{default:s(()=>[t(v,{prop:"routeName"},{label:s(()=>[m("span",null,[t(D,{content:"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[28]||(o[28]=i(" 路由名称 "))])]),default:s(()=>[t(b,{modelValue:_(n).routeName,"onUpdate:modelValue":o[8]||(o[8]=l=>_(n).routeName=l),placeholder:"请输入路由名称"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType!="F"?(a(),r(p,{key:2,span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[29]||(o[29]=i("是否外链 "))])]),default:s(()=>[t(F,{modelValue:_(n).isFrame,"onUpdate:modelValue":o[9]||(o[9]=l=>_(n).isFrame=l)},{default:s(()=>[t(R,{value:"0"},{default:s(()=>o[30]||(o[30]=[i("是")])),_:1,__:[30]}),t(R,{value:"1"},{default:s(()=>o[31]||(o[31]=[i("否")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType!="F"?(a(),r(p,{key:3,span:12},{default:s(()=>[t(v,{prop:"path"},{label:s(()=>[m("span",null,[t(D,{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[32]||(o[32]=i(" 路由地址 "))])]),default:s(()=>[t(b,{modelValue:_(n).path,"onUpdate:modelValue":o[10]||(o[10]=l=>_(n).path=l),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType=="C"?(a(),r(p,{key:4,span:12},{default:s(()=>[t(v,{prop:"component"},{label:s(()=>[m("span",null,[t(D,{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[33]||(o[33]=i(" 组件路径 "))])]),default:s(()=>[t(b,{modelValue:_(n).component,"onUpdate:modelValue":o[11]||(o[11]=l=>_(n).component=l),placeholder:"请输入组件路径"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType!="M"?(a(),r(p,{key:5,span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[34]||(o[34]=i(" 权限字符 "))])]),default:s(()=>[t(b,{modelValue:_(n).perms,"onUpdate:modelValue":o[12]||(o[12]=l=>_(n).perms=l),placeholder:"请输入权限标识",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType=="C"?(a(),r(p,{key:6,span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:'访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[35]||(o[35]=i(" 路由参数 "))])]),default:s(()=>[t(b,{modelValue:_(n).query,"onUpdate:modelValue":o[13]||(o[13]=l=>_(n).query=l),placeholder:"请输入路由参数",maxlength:"255"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType=="C"?(a(),r(p,{key:7,span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[36]||(o[36]=i(" 是否缓存 "))])]),default:s(()=>[t(F,{modelValue:_(n).isCache,"onUpdate:modelValue":o[14]||(o[14]=l=>_(n).isCache=l)},{default:s(()=>[t(R,{value:"0"},{default:s(()=>o[37]||(o[37]=[i("缓存")])),_:1,__:[37]}),t(R,{value:"1"},{default:s(()=>o[38]||(o[38]=[i("不缓存")])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),_(n).menuType!="F"?(a(),r(p,{key:8,span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[39]||(o[39]=i(" 显示状态 "))])]),default:s(()=>[t(F,{modelValue:_(n).visible,"onUpdate:modelValue":o[15]||(o[15]=l=>_(n).visible=l)},{default:s(()=>[(a(!0),h(j,null,z(_(G),l=>(a(),r(R,{key:l.value,value:l.value},{default:s(()=>[i(K(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),t(p,{span:12},{default:s(()=>[t(v,null,{label:s(()=>[m("span",null,[t(D,{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"},{default:s(()=>[t(V,null,{default:s(()=>[t(P)]),_:1})]),_:1}),o[40]||(o[40]=i(" 菜单状态 "))])]),default:s(()=>[t(F,{modelValue:_(n).status,"onUpdate:modelValue":o[16]||(o[16]=l=>_(n).status=l)},{default:s(()=>[(a(!0),h(j,null,z(_(E),l=>(a(),r(R,{key:l.value,value:l.value},{default:s(()=>[i(K(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{We as default};
