import{$,B as Q,d as j,r as d,e as s,Q as V,c as F,o as h,f as e,R as y,i as n,m as C,l as t,n as N,j as L,h as S,t as B,S as O,P as R}from"./index-BZGe1FpZ.js";function z(m){return $({url:"/system/online/list",method:"get",params:m})}function E(m){return $({url:"/system/online/"+m,method:"delete"})}const A={class:"app-container"},G=Q({name:"Online"}),M=Object.assign(G,{setup(m){const{proxy:f}=j(),k=d([]),_=d(!0),g=d(0),r=d(1),u=d(10),p=d({ipaddr:void 0,userName:void 0});function w(){_.value=!0,z(p.value).then(i=>{k.value=i.rows,g.value=i.total,_.value=!1})}function c(){r.value=1,w()}function D(){f.resetForm("queryRef"),c()}function P(i){f.$modal.confirm('是否确认强退名称为"'+i.userName+'"的用户?').then(function(){return E(i.tokenId)}).then(()=>{w(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}return w(),(i,o)=>{const x=s("el-input"),v=s("el-form-item"),b=s("el-button"),T=s("el-form"),a=s("el-table-column"),U=s("el-table"),q=s("pagination"),I=V("hasPermi"),K=V("loading");return h(),F("div",A,[e(T,{model:t(p),ref:"queryRef",inline:!0},{default:n(()=>[e(v,{label:"登录地址",prop:"ipaddr"},{default:n(()=>[e(x,{modelValue:t(p).ipaddr,"onUpdate:modelValue":o[0]||(o[0]=l=>t(p).ipaddr=l),placeholder:"请输入登录地址",clearable:"",style:{width:"200px"},onKeyup:C(c,["enter"])},null,8,["modelValue"])]),_:1}),e(v,{label:"用户名称",prop:"userName"},{default:n(()=>[e(x,{modelValue:t(p).userName,"onUpdate:modelValue":o[1]||(o[1]=l=>t(p).userName=l),placeholder:"请输入用户名称",clearable:"",style:{width:"200px"},onKeyup:C(c,["enter"])},null,8,["modelValue"])]),_:1}),e(v,null,{default:n(()=>[e(b,{type:"primary",icon:"Search",onClick:c},{default:n(()=>o[4]||(o[4]=[N("搜索")])),_:1,__:[4]}),e(b,{icon:"Refresh",onClick:D},{default:n(()=>o[5]||(o[5]=[N("重置")])),_:1,__:[5]})]),_:1})]),_:1},8,["model"]),y((h(),L(U,{data:t(k).slice((t(r)-1)*t(u),t(r)*t(u)),style:{width:"100%"}},{default:n(()=>[e(a,{label:"序号",width:"50",type:"index",align:"center"},{default:n(l=>[S("span",null,B((t(r)-1)*t(u)+l.$index+1),1)]),_:1}),e(a,{label:"会话编号",align:"center",prop:"tokenId","show-overflow-tooltip":!0}),e(a,{label:"登录名称",align:"center",prop:"userName","show-overflow-tooltip":!0}),e(a,{label:"所属部门",align:"center",prop:"deptName","show-overflow-tooltip":!0}),e(a,{label:"主机",align:"center",prop:"ipaddr","show-overflow-tooltip":!0}),e(a,{label:"登录地点",align:"center",prop:"loginLocation","show-overflow-tooltip":!0}),e(a,{label:"操作系统",align:"center",prop:"os","show-overflow-tooltip":!0}),e(a,{label:"浏览器",align:"center",prop:"browser","show-overflow-tooltip":!0}),e(a,{label:"登录时间",align:"center",prop:"loginTime",width:"180"},{default:n(l=>[S("span",null,B(i.parseTime(l.row.loginTime)),1)]),_:1}),e(a,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(l=>[y((h(),L(b,{link:"",type:"primary",icon:"Delete",onClick:H=>P(l.row)},{default:n(()=>o[6]||(o[6]=[N("强退")])),_:2,__:[6]},1032,["onClick"])),[[I,["monitor:online:forceLogout"]]])]),_:1})]),_:1},8,["data"])),[[K,t(_)]]),y(e(q,{total:t(g),page:t(r),"onUpdate:page":o[2]||(o[2]=l=>R(r)?r.value=l:null),limit:t(u),"onUpdate:limit":o[3]||(o[3]=l=>R(u)?u.value=l:null)},null,8,["total","page","limit"]),[[O,t(g)>0]])])}}});export{M as default};
