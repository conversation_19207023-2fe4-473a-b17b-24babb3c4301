import{$ as G,B as ie,d as pe,r as b,u as me,N as _e,T as fe,e as s,Q as Y,c as k,o as i,R as j,f as e,S as z,l as t,i as l,m as ce,G as E,H as F,j as g,P as T,n as r,h as K,t as v,k as O}from"./index-BZGe1FpZ.js";import{g as be}from"./job-Cs9qwivn.js";function ge(V){return G({url:"/schedule/job/log/list",method:"get",params:V})}function ve(V){return G({url:"/schedule/job/log/"+V,method:"delete"})}function we(){return G({url:"/schedule/job/log/clean",method:"delete"})}const je={class:"app-container"},ye={key:0},he={key:1},ke={class:"dialog-footer"},Ve=ie({name:"JobLog"}),De=Object.assign(Ve,{setup(V){const{proxy:_}=pe(),{sys_common_status:R,sys_job_group:$}=_.useDict("sys_common_status","sys_job_group"),U=b([]),y=b(!1),N=b(!0),C=b(!0),S=b([]),I=b(!0),D=b(0),h=b([]),P=me(),H=_e({form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0}}),{queryParams:n,form:p,rules:Ce}=fe(H);function w(){N.value=!0,ge(_.addDateRange(n.value,h.value)).then(u=>{U.value=u.rows,D.value=u.total,N.value=!1})}function A(){const u={path:"/monitor/job"};_.$tab.closeOpenPage(u)}function L(){n.value.pageNum=1,w()}function W(){h.value=[],_.resetForm("queryRef"),L()}function X(u){S.value=u.map(o=>o.jobLogId),I.value=!u.length}function Z(u){y.value=!0,p.value=u}function ee(u){_.$modal.confirm('是否确认删除调度日志编号为"'+S.value+'"的数据项?').then(function(){return ve(S.value)}).then(()=>{w(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function le(){_.$modal.confirm("是否确认清空所有调度日志数据项?").then(function(){return we()}).then(()=>{w(),_.$modal.msgSuccess("清空成功")}).catch(()=>{})}function oe(){_.download("monitor/jobLog/export",{...n.value},`job_log_${new Date().getTime()}.xlsx`)}return(()=>{const u=P.params&&P.params.jobId;u!==void 0&&u!=0?be(u).then(o=>{n.value.jobName=o.data.jobName,n.value.jobGroup=o.data.jobGroup,w()}):w()})(),(u,o)=>{const te=s("el-input"),d=s("el-form-item"),q=s("el-option"),B=s("el-select"),ae=s("el-date-picker"),c=s("el-button"),J=s("el-form"),m=s("el-col"),ne=s("right-toolbar"),M=s("el-row"),f=s("el-table-column"),Q=s("dict-tag"),ue=s("el-table"),se=s("pagination"),re=s("el-dialog"),x=Y("hasPermi"),de=Y("loading");return i(),k("div",je,[j(e(J,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(d,{label:"任务名称",prop:"jobName"},{default:l(()=>[e(te,{modelValue:t(n).jobName,"onUpdate:modelValue":o[0]||(o[0]=a=>t(n).jobName=a),placeholder:"请输入任务名称",clearable:"",style:{width:"240px"},onKeyup:ce(L,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"任务组名",prop:"jobGroup"},{default:l(()=>[e(B,{modelValue:t(n).jobGroup,"onUpdate:modelValue":o[1]||(o[1]=a=>t(n).jobGroup=a),placeholder:"请选择任务组名",clearable:"",style:{width:"240px"}},{default:l(()=>[(i(!0),k(E,null,F(t($),a=>(i(),g(q,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"执行状态",prop:"status"},{default:l(()=>[e(B,{modelValue:t(n).status,"onUpdate:modelValue":o[2]||(o[2]=a=>t(n).status=a),placeholder:"请选择执行状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(i(!0),k(E,null,F(t(R),a=>(i(),g(q,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"执行时间",style:{width:"308px"}},{default:l(()=>[e(ae,{modelValue:t(h),"onUpdate:modelValue":o[3]||(o[3]=a=>T(h)?h.value=a:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(d,null,{default:l(()=>[e(c,{type:"primary",icon:"Search",onClick:L},{default:l(()=>o[9]||(o[9]=[r("搜索")])),_:1,__:[9]}),e(c,{icon:"Refresh",onClick:W},{default:l(()=>o[10]||(o[10]=[r("重置")])),_:1,__:[10]})]),_:1})]),_:1},8,["model"]),[[z,t(C)]]),e(M,{gutter:10,class:"mb8"},{default:l(()=>[e(m,{span:1.5},{default:l(()=>[j((i(),g(c,{type:"danger",plain:"",icon:"Delete",disabled:t(I),onClick:ee},{default:l(()=>o[11]||(o[11]=[r("删除")])),_:1,__:[11]},8,["disabled"])),[[x,["monitor:job:remove"]]])]),_:1}),e(m,{span:1.5},{default:l(()=>[j((i(),g(c,{type:"danger",plain:"",icon:"Delete",onClick:le},{default:l(()=>o[12]||(o[12]=[r("清空")])),_:1,__:[12]})),[[x,["monitor:job:remove"]]])]),_:1}),e(m,{span:1.5},{default:l(()=>[j((i(),g(c,{type:"warning",plain:"",icon:"Download",onClick:oe},{default:l(()=>o[13]||(o[13]=[r("导出")])),_:1,__:[13]})),[[x,["monitor:job:export"]]])]),_:1}),e(m,{span:1.5},{default:l(()=>[e(c,{type:"warning",plain:"",icon:"Close",onClick:A},{default:l(()=>o[14]||(o[14]=[r("关闭")])),_:1,__:[14]})]),_:1}),e(ne,{showSearch:t(C),"onUpdate:showSearch":o[4]||(o[4]=a=>T(C)?C.value=a:null),onQueryTable:w},null,8,["showSearch"])]),_:1}),j((i(),g(ue,{data:t(U),onSelectionChange:X},{default:l(()=>[e(f,{type:"selection",width:"55",align:"center"}),e(f,{label:"日志编号",width:"80",align:"center",prop:"jobLogId"}),e(f,{label:"任务名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}),e(f,{label:"任务组名",align:"center",prop:"jobGroup","show-overflow-tooltip":!0},{default:l(a=>[e(Q,{options:t($),value:a.row.jobGroup},null,8,["options","value"])]),_:1}),e(f,{label:"调用目标字符串",align:"center",prop:"invokeTarget","show-overflow-tooltip":!0}),e(f,{label:"日志信息",align:"center",prop:"jobMessage","show-overflow-tooltip":!0}),e(f,{label:"执行状态",align:"center",prop:"status"},{default:l(a=>[e(Q,{options:t(R),value:a.row.status},null,8,["options","value"])]),_:1}),e(f,{label:"执行时间",align:"center",prop:"createTime",width:"180"},{default:l(a=>[K("span",null,v(u.parseTime(a.row.createTime)),1)]),_:1}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[j((i(),g(c,{link:"",type:"primary",icon:"View",onClick:xe=>Z(a.row)},{default:l(()=>o[15]||(o[15]=[r("详细")])),_:2,__:[15]},1032,["onClick"])),[[x,["monitor:job:query"]]])]),_:1})]),_:1},8,["data"])),[[de,t(N)]]),j(e(se,{total:t(D),page:t(n).pageNum,"onUpdate:page":o[5]||(o[5]=a=>t(n).pageNum=a),limit:t(n).pageSize,"onUpdate:limit":o[6]||(o[6]=a=>t(n).pageSize=a),onPagination:w},null,8,["total","page","limit"]),[[z,t(D)>0]]),e(re,{title:"调度日志详细",modelValue:t(y),"onUpdate:modelValue":o[8]||(o[8]=a=>T(y)?y.value=a:null),width:"700px","append-to-body":""},{footer:l(()=>[K("div",ke,[e(c,{onClick:o[7]||(o[7]=a=>y.value=!1)},{default:l(()=>o[16]||(o[16]=[r("关 闭")])),_:1,__:[16]})])]),default:l(()=>[e(J,{model:t(p),"label-width":"100px"},{default:l(()=>[e(M,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"日志序号："},{default:l(()=>[r(v(t(p).jobLogId),1)]),_:1}),e(d,{label:"任务名称："},{default:l(()=>[r(v(t(p).jobName),1)]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"任务分组："},{default:l(()=>[r(v(t(p).jobGroup),1)]),_:1}),e(d,{label:"执行时间："},{default:l(()=>[r(v(t(p).createTime),1)]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(d,{label:"调用方法："},{default:l(()=>[r(v(t(p).invokeTarget),1)]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(d,{label:"日志信息："},{default:l(()=>[r(v(t(p).jobMessage),1)]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(d,{label:"执行状态："},{default:l(()=>[t(p).status==0?(i(),k("div",ye,"正常")):t(p).status==1?(i(),k("div",he,"失败")):O("",!0)]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[t(p).status==1?(i(),g(d,{key:0,label:"异常信息："},{default:l(()=>[r(v(t(p).exceptionInfo),1)]),_:1})):O("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{De as default};
