import{$ as t}from"./index-BZGe1FpZ.js";function r(e){return t({url:"/code/gen/list",method:"get",params:e})}function o(e){return t({url:"/code/gen/db/list",method:"get",params:e})}function a(e){return t({url:"/code/gen/"+e,method:"get"})}function u(e){return t({url:"/code/gen",method:"put",data:e})}function d(e){return t({url:"/code/gen/importTable",method:"post",params:e})}function l(e){return t({url:"/code/gen/createTable",method:"post",params:e})}function c(e){return t({url:"/code/gen/preview/"+e,method:"get"})}function s(e){return t({url:"/code/gen/"+e,method:"delete"})}function i(e){return t({url:"/code/gen/genCode/"+e,method:"get"})}function g(e){return t({url:"/code/gen/synchDb/"+e,method:"get"})}export{r as a,i as b,l as c,s as d,a as g,d as i,o as l,c as p,g as s,u};
