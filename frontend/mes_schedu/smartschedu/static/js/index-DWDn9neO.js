import{_ as _e,B as ke,a2 as he,d as Se,r as v,N as Ne,T as Te,e as m,Q as j,c as z,o as l,f as n,j as r,k as s,i as a,l as i,R as P,P as Ce,G as K,H as W,n as h,S as X,h as q,J as xe,t as T}from"./index-BZGe1FpZ.js";import De from"./addForm-BMl0bKLb.js";import{g as Pe,h as qe}from"./plan-KEsYJfaJ.js";import{M as Ve,g as Z}from"./splitpanes.es-nC04_w-q.js";import Re from"./areaTree-BxzGSRqT.js";import Ie from"./commonFormSearch-L73kV2iA.js";import{p as Ue,b as je,f as ee,g as ze,c as $}from"./optionsData-BhtT3Fxt.js";/* empty css                   */import"./common-BS8XB_Gq.js";import"./commonForm-D1gG5yoU.js";const $e={class:"app-container"},Le=ke({name:"User"}),Ae=Object.assign(Le,{setup(Be){const te=he(),{proxy:C}=Se(),L=v([]),N=v(!1),V=v(!0),ae=v(!0),A=v([]),le=v(!0),ie=v(!0),R=v(0),I=v(""),ne=v([]),U=v({}),x=v(""),B=v([]),F=v([]),re=[{text:"最近一周",value:()=>{const u=new Date,e=new Date(new Date().setDate(new Date().getDate()-7));return[u,e]}},{text:"最近一个月",value:()=>{const u=new Date,e=new Date(new Date().setDate(new Date().getDate()-30));return[u,e]}},{text:"最近三个月",value:()=>{const u=new Date,e=new Date(new Date().setDate(new Date().getDate()-90));return[u,e]}}],d=v([{key:0,label:"用户编号",visible:!0},{key:1,label:"省",visible:!0},{key:2,label:"市",visible:!0},{key:3,label:"站点名称",visible:!0},{key:4,label:"站点类型",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"业务分类",visible:!0},{key:7,label:"监测活动大类",visible:!0},{key:8,label:"监测活动小类",visible:!0},{key:9,label:"目标值",visible:!0},{key:10,label:"已完成",visible:!0},{key:11,label:"创建人",visible:!0},{key:12,label:"创建时间",visible:!0},{key:13,label:"编辑时间",visible:!0}]),oe=Ne({form:{},queryParams:{pageNo:1,pageSize:10,cityCode:void 0,siteName:void 0,siteId:void 0,businessType:"water",siteType:void 0,activitySubtype:void 0,activityType:void 0},rules:{siteType:[{required:!0,message:"请选择站点类型",trigger:["blur","change"]}],businessType:[{required:!0,message:"请选择业务类型",trigger:["blur","change"]}],activityType:[{required:!0,message:"请选择监测活动大类",trigger:["blur","change"]}],activitySubtype:[{required:!0,message:"请选择监测活动小类",trigger:["blur","change"]}],targetValue:[{required:!0,message:"请输入目标值",trigger:"blur"}],provinceCode:[{required:!0,message:"请选择省份",trigger:["blur","change"]}]}}),{queryParams:o,form:ue,rules:Fe}=Te(oe),se=(u,e)=>{var f,k,c,D;const w=(k=(f=e.checkedNodes)==null?void 0:f.filter(y=>y.type==="city"))==null?void 0:k.map(y=>y.id),_=(D=(c=e.checkedNodes)==null?void 0:c.filter(y=>y.type==="site"))==null?void 0:D.map(y=>y.id);F.value=_,B.value=w,S()};function S(){var u,e,w,_,f,k;V.value=!0,Pe({...o.value,approvalStatus:o.value.approvalStatus?o.value.approvalStatus:"pending,rejected,approved",cityCode:(u=B.value)==null?void 0:u.join(","),siteId:(e=F.value)==null?void 0:e.join(","),planStartTime:(_=(w=o.value)==null?void 0:w.planTime)==null?void 0:_[0],planEndTime:(k=(f=o.value)==null?void 0:f.planTime)==null?void 0:k[1]}).then(c=>{V.value=!1,L.value=c.data.data,R.value=c.data.totalRecords})}function M(){o.value.pageNo=1,S()}function pe(){ne.value=[],C.resetForm("queryRef"),M()}function de(u){const e=u.id||A.value;C.$modal.confirm("是否确认删除该条数据项？").then(function(){return qe(e)}).then(()=>{S(),C.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ve(u){A.value=u.map(e=>e.id),le.value=u.length!=1,ie.value=!u.length}function Q(){ue.value={provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,siteName:void 0,siteId:void 0,siteType:void 0,businessType:"water",activityType:void 0,activitySubtype:void 0,targetValue:void 0},U.value={},x.value="add",C.resetForm("siteRef")}function ce(){Q(),x.value="add",N.value=!0,I.value="手动新增计划"}function E(u,e){Q(),x.value=e,U.value={...u},I.value=`${e==="view"?"查看":"编辑"}计划`,N.value=!0}return S(),(u,e)=>{const w=m("el-option"),_=m("el-select"),f=m("el-form-item"),k=m("el-date-picker"),c=m("el-button"),D=m("el-form"),y=m("el-col"),H=m("el-row"),p=m("el-table-column"),me=m("el-tag"),ye=m("el-table"),be=m("pagination"),fe=j("hasPermi"),ge=j("loading"),we=j("el-table-infinite-scroll");return l(),z("div",$e,[n(H,{gutter:20},{default:a(()=>[n(i(Ve),{horizontal:i(te).device==="mobile",class:"default-theme"},{default:a(()=>[n(i(Z),{size:"16"},{default:a(()=>[n(Re,{onHandleCheck:se})]),_:1}),n(i(Z),{class:"table-container",size:"84"},{default:a(()=>[n(y,null,{default:a(()=>[P(n(D,{model:i(o),ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:a(()=>[n(i(Ie),{needActivity:!0,queryParams:i(o),"onUpdate:queryParams":e[0]||(e[0]=t=>Ce(o)?o.value=t:null)},null,8,["queryParams"]),n(f,{label:"计划来源",prop:"planSource"},{default:a(()=>[n(_,{modelValue:i(o).planSource,"onUpdate:modelValue":e[1]||(e[1]=t=>i(o).planSource=t),placeholder:"计划来源",clearable:"",style:{width:"240px"}},{default:a(()=>[(l(!0),z(K,null,W(i(Ue),t=>(l(),r(w,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(f,{label:"审批状态",prop:"approvalStatus"},{default:a(()=>[n(_,{modelValue:i(o).approvalStatus,"onUpdate:modelValue":e[2]||(e[2]=t=>i(o).approvalStatus=t),placeholder:"审批状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(l(!0),z(K,null,W(i(je),t=>(l(),r(w,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(f,{label:"生成时间",prop:"planTime"},{default:a(()=>[n(k,{type:"datetimerange",modelValue:i(o).planTime,"onUpdate:modelValue":e[3]||(e[3]=t=>i(o).planTime=t),placeholder:"选择开始时间",style:{width:"240px"},"start-placeholder":"开始时间","end-placeholder":"结束时间",shortcuts:re},null,8,["modelValue"])]),_:1}),n(f,{class:"form-btn"},{default:a(()=>[n(c,{type:"primary",icon:"Search",onClick:M},{default:a(()=>e[7]||(e[7]=[h("搜索")])),_:1,__:[7]}),n(c,{icon:"Refresh",onClick:pe},{default:a(()=>e[8]||(e[8]=[h("重置")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"]),[[X,ae.value]]),n(H,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:a(()=>[n(y,{span:12},{default:a(()=>e[9]||(e[9]=[q("div",{style:{width:"100%"},class:"table-title"}," 调度计划管理列表 ",-1)])),_:1,__:[9]}),n(y,{span:12,style:{"text-align":"right"}},{default:a(()=>[P((l(),r(c,{type:"primary",icon:"Plus",onClick:ce},{default:a(()=>e[10]||(e[10]=[h("手动新增计划")])),_:1,__:[10]})),[[fe,["system:user:add"]]])]),_:1})]),_:1}),P((l(),r(ye,{data:L.value,onSelectionChange:ve,"max-height":"calc(100vh - 460px)",style:{width:"100%"}},{default:a(()=>[d.value[0].visible?(l(),r(p,{key:0,label:"序号",align:"center",type:"index",width:"50",fixed:""})):s("",!0),d.value[3].visible?(l(),r(p,{label:"计划编码",align:"center",key:"planCode",prop:"planCode","show-overflow-tooltip":!0,width:"160"})):s("",!0),d.value[3].visible?(l(),r(p,{label:"计划名称",align:"center",key:"planName",prop:"planName","show-overflow-tooltip":!0,width:"160"})):s("",!0),d.value[4].visible?(l(),r(p,{label:"优先级",align:"center",key:"planPriority",prop:"planPriority",width:"160"},{default:a(t=>{var b,g;return[q("span",{style:xe({color:(b=i(ee)[t.row.planPriority])==null?void 0:b.color})},T(((g=i(ee)[t.row.planPriority])==null?void 0:g.text)||""),5)]}),_:1})):s("",!0),d.value[9].visible?(l(),r(p,{key:4,label:"计划来源",align:"center",width:"120"},{default:a(t=>{var b,g;return[q("span",null,T((g=(b=i(ze))==null?void 0:b[t.row.planSource])==null?void 0:g.text),1)]}),_:1})):s("",!0),d.value[1].visible?(l(),r(p,{label:"省",align:"center",key:"provinceName",prop:"provinceName","show-overflow-tooltip":!0,width:"120"})):s("",!0),d.value[2].visible?(l(),r(p,{label:"市",align:"center",key:"cityName",prop:"cityName","show-overflow-tooltip":!0,width:"120"})):s("",!0),d.value[5].visible?(l(),r(p,{label:"业务分类",align:"center",key:"businessTypeName",prop:"businessTypeName"})):s("",!0),d.value[6].visible?(l(),r(p,{label:"监测活动大类",align:"center",key:"activityTypeName",prop:"activityTypeName",width:"160","show-overflow-tooltip":""})):s("",!0),d.value[7].visible?(l(),r(p,{label:"监测活动小类",align:"center",key:"activitySubtypeName",prop:"activitySubtypeName",width:"180","show-overflow-tooltip":""})):s("",!0),d.value[3].visible?(l(),r(p,{label:"站点名称",align:"center",key:"siteName",prop:"siteName","show-overflow-tooltip":!0,width:"180"})):s("",!0),d.value[4].visible?(l(),r(p,{label:"站点类型",align:"center",key:"siteTypeName",prop:"siteTypeName",width:"160"},{default:a(t=>[q("span",null,T(t.row.siteType==="01"&&t.row.businessType==="water"&&t.row.hasAutomaticStation==="N"?"水断面":t.row.siteTypeName)+" "+T(t.row.hasAutomaticStation==="Y"&&t.row.businessType==="water"?"(自动站)":""),1)]),_:1})):s("",!0),d.value[9].visible?(l(),r(p,{key:12,label:"审批状态",align:"center",width:"120"},{default:a(t=>{var b,g,G,J;return[(g=(b=i($))==null?void 0:b[t.row.approvalStatus])!=null&&g.type?(l(),r(me,{key:0,type:(J=(G=i($))==null?void 0:G[t.row.approvalStatus])==null?void 0:J.type},{default:a(()=>{var O,Y;return[h(T((Y=(O=i($))==null?void 0:O[t.row.approvalStatus])==null?void 0:Y.text),1)]}),_:2},1032,["type"])):s("",!0)]}),_:1})):s("",!0),d.value[9].visible?(l(),r(p,{label:"审批人",align:"center",key:"approver",prop:"approver",width:"120"})):s("",!0),d.value[11].visible?(l(),r(p,{label:"生成时间",align:"center",key:"createTime",prop:"createTime",width:"160"})):s("",!0),n(p,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width custom-action-column",fixed:"right"},{default:a(t=>[n(c,{link:"",type:"primary",onClick:b=>E(t.row,"view")},{default:a(()=>e[11]||(e[11]=[h("查看")])),_:2,__:[11]},1032,["onClick"]),!t.row.approvalStatus||t.row.approvalStatus==="rejected"?(l(),r(c,{key:0,link:"",type:"primary",onClick:b=>E(t.row,"edit")},{default:a(()=>e[12]||(e[12]=[h("编辑")])),_:2,__:[12]},1032,["onClick"])):s("",!0),t.row.isCompanion!==1&&t.row.approvalStatus!=="approved"?(l(),r(c,{key:1,link:"",type:"primary",onClick:b=>de(t.row)},{default:a(()=>e[13]||(e[13]=[h("删除")])),_:2,__:[13]},1032,["onClick"])):s("",!0)]),_:1})]),_:1},8,["data"])),[[ge,V.value],[we,u.load]]),P(n(be,{total:R.value,page:i(o).pageNo,"onUpdate:page":e[4]||(e[4]=t=>i(o).pageNo=t),limit:i(o).pageSize,"onUpdate:limit":e[5]||(e[5]=t=>i(o).pageSize=t),onPagination:S},null,8,["total","page","limit"]),[[X,R.value>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),N.value?(l(),r(De,{key:0,getList:S,title:I.value,open:N.value,"onUpdate:open":e[6]||(e[6]=t=>N.value=t),editRecord:U.value,type:x.value},null,8,["title","open","editRecord","type"])):s("",!0)])}}}),Xe=_e(Ae,[["__scopeId","data-v-c448d9d5"]]);export{Xe as default};
