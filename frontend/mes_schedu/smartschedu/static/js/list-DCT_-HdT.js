import{l as z,a as G,c as J,b as M,d as P,e as W}from"./cache-Crrih8In.js";import{B as X,d as Y,r as c,e as s,Q as Z,c as ee,o as b,f as t,i as l,R as B,j as S,l as n,n as f,h as k}from"./index-BZGe1FpZ.js";const te={class:"app-container"},le=X({name:"CacheList"}),se=Object.assign(le,{setup(ae){const{proxy:u}=Y(),N=c([]),V=c([]),i=c({}),g=c(!0),_=c(!1),p=c(""),x=c(window.innerHeight-200);function K(){g.value=!0,z().then(a=>{N.value=a.data,g.value=!1})}function O(){K(),u.$modal.msgSuccess("刷新缓存列表成功")}function F(a){J(a.cacheName).then(e=>{u.$modal.msgSuccess("清理缓存名称["+a.cacheName+"]成功"),h()})}function h(a){const e=a!==void 0?a.cacheName:p.value;e!==""&&(_.value=!0,G(e).then(y=>{V.value=y.data,_.value=!1,p.value=e}))}function U(){h(),u.$modal.msgSuccess("刷新键名列表成功")}function j(a){P(a).then(e=>{u.$modal.msgSuccess("清理缓存键名["+a+"]成功"),h()})}function A(a){return a.cacheName.replace(":","")}function H(a){return a.replace(p.value,"")}function L(a){M(p.value,a).then(e=>{i.value=e.data})}function E(){W().then(a=>{u.$modal.msgSuccess("清理全部缓存成功")})}return K(),(a,e)=>{const y=s("Collection"),m=s("el-button"),r=s("el-table-column"),$=s("el-table"),v=s("el-card"),d=s("el-col"),I=s("Key"),Q=s("Document"),C=s("el-input"),w=s("el-form-item"),D=s("el-row"),T=s("el-form"),R=Z("loading");return b(),ee("div",te,[t(D,{gutter:10},{default:l(()=>[t(d,{span:8},{default:l(()=>[t(v,{style:{height:"calc(100vh - 125px)"}},{header:l(()=>[t(y,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[6]||(e[6]=f()),e[7]||(e[7]=k("span",{style:{"vertical-align":"middle"}},"缓存列表",-1)),t(m,{style:{float:"right",padding:"3px 0"},link:"",type:"primary",icon:"Refresh",onClick:e[0]||(e[0]=o=>O())})]),default:l(()=>[B((b(),S($,{data:n(N),height:n(x),"highlight-current-row":"",onRowClick:h,style:{width:"100%"}},{default:l(()=>[t(r,{label:"序号",width:"60",type:"index"}),t(r,{label:"缓存名称",align:"center",prop:"cacheName","show-overflow-tooltip":!0,formatter:A}),t(r,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),t(r,{label:"操作",width:"60",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[t(m,{link:"",type:"primary",icon:"Delete",onClick:q=>F(o.row)},null,8,["onClick"])]),_:1})]),_:1},8,["data","height"])),[[R,n(g)]])]),_:1})]),_:1}),t(d,{span:8},{default:l(()=>[t(v,{style:{height:"calc(100vh - 125px)"}},{header:l(()=>[t(I,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[8]||(e[8]=f()),e[9]||(e[9]=k("span",{style:{"vertical-align":"middle"}},"键名列表",-1)),t(m,{style:{float:"right",padding:"3px 0"},link:"",type:"primary",icon:"Refresh",onClick:e[1]||(e[1]=o=>U())})]),default:l(()=>[B((b(),S($,{data:n(V),height:n(x),"highlight-current-row":"",onRowClick:L,style:{width:"100%"}},{default:l(()=>[t(r,{label:"序号",width:"60",type:"index"}),t(r,{label:"缓存键名",align:"center","show-overflow-tooltip":!0,formatter:H}),t(r,{label:"操作",width:"60",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[t(m,{link:"",type:"primary",icon:"Delete",onClick:q=>j(o.row)},null,8,["onClick"])]),_:1})]),_:1},8,["data","height"])),[[R,n(_)]])]),_:1})]),_:1}),t(d,{span:8},{default:l(()=>[t(v,{bordered:!1,style:{height:"calc(100vh - 125px)"}},{header:l(()=>[t(Q,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),e[11]||(e[11]=f()),e[12]||(e[12]=k("span",{style:{"vertical-align":"middle"}},"缓存内容",-1)),t(m,{style:{float:"right",padding:"3px 0"},link:"",type:"primary",icon:"Refresh",onClick:e[2]||(e[2]=o=>E())},{default:l(()=>e[10]||(e[10]=[f("清理全部")])),_:1,__:[10]})]),default:l(()=>[t(T,{model:n(i)},{default:l(()=>[t(D,{gutter:32},{default:l(()=>[t(d,{offset:1,span:22},{default:l(()=>[t(w,{label:"缓存名称:",prop:"cacheName"},{default:l(()=>[t(C,{modelValue:n(i).cacheName,"onUpdate:modelValue":e[3]||(e[3]=o=>n(i).cacheName=o),readOnly:!0},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{offset:1,span:22},{default:l(()=>[t(w,{label:"缓存键名:",prop:"cacheKey"},{default:l(()=>[t(C,{modelValue:n(i).cacheKey,"onUpdate:modelValue":e[4]||(e[4]=o=>n(i).cacheKey=o),readOnly:!0},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{offset:1,span:22},{default:l(()=>[t(w,{label:"缓存内容:",prop:"cacheValue"},{default:l(()=>[t(C,{modelValue:n(i).cacheValue,"onUpdate:modelValue":e[5]||(e[5]=o=>n(i).cacheValue=o),type:"textarea",rows:8,readOnly:!0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1})])}}});export{se as default};
