import{f as D,g as I}from"./optionsData-BhtT3Fxt.js";import{i as E}from"./task-Bw3yL6aD.js";import{_ as G,B as H,d as L,r as k,N as U,T as J,w as K,D as Q,e as b,j as R,o as r,i as l,h as e,c as S,k as g,f as s,n as c,t as o,G as W,H as X,l as m}from"./index-BZGe1FpZ.js";const Y={class:"detail-container"},Z={key:0,class:"table-title"},$={class:"detail-item"},ee={class:"detail-value"},te={class:"detail-item"},ae={class:"detail-value"},le={class:"detail-item"},se={class:"detail-value"},de={class:"detail-item"},ne={class:"detail-value"},oe={class:"detail-item"},ie={class:"detail-value"},ce={class:"detail-item"},ue={class:"detail-value"},pe={class:"detail-item"},re={class:"detail-value"},fe={class:"detail-item"},ve={class:"detail-value"},_e={class:"detail-item"},ye={class:"detail-value"},be={class:"detail-item"},Re={class:"detail-value"},ge={class:"detail-item"},me={class:"detail-value"},Ne={class:"detail-item"},ke={class:"detail-value"},Se={key:2,class:"table-title"},he={class:"detail-item"},Ce={class:"detail-value"},Te={class:"detail-item"},xe={class:"detail-value"},we={class:"detail-item"},De={class:"detail-value"},Ie={class:"detail-item"},Be={class:"detail-value"},Fe={class:"detail-item"},je={class:"detail-value"},qe={class:"detail-item"},Me={class:"detail-value"},Pe={class:"detail-item"},Ve={class:"detail-value"},Oe={class:"detail-item"},Ae={class:"detail-value"},ze={class:"dialog-footer"},Ee=H({name:"User"}),Ge=Object.assign(Ee,{props:{open:{type:Boolean,default:!1},getList:{type:Function,default:()=>{}},handleManuleDispatch:{type:Function,default:()=>{}},handleDispatch:{type:Function,default:()=>{}},title:{type:String,default:""},type:{type:String,default:""},detailRecord:{type:Object,default:()=>{}},ids:{type:Array,default:()=>[]}},emits:["update:open"],setup(d,{emit:B}){const{proxy:F}=L(),j=B,u=d,h=k(!1),q=k(!0),M=U({form:{approvalStatus:"approved"},queryParams:{pageNo:1,pageSize:10,cityCode:void 0,siteName:void 0,siteId:void 0,businessType:"water",siteType:void 0,activitySubtype:void 0,activityMajorType:void 0},rules:{approvalStatus:[{required:!0,message:"请选择审批状态",trigger:"change"}],approvalOpinion:[{required:!1,message:"请输入审批意见",trigger:"blur"}]}}),{queryParams:He,form:C,rules:Le}=J(M),N=k({});K(()=>u.detailRecord,(v,t)=>{v&&(T(),Q(()=>{C.value={...v,approvalStatus:"approved",isInit:!0}}))});async function T(){var t,i,n;if(!u.detailRecord.id)return;const v=await E({id:u.detailRecord.id});if(v.code===200){const p=(t=v.data)==null?void 0:t.extendInfos;N.value={person:(i=p==null?void 0:p.map(f=>f.executorName))==null?void 0:i.join("，"),phone:(n=p==null?void 0:p.map(f=>f.phone))==null?void 0:n.join("，")}}}function P(){C.value={provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,siteName:void 0,siteId:void 0,siteType:void 0,businessType:"water",activityType:void 0,activitySubtype:void 0,targetValue:void 0},h.value=!1,q.value=!0,F.resetForm("siteRef")}function x(){P(),j("update:open",!1)}function V(){u==null||u.handleManuleDispatch(u==null?void 0:u.detailRecord)}function O(){u==null||u.handleDispatch(u==null?void 0:u.detailRecord)}return T(),(v,t)=>{const i=b("el-tooltip"),n=b("el-col"),p=b("el-row"),f=b("el-button"),A=b("el-dialog");return r(),R(A,{title:"任务详情","model-value":d.open,"align-center":"",width:"710px","append-to-body":"",onClose:x},{footer:l(()=>[e("div",ze,[s(f,{onClick:x},{default:l(()=>t[20]||(t[20]=[c("取 消")])),_:1,__:[20]}),d.detailRecord.approvalStatus==="approved"&&d.detailRecord.taskStatus!=="2"?(r(),R(f,{key:0,type:"primary",loading:h.value,onClick:O},{default:l(()=>t[21]||(t[21]=[c("推送活动系统")])),_:1,__:[21]},8,["loading"])):g("",!0),d.detailRecord.taskStatus==="1"?(r(),R(f,{key:1,type:"primary",onClick:V},{default:l(()=>t[22]||(t[22]=[c("调整")])),_:1,__:[22]})):g("",!0)])]),default:l(()=>{var w;return[e("div",Y,[d.detailRecord.id?(r(),S("div",Z,"任务信息")):g("",!0),d.detailRecord.id?(r(),R(p,{key:1,gutter:20,class:"detail-row"},{default:l(()=>[s(n,{span:12},{default:l(()=>[e("div",$,[t[0]||(t[0]=e("label",{class:"detail-label"},"任务名称：",-1)),e("span",ee,[s(i,{content:d.detailRecord.taskName||"-",placement:"top"},{default:l(()=>[c(o(d.detailRecord.taskName||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",te,[t[1]||(t[1]=e("label",{class:"detail-label"},"任务编码：",-1)),e("span",ae,[s(i,{content:d.detailRecord.taskCode||"-",placement:"top"},{default:l(()=>[c(o(d.detailRecord.taskCode||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",le,[t[2]||(t[2]=e("label",{class:"detail-label"},"省份：",-1)),e("span",se,o(d.detailRecord.provinceName||"-"),1)])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",de,[t[3]||(t[3]=e("label",{class:"detail-label"},"地市：",-1)),e("span",ne,o(d.detailRecord.cityName||"-"),1)])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",oe,[t[4]||(t[4]=e("label",{class:"detail-label"},"任务执行人：",-1)),e("span",ie,o(N.value.person||"-"),1)])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",ce,[t[5]||(t[5]=e("label",{class:"detail-label"},"任务派发时间：",-1)),e("span",ue,[s(i,{content:d.detailRecord.dispatchedTime||"-",placement:"top"},{default:l(()=>[c(o(d.detailRecord.dispatchedTime||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",pe,[t[6]||(t[6]=e("label",{class:"detail-label"},"联系方式：",-1)),e("span",re,o(N.value.phone||"-"),1)])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",fe,[t[7]||(t[7]=e("label",{class:"detail-label"},"任务开始时间：",-1)),e("span",ve,[s(i,{content:d.detailRecord.startTime||"-",placement:"top"},{default:l(()=>[c(o(d.detailRecord.startTime||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",_e,[t[8]||(t[8]=e("label",{class:"detail-label"},"运维单位：",-1)),e("span",ye,[s(i,{content:d.detailRecord.activitySubtypeName||"-",placement:"top"},{default:l(()=>[c(o(d.detailRecord.activitySubtypeName||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",be,[t[9]||(t[9]=e("label",{class:"detail-label"},"任务结束时间：",-1)),e("span",Re,o(d.detailRecord.isCompanion||"-"),1)])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",ge,[t[10]||(t[10]=e("label",{class:"detail-label"},"交割地址：",-1)),e("span",me,[s(i,{content:d.detailRecord.companionRule||"-",placement:"topLeft"},{default:l(()=>[c(o(d.detailRecord.companionRule||"-"),1)]),_:1},8,["content"])])])]),_:1}),s(n,{span:12},{default:l(()=>[e("div",Ne,[t[11]||(t[11]=e("label",{class:"detail-label"},"送样地点：",-1)),e("span",ke,[s(i,{content:d.detailRecord.companionRule||"-",placement:"topLeft"},{default:l(()=>[c(o(d.detailRecord.companionRule||"-"),1)]),_:1},8,["content"])])])]),_:1})]),_:1})):g("",!0),d.detailRecord.id?(r(),S("div",Se,"计划信息")):g("",!0),(r(!0),S(W,null,X((w=d.detailRecord)==null?void 0:w.planInfos,(a,z)=>(r(),R(p,{key:z,gutter:20,class:"detail-row"},{default:l(()=>[s(n,{span:12},{default:l(()=>[e("div",he,[t[12]||(t[12]=e("label",{class:"detail-label"},"计划名称：",-1)),e("span",Ce,[s(i,{content:(a==null?void 0:a.planName)||"-",placement:"top"},{default:l(()=>[c(o((a==null?void 0:a.planName)||"-"),1)]),_:2},1032,["content"])])])]),_:2},1024),s(n,{span:12},{default:l(()=>[e("div",Te,[t[13]||(t[13]=e("label",{class:"detail-label"},"计划编码：",-1)),e("span",xe,[s(i,{content:(a==null?void 0:a.planCode)||"-",placement:"top"},{default:l(()=>[c(o((a==null?void 0:a.planCode)||"-"),1)]),_:2},1032,["content"])])])]),_:2},1024),s(n,{span:12},{default:l(()=>{var _;return[e("div",we,[t[14]||(t[14]=e("label",{class:"detail-label"},"计划优先级：",-1)),e("span",De,[s(i,{content:((_=m(D)[a==null?void 0:a.planPriority])==null?void 0:_.text)||"-",placement:"top"},{default:l(()=>{var y;return[c(o(((y=m(D)[a==null?void 0:a.planPriority])==null?void 0:y.text)||"-"),1)]}),_:2},1032,["content"])])])]}),_:2},1024),s(n,{span:12},{default:l(()=>{var _;return[e("div",Ie,[t[15]||(t[15]=e("label",{class:"detail-label"},"计划来源：",-1)),e("span",Be,[s(i,{content:((_=m(I)[a==null?void 0:a.planSource])==null?void 0:_.text)||"-",placement:"top"},{default:l(()=>{var y;return[c(o(((y=m(I)[a==null?void 0:a.planSource])==null?void 0:y.text)||"-"),1)]}),_:2},1032,["content"])])])]}),_:2},1024),s(n,{span:12},{default:l(()=>[e("div",Fe,[t[16]||(t[16]=e("label",{class:"detail-label"},"站点类型：",-1)),e("span",je,o((a==null?void 0:a.siteTypeName)||"-"),1)])]),_:2},1024),s(n,{span:12},{default:l(()=>[e("div",qe,[t[17]||(t[17]=e("label",{class:"detail-label"},"监测活动大类：",-1)),e("span",Me,[s(i,{content:(a==null?void 0:a.activityTypeName)||"-",placement:"top"},{default:l(()=>[c(o((a==null?void 0:a.activityTypeName)||"-"),1)]),_:2},1032,["content"])])])]),_:2},1024),s(n,{span:12},{default:l(()=>[e("div",Pe,[t[18]||(t[18]=e("label",{class:"detail-label"},"站点名称：",-1)),e("span",Ve,[s(i,{content:(a==null?void 0:a.siteName)||"-",placement:"top"},{default:l(()=>[c(o((a==null?void 0:a.siteName)||"-"),1)]),_:2},1032,["content"])])])]),_:2},1024),s(n,{span:12},{default:l(()=>[e("div",Oe,[t[19]||(t[19]=e("label",{class:"detail-label"},"监测活动小类：",-1)),e("span",Ae,[s(i,{content:(a==null?void 0:a.activitySubtypeName)||"-",placement:"top"},{default:l(()=>[c(o((a==null?void 0:a.activitySubtypeName)||"-"),1)]),_:2},1032,["content"])])])]),_:2},1024)]),_:2},1024))),128))])]}),_:1},8,["model-value"])}}}),Qe=G(Ge,[["__scopeId","data-v-72352883"]]);export{Qe as default};
