function r(t,n){const a=Object.create(null),s=t.split(",");for(let e=0;e<s.length;e++)a[s[e]]=!0;return n?e=>a[e.toLowerCase()]:e=>a[e]}const i={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function l(t){return t.replace(/( |^)[a-z]/g,n=>n.toUpperCase())}function o(t){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(t)}function d(t,n="download"){const a=new Blob([t]),s=window.URL.createObjectURL(a),e=document.createElement("a");e.style.display="none",e.href=s,e.setAttribute("download",n),document.body.appendChild(e),e.click(),document.body.removeChild(e)}export{i as b,d,o as i,r as m,l as t};
