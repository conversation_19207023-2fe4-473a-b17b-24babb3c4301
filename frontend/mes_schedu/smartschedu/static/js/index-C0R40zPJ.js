import{_ as M,B as W,r,d as X,e as i,Q as h,c as N,o as p,f as e,i as a,R as c,m as V,G as Y,H as Z,l as $,j as y,n as b,S as D,h as ee}from"./index-BZGe1FpZ.js";import te from"./addForm-yGYnIzJk.js";import{b as le,c as oe}from"./algorithm-Ex_U_8wD.js";import{h as ae}from"./optionsData-BhtT3Fxt.js";import{d as ne}from"./index-CUycEg5u.js";import"./plan-KEsYJfaJ.js";import"./common-BS8XB_Gq.js";import"./commonForm-D1gG5yoU.js";const re={class:"app-container"},ie=W({name:"User"}),se=Object.assign(ie,{setup(ue){const l=r({pageNo:1,pageSize:10,algorithmName:void 0,algorithmCode:void 0,status:void 0}),{proxy:S}=X(),w=r([]),k=r(!1),v=r(!0),R=r(!0),U=r([]),L=r(!0),q=r(!0),_=r(0),B=r(""),F=r(null),P=r("");function d(){v.value=!0,le({...l.value}).then(n=>{v.value=!1,w.value=n.data.data,_.value=n.data.totalRecords})}function T(){oe({algorithmCode:l.value.algorithmCode,algorithmName:l.value.algorithmName,invokeDate:l.value.invokeDate}).then(n=>{n&&ne(n,"算法调用执行记录.xlsx")})}function m(){l.value.pageNo=1,d()}function j(){S.resetForm("queryRef"),m()}function z(n){U.value=n.map(t=>t.userId),L.value=n.length!=1,q.value=!n.length}return d(),(n,t)=>{const C=i("el-input"),u=i("el-form-item"),I=i("el-option"),K=i("el-select"),Q=i("el-date-picker"),g=i("el-button"),A=i("el-form"),f=i("el-col"),x=i("el-row"),s=i("el-table-column"),E=i("el-table"),G=i("pagination"),H=h("hasPermi"),O=h("loading"),J=h("el-table-infinite-scroll");return p(),N("div",re,[e(x,{gutter:20},{default:a(()=>[e(f,null,{default:a(()=>[c(e(A,{model:l.value,ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:a(()=>[e(u,{label:"算法名称",prop:"algorithmName"},{default:a(()=>[e(C,{modelValue:l.value.algorithmName,"onUpdate:modelValue":t[0]||(t[0]=o=>l.value.algorithmName=o),placeholder:"请输入算法名称",clearable:"",style:{width:"240px"},onKeyup:V(m,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"算法编码",prop:"algorithmCode"},{default:a(()=>[e(C,{modelValue:l.value.algorithmCode,"onUpdate:modelValue":t[1]||(t[1]=o=>l.value.algorithmCode=o),placeholder:"请输入算法编码",clearable:"",style:{width:"240px"},onKeyup:V(m,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"状态",prop:"status"},{default:a(()=>[e(K,{modelValue:l.value.status,"onUpdate:modelValue":t[2]||(t[2]=o=>l.value.status=o),placeholder:"请选择状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(p(!0),N(Y,null,Z($(ae),o=>(p(),y(I,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"调用日期",prop:"invokeDate"},{default:a(()=>[e(Q,{type:"daterange",modelValue:l.value.invokeDate,"onUpdate:modelValue":t[3]||(t[3]=o=>l.value.invokeDate=o),placeholder:"选择调用日期",style:{width:"240px"},"start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:n.shortcuts},null,8,["modelValue","shortcuts"])]),_:1}),e(u,{class:"form-btn"},{default:a(()=>[e(g,{type:"primary",icon:"Search",onClick:m},{default:a(()=>t[7]||(t[7]=[b("搜索")])),_:1,__:[7]}),e(g,{icon:"Refresh",onClick:j},{default:a(()=>t[8]||(t[8]=[b("重置")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"]),[[D,R.value]]),e(x,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:a(()=>[e(f,{span:12},{default:a(()=>t[9]||(t[9]=[ee("div",{style:{width:"100%"},class:"table-title"},"算法调用情况列表",-1)])),_:1,__:[9]}),e(f,{span:12,style:{"text-align":"right"}},{default:a(()=>[c((p(),y(g,{type:"primary",icon:"Download",onClick:T},{default:a(()=>t[10]||(t[10]=[b("导 出")])),_:1,__:[10]})),[[H,["system:user:add"]]])]),_:1})]),_:1}),c((p(),y(E,{data:w.value,onSelectionChange:z,"max-height":"calc(100vh - 400px)",style:{width:"100%"}},{default:a(()=>[e(s,{label:"序号",align:"center",type:"index",width:"50",fixed:""}),e(s,{label:"算法编码",align:"center",key:"algorithmCode",prop:"algorithmCode","show-overflow-tooltip":!0}),e(s,{label:"算法名称",align:"center",key:"algorithmName",prop:"algorithmName","show-overflow-tooltip":!0}),e(s,{label:"调用日期",align:"center",key:"invokeDate",prop:"invokeDate","show-overflow-tooltip":!0,width:"180"}),e(s,{label:"调用时间",align:"center",key:"invokeTime",prop:"invokeTime","show-overflow-tooltip":!0,width:"180"}),e(s,{label:"处理时长",align:"center",key:"avgDuration",prop:"avgDuration","show-overflow-tooltip":!0}),e(s,{label:"处理计划数",align:"center",key:"planCount",prop:"planCount"}),e(s,{label:"生成任务数",align:"center",key:"taskCount",prop:"taskCount"}),e(s,{label:"状态",align:"center",key:"status",prop:"status"})]),_:1},8,["data"])),[[O,v.value],[J,n.load]]),c(e(G,{total:_.value,page:l.value.pageNo,"onUpdate:page":t[4]||(t[4]=o=>l.value.pageNo=o),limit:l.value.pageSize,"onUpdate:limit":t[5]||(t[5]=o=>l.value.pageSize=o),onPagination:d},null,8,["total","page","limit"]),[[D,_.value>0]])]),_:1})]),_:1}),e(te,{getList:d,title:B.value,open:k.value,"onUpdate:open":t[6]||(t[6]=o=>k.value=o),editRecord:F.value,type:P.value},null,8,["title","open","editRecord","type"])])}}}),he=M(se,[["__scopeId","data-v-47a4ab1f"]]);export{he as default};
