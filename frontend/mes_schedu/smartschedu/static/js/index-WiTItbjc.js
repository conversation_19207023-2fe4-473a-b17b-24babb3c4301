import{_ as Z,B as ee,r as n,d as te,e as r,Q as x,c as I,o as m,f as t,i as a,R as _,m as q,G as le,H as ae,l as oe,j as N,n as c,S as D,h as ne}from"./index-BZGe1FpZ.js";import ie from"./addForm-CUDB8pDT.js";import{a as re,f as ue,g as se}from"./algorithm-Ex_U_8wD.js";import{h as de}from"./optionsData-BhtT3Fxt.js";const pe={class:"app-container"},me=ee({name:"User"}),ce=Object.assign(me,{setup(ge){const i=n({pageNo:1,pageSize:10,algorithmName:void 0,algorithmCode:void 0,status:void 0}),{proxy:v}=te(),V=n([{}]),g=n(!1),h=n(!0),L=n(!0),R=n([]),P=n(!0),$=n(!0),y=n(0),b=n(""),A=n([]),U=n(null),w=n("");n([]);function s(){h.value=!0,re({...i.value}).then(o=>{var e;h.value=!1,V.value=(e=o.data)==null?void 0:e.data,y.value=o.data.totalRecords})}function f(){i.value.pageNo=1,s()}function T(){A.value=[],v.resetForm("queryRef"),f()}function j(o){const e=o.id||R.value;v.$modal.confirm('是否确认删除编号为"'+e+'"的数据项？').then(function(){return se({id:e})}).then(()=>{s(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function z(o,e){w.value=e,U.value={...o},b.value="编辑调度算法",g.value=!0}function F(o,e){!("algStatus"in e)||o===e.algStatus||ue({...e,algStatus:o?1:0}).then(C=>{s()})}function K(o){R.value=o.map(e=>e.userId),P.value=o.length!=1,$.value=!o.length}function Q(){g.value=!0,w.value="add",b.value="新增算法"}return s(),(o,e)=>{const C=r("el-input"),d=r("el-form-item"),E=r("el-option"),G=r("el-select"),p=r("el-button"),H=r("el-form"),k=r("el-col"),B=r("el-row"),u=r("el-table-column"),O=r("el-switch"),J=r("el-table"),M=r("pagination"),W=x("hasPermi"),X=x("loading"),Y=x("el-table-infinite-scroll");return m(),I("div",pe,[t(B,{gutter:20},{default:a(()=>[t(k,null,{default:a(()=>[_(t(H,{model:i.value,ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:a(()=>[t(d,{label:"算法名称",prop:"algorithmName"},{default:a(()=>[t(C,{modelValue:i.value.algorithmName,"onUpdate:modelValue":e[0]||(e[0]=l=>i.value.algorithmName=l),placeholder:"请输入算法名称",clearable:"",style:{width:"240px"},onKeyup:q(f,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"算法编码",prop:"algorithmCode"},{default:a(()=>[t(C,{modelValue:i.value.algorithmCode,"onUpdate:modelValue":e[1]||(e[1]=l=>i.value.algorithmCode=l),placeholder:"请输入算法编码",clearable:"",style:{width:"240px"},onKeyup:q(f,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"算法状态",prop:"algStatus"},{default:a(()=>[t(G,{modelValue:i.value.algStatus,"onUpdate:modelValue":e[2]||(e[2]=l=>i.value.algStatus=l),placeholder:"请选择算法状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(m(!0),I(le,null,ae(oe(de),l=>(m(),N(E,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d),t(d),t(d,{class:"form-btn"},{default:a(()=>[t(p,{type:"primary",icon:"Search",onClick:f},{default:a(()=>e[6]||(e[6]=[c("搜索")])),_:1,__:[6]}),t(p,{icon:"Refresh",onClick:T},{default:a(()=>e[7]||(e[7]=[c("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"]),[[D,L.value]]),t(B,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:a(()=>[t(k,{span:12},{default:a(()=>e[8]||(e[8]=[ne("div",{style:{width:"100%"},class:"table-title"},"调度算法列表",-1)])),_:1,__:[8]}),t(k,{span:12,style:{"text-align":"right"}},{default:a(()=>[_((m(),N(p,{type:"primary",icon:"Plus",onClick:Q},{default:a(()=>e[9]||(e[9]=[c("新增算法")])),_:1,__:[9]})),[[W,["system:user:add"]]])]),_:1})]),_:1}),_((m(),N(J,{data:V.value,onSelectionChange:K,"max-height":"calc(100vh - 400px)",style:{width:"100%"}},{default:a(()=>[t(u,{label:"序号",align:"center",type:"index",width:"50",fixed:""}),t(u,{label:"算法编码",align:"center",key:"algorithmCode",prop:"algorithmCode","show-overflow-tooltip":!0}),t(u,{label:"算法名称",align:"center",key:"algorithmName",prop:"algorithmName","show-overflow-tooltip":!0}),t(u,{label:"调用时间",align:"center",key:"algCronRule",prop:"algCronRule","show-overflow-tooltip":!0,width:"180"}),t(u,{label:"算法描述",align:"center",key:"algDesc",prop:"algDesc","show-overflow-tooltip":!0,width:"220"}),t(u,{label:"启用状态",align:"center"},{default:a(l=>[t(O,{"model-value":l.row.algStatus,"active-value":1,"inactive-value":0,onChange:S=>F(S,l.row)},null,8,["model-value","onChange"])]),_:1}),t(u,{label:"创建时间",align:"center",key:"createTime",prop:"createTime"}),t(u,{label:"创建人",align:"center",key:"createBy",prop:"createBy"}),t(u,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width custom-action-column",fixed:"right"},{default:a(l=>[t(p,{link:"",type:"primary",onClick:S=>z(l.row,"edit")},{default:a(()=>e[10]||(e[10]=[c("编辑")])),_:2,__:[10]},1032,["onClick"]),t(p,{link:"",type:"primary",onClick:S=>j(l.row)},{default:a(()=>e[11]||(e[11]=[c("删除")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,h.value],[Y,o.load]]),_(t(M,{total:y.value,page:i.value.pageNo,"onUpdate:page":e[3]||(e[3]=l=>i.value.pageNo=l),limit:i.value.pageSize,"onUpdate:limit":e[4]||(e[4]=l=>i.value.pageSize=l),onPagination:s},null,8,["total","page","limit"]),[[D,y.value>0]])]),_:1})]),_:1}),t(ie,{getList:s,title:b.value,open:g.value,"onUpdate:open":e[5]||(e[5]=l=>g.value=l),editRecord:U.value,type:w.value},null,8,["title","open","editRecord","type"])])}}}),ye=Z(ce,[["__scopeId","data-v-72acda6b"]]);export{ye as default};
