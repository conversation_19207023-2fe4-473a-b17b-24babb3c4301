import{_ as fe,B as ve,r as d,a2 as ye,d as _e,N as be,T as ge,w as O,K as we,e as y,Q as $,c as E,o,f as n,j as s,k as c,i,l as a,R as q,P as A,G as he,H as ke,n as b,S as G,h as p,t as m}from"./index-BZGe1FpZ.js";import{q as xe,d as Re}from"./target-CQgpzWbZ.js";import Ne from"./addForm-B5_Lbhc7.js";import{M as Te,g as K}from"./splitpanes.es-nC04_w-q.js";/* empty css                   */import qe from"./areaTree-BxzGSRqT.js";import Ce from"./commonFormSearch-L73kV2iA.js";import"./commonForm-D1gG5yoU.js";import"./common-BS8XB_Gq.js";const Se={class:"app-container"},Ie={class:"percent right"},Pe={class:"percent left"},Ye={class:"percent right"},Ve={class:"percent left"},De={class:"percent right"},Ue={class:"percent left"},je={class:"percent right"},ze={class:"percent left"},$e={class:"percent right"},Ae={class:"percent left"},Fe=ve({name:"qualityTarget"}),Le=Object.assign(Fe,{setup(Qe,{emit:Be}){const R=d([]),J=ye(),{proxy:h}=_e(),F=d([]),N=d(!1),Y=d(!0),W=d(!0),X=d(!0),Z=d(!0),V=d(0),D=d(""),g=d([]),U=d(null),L=d([]),Q=d([]),S=d([]),I=d([]),f=d([{key:0,label:"用户编号",visible:!0},{key:1,label:"省",visible:!0},{key:2,label:"市",visible:!0},{key:3,label:"站点名称",visible:!0},{key:4,label:"站点类型",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"业务分类",visible:!0},{key:7,label:"监测参数",visible:!0},{key:8,label:"偏离目标",visible:!0},{key:9,label:"当前偏离",visible:!0},{key:11,label:"创建人",visible:!0},{key:12,label:"创建时间",visible:!0},{key:13,label:"修改时间",visible:!0}]),ee=be({form:{},queryParams:{pageNo:1,pageSize:10,cityCode:void 0,siteName:void 0,businessType:"water",siteType:void 0,activitySubtype:void 0,monitIndex:void 0}}),{queryParams:r,form:te}=ge(ee);O([()=>S.value,()=>I.value],([l,e])=>{R.value.length===0&&l.length>0&&e.length>0&&(R.value=[...l,...e])}),O([()=>r.value.businessType],([l],[e])=>{l!==e&&(r.value.monitIndex="",k(),l!==e&&(l?R.value=l==="water"?S.value:I.value:R.value=[...S.value,...I.value]))},{deep:!0});function _(l){return l?(l*100).toFixed(2)+"%":"0%"}async function ae(){const l=await h.useBusDict("water_quality_index","gas_quality_index");R.value=[...l==null?void 0:l.water_quality_index.value,...l==null?void 0:l.gas_quality_index.value],S.value=l==null?void 0:l.water_quality_index.value,I.value=l==null?void 0:l.gas_quality_index.value}const le=(l,e)=>{var x,C,w,P;const T=(C=(x=e.checkedNodes)==null?void 0:x.filter(v=>v.type==="city"))==null?void 0:C.map(v=>v.id),j=(P=(w=e.checkedNodes)==null?void 0:w.filter(v=>v.type==="site"))==null?void 0:P.map(v=>v.id);Q.value=j,L.value=T,k()};function k(){var l,e;Y.value=!0,xe({...r.value,cityCode:(l=L.value)==null?void 0:l.join(","),siteId:(e=Q.value)==null?void 0:e.join(","),startTime:g.value[0]?h.parseTime(g.value[0]):"",endTime:g.value[1]?h.parseTime(we(g.value[1]).endOf("day").toDate()):""}).then(T=>{Y.value=!1,F.value=T.data.data,V.value=T.data.totalRecords})}function B(){r.value.pageNo=1,k()}function ie(){g.value=[],h.resetForm("queryRef"),B()}function ne(l){const e=l.id||ids.value;h.$modal.confirm("是否确认删除该条数据项？").then(function(){return Re(e)}).then(()=>{k(),h.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(l){ids.value=l.map(e=>e.userId),W.value=l.length!=1,X.value=!l.length}function M(){te.value={provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,siteName:void 0,siteId:void 0,siteType:void 0,businessType:"water",monitIndex:void 0,activitySubtype:void 0,qualityTarget:void 0},U.value={},h.resetForm("siteRef")}function se(){M(),N.value=!0,D.value="新增质量目标"}function re(l){M(),U.value={...l},N.value=!0,D.value="修改质量目标"}return ae(),k(),(l,e)=>{const T=y("el-option"),j=y("el-select"),x=y("el-form-item"),C=y("el-date-picker"),w=y("el-button"),P=y("el-form"),v=y("el-col"),H=y("el-row"),u=y("el-table-column"),ue=y("el-table"),de=y("pagination"),z=$("hasPermi"),ce=$("loading"),pe=$("el-table-infinite-scroll");return o(),E("div",Se,[n(H,{gutter:20},{default:i(()=>[n(a(Te),{horizontal:a(J).device==="mobile",class:"default-theme"},{default:i(()=>[n(a(K),{size:"16"},{default:i(()=>[n(qe,{onHandleCheck:le})]),_:1}),n(a(K),{class:"table-container",size:"84"},{default:i(()=>[n(v,null,{default:i(()=>[q(n(P,{model:a(r),ref:"queryRef",inline:!0,"label-width":"100px",class:"query-form"},{default:i(()=>[n(a(Ce),{needActivity:!1,queryParams:a(r),"onUpdate:queryParams":e[0]||(e[0]=t=>A(r)?r.value=t:null)},null,8,["queryParams"]),n(x,{label:"监测参数",prop:"monitIndex"},{default:i(()=>[n(j,{modelValue:a(r).monitIndex,"onUpdate:modelValue":e[1]||(e[1]=t=>a(r).monitIndex=t),placeholder:"监测参数",clearable:"",style:{width:"240px"}},{default:i(()=>[(o(!0),E(he,null,ke(a(R),t=>(o(),s(T,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(x,{label:"创建时间"},{default:i(()=>[n(C,{modelValue:a(g),"onUpdate:modelValue":e[2]||(e[2]=t=>A(g)?g.value=t:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),n(x,{label:"年度",prop:"statYear"},{default:i(()=>[n(C,{modelValue:a(r).statYear,"onUpdate:modelValue":e[3]||(e[3]=t=>a(r).statYear=t),type:"year",placeholder:"选择年度","value-format":"YYYY",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),n(x,{class:"form-btn"},{default:i(()=>[n(w,{type:"primary",icon:"Search",onClick:B},{default:i(()=>e[7]||(e[7]=[b("搜索")])),_:1,__:[7]}),n(w,{icon:"Refresh",onClick:ie},{default:i(()=>e[8]||(e[8]=[b("重置")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"]),[[G,a(Z)]]),n(H,{gutter:24,style:{display:"flex","justify-content":"space-between"},class:"mb8 table-header"},{default:i(()=>[n(v,{span:12},{default:i(()=>e[9]||(e[9]=[p("div",{style:{width:"100%"},class:"table-title"}," 质量目标管理列表 ",-1)])),_:1,__:[9]}),n(v,{span:12,style:{"text-align":"right"}},{default:i(()=>[q((o(),s(w,{type:"primary",icon:"Plus",onClick:se},{default:i(()=>e[10]||(e[10]=[b("新增质量目标")])),_:1,__:[10]})),[[z,["system:user:add"]]])]),_:1})]),_:1}),q((o(),s(ue,{data:a(F),style:{width:"100%"},onSelectionChange:oe,"max-height":"calc(100vh - 460px)"},{default:i(()=>[a(f)[0].visible?(o(),s(u,{key:0,label:"序号",align:"center",type:"index",width:"50",fixed:""})):c("",!0),n(u,{label:"年度",align:"center",key:"statYear",prop:"statYear",width:"120"}),a(f)[1].visible?(o(),s(u,{label:"省",align:"center",key:"provinceName",prop:"provinceName","show-overflow-tooltip":!0,width:"120"})):c("",!0),a(f)[2].visible?(o(),s(u,{label:"市",align:"center",key:"cityName",prop:"cityName","show-overflow-tooltip":!0,width:"120"})):c("",!0),a(f)[3].visible?(o(),s(u,{label:"站点名称",align:"center",key:"siteName",prop:"siteName","show-overflow-tooltip":!0,width:"120"})):c("",!0),a(f)[4].visible?(o(),s(u,{key:4,label:"站点类型",align:"center",width:"160"},{default:i(t=>[p("span",null,m(t.row.siteType==="01"&&t.row.businessType==="water"&&t.row.hasAutomaticStation==="N"?"水断面":t.row.siteTypeName)+" "+m(t.row.hasAutomaticStation==="Y"&&t.row.businessType==="water"?"(自动站)":""),1)]),_:1})):c("",!0),a(f)[5].visible?(o(),s(u,{label:"业务分类",align:"center",key:"businessTypeName",prop:"businessTypeName"})):c("",!0),a(f)[6].visible?(o(),s(u,{label:"监测参数",align:"center",key:"monitIndexName",prop:"monitIndexName",width:"190"})):c("",!0),n(u,{label:"准确度",align:"center",width:"160"},{default:i(t=>[p("div",Ie,m(_(t.row.accuracyResult)),1),e[11]||(e[11]=b(" | ")),p("div",Pe,m(_(t.row.accuracy)),1)]),_:1}),n(u,{label:"精密度",align:"center",width:"160"},{default:i(t=>[p("div",Ye,m(_(t.row.precisionResult)),1),e[12]||(e[12]=b(" | ")),p("div",Ve,m(_(t.row.precision)),1)]),_:1}),a(f)[8].visible?(o(),s(u,{label:"数据有效率",align:"center",key:"effectivenessRate",prop:"effectivenessRate",width:"160"},{default:i(t=>[p("div",De,m(_(t.row.effectivenessRateResult)),1),e[13]||(e[13]=b(" | ")),p("div",Ue,m(_(t.row.effectivenessRate)),1)]),_:1})):c("",!0),a(r).businessType==="water"?(o(),s(u,{label:"质控合格率",align:"center",key:"quactrlPassRate",prop:"quactrlPassRate",width:"160"},{default:i(t=>[p("div",je,m(_(t.row.quactrlPassRateResult)),1),e[14]||(e[14]=b(" | ")),p("div",ze,m(_(t.row.quactrlPassRate)),1)]),_:1})):c("",!0),a(r).businessType==="air"?(o(),s(u,{label:"数据获取率",align:"center",key:"captureRate",prop:"captureRate",width:"160"},{default:i(t=>[p("div",$e,m(_(t.row.captureRateResult)),1),e[15]||(e[15]=b(" | ")),p("div",Ae,m(_(t.row.captureRate)),1)]),_:1})):c("",!0),a(f)[10].visible?(o(),s(u,{label:"创建人",align:"center",key:"createBy",prop:"createBy",width:"120"})):c("",!0),a(f)[11].visible?(o(),s(u,{label:"创建时间",align:"center",key:"createTime",prop:"createTime",width:"160"})):c("",!0),a(f)[12].visible?(o(),s(u,{label:"修改时间",align:"center",key:"updateTime",prop:"updateTime",width:"160"})):c("",!0),n(u,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width",fixed:"right"},{default:i(t=>[q((o(),s(w,{link:"",type:"primary",onClick:me=>re(t.row)},{default:i(()=>e[16]||(e[16]=[b("编辑")])),_:2,__:[16]},1032,["onClick"])),[[z,["system:user:edit"]]]),q((o(),s(w,{link:"",type:"primary",onClick:me=>ne(t.row)},{default:i(()=>e[17]||(e[17]=[b("删除")])),_:2,__:[17]},1032,["onClick"])),[[z,["system:user:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,a(Y)],[pe,l.load]]),q(n(de,{total:a(V),page:a(r).pageNo,"onUpdate:page":e[4]||(e[4]=t=>a(r).pageNo=t),limit:a(r).pageSize,"onUpdate:limit":e[5]||(e[5]=t=>a(r).pageSize=t),onPagination:k},null,8,["total","page","limit"]),[[G,a(V)>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),a(N)?(o(),s(Ne,{key:0,title:a(D),onFormSubmitted:k,open:a(N),"onUpdate:open":e[6]||(e[6]=t=>A(N)?N.value=t:null),editRecord:a(U)},null,8,["title","open","editRecord"])):c("",!0)])}}}),Ze=fe(Le,[["__scopeId","data-v-4d06d059"]]);export{Ze as default};
