import{B as _e,u as ge,d as be,r as m,N as ve,T as ye,bA as he,e as r,Q as V,c as F,o as c,R as d,f as t,S as G,l,i as a,m as Q,P as Y,n as b,j as v,h as K,t as L,G as we,H as Ce}from"./index-BZGe1FpZ.js";import{a as Ne,b as ke,d as Se,p as $e,s as xe}from"./gen-CkNW6Pwe.js";import Ve from"./importTable-DVoaFNu_.js";import Re from"./createTable-Ce4EHHg5.js";const Te={class:"app-container"},De=_e({name:"Gen"}),Oe=Object.assign(De,{setup(Ie){const U=ge(),{proxy:s}=be(),q=m([]),R=m(!0),x=m(!0),T=m([]),B=m(!0),D=m(!0),I=m(0),P=m([]),h=m([]),O=m(""),N=m({prop:"createTime",order:"descending"}),M=ve({queryParams:{pageNum:1,pageSize:10,tableName:void 0,tableComment:void 0,orderByColumn:N.value.prop,isAsc:N.value.order},preview:{open:!1,title:"代码预览",data:{},activeName:"domain.java"}}),{queryParams:i,preview:f}=ye(M);he(()=>{const n=U.query.t;n!=null&&n!=O.value&&(O.value=n,i.value.pageNum=Number(U.query.pageNum),h.value=[],s.resetForm("queryForm"),y())});function y(){R.value=!0,Ne(s.addDateRange(i.value,h.value)).then(n=>{q.value=n.rows,I.value=n.total,R.value=!1})}function k(){i.value.pageNum=1,y()}function z(n){const e=n.tableName||P.value;if(e==""){s.$modal.msgError("请选择要生成的数据");return}n.genType==="1"?ke(n.tableName).then(w=>{s.$modal.msgSuccess("成功生成到自定义路径："+n.genPath)}):s.$download.zip("/tool/gen/batchGenCode?tables="+e,"ruoyi.zip")}function H(n){const e=n.tableName;s.$modal.confirm('确认要强制同步"'+e+'"表结构吗？').then(function(){return xe(e)}).then(()=>{s.$modal.msgSuccess("同步成功")}).catch(()=>{})}function J(){s.$refs.importRef.show()}function W(){s.$refs.createRef.show()}function X(){h.value=[],s.resetForm("queryRef"),i.value.pageNum=1,s.$refs.genRef.sort(N.value.prop,N.value.order)}function Z(n){$e(n.tableId).then(e=>{f.value.data=e.data,f.value.open=!0,f.value.activeName="domain.java"})}function ee(){s.$modal.msgSuccess("复制成功")}function te(n){T.value=n.map(e=>e.tableId),P.value=n.map(e=>e.tableName),B.value=n.length!=1,D.value=!n.length}function le(n,e,w){i.value.orderByColumn=n.prop,i.value.isAsc=n.order,y()}function E(n){const e=n.tableId||T.value[0],w=n.tableName||P.value[0],C={pageNum:i.value.pageNum};s.$tab.openPage("修改["+w+"]生成配置","/tool/gen-edit/index/"+e,C)}function j(n){const e=n.tableId||T.value;s.$modal.confirm('是否确认删除表编号为"'+e+'"的数据项？').then(function(){return Se(e)}).then(()=>{y(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return y(),(n,e)=>{const w=r("el-input"),C=r("el-form-item"),ae=r("el-date-picker"),u=r("el-button"),ne=r("el-form"),S=r("el-col"),oe=r("right-toolbar"),ie=r("el-row"),g=r("el-table-column"),$=r("el-tooltip"),re=r("el-table"),se=r("pagination"),de=r("el-link"),ue=r("el-tab-pane"),pe=r("el-tabs"),me=r("el-dialog"),_=V("hasPermi"),ce=V("hasRole"),fe=V("loading"),A=V("copyText");return c(),F("div",Te,[d(t(ne,{model:l(i),ref:"queryRef",inline:!0},{default:a(()=>[t(C,{label:"表名称",prop:"tableName"},{default:a(()=>[t(w,{modelValue:l(i).tableName,"onUpdate:modelValue":e[0]||(e[0]=o=>l(i).tableName=o),placeholder:"请输入表名称",clearable:"",style:{width:"200px"},onKeyup:Q(k,["enter"])},null,8,["modelValue"])]),_:1}),t(C,{label:"表描述",prop:"tableComment"},{default:a(()=>[t(w,{modelValue:l(i).tableComment,"onUpdate:modelValue":e[1]||(e[1]=o=>l(i).tableComment=o),placeholder:"请输入表描述",clearable:"",style:{width:"200px"},onKeyup:Q(k,["enter"])},null,8,["modelValue"])]),_:1}),t(C,{label:"创建时间",style:{width:"308px"}},{default:a(()=>[t(ae,{modelValue:l(h),"onUpdate:modelValue":e[2]||(e[2]=o=>Y(h)?h.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(C,null,{default:a(()=>[t(u,{type:"primary",icon:"Search",onClick:k},{default:a(()=>e[8]||(e[8]=[b("搜索")])),_:1,__:[8]}),t(u,{icon:"Refresh",onClick:X},{default:a(()=>e[9]||(e[9]=[b("重置")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"]),[[G,l(x)]]),t(ie,{gutter:10,class:"mb8"},{default:a(()=>[t(S,{span:1.5},{default:a(()=>[d((c(),v(u,{type:"primary",plain:"",icon:"Download",disabled:l(D),onClick:z},{default:a(()=>e[10]||(e[10]=[b("生成")])),_:1,__:[10]},8,["disabled"])),[[_,["tool:gen:code"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[d((c(),v(u,{type:"primary",plain:"",icon:"Plus",onClick:W},{default:a(()=>e[11]||(e[11]=[b("创建")])),_:1,__:[11]})),[[ce,["admin"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[d((c(),v(u,{type:"info",plain:"",icon:"Upload",onClick:J},{default:a(()=>e[12]||(e[12]=[b("导入")])),_:1,__:[12]})),[[_,["tool:gen:import"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[d((c(),v(u,{type:"success",plain:"",icon:"Edit",disabled:l(B),onClick:E},{default:a(()=>e[13]||(e[13]=[b("修改")])),_:1,__:[13]},8,["disabled"])),[[_,["tool:gen:edit"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[d((c(),v(u,{type:"danger",plain:"",icon:"Delete",disabled:l(D),onClick:j},{default:a(()=>e[14]||(e[14]=[b("删除")])),_:1,__:[14]},8,["disabled"])),[[_,["tool:gen:remove"]]])]),_:1}),t(oe,{showSearch:l(x),"onUpdate:showSearch":e[3]||(e[3]=o=>Y(x)?x.value=o:null),onQueryTable:y},null,8,["showSearch"])]),_:1}),d((c(),v(re,{ref:"genRef",data:l(q),onSelectionChange:te,"default-sort":l(N),onSortChange:le},{default:a(()=>[t(g,{type:"selection",align:"center",width:"55"}),t(g,{label:"序号",type:"index",width:"50",align:"center"},{default:a(o=>[K("span",null,L((l(i).pageNum-1)*l(i).pageSize+o.$index+1),1)]),_:1}),t(g,{label:"表名称",align:"center",prop:"tableName","show-overflow-tooltip":!0}),t(g,{label:"表描述",align:"center",prop:"tableComment","show-overflow-tooltip":!0}),t(g,{label:"实体",align:"center",prop:"className","show-overflow-tooltip":!0}),t(g,{label:"创建时间",align:"center",prop:"createTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),t(g,{label:"更新时间",align:"center",prop:"updateTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),t(g,{label:"操作",align:"center",width:"330","class-name":"small-padding fixed-width"},{default:a(o=>[t($,{content:"预览",placement:"top"},{default:a(()=>[d(t(u,{link:"",type:"primary",icon:"View",onClick:p=>Z(o.row)},null,8,["onClick"]),[[_,["tool:gen:preview"]]])]),_:2},1024),t($,{content:"编辑",placement:"top"},{default:a(()=>[d(t(u,{link:"",type:"primary",icon:"Edit",onClick:p=>E(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),t($,{content:"删除",placement:"top"},{default:a(()=>[d(t(u,{link:"",type:"primary",icon:"Delete",onClick:p=>j(o.row)},null,8,["onClick"]),[[_,["tool:gen:remove"]]])]),_:2},1024),t($,{content:"同步",placement:"top"},{default:a(()=>[d(t(u,{link:"",type:"primary",icon:"Refresh",onClick:p=>H(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),t($,{content:"生成代码",placement:"top"},{default:a(()=>[d(t(u,{link:"",type:"primary",icon:"Download",onClick:p=>z(o.row)},null,8,["onClick"]),[[_,["tool:gen:code"]]])]),_:2},1024)]),_:1})]),_:1},8,["data","default-sort"])),[[fe,l(R)]]),d(t(se,{total:l(I),page:l(i).pageNum,"onUpdate:page":e[4]||(e[4]=o=>l(i).pageNum=o),limit:l(i).pageSize,"onUpdate:limit":e[5]||(e[5]=o=>l(i).pageSize=o),onPagination:y},null,8,["total","page","limit"]),[[G,l(I)>0]]),t(me,{title:l(f).title,modelValue:l(f).open,"onUpdate:modelValue":e[7]||(e[7]=o=>l(f).open=o),width:"80%",top:"5vh","append-to-body":"",class:"scrollbar"},{default:a(()=>[t(pe,{modelValue:l(f).activeName,"onUpdate:modelValue":e[6]||(e[6]=o=>l(f).activeName=o)},{default:a(()=>[(c(!0),F(we,null,Ce(l(f).data,(o,p)=>(c(),v(ue,{label:p.substring(p.lastIndexOf("/")+1,p.indexOf(".vm")),name:p.substring(p.lastIndexOf("/")+1,p.indexOf(".vm")),key:o},{default:a(()=>[d((c(),v(de,{underline:!1,icon:"DocumentCopy",style:{float:"right"}},{default:a(()=>e[15]||(e[15]=[b(" 复制")])),_:2,__:[15]},1024)),[[A,o],[A,ee,"callback"]]),K("pre",null,L(o),1)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),t(l(Ve),{ref:"importRef",onOk:k},null,512),t(l(Re),{ref:"createRef",onOk:k},null,512)])}}});export{Oe as default};
