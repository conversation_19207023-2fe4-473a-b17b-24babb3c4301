import{a1 as x,_ as L,x as m,C as w,e as n,c as a,o as c,h as l,f as e,i as t,G as p,H as f,n as y,t as r}from"./index-BZGe1FpZ.js";/* empty css                                                             */const S=x("user",{state:()=>({userInfo:{avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",nickname:"<PERSON>min",todoList:[],tasks:[]}}),actions:{initUserInfo(){this.userInfo={avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",nickname:"<PERSON><PERSON>",todoList:[{id:1,title:"待审批流程",status:0},{id:2,title:"月度报告",status:0}],tasks:[{id:1,title:"项目会议",time:"14:00"},{id:2,title:"客户回访",time:"16:30"}]}},logout(){this.$reset(),this.initUserInfo(),window.location.reload()}}}),U={class:"home-container"},B={class:"home-main"},C={class:"title"},N={class:"time"},V={class:"title"},A={__name:"home",setup(D){const i=S(),h=m(()=>{var o;return((o=i.userInfo)==null?void 0:o.todoList)||[]}),k=m(()=>{var o;return((o=i.userInfo)==null?void 0:o.tasks)||[]});return w(()=>{i.userInfo.todoList||i.initUserInfo()}),(o,d)=>{const g=n("el-tag"),_=n("el-card"),u=n("el-col"),v=n("alarm-clock"),I=n("el-icon"),b=n("el-row");return c(),a("div",U,[l("div",B,[e(b,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(_,{header:"我的待办"},{default:t(()=>[(c(!0),a(p,null,f(h.value,s=>(c(),a("div",{key:s.id,class:"todo-item"},[e(g,{type:"info"},{default:t(()=>d[0]||(d[0]=[y("未完成")])),_:1,__:[0]}),l("span",C,r(s.title),1)]))),128))]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(_,{header:"今日任务"},{default:t(()=>[(c(!0),a(p,null,f(k.value,s=>(c(),a("div",{key:s.id,class:"task-item"},[e(I,null,{default:t(()=>[e(v)]),_:1}),l("span",N,r(s.time),1),l("span",V,r(s.title),1)]))),128))]),_:1})]),_:1})]),_:1})])])}}},G=L(A,[["__scopeId","data-v-c82163d6"]]);export{G as default};
