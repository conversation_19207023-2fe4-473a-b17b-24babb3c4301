/* 
 * @Description  : 首页接口
 * <AUTHOR> wnj
 * @Date         : 2025-07-30 09:04:57
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 09:08:05
 * @FilePath     :  / src / api / resourcemgr / homeStatMgr.js
 */

import request from "@/utils/request";

export const siteRunStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/siteRunStatistics",
    method: "get",
    params: query
  });
};

export const pointStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/pointStatistics",
    method: "get",
    params: query
  });
};

export const personnelStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/personnelStatistics",
    method: "get",
    params: query
  });
};

export const equipmentLifeStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/equipmentLifeStatistics",
    method: "get",
    params: query
  });
};

export const deviceStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/deviceStatistics",
    method: "get",
    params: query
  });
};

export const deviceCategoryStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/deviceCategoryStatistics",
    method: "get",
    params: query
  });
};

export const deviceBrandStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/deviceBrandStatistics",
    method: "get",
    params: query
  });
};

export const consumablesStatistics = (query) => {
  return request({
    url: import.meta.env.VITE_REQUEST_HIGH_PREFIX + "/homeStatistics/consumablesStatistics",
    method: "get",
    params: query
  });
};




