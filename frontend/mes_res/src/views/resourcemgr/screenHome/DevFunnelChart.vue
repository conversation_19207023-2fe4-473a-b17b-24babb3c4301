<template>
	<div
		class="dev-stat-chart-container"
		v-loading="loading"
		element-loading-text="图表加载中..."
	>
		<div ref="chartRef" class="chart-container"></div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import * as echarts from "echarts";
import { equipmentLifeStatistics } from "@/api/resourcemgr/homeStatMgr";

// 接收父组件参数
const props = defineProps({
	monitoringElement: {
		type: String,
		default: "",
		required: false,
	},
});

// 状态管理
const chartRef = ref(null);
const myChart = ref(null);
const loading = ref(true);
// 初始化5个固定层级（强制保留位置）
const chartData = ref([
	{ value: 0, name: "0～2年设备数量", isZero: true },
	{ value: 0, name: "2～4年设备数量", isZero: true },
	{ value: 0, name: "4～6年设备数量", isZero: true },
	{ value: 0, name: "6～8年设备数量", isZero: true },
	{ value: 0, name: "8～10年设备数量", isZero: true },
]);

// 图表配置项
const getChartOption = () => ({
	tooltip: {
		trigger: "item",
		formatter: "{b} <br/>占比: {d}%",
		backgroundColor: "rgba(255,255,255,0.9)",
		borderColor: "#eee",
		borderWidth: 1,
	},
	legend: { show: false },
	series: [
		// 右侧文本标注（强制保留5项）
		{
			type: "custom",
			coordinateSystem: "none",
			renderItem: (params, api) => {
				if (!myChart.value) return null;

				const dataIndex = params.dataIndex;
				const seriesData = chartData.value[dataIndex];
				const chartHeight = myChart.value.getHeight();
				// 固定5个层级的位置计算（平均分布）
				const y = ((dataIndex + 0.6) / chartData.value.length) * chartHeight;

				return {
					type: "text",
					position: [100, y],
					style: {
						text: `--------------- ${seriesData.name}  ${seriesData.value}`,
						fontSize: 14,
						fill: seriesData.isZero ? "#ccc" : "#666", // 0值文本灰色
					},
				};
			},
			data: [0, 1, 2, 3, 4], // 强制5条数据，对应5个层级
		},
		// 漏斗图（强制保留5个层级）
		{
			name: "设备年限分布",
			type: "funnel",
			left: 5, // 调整漏斗图位置，留出右侧标注空间
			width: "40%", // 漏斗图宽度
			top: 5,
			bottom: 5,
			label: { show: false },
			data: chartData.value.map((item) => ({
				...item,
				// 0值数据强制设置最小高度（避免层级消失）
				value: item.value === 0 ? 1 : item.value,
			})),
			funnelAlign: "center",
			sort: "none", // 关闭排序，保持原始年限顺序（0~2年 → 8~10年）
			gap: 0,
			// itemStyle: {
			// 	// 0值数据使用浅色边框和背景
			// 	color: (params) =>
			// 		chartData.value[params.dataIndex].isZero
			// 			? "rgba(240,240,240,0.5)"
			// 			: undefined,
			// 	borderColor: (params) =>
			// 		chartData.value[params.dataIndex].isZero ? "#eee" : "#ddd",
			// 	borderWidth: 1,
			// },
		},
	],
});

// 获取数据并更新（保留5个层级）
const fetchChartData = async () => {
	try {
		loading.value = true;
		const params = { monitoringElement: props.monitoringElement };
		const res = await equipmentLifeStatistics(params);

		if (res.data) {
			// 映射接口数据到5个层级，保留原始顺序
			chartData.value = [
				{
					value: res.data.yearIndexOne || 0,
					name: "0～2年设备数量",
					isZero: !(res.data.yearIndexOne > 0),
				},
				{
					value: res.data.yearIndexTwo || 0,
					name: "2～4年设备数量",
					isZero: !(res.data.yearIndexTwo > 0),
				},
				{
					value: res.data.yearIndexThree || 0,
					name: "4～6年设备数量",
					isZero: !(res.data.yearIndexThree > 0),
				},
				{
					value: res.data.yearIndexFour || 0,
					name: "6～8年设备数量",
					isZero: !(res.data.yearIndexFour > 0),
				},
				{
					value: res.data.yearIndexFive || 0,
					name: "8～10年设备数量",
					isZero: !(res.data.yearIndexFive > 0),
				},
			];
		} else {
			// 接口无数据时，重置为5个0值层级
			chartData.value.forEach((item) => {
				item.value = 0;
				item.isZero = true;
			});
		}
	} catch (err) {
		console.error("获取设备年限数据失败:", err);
		chartData.value.forEach((item) => {
			item.value = 0;
			item.isZero = true;
		});
	} finally {
		loading.value = false;
		updateChart();
	}
};

// 初始化图表
const initChart = () => {
	if (chartRef.value && !myChart.value) {
		myChart.value = echarts.init(chartRef.value);
		myChart.value.setOption(getChartOption());
	}
};

// 更新图表
const updateChart = () => {
	if (myChart.value) {
		myChart.value.setOption(getChartOption());
		myChart.value.hideLoading(); // 即使数据为0也隐藏加载态（已显示空数据样式）
	}
};

// 窗口适配
const handleResize = () => {
	if (myChart.value) {
		myChart.value.resize();
	}
};

// 生命周期
onMounted(() => {
	initChart();
	fetchChartData();
	window.addEventListener("resize", handleResize);
});

watch(
	() => props.monitoringElement,
	(newVal) => {
		if (newVal !== undefined) fetchChartData();
	}
);

onUnmounted(() => {
	window.removeEventListener("resize", handleResize);
	if (myChart.value) myChart.value.dispose();
});
</script>

<style scoped>
.dev-stat-chart-container {
	width: 100%;
	height: 180px;
	margin-bottom: 16px;
	position: relative;
}

.chart-container {
	width: 100%;
	height: 100%;
}

:deep(.el-loading-mask) {
	background-color: rgba(255, 255, 255, 0.7);
}
</style>
