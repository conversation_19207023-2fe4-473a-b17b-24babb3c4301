<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-09 09:32:19
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 09:12:18
 * @FilePath     :  / src / views / resourcemgr / screenHome / index.vue
-->
<!-- 首页 -->
<template>
	<ScaleScreen
		:width="1920"
		:height="960"
		class="scale-wrap"
		:selfAdaption="true"
		@resetOther="resizeMap"
	>
		<div class="resource-mgmt-home-container" ref="containerRef">
			<el-row :gutter="24">
				<el-col :span="6">
					<div class="card-box-swap mb24">
						<Title titleName="站点数量统计" />
						<SiteWaterStat
							v-if="monitoringElement === 'water'"
							:monitoringElement="monitoringElement"
						/>
						<SiteAirStat
							v-if="monitoringElement === 'air'"
							:monitoringElement="monitoringElement"
						/>

						<SubTitle titleName="站点状态统计" unit="单位：个" />
						<PointBarChart :monitoringElement="monitoringElement" />
					</div>

					<div class="card-box-swap pr11 pb0 mb24">
						<Title titleName="耗材库" />
						<SuppliesStat :monitoringElement="monitoringElement" />
					</div>

					<div class="card-box-swap pr11">
						<Title titleName="运维人员统计" />
						<PersonStat :monitoringElement="monitoringElement" />
					</div>
				</el-col>

				<el-col :span="12">
					<div
						class="card-box-swap mb24"
						style="height: 60%; position: relative"
					>
						<MainMap
							ref="mapRef"
							:monitoringElement="monitoringElement"
							:selectProvince="selectProvince"
							:selectSiteInfo="selectSiteInfo"
						/>
						<div class="home-select-box">
							<el-select
								v-model="selectProvince"
								value-key="code"
								placeholder="请选择"
								check-strictly
								:disabled="disabled"
								style="width: 100px"
							>
								<el-option
									v-for="dict in provinces"
									:key="dict.code"
									:label="dict.name"
									:value="dict.code"
								/>
							</el-select>
							<el-select
								v-model="siteName"
								filterable
								remote
								reserve-keyword
								remote-show-suffix
								placeholder="请输入站点名称"
								:remote-method="remoteMethod"
								:loading="loading"
								@change="handleChange"
								style="width: 240px; margin-left: 16px"
							>
								<el-option
									v-for="item in options"
									:key="item.id"
									:label="item.siteName"
									:value="item.id"
								/>
							</el-select>
						</div>
						<div class="header-types">
							<div
								class="header-types_item"
								:class="{
									'header-types_item_active': item.value === monitoringElement,
								}"
								v-for="item in monitoring_element"
								:key="item.value"
								@click="selectTypes(item)"
							>
								{{ item.label }}
							</div>
						</div>
					</div>
					<div class="card-box-swap" style="height: calc(40% - 24px)">
						<Title titleName="通知与待办" />
						<NoticeListBox :monitoringElement="monitoringElement" />
					</div>
				</el-col>

				<el-col :span="6">
					<div class="card-box-swap" style="height: 100%">
						<Title titleName="设备管理" />
						<DeviceStat :monitoringElement="monitoringElement" />

						<SubTitle titleName="监测设备统计" unit="" />
						<DevStatChart :monitoringElement="monitoringElement" />

						<SubTitle titleName="设备品牌占比" unit="" />
						<DevBrandRatioChart :monitoringElement="monitoringElement" />

						<SubTitle titleName="设备寿命情况" unit="" />
						<DevFunnelChart :monitoringElement="monitoringElement" />
					</div>
				</el-col>
			</el-row>
		</div>
	</ScaleScreen>
</template>

<script setup name="ResourceHome">
import { ref, onUnmounted, onMounted, defineComponent, watchEffect } from "vue";
import { ElMessage } from "element-plus";
import useAppStore from "@/store/modules/app";
import Title from "@/components/Title";
import ScaleScreen from "@/components/ScaleScreen";
import SubTitle from "@/components/SubTitle";
import DevFunnelChart from "./DevFunnelChart";
import DevStatChart from "./DevStatChart";
import DevBrandRatioChart from "./DevBrandRatioChart";
import PointBarChart from "./PointBarChart";
import MainMap from "./MainMap";
import NoticeListBox from "./NoticeListBox";
import { provinces } from "../common/optionsData";
import SiteWaterStat from "./SiteWaterStat";
import SiteAirStat from "./SiteAirStat";
import SuppliesStat from "./SuppliesStat";
import PersonStat from "./PersonStat";
import DeviceStat from "./DeviceStat";
import { siteList } from "@/api/resourcemgr/pointMgr";
const { proxy } = getCurrentInstance();
const { monitoring_element } = proxy.useBusDict("monitoring_element");

defineComponent({
	Title,
	SubTitle,
	DevFunnelChart,
	DevStatChart,
	DevBrandRatioChart,
	PointBarChart,
	MainMap,
	NoticeListBox,
	SiteWaterStat,
	PersonStat,
	SuppliesStat,
});

const mapRef = ref(null);

const appStore = useAppStore();

const containerRef = ref(null);

const monitoringElement = ref(import.meta.env.VITE_APP_MONITORING_ELEMENT);

const loading = ref(false);

const selectSiteInfo = ref({});

const siteName = ref("");

const selectProvince = ref("");

const options = ref([]);

const remoteMethod = (query) => {
	if (query) {
		loading.value = true;
		siteList({
			monitoringElement: monitoringElement.value,
			siteName: query,
		}).then((response) => {
			loading.value = false;
			options.value = response?.data?.data;
		});
	}
};

function handleChange(value) {
	const selected = options.value.find((item) => item.siteId === value);
	selectSiteInfo.value = selected;
}

function selectTypes(item) {
	monitoringElement.value = item.value;
}

function resizeMap() {
	mapRef.value.refreshMap();
}

onMounted(() => {
	nextTick(() => {
		appStore.closeSideBar(true);
	});
});

onUnmounted(() => {
	const sidebar = appStore.sidebar;
	if (!sidebar.opened) {
		appStore.toggleSideBar(true);
	}
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
