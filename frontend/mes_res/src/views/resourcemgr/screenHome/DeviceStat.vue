<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-07-28 18:11:38
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 10:15:17
 * @FilePath     :  / src / views / resourcemgr / screenHome / DeviceStat.vue
-->
<!-- 首页 -->
<template>
	<div class="device-stat-content">
		<div class="device-part-one">
			<div class="part-one-left">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr16"
				/>
				<div>
					<span>在线设备</span>
					<span class="num">{{statData?.numberOfDevices || 0}}</span>
				</div>
			</div>
			<div class="part-one-right">
				<div class="part-one-right-item mb11">
					<div class="item-title">
						<img
							src="@/assets/images/resourcemgr/home/<USER>"
							alt=""
						/>
						<span>备机</span>
					</div>
					<span>{{statData?.prepareTheMachine || 0}}</span>
				</div>
				<div class="part-one-right-item">
					<div class="item-title">
						<img
							src="@/assets/images/resourcemgr/home/<USER>"
							alt=""
						/>
						<span>原机</span>
					</div>
					<span>{{statData?.originalMachine || 0}}</span>
				</div>
			</div>
		</div>
		<div class="device-part-two">
			<div class="part-two-item">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr16"
				/>
				<div>
					<span>在用</span>
					<span class="num">{{statData?.enabledDevices || 0}}</span>
				</div>
			</div>
			<div class="part-two-item">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr16"
				/>
				<div>
					<span>库存</span>
					<span class="num">{{statData?.stockDevices || 0}}</span>
				</div>
			</div>
			<div class="part-two-item">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr16"
				/>
				<div>
					<span>修复中</span>
					<span class="num">{{statData?.underMaintenanceDevices || 0}}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup name="ResourceHome">
import { ref, onMounted } from "vue";
import { deviceStatistics } from "@/api/resourcemgr/homeStatMgr";

const props = defineProps({
	monitoringElement: String,
});

const loading = ref(true);

// enabledDevices: 5;
// numberOfDevices: 0;
// originalMachine: 0;
// prepareTheMachine: 0;
// stockDevices: 0;
// underMaintenanceDevices: 2;
const statData = ref({});

const getServiceData = () => {
	loading.value = true;
	const params = {
		monitoringElement: props.monitoringElement,
	};

	deviceStatistics(params).then((res) => {
		if (res.data) {
			statData.value = res.data;
			loading.value = false;
		} else {
			loading.value = false;
		}
	});
};

watch(
	() => props?.monitoringElement,
	(newVal) => {
		getServiceData();
	}
);

onMounted(() => {
	getServiceData();
});
</script>

<style scoped lang="scss">
.device-stat-content {
	margin-bottom: 16px;
	.device-part-one {
		height: 74px;
		background: initial;
		background-image: url("@/assets/images/resourcemgr/home/<USER>");
		background-size: 100% 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 16px;
		.part-one-left {
			display: flex;
			align-items: center;
			font-family: PingFangSC-Medium;
			font-size: 14px;
			color: #ffffff;
			font-weight: 500;
			div {
				display: flex;
				flex-direction: column;
				.num {
					font-family: DINAlternate-Bold;
					font-size: 28px;
					color: #ffffff;
					line-height: 30px;
					font-weight: 700;
				}
			}
		}
		.part-one-right {
			.part-one-right-item {
				width: 190px;
				height: 20px;
				background-image: url("@/assets/images/resourcemgr/home/<USER>");
				background-size: 100% 100%;
				display: flex;
				justify-content: space-between;
				font-family: DINAlternate-Bold;
				font-size: 20px;
				color: #ffffff;
				font-weight: 700;
				padding: 0 16px;
				.item-title {
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: PingFangSC-Medium;
					font-size: 14px;
					color: #ffffff;
					font-weight: 500;
				}
			}
		}
	}
	.device-part-two {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 8px;
		.part-two-item {
			width: calc(calc(100% - 22px) / 3);
			padding: 8px 16px;
			background: rgba(49, 103, 213, 0.05);
			border-radius: 3px;
			display: flex;
			align-items: center;
			font-family: PingFangSC-Medium;
			font-size: 14px;
			color: #333333;
			font-weight: 500;
			div {
				display: flex;
				flex-direction: column;
				.num {
					font-family: DINAlternate-Bold;
					font-size: 28px;
					color: #333333;
					line-height: 30px;
					font-weight: 700;
				}
			}
		}
	}

	.mb11 {
		margin-bottom: 11px;
	}
	.mr16 {
		margin-right: 16px;
	}
}
</style>
