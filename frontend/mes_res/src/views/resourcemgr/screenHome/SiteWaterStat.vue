<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-07-28 16:19:22
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 09:21:58
 * @FilePath     :  / src / views / resourcemgr / screenHome / SiteWaterStat.vue
-->
<!-- 首页 -->
<template>
	<div class="card-item-1 lan-bg" v-loading="loading">
		<el-row justify="space-between" align="middle" class="card-row-1">
			<el-row justify="space-between" align="middle" style="width: 49%">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">自动站</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.waterAutomaticStation || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
			<el-row justify="space-between" align="middle" style="width: 49%">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">数智化改造</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.waterSmartTransform || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
		</el-row>
		<el-divider class="card-divider" />
		<el-row justify="space-between" align="middle" class="card-row-1">
			<el-row justify="space-between" align="middle" style="width: 49%">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">断面</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.waterSection || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
			<el-row justify="space-between" align="middle" style="width: 49%">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">无人岸边站</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.waterUnattendedStation || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
		</el-row>
	</div>
</template>

<script setup name="ResourceHome">
import { ref, onMounted } from "vue";
import { pointStatistics } from "@/api/resourcemgr/homeStatMgr";

const props = defineProps({
	monitoringElement: String,
});

const loading = ref(true);

// airAreaStation: 0;
// airBackgroundStation: 0;
// airCityStation: 0;
// airSmartTransform: 0;
// waterAutomaticStation: 3;
// waterSection: 3;
// waterSmartTransform: 0;
// waterUnattendedStation: 0;
const siteStatData = ref({});

const getServiceData = () => {
	loading.value = true;
	const params = {
		monitoringElement: props.monitoringElement,
	};

	pointStatistics(params).then((res) => {
		if (res.data) {
			siteStatData.value = res.data;
			loading.value = false;
		} else {
			loading.value = false;
		}
	});
};

onMounted(() => {
	getServiceData();
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
