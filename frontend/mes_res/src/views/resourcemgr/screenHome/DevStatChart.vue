<template>
	<div
		class="dev-stat-chart-container"
		v-loading="loading"
		element-loading-text="图表加载中..."
	>
		<!-- ECharts 容器，宽高通过样式控制 -->
		<div ref="chartRef" class="chart-container"></div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import * as echarts from "echarts";
import { deviceCategoryStatistics } from "@/api/resourcemgr/homeStatMgr";

// 接收父组件传入的监测要素参数
const props = defineProps({
	monitoringElement: {
		type: String,
		default: "",
		required: false,
	},
});

// 状态管理
const chartRef = ref(null); // ECharts 容器 DOM
const myChart = ref(null); // ECharts 实例
const loading = ref(true); // 加载状态
const chartData = ref([]); // 图表数据

// 图表配置项（抽离为函数，便于动态更新）
const getChartOption = () => ({
	tooltip: {
		trigger: "item",
		formatter: "{a} <br/>{b}: {c} 台 ({d}%)", // 显示数量和占比
		backgroundColor: "rgba(255, 255, 255, 0.9)", // 半透明白色背景
		borderColor: "#eee",
		borderWidth: 1,
		textStyle: { color: "#333" },
	},
	legend: {
		icon: "circle",
		orient: "vertical",
		left: "45%", // 图例位置右移，避免遮挡饼图
		top: "center", // 垂直居中
		itemWidth: 10, // 图例图标大小
		itemHeight: 10,
		textStyle: { fontSize: 12 }, // 图例文字自适应
		// 动态获取图例数据（从图表数据中提取名称）
		data: chartData.value.map((item) => item.name),
	},
	series: [
		{
			name: "设备类型占比",
			type: "pie",
			radius: ["40%", "70%"], // 环形饼图（内半径40%，外半径70%）
			center: ['20%', '50%'], // 饼图位置左移，给图例留空间
			data: chartData.value,
			label: {
				show: false,
				position: "inside",
				formatter: "{d}%", // 内部显示百分比
				fontSize: 12,
			},
			labelLine: {
				show: true,
				length: 10, // 标签连接线长度
				length2: 20,
			},
			// 鼠标 hover 高亮效果
			emphasis: {
				scale: true, // 放大效果
				itemStyle: {
					shadowBlur: 10,
					shadowColor: "rgba(0, 0, 0, 0.3)",
				},
			},
		},
	],
});

// 获取设备统计数据（从接口动态获取）
const fetchChartData = async () => {
	try {
		loading.value = true;
		const params = { monitoringElement: props.monitoringElement };
		const res = await deviceCategoryStatistics(params);
		// 处理接口返回数据（适配图表格式）
		if (res.data && Array.isArray(res.data)) {
			// 过滤掉值为 0 的数据（避免占比为 0 却显示的问题）
			chartData.value = res.data
				.filter((item) => item.value > 0)
				.map((item) => ({
					name: item.deviceTypeLevel3Name || "未知设备", // 设备类型名称
					value: item.value || 0, // 设备数量
				}));
		} else {
			chartData.value = []; // 数据格式错误时置空
		}
	} catch (err) {
		console.error("获取设备统计数据失败:", err);
		chartData.value = []; // 错误时置空
	} finally {
		loading.value = false;
		updateChart(); // 无论成功失败都更新图表
	}
};

// 初始化 ECharts 实例
const initChart = () => {
	if (chartRef.value && !myChart.value) {
		myChart.value = echarts.init(chartRef.value);
		// 设置初始配置
		myChart.value.setOption(getChartOption());
	}
};

// 更新图表数据
const updateChart = () => {
	if (myChart.value) {
		myChart.value.setOption(getChartOption());
		// 处理无数据场景
		if (chartData.value.length === 0) {
			myChart.value.showLoading({
				text: "暂无数据",
				fontSize: 14,
				showSpinner: false,
			});
		} else {
			myChart.value.hideLoading();
		}
	}
};

// 监听窗口大小变化，自适应图表
const handleResize = () => {
	if (myChart.value) {
		myChart.value.resize();
	}
};

// 生命周期管理
onMounted(() => {
	initChart();
	fetchChartData();
	window.addEventListener("resize", handleResize);
});

// 监听参数变化，重新获取数据
watch(
	() => props.monitoringElement,
	(newVal) => {
		if (newVal !== undefined) {
			fetchChartData();
		}
	},
	{ immediate: false } // 初始化时已调用，无需重复
);

// 组件卸载时清理
onUnmounted(() => {
	window.removeEventListener("resize", handleResize);
	if (myChart.value) {
		myChart.value.dispose(); // 销毁实例，释放内存
		myChart.value = null;
	}
});
</script>

<style scoped>
.dev-stat-chart-container {
	width: 100%;
	height: 180px; /* 保持原高度 */
	margin-bottom: 16px;
}

.chart-container {
	width: 100%;
	height: 100%; /* 继承父容器高度，确保图表占满 */
}

/* 优化 loading 样式 */
:deep(.el-loading-mask) {
	background-color: rgba(255, 255, 255, 0.7);
}
</style>
