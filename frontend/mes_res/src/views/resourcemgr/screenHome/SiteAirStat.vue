<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-07-28 18:06:45
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 09:23:49
 * @FilePath     :  / src / views / resourcemgr / screenHome / SiteAirStat.vue
-->
<!-- 首页 -->
<template>
	<div class="card-item-1 lv-bg" v-loading="loading">
		<el-row justify="space-between" align="middle" class="card-row-1">
			<el-row justify="space-between" align="middle" style="width: 49%;">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">国控城市站</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.airCityStation || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
			<el-row justify="space-between" align="middle" style="width: 49%;">
				<el-col :span="6">
					<img
						src="@/assets/images/resourcemgr/home/<USER>"
						alt=""
						class="mr8"
					/>
					<span class="font-5">数智化改造</span>
				</el-col>
				<el-col :span="6">
					<span class="font-6 mr8">{{siteStatData?.airSmartTransform || 0}}</span>
					<span class="font-5">个</span>
				</el-col>
			</el-row>
		</el-row>
		<el-divider class="card-divider" />
		<el-row justify="space-between" align="middle" class="card-row-1">
			<el-row justify="space-between" align="middle" style="width: 49%;">
			<el-col :span="6">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr8"
				/>
				<span class="font-5">区域站</span>
			</el-col>
			<el-col :span="6">
				<span class="font-6 mr8">{{siteStatData?.airAreaStation || 0}}</span>
				<span class="font-5">个</span>
			</el-col>
			</el-row>
			<el-row justify="space-between" align="middle" style="width: 49%;">
			<el-col :span="6">
				<img
					src="@/assets/images/resourcemgr/home/<USER>"
					alt=""
					class="mr8"
				/>
				<span class="font-5">背景站</span>
			</el-col>
			<el-col :span="6">
				<span class="font-6 mr8">{{siteStatData?.airBackgroundStation || 0}}</span>
				<span class="font-5">个</span>
			</el-col>
			</el-row>
		</el-row>
	</div>
</template>

<script setup name="ResourceHome">
import { ref, onMounted } from "vue";
import { pointStatistics } from "@/api/resourcemgr/homeStatMgr";

const props = defineProps({
	monitoringElement: String,
});

const loading = ref(true);

// airAreaStation: 0;
// airBackgroundStation: 0;
// airCityStation: 0;
// airSmartTransform: 0;
// waterAutomaticStation: 3;
// waterSection: 3;
// waterSmartTransform: 0;
// waterUnattendedStation: 0;
const siteStatData = ref({});

const getServiceData = () => {
	loading.value = true;
	const params = {
		monitoringElement: props.monitoringElement,
	};

	pointStatistics(params).then((res) => {
		if (res.data) {
			siteStatData.value = res.data;
			loading.value = false;
		} else {
			loading.value = false;
		}
	});
};

onMounted(() => {
	getServiceData();
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
