<template>
	<div
		class="dev-stat-chart-container"
		v-loading="loading"
		element-loading-text="图表加载中..."
	>
		<!-- ECharts 容器，宽高通过样式控制 -->
		<div ref="chartRef" class="chart-container"></div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import * as echarts from "echarts";
import { siteRunStatistics } from "@/api/resourcemgr/homeStatMgr";

// 接收父组件传入的监测要素参数
const props = defineProps({
	monitoringElement: {
		type: String,
		default: "",
		required: false,
	},
});

// 状态管理
const chartRef = ref(null); // ECharts 容器 DOM
const myChart = ref(null); // ECharts 实例
const loading = ref(true); // 加载状态
const chartData = ref([]); // 图表数据
const chartXAxisData = ref([]);

// 图表配置项（抽离为函数，便于动态更新）
const getChartOption = () => ({
	grid: {
		top: "8%",
		left: "3%",
		right: "4%",
		bottom: "3%",
		containLabel: true,
	},
	xAxis: {
		type: "category",
		axisLabel: {
			interval: 0,
			fontSize: 14,
			color: "#999999",
			formatter: function (value) {
				// 将字符串拆分为数组，每个字符后添加换行符
				return value.split("").join("\n");
			},
		},
		data: chartXAxisData.value,
	},
	yAxis: {
		axisLabel: {
			show: false, // 强制显示纵坐标标签
		},
		splitLine: {
			show: true, // 显示网格线
			lineStyle: {
				type: "dashed", // 网格线样式
				opacity: 0.8, // 网格线透明度
			},
		},
		type: "value",
	},
	series: [
		{
			name: "数量",
			type: "bar",
			label: {
				show: true,
				position: "top",
				color: "#1472FF",
				fontWeight: "bold",
			},
			data: chartData.value,
			itemStyle: {
				color: "#5793f3",
			},
		},
	],
});

// 获取设备统计数据（从接口动态获取）
const fetchChartData = async () => {
	try {
		loading.value = true;
		const params = { monitoringElement: props.monitoringElement };
		const res = await siteRunStatistics(params);
		// 处理接口返回数据（适配图表格式）
		if (res.data && Array.isArray(res.data)) {
			// 过滤掉值为 0 的数据（避免占比为 0 却显示的问题）
			chartData.value = res.data.map((item) => item.num || 0);
			chartXAxisData.value = res.data.map(
				(item) => item.siteStatusName || "未知状态"
			);
		} else {
			chartData.value = []; // 数据格式错误时置空
      chartXAxisData.value = []; // 错误时置空
		}
	} catch (err) {
		console.error("获取点位统计数据失败:", err);
		chartData.value = []; // 错误时置空
    chartXAxisData.value = []; // 错误时置空
	} finally {
		loading.value = false;
		updateChart(); // 无论成功失败都更新图表
	}
};

// 初始化 ECharts 实例
const initChart = () => {
	if (chartRef.value && !myChart.value) {
		myChart.value = echarts.init(chartRef.value);
		// 设置初始配置
		myChart.value.setOption(getChartOption());
	}
};

// 更新图表数据
const updateChart = () => {
	if (myChart.value) {
		myChart.value.setOption(getChartOption());
		// 处理无数据场景
		if (chartData.value.length === 0) {
			myChart.value.showLoading({
				text: "暂无数据",
				fontSize: 14,
				showSpinner: false,
			});
		} else {
			myChart.value.hideLoading();
		}
	}
};

// 监听窗口大小变化，自适应图表
const handleResize = () => {
	if (myChart.value) {
		myChart.value.resize();
	}
};

// 生命周期管理
onMounted(() => {
	initChart();
	fetchChartData();
	window.addEventListener("resize", handleResize);
});

// 监听参数变化，重新获取数据
watch(
	() => props.monitoringElement,
	(newVal) => {
		if (newVal !== undefined) {
			fetchChartData();
		}
	},
	{ immediate: false } // 初始化时已调用，无需重复
);

// 组件卸载时清理
onUnmounted(() => {
	window.removeEventListener("resize", handleResize);
	if (myChart.value) {
		myChart.value.dispose(); // 销毁实例，释放内存
		myChart.value = null;
	}
});
</script>

<style scoped>
.dev-stat-chart-container {
	width: 100%;
	height: 280px; /* 保持原高度 */
}

.chart-container {
	width: 100%;
	height: 100%; /* 继承父容器高度，确保图表占满 */
}

/* 优化 loading 样式 */
:deep(.el-loading-mask) {
	background-color: rgba(255, 255, 255, 0.7);
}
</style>
