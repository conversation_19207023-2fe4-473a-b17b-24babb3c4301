<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-07-28 16:56:15
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 10:06:06
 * @FilePath     :  / src / views / resourcemgr / screenHome / SuppliesStat.vue
-->
<!-- 首页 -->
<template>
	<!-- 数据为空状态 -->
	<div v-if="!statData.length" class="empty-container">
		<el-empty description="暂无数据"></el-empty>
	</div>
	<!-- 数据展示 -->
	<el-carousel v-else height="155px" :autoplay="true">
		<el-carousel-item v-for="carouselIndex in pageCount" :key="carouselIndex">
			<el-row :gutter="10" justify="space-between">
				<el-col
					:span="8"
					v-for="(item, colIndex) in currentPageItems(carouselIndex)"
					:key="item.id || colIndex"
				>
					<div class="card-item-2">
						<el-row justify="space-between" align="middle" class="card-row-1">
							<el-col :span="24">
								<img
									:src="imageList[(carouselIndex * 3 + colIndex) % 5]"
									alt=""
									class="mr8"
								/>
								<span class="font-1">{{ item.suppliesTypeName }}</span>
							</el-col>
						</el-row>
						<el-row justify="space-between" align="middle" class="card-row-1">
							<el-col :span="8">
								<span class="font-3">实时库存:</span>
							</el-col>
							<el-col :span="8">
								<span class="font-2">{{ item.currentStock }}</span>
							</el-col>
						</el-row>
						<el-row justify="space-between" align="middle" class="card-row-1">
							<el-col :span="8">
								<span class="font-3">当日出库:</span>
							</el-col>
							<el-col :span="8">
								<span class="font-2">{{ item.dailyOutbound }}</span>
							</el-col>
						</el-row>
						<el-row justify="space-between" align="middle" class="card-row-1">
							<el-col :span="8">
								<span class="font-3">当日入库:</span>
							</el-col>
							<el-col :span="8">
								<span class="font-2">{{ item.dailyInbound }}</span>
							</el-col>
						</el-row>
					</div>
				</el-col>
			</el-row>
		</el-carousel-item>
	</el-carousel>
</template>

<script setup name="ResourceHome">
import { ref, onMounted } from "vue";
import { consumablesStatistics } from "@/api/resourcemgr/homeStatMgr";
import imgLan from "@/assets/images/resourcemgr/home/<USER>";
import imgLv from "@/assets/images/resourcemgr/home/<USER>";
import imgHuang from "@/assets/images/resourcemgr/home/<USER>";
import imgHong from "@/assets/images/resourcemgr/home/<USER>";
import imgHui from "@/assets/images/resourcemgr/home/<USER>";

const imageList = [imgLan, imgLv, imgHuang, imgHong, imgHui];

const props = defineProps({
	monitoringElement: String,
});

const loading = ref(true);

const statData = ref([]);

// 计算属性：总页数
const pageCount = computed(() => {
	return Math.ceil(statData.value.length / 3) || 1;
});

// 方法：获取当前页的项目
const currentPageItems = (pageIndex) => {
	const start = (pageIndex - 1) * 3;
	const end = start + 3;
	return statData.value.slice(start, end);
};

const getServiceData = () => {
	loading.value = true;
	const params = {
		monitoringElement: props.monitoringElement,
	};

	consumablesStatistics(params).then((res) => {
		if (res.data) {
			statData.value = res.data;
			loading.value = false;
		} else {
			loading.value = false;
		}
	});
};

watch(
	() => props?.monitoringElement,
	(newVal) => {
		getServiceData();
	}
);

onMounted(() => {
	getServiceData();
});
</script>

<style scoped lang="scss">
@import "./index.scss";
.empty-container {
	height: 155px;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
