<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-07-28 16:56:16
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-30 10:08:34
 * @FilePath     :  / src / views / resourcemgr / screenHome / PersonStat.vue
-->
<template>
	<el-row :gutter="10" justify="space-between">
		<el-col :span="12">
			<div class="card-item-2">
				<el-row class="card-row-1">
					<el-col :span="8">
						<img
							src="@/assets/images/resourcemgr/home/<USER>"
							alt=""
						/>
					</el-col>
					<el-col :span="8">
						<el-row align="middle" style="flex-direction: column">
							<el-col :span="8">
								<span class="font-1">在岗</span>
							</el-col>
							<el-col :span="8">
								<span class="font-2 mr8">{{statData?.onDuty || 0}}</span>
								<span class="font-3">个</span>
							</el-col>
						</el-row>
					</el-col>
				</el-row>
			</div>
		</el-col>
		<el-col :span="12">
			<div class="card-item-2">
				<el-row class="card-row-1">
					<el-col :span="8">
						<img
							src="@/assets/images/resourcemgr/home/<USER>"
							alt=""
						/>
					</el-col>
					<el-col :span="8">
						<el-row align="middle" style="flex-direction: column">
							<el-col :span="8">
								<span class="font-1">休假</span>
							</el-col>
							<el-col :span="8">
								<span class="font-2 mr8">{{statData?.vacation || 0}}</span>
								<span class="font-3">个</span>
							</el-col>
						</el-row>
					</el-col>
				</el-row>
			</div>
		</el-col>
	</el-row>
</template>

<script setup name="ResourceHome">
import { ref, onMounted } from "vue";
import { personnelStatistics } from "@/api/resourcemgr/homeStatMgr";

const props = defineProps({
	monitoringElement: String,
});

const loading = ref(true);

const statData = ref({});

const getServiceData = () => {
	loading.value = true;
	const params = {
		monitoringElement: props.monitoringElement,
	};

	personnelStatistics(params).then((res) => {
		if (res.data) {
			statData.value = res.data;
			loading.value = false;
		} else {
			loading.value = false;
		}
	});
};

watch(
	() => props?.monitoringElement,
	(newVal) => {
		getServiceData();
	}
);

onMounted(() => {
	getServiceData();
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
