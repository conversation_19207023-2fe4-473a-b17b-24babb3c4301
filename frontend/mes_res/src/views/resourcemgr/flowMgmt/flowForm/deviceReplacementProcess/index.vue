<!--
 * @Description  : 设备更换
 * <AUTHOR> wnj
 * @Date         : 2025-07-10 14:38:26
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-29 11:02:51
 * @FilePath     :  / src / views / resourcemgr / flowMgmt / flowForm / deviceReplacementProcess / index.vue
-->
<template>
	<el-form
		ref="formRef"
		:model="formData"
		:rules="formRules"
		:label-width="120"
		label-position="left"
		label-width="120px"
		class="info-form"
	>
		<!-- 表单内容区域，使用栅格布局 -->
		<el-row :gutter="20">
			<el-col :span="24" class="mb20">
				<el-form-item label="原设备" prop="sites">
					<span>{{ selections }}</span>
				</el-form-item>
			</el-col>
			<el-col :span="12" class="mb20">
				<el-form-item label="更换设备" prop="newDeviceId" required>
					<CustomSearchSelect
						v-model="formData.newDeviceId"
						placeholder="请选择"
						:fetch-api="fetchDeviceData"
					/>
				</el-form-item>
			</el-col>
			<el-col :span="12" class="mb20">
				<el-form-item label="更换原因" prop="type" required>
					<el-select v-model="formData.type" placeholder="请选择" filterable>
						<el-option value="4" label="停用">停用</el-option>
						<el-option value="5" label="维修">维修</el-option>
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="24" class="mb20">
				<el-form-item label="上传附件" prop="files">
					<el-upload
						drag
						:action="uploadUrl"
						accept="application/pdf,image/*"
						:before-upload="handleBeforeUpload"
						:on-success="handleUploadSuccess"
						:on-error="handleUploadError"
						:on-remove="handleRemove"
						:headers="headers"
						:file-list="fileList"
						style="width: 100%"
					>
						<i class="el-icon-upload"></i>
						<div class="el-upload__text">拖拽材料到此处或点击上传</div>
						<el-button type="primary" round class="up-button">上传</el-button>
						<div class="el-upload__tip" slot="tip">
							支持不超过20MB的pdf文件或者图片
						</div>
					</el-upload>
				</el-form-item>
			</el-col>
			<el-col :span="24" class="mb20">
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="formData.remark"
						type="textarea"
						:rows="2"
						placeholder="请输入备注"
					>
					</el-input>
				</el-form-item>
			</el-col>
		</el-row>
	</el-form>
	<div class="dialog-footer">
		<el-button @click="handleClose">取消</el-button>
		<el-button type="primary" @click="saveHandle"> 确定 </el-button>
	</div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import { saveSysAttach, delSysAttach } from "@/api/resourcemgr/common";
import { definitionStartByKey } from "@/api/flowable/definition";
import { nodePersonList } from "@/api/resourcemgr/flowMgr";
import { deviceList } from "@/api/resourcemgr/deviceMgr";
import CustomSearchSelect from "@/components/CustomSearchSelect";

const userStore = useUserStore();
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传的图片服务器地址

const headers = ref({
	Authorization: "Bearer " + getToken(),
});

const props = defineProps({
	record: {
		type: Object,
		default: () => ({}),
	},
});
const emit = defineEmits(["handleClose"]);
const formData = ref({});

const formRef = ref(null);

const record = ref(props.record);

const selections = ref("");

// 文件列表
const fileList = ref([]);

// 文件列表
const newDeviceList = ref([]);

const formRules = {
	newDeviceId: [{ required: true, message: "更换设备不能为空", trigger: "blur" }],
	type: [{ required: true, message: "更换原因不能为空", trigger: "blur" }],
};

const handleClose = () => {
	emit("handleClose");
};

const saveHandle = async () => {
	// 表单验证
	const isValidate = (await formRef.value?.validate()) || false;
	if (!isValidate) return;

	// 禁用按钮防止重复提交
	const loading = ElLoading.service({ fullscreen: true, text: "提交中..." });
	let attachList = [];
	fileList.value.forEach((t) => {
		attachList.push({
			attachId: t.uid,
			attachPath: t.fullPath,
			attachName: t.name,
		});
	});
	const params = {
		resourceType: record.value.resourceType,
		taskName: record.value.name,
		processKey: record.value.flowKey,
		nodeKey: record.value.nodeKey,
	};

	const curFormData = {
		...params,
		startData: {
			type: formData.value.type,
			remark: formData.value.remark,
			files: attachList,
		},
	};

	try {
		// 并发处理多个资源ID
		const results = await Promise.all(
			record.value.ids.map(async (resourceId) => {
				const { data } = await nodePersonList({
					resourceId,
					resourceType: params.resourceType,
					nodeId: record.value.nodeKey,
				});
				const taskHandle = data.map((t) => t.userId).join(",");

				const deviceOptions = record.value.selections.filter(
					(t) => t.id === resourceId
				);
				if (deviceOptions.length > 0) {
					const cutSite = deviceOptions[0];
					let oldDevice = { ...cutSite };
					oldDevice.deviceStatus = formData.value.type;
					curFormData.oldDevice = oldDevice;
				}

				const newDeviceOptions = newDeviceList.value.filter(
					(t) => t.id === formData.value.newDeviceId
				);
				if (newDeviceOptions.length > 0) {
					const cutSite = newDeviceOptions[0];
					let newDevice = { ...cutSite };
					newDevice.deviceStatus = "3";
					curFormData.newDevice = newDevice;
				}
				curFormData.resourceId = resourceId;
				params.resourceId = resourceId;
				// 1. 启动流程实例
				const { code: procCode, data: procData } = await definitionStartByKey(
					record.value.flowKey,
					{
						...params,
						taskHandle,
						formData: JSON.stringify(curFormData),
						processTitle: `${record.value.name}-${resourceId}`,
						tenantId: "1",
						userId: userStore.id,
						userName: userStore.name,
						nickName: userStore.nickName,
						sysUser: {
							userId: userStore.id,
							userName: userStore.name,
							nickName: userStore.nickName,
						},
					}
				);

				if (procCode !== 200) {
					throw new Error(`资源ID ${resourceId} 启动流程失败`);
				}
			})
		);

		// 统一处理成功提示
		ElMessage.success("提交成功");
		emit("handleClose");
	} catch (error) {
		// 统一错误处理
		ElMessage.error(error.message || "提交失败，请重试");
		console.error("批量提交错误:", error);
	} finally {
		// 关闭加载状态
		loading.close();
	}
};

const fetchDeviceData = async (keyword, page, pageSize) => {
	try {
		let params = {
			deviceName: keyword,
			pageSize: pageSize,
			pageNum: page,
		};
		if (record.value.selections.length > 0) {
			const cutSite = record.value.selections[0];
			params.monitoringElement = cutSite.monitoringElement;
		}
		const res = await deviceList({ ...params });
		const { data } = res;
		const reruenData = data.data;
		newDeviceList.value = reruenData;
		const list = reruenData.map((item) => ({
			value: item.id,
			label: item.deviceName,
		}));
		const hasMore = page * pageSize < data.totalRecords;
		return { list, hasMore };
	} catch (error) {
		throw new Error("获取设备数据失败");
	}
};

const initSysAttach = (params, callback) => {
	saveSysAttach(params).then((res) => {
		const { data } = res;
		callback(data);
	});
};

// 上传前校检格式和大小
function handleBeforeUpload(file) {
	// 允许的文件类型：PDF、JPG、PNG、GIF、WebP、SVG
	const allowedTypes = [
		"application/pdf",
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/svg+xml",
	];

	// 检查文件类型
	const isValidType = allowedTypes.includes(file.type);

	// 检查文件大小（20MB限制）
	const isLt20M = file.size / 1024 / 1024 < 20;

	// 错误处理
	if (!isValidType) {
		ElMessage.error("请上传 PDF 或图片文件（支持 JPG、PNG、GIF、WebP、SVG）！");
		return false;
	}

	if (!isLt20M) {
		ElMessage.error("文件大小不能超过20MB！");
		return false;
	}

	// 验证通过
	return true;
}

// 上传成功处理
function handleUploadSuccess(res, file) {
	const { code, data } = res;
	if (code == 200) {
		const { fullPath, name } = data;
		initSysAttach(
			{ attachName: name, attachPath: fullPath, attachType: "2" },
			(attachId) => {
				file.uid = attachId;
				file.fullPath = fullPath;
				file.name = name;
				fileList.value.push(file);
			}
		);
	} else {
		ElMessage.error("附件插入失败");
	}
}

// 上传失败处理
function handleUploadError() {
	ElMessage.error("附件插入失败");
}

function handleRemove(val) {
	const attachId = val.uid;
	delSysAttach({ attachId }).then((res) => {
		const { code } = res;
		if (code == 200) {
			ElMessage.success("附件删除成功");
		}
	});
}

watch(
	() => props.record,
	(val) => {
		console.log(val);
		record.value = val;
		selections.value = (val.selections || [])
			.map((t) => t.deviceName)
			.join(",");
	},
	{
		immediate: true,
		deep: true,
	}
);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.dialog-footer {
	text-align: right;
}

.info-form {
	.el-form-item.is-required:not(.is-no-asterisk) > label::before {
		content: "*";
		color: red;
		margin-right: 4px;
	}

	.el-row {
		margin-bottom: 12px;
	}

	.el-form-item {
		margin-bottom: 0;
		display: flex;
		flex-direction: column;
	}
}
</style>
