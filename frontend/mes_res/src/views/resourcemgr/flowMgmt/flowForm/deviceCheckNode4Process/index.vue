<!--
 * @Description  : 设备校验
 * <AUTHOR> wnj
 * @Date         : 2025-07-10 14:38:26
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-29 10:29:34
 * @FilePath     :  / src / views / resourcemgr / flowMgmt / flowForm / deviceCheckNode4Process / index.vue
-->
<template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules" 
      :label-width="82"
    >
      <el-form-item label="返场更换或者维修" prop="files">
        <el-upload
          drag
          :action="uploadUrl"
          accept="application/pdf,image/*"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          :headers="headers"
          :file-list="fileList"
          style="width: 100%;"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            拖拽材料到此处或点击上传
          </div>
          <el-button type="primary" round class='up-button'>上传</el-button>
          <div class="el-upload__tip" slot="tip">
            支持不超过20MB的pdf文件或者图片
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注" prop="remark" required>
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注">
        </el-input>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="handleClose">返回</el-button>
        <el-button type="primary" @click="saveHandle"> 确定 </el-button>
      </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/auth"
import useUserStore from "@/store/modules/user";
import { saveSysAttach, delSysAttach } from '@/api/resourcemgr/common';
import { complete, rejectTask, getNextNodeList } from "@/api/flowable/todo";
import { nodePersonList } from "@/api/resourcemgr/flowMgr";

const userStore = useUserStore();
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址

const headers = ref({
  Authorization: "Bearer " + getToken(),
})

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  procInsId: {
    type: String,
    default: "",
  },
  taskId: {
    type: String,
    default: "",
  },
  executionId: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["refresh", "cancal"]);
const formData = ref({});

const formRef = ref(null);

const record = ref(props.record);

const selections = ref('');

// 文件列表
const fileList = ref([]);

const formRules = {
  remark: [{ required: true, message: "请输入处理备注", trigger: "blur" }],
};

const handleClose = () => {
  emit("cancal");
};

const saveHandle = async () => {
  // 表单验证
  const isValidate = await formRef.value?.validate() || false;
  if (!isValidate) return;
  const { code: nodeCode, data: nodeList, msg: nodeMsg } = await getNextNodeList({
    taskId: props.taskId,
    variables: {
    },
  });
  if (nodeCode !== 200) {
    ElMessage.error(nodeMsg);
    return;
  }
  const nextNode = nodeList?.[0].id;
  const { data: persons } = await nodePersonList({resourceId: record.value.resourceId, resourceType: record.value.resourceType, nodeId: nextNode});
  const taskHandle = persons.map(t => t.userId).join(',');
  // const { data } = await nodePersonList({resourceId: record.value.resourceId, resourceType: record.value.resourceType, nodeId: record.value.nodeKey});
  // const taskHandle = data.map(t => t.userId).join(',');

  let attachList = [];
  fileList.value.forEach(t => {
    attachList.push({
      attachId: t.uid,
      attachPath: t.fullPath,
      attachName: t.name,
    })
  });

  const curFormData = {
      ...record.value,
      resourceId: record.value.resourceId,
      resourceType: record.value.resourceType,
      taskName: record.value.name,
      processKey: record.value.flowKey,
      nodeKey: record.value.nodeKey,
      node4Data: {
        remark: formData.value.remark,
        files: attachList,
      }
  }

  const params = {
    comment: formData.value.remark,
    multiple: false,
    procInsId: props.procInsId,
    instanceId: props.procInsId,
    deployId: null,
    taskId: props.taskId,
    targetKey: "",
    executionId: props.executionId,
    variables: {
      ...curFormData,
      taskHandle,
      formData: JSON.stringify(curFormData),
      tenantId: '1',
      userId: userStore.id,
      userName: userStore.name,
      nickName: userStore.nickName,
    },
  };

  const { code, msg } = await complete(params);
  if (code !== 200) {
    ElMessage.error(msg);
    return;
  }

  ElMessage.success("处理成功");
  emit("refresh");
};

const initSysAttach = (params, callback) => {
  saveSysAttach(params).then(res => {
    const { data } = res;
    callback(data)
  });
}

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 允许的文件类型：PDF、JPG、PNG、GIF、WebP、SVG
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ];

  // 检查文件类型
  const isValidType = allowedTypes.includes(file.type);
  
  // 检查文件大小（20MB限制）
  const isLt20M = file.size / 1024 / 1024 < 20;

  // 错误处理
  if (!isValidType) {
    ElMessage.error('请上传 PDF 或图片文件（支持 JPG、PNG、GIF、WebP、SVG）！');
    return false;
  }

  if (!isLt20M) {
    ElMessage.error('文件大小不能超过20MB！');
    return false;
  }

  // 验证通过
  return true;
}

// 上传成功处理
function handleUploadSuccess(res, file) {
  const { code, data } = res;
  if (code == 200) {
    const { fullPath, name } = data;
    initSysAttach({attachName: name, attachPath: fullPath, attachType: '2'}, (attachId) => {
      file.uid = attachId;
      file.fullPath = fullPath;
      file.name = name;
      fileList.value.push(file)
    });
  } else {
    ElMessage.error("附件插入失败")
  }
}

// 上传失败处理
function handleUploadError() {
  ElMessage.error("附件插入失败")
}

function handleRemove(val) {
  const attachId = val.uid;
  delSysAttach({ attachId }).then(res => {
    const { code } = res;
    if (code == 200) {
      ElMessage.success("附件删除成功")
    }
  })
}

watch(
  () => props.record,
  (val) => {
    console.log(val);
    record.value = val;
    selections.value = (val.selections || []).map(t => t.deviceName).join(',');
  },
  {
    immediate: true,
    deep: true,
  }
);

onMounted(() => {
});
</script>

<style lang="scss" scoped>
  .dialog-footer{
    text-align: right;
  }
</style>