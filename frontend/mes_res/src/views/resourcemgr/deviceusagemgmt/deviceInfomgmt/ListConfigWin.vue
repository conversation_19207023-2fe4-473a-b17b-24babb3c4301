<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-06 09:26:19
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-22 17:56:50
 * @FilePath     :  / src / views / resourcemgr / deviceusagemgmt / deviceInfomgmt / ListConfigWin.vue
-->
<template>
  <el-dialog
    :model-value="visible"
    @close="handleClose"
    width="800"
    align-center
  >
    <el-row>
      <Title titleName="默认属性" />
    </el-row>
    <el-checkbox-group v-model="checkIds" class="list-confg-checkbox-group" @change="handleChangeCheck">
      <el-checkbox v-for="check in checkList" :label="check.id" :key="check.id">{{check.displayName}}</el-checkbox>
    </el-checkbox-group>
    <el-row class="mt20">
      <Title titleName="可选属性" />
    </el-row>
    <el-checkbox-group v-model="unCheckList" class="list-confg-checkbox-group" @change="handleUnChangeCheck">
      <el-checkbox v-for="check in unCheckList" :label="check.id" :key="check.id">{{check.displayName}}</el-checkbox>
    </el-checkbox-group>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" plain>取消</el-button>
        <el-button type="primary" @click="saveHandle"> 设置 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, defineComponent, toRaw } from "vue";
import useUserStore from "@/store/modules/user";
import { ElMessage } from "element-plus";
import Title from '@/components/Title';
import { personDisplayConfig, savePersonDisplayConfig } from '@/api/resourcemgr/common';

defineComponent({
  Title,
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  monitoringElement: {
    type: String,
    default: "",
  },
});

const userStore = useUserStore()

const emit = defineEmits(['update:visible', 'refreshData']);
const loading = ref(true)
const checkIds = ref([])
const checkList = ref([])
const unCheckList = ref([]);
const editList = ref([]);
const defaultList = ref([]);

const handleClose = () => {
  emit("update:visible", false);
};

const saveHandle = () => {
  savePersonDisplayConfig({configList: toRaw(editList.value)}).then(res => {
    const { code } = res;
    if (code === 200) {
      ElMessage.success("设置成功");
      emit("update:visible", false);
      emit("refreshData");
    } else {
      ElMessage.error("设置失败");
    }
  })
};
const handleUnChangeCheck = (checkIds) => {
  editList.value = editList.value.map(val => ({
    ...val,
    isVisible: checkIds.includes(val.id) ? 1 : val.isVisible
  }))
  dealCheckList();
}

const handleChangeCheck = (checkIds) => {
  editList.value = editList.value.map(val => ({
    ...val,
    isVisible: checkIds.includes(val.id)
  }))
  dealCheckList();
}

const getConfig = () => {
  const personId = userStore.id;
  loading.value = true;
  personDisplayConfig({monitoringElement: props.monitoringElement, resourceType: 'device', personId: 'default'}).then(res => {
    const { data } = res;
    const dataList = data || [];
    defaultList.value = dataList;
    editList.value = dataList;
    loading.value = false;
    dealCheckList();
  })
}

const dealCheckList = () => {
  checkList.value = editList.value.filter(t => t.isVisible);
  checkIds.value =  checkList.value.map(t => t.id);
  unCheckList.value = editList.value.filter(t => !t.isVisible);
}

onMounted(() => {
  getConfig();
});

</script>
<style lang="scss" scoped>
.list-confg-checkbox-group{
  :deep(.el-checkbox) {
    margin-right: 0;
    width: 20%;
  }
  :deep(.el-checkbox__input.is-checked+.el-checkbox__label){
    color: #606266;
  }
}
.edit-parameter-win-moniParam-transfer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 220px;
  .table-container {
    width: calc(50% - 30px);
  }
  .edit-parameter-win-moniParam-transfer-btns {
    display: flex;
    flex-flow: column;
    align-items: center;
    width: 50px;
    row-gap: 10px;
    :deep(.el-button + .el-button) {
      margin-left: 0;
    }
  }
}
</style>