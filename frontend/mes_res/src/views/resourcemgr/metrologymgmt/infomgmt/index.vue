<!--
 * @Description  : 
 * <AUTHOR> wnj
 * @Date         : 2025-06-30 16:36:25
 * @LastEditors  : wnj
 * @LastEditTime : 2025-07-26 16:24:39
 * @FilePath     :  / src / views / resourcemgr / metrologymgmt / infomgmt / index.vue
-->
<!-- 点位管理 -->
<template>
  <div class="mgmt-container">
    <el-form ref="formRef" label-width="100px">
      <el-row justify="space-between">
        <el-col :span="8">
          <el-form-item label="仪器名称">
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入"
              clearable
              style="width: 300px"
              >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仪器类别">
            <el-select
              v-model="queryParams.deviceTypeLevel3"
              placeholder="请选择"
              clearable
              filterable
              style="width: 300px"
            >
              <el-option
                v-for="item in device_type_level3"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
       
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仪器型号">
            <CustomSearchSelect
              v-model="queryParams.deviceModelId"
              :fetch-api="fetchModelData"
              placeholder="请选择"
              clearable
              style="width: 300px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row justify="space-between">
        <el-col :span="8">
        </el-col>
        <el-col :span="8">
        </el-col>
        <el-col :span="8" style="width: 400px; display: flex; justify-content: flex-end">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-divider />

    <el-row :gutter="10" justify="space-between">
        <el-col :span="5">
          <Title titleName="计量管理列表" />
        </el-col>
        <el-col :span="19" class="row-butons">
          <el-button
              type="primary"
              icon="Download"
              @click="handleExport"
              v-hasPermi="['system:dict:export']"
          >导出</el-button>
        </el-col>
    </el-row>
    <div class="parameter-bottom">
      <div class="table-content">
        <el-table v-loading="loading" 
          :data="dataList" 
          style="width: 100%" 
          height="100%"
         >
          <el-table-column 
            v-for="item in tableHeaderList" 
            :label="item.label"
            :prop="item.prop"
            align="center"
            show-overflow-tooltip
          >
           <!-- 字段转译 -->
            <template #default="scope">
              <dict-tag v-if="item.prop === 'deviceStatus'" :options="devc_status" :value="scope.row[item.prop]" />
              <dict-tag v-else-if="item.prop === 'deviceLevel'" :options="device_level" :value="scope.row[item.prop]" />
              <dict-tag v-else-if="item.prop === 'calibrationCycle'" :options="calibration_cycle" :value="scope.row[item.prop]" />
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right">
            <template #default="scope">
              <el-button type="text">
                <router-link :to="'/resourcemgr/metrologymgmt/details/' + scope.row.deviceSn">
                  详情
                </router-link>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-content">
        <el-pagination
          v-model:current-page="pageInfo.currentPage"
          v-model:page-size="pageInfo.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="pageInfo.total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

  </div>
</template>

<script setup name="Metrologyinfomgmt">
import { reactive, onMounted, defineComponent, toRaw } from "vue";
import Title from '@/components/Title';
import CustomSearchSelect from '@/components/CustomSearchSelect';
import { calibrationPage } from '@/api/resourcemgr/metrologyMgr';
import { deviceModelList } from '@/api/resourcemgr/deviceMgr';
const { proxy } = getCurrentInstance()
const { device_type_level3, devc_status, device_level, calibration_cycle  } = proxy.useBusDict("device_type_level3", "devc_status", "device_level", "calibration_cycle");

defineComponent({
  Title,
  CustomSearchSelect
});

// 动态配置列表
const tableHeaderList = [
    {
        label: '仪器类别',
        prop: 'deviceTypeLevel3Name',
    },
    {
        label: '仪器名称',
        prop: 'deviceName'
    },
    {
        label: '所属部门',
        prop: 'operationUnitIdName'
    },
    {
        label: '生产厂家',
        prop: 'deviceBrandIdName'
    },
    {
        label: '设备型号',
        prop: 'deviceModelIdName'
    },
    {
        label: '仪器状态',
        prop: 'deviceStatus',
    },
    {
        label: '仪器标准等级',
        prop: 'deviceLevel',
    },
    {
        label: '上级仪器',
        prop: 'parentDeviceSnName'
    },
    {
        label: '检定时间',
        prop: 'calibrationDate',
    },
    {
        label: '检定计划',
        prop: 'calibrationCycle',
    },
    
];

const queryParams = ref({
  deviceName: "", 
  deviceTypeLevel3: "",
  deviceModelId: "", 
});

// 配置列表数据
const dataList = ref([]);
const pageInfo = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const loading = ref(true)

// 新增、修改
const isDialogEdit = ref(true);
const isDialogEditVisible = ref(false);
const dialogEditRecord = ref({});

const getTableData = () => {
  loading.value = true;
  calibrationPage(toRaw({...queryParams.value, pageSize: pageInfo.pageSize, pageNum: pageInfo.currentPage})).then(res => {
    const { rows, total } = res;
    dataList.value = rows || [];
    pageInfo.total = total || 0;
    loading.value = false;
  })
};

const handleSearch = () => {
  pageInfo.currentPage = 1;
  getTableData();
};

const handleReset = () => {
  queryParams.value.deviceName = "";
  queryParams.value.deviceTypeLevel3 = "";
  queryParams.value.deviceModelId = "";
  handleSearch();
};

const handleCurrentChange = (currentPage) => {
  pageInfo.currentPage = currentPage;
  getTableData();
};

const handleAdd = () => {
  isDialogEdit.value = false;
  isDialogEditVisible.value = true;
  dialogEditRecord.value = {};
};

const handleUpdate = (row) => {
  dialogEditRecord.value = {...row}
  isDialogEdit.value = true;
  isDialogEditVisible.value = true;
}

/**
 * 异步获取模型数据
 *
 * @param keyword 模型名称关键词
 * @param page 页码
 * @param pageSize 每页显示条数
 * @returns 返回包含模型数据列表和是否还有更多数据的对象
 * @throws 如果获取数据失败，则抛出错误
 */
 const fetchModelData = async (keyword, page, pageSize) => {
  try {
    const res = await deviceModelList({modelName: keyword, pageSize: pageSize, pageNum: page})
    const { data } = res;
    const reruenData = data.data;
    const list = reruenData.map(item => ({
      value: item.modelId,
      label: item.modelName
    }));

    const hasMore = page * pageSize < data.totalRecords;
    return { list, hasMore };
  } catch (error) {
    throw new Error('获取设备类型数据失败');
  }
};

const handleExport = () => {
  proxy.download(`${import.meta.env.VITE_REQUEST_HIGH_PREFIX}/calibrationHistory/export`,
    toRaw(queryParams.value)
  , `计量管理信息_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getTableData();
});

</script>

<style scoped lang="scss">
  .mgmt-container {
    width: calc(100% - 48px);
    height: calc(100% - 48px);
    position: absolute;
    left: 24px;
    top: 24px;
    background: #fff;
    border-radius: 16px;
    padding: 24px 24px 0 24px;
    display: flex;
    flex-direction: column;
  
    .row-butons{
      display: flex;
      justify-content: end;
    }
    .el-col-8{
      flex: initial;
    }

    .parameter-bottom {
      flex: 1;
      height: calc(100% - 240px);
      position: relative;
      .table-content {
        height: calc(100% - 56px);
      }
      .page-content {
        height: 56px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: end;
      }
    }
  }
</style>

