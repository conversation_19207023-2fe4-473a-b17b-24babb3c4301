import{_ as h,r as n,w as E,e as c,j as g,o as l,i,h as e,c as m,F as b,G as y,t as r,f as B,n as C}from"./index-Cl6Blbqy.js";const k={class:"base-info-card"},x={class:"label"},D={class:"value"},w={class:"dialog-footer"},F={__name:"DetailWin",props:{visible:{type:Boolean,default:!1},record:{type:Object,default:()=>({})}},emits:["update:visible"],setup(d,{emit:N}){const p=d,u=n([{fieldId:"monitoringElement",fieldName:"监测要素",fieldNameEn:"monitoringElementName"},{fieldId:"plateNo",fieldName:"车牌号",fieldNameEn:"plateNo"},{fieldId:"vehicleType",fieldName:"车辆类型",fieldNameEn:"vehicleTypeName"},{fieldId:"vehicleBrand",fieldName:"车辆品牌",fieldNameEn:"vehicleBrand"},{fieldId:"vehicleModel",fieldName:"车辆型号",fieldNameEn:"vehicleModel"},{fieldId:"officeId",fieldName:"所属办事处",fieldNameEn:"officeId"},{fieldId:"officeName",fieldName:"办事处名称",fieldNameEn:"officeName"},{fieldId:"officeAddr",fieldName:"办事处位置",fieldNameEn:"officeAddr"},{fieldId:"orgId",fieldName:"所属公司",fieldNameEn:"orgId"},{fieldId:"orgName",fieldName:"所属公司名称",fieldNameEn:"orgName"},{fieldId:"vehicleStatus",fieldName:"车辆状态",fieldNameEn:"vehicleStatusName"},{fieldId:"purchaseDate",fieldName:"采购日期",fieldNameEn:"purchaseDate"},{fieldId:"serviceLife",fieldName:"使用年限(年)",fieldNameEn:"serviceLife"},{fieldId:"capacity",fieldName:"容量(升)",fieldNameEn:"capacity"},{fieldId:"personInCharge",fieldName:"车辆负责人",fieldNameEn:"personInCharge"}]),v=N,t=n({});E(()=>p.record,f=>{t.value={...f}},{immediate:!0,deep:!0});const o=()=>{v("update:visible",!1)};return(f,s)=>{const _=c("el-button"),I=c("el-dialog");return l(),g(I,{"model-value":d.visible,title:"车辆信息",onClose:o,width:"800","align-center":"",class:"detail-key-dialog"},{footer:i(()=>[e("div",w,[B(_,{onClick:o},{default:i(()=>s[0]||(s[0]=[C("关闭")])),_:1,__:[0]})])]),default:i(()=>[e("div",k,[(l(!0),m(b,null,y(u.value,a=>(l(),m("div",{class:"info-item",key:a.fieldId},[e("span",x,r(a.fieldName)+"：",1),e("span",D,r(t.value[a.fieldNameEn]||""),1)]))),128))])]),_:1},8,["model-value"])}}},S=h(F,[["__scopeId","data-v-5865883c"]]);export{S as default};
