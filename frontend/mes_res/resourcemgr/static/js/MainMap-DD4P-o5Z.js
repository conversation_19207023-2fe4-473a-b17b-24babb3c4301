import{L as v}from"./namespace-BzCHtBJt.js";import{a2 as A,_ as Z,r as S,B as I,D as T,w as N,e as P,c as E,o as U,h as O,f as C,i as x,n as b}from"./index-Cl6Blbqy.js";var G={exports:{}};(function(w,M){(function(c,p){p(M)})(A,function(c){var p=L.MarkerClusterGroup=L.FeatureGroup.extend({options:{maxClusterRadius:80,iconCreateFunction:null,clusterPane:L.Marker.prototype.options.pane,spiderfyOnEveryZoom:!1,spiderfyOnMaxZoom:!0,showCoverageOnHover:!0,zoomToBoundsOnClick:!0,singleMarkerMode:!1,disableClusteringAtZoom:null,removeOutsideVisibleBounds:!0,animate:!0,animateAddingMarkers:!1,spiderfyShapePositions:null,spiderfyDistanceMultiplier:1,spiderLegPolylineOptions:{weight:1.5,color:"#222",opacity:.5},chunkedLoading:!1,chunkInterval:200,chunkDelay:50,chunkProgress:null,polygonOptions:{}},initialize:function(e){L.Util.setOptions(this,e),this.options.iconCreateFunction||(this.options.iconCreateFunction=this._defaultIconCreateFunction),this._featureGroup=L.featureGroup(),this._featureGroup.addEventParent(this),this._nonPointGroup=L.featureGroup(),this._nonPointGroup.addEventParent(this),this._inZoomAnimation=0,this._needsClustering=[],this._needsRemoving=[],this._currentShownBounds=null,this._queue=[],this._childMarkerEventHandlers={dragstart:this._childMarkerDragStart,move:this._childMarkerMoved,dragend:this._childMarkerDragEnd};var t=L.DomUtil.TRANSITION&&this.options.animate;L.extend(this,t?this._withAnimation:this._noAnimation),this._markerCluster=t?L.MarkerCluster:L.MarkerClusterNonAnimated},addLayer:function(e){if(e instanceof L.LayerGroup)return this.addLayers([e]);if(!e.getLatLng)return this._nonPointGroup.addLayer(e),this.fire("layeradd",{layer:e}),this;if(!this._map)return this._needsClustering.push(e),this.fire("layeradd",{layer:e}),this;if(this.hasLayer(e))return this;this._unspiderfy&&this._unspiderfy(),this._addLayer(e,this._maxZoom),this.fire("layeradd",{layer:e}),this._topClusterLevel._recalculateBounds(),this._refreshClustersIcons();var t=e,i=this._zoom;if(e.__parent)for(;t.__parent._zoom>=i;)t=t.__parent;return this._currentShownBounds.contains(t.getLatLng())&&(this.options.animateAddingMarkers?this._animationAddLayer(e,t):this._animationAddLayerNonAnimated(e,t)),this},removeLayer:function(e){return e instanceof L.LayerGroup?this.removeLayers([e]):e.getLatLng?this._map?e.__parent?(this._unspiderfy&&(this._unspiderfy(),this._unspiderfyLayer(e)),this._removeLayer(e,!0),this.fire("layerremove",{layer:e}),this._topClusterLevel._recalculateBounds(),this._refreshClustersIcons(),e.off(this._childMarkerEventHandlers,this),this._featureGroup.hasLayer(e)&&(this._featureGroup.removeLayer(e),e.clusterShow&&e.clusterShow()),this):this:(!this._arraySplice(this._needsClustering,e)&&this.hasLayer(e)&&this._needsRemoving.push({layer:e,latlng:e._latlng}),this.fire("layerremove",{layer:e}),this):(this._nonPointGroup.removeLayer(e),this.fire("layerremove",{layer:e}),this)},addLayers:function(e,t){if(!L.Util.isArray(e))return this.addLayer(e);var i=this._featureGroup,n=this._nonPointGroup,s=this.options.chunkedLoading,a=this.options.chunkInterval,r=this.options.chunkProgress,u=e.length,o=0,h=!0,l;if(this._map){var d=new Date().getTime(),_=L.bind(function(){var m=new Date().getTime();for(this._map&&this._unspiderfy&&this._unspiderfy();o<u;o++){if(s&&o%200===0){var k=new Date().getTime()-m;if(k>a)break}if(l=e[o],l instanceof L.LayerGroup){h&&(e=e.slice(),h=!1),this._extractNonGroupLayers(l,e),u=e.length;continue}if(!l.getLatLng){n.addLayer(l),t||this.fire("layeradd",{layer:l});continue}if(!this.hasLayer(l)&&(this._addLayer(l,this._maxZoom),t||this.fire("layeradd",{layer:l}),l.__parent&&l.__parent.getChildCount()===2)){var g=l.__parent.getAllChildMarkers(),B=g[0]===l?g[1]:g[0];i.removeLayer(B)}}r&&r(o,u,new Date().getTime()-d),o===u?(this._topClusterLevel._recalculateBounds(),this._refreshClustersIcons(),this._topClusterLevel._recursivelyAddChildrenToMap(null,this._zoom,this._currentShownBounds)):setTimeout(_,this.options.chunkDelay)},this);_()}else for(var f=this._needsClustering;o<u;o++){if(l=e[o],l instanceof L.LayerGroup){h&&(e=e.slice(),h=!1),this._extractNonGroupLayers(l,e),u=e.length;continue}if(!l.getLatLng){n.addLayer(l);continue}this.hasLayer(l)||f.push(l)}return this},removeLayers:function(e){var t,i,n=e.length,s=this._featureGroup,a=this._nonPointGroup,r=!0;if(!this._map){for(t=0;t<n;t++){if(i=e[t],i instanceof L.LayerGroup){r&&(e=e.slice(),r=!1),this._extractNonGroupLayers(i,e),n=e.length;continue}this._arraySplice(this._needsClustering,i),a.removeLayer(i),this.hasLayer(i)&&this._needsRemoving.push({layer:i,latlng:i._latlng}),this.fire("layerremove",{layer:i})}return this}if(this._unspiderfy){this._unspiderfy();var u=e.slice(),o=n;for(t=0;t<o;t++){if(i=u[t],i instanceof L.LayerGroup){this._extractNonGroupLayers(i,u),o=u.length;continue}this._unspiderfyLayer(i)}}for(t=0;t<n;t++){if(i=e[t],i instanceof L.LayerGroup){r&&(e=e.slice(),r=!1),this._extractNonGroupLayers(i,e),n=e.length;continue}if(!i.__parent){a.removeLayer(i),this.fire("layerremove",{layer:i});continue}this._removeLayer(i,!0,!0),this.fire("layerremove",{layer:i}),s.hasLayer(i)&&(s.removeLayer(i),i.clusterShow&&i.clusterShow())}return this._topClusterLevel._recalculateBounds(),this._refreshClustersIcons(),this._topClusterLevel._recursivelyAddChildrenToMap(null,this._zoom,this._currentShownBounds),this},clearLayers:function(){return this._map||(this._needsClustering=[],this._needsRemoving=[],delete this._gridClusters,delete this._gridUnclustered),this._noanimationUnspiderfy&&this._noanimationUnspiderfy(),this._featureGroup.clearLayers(),this._nonPointGroup.clearLayers(),this.eachLayer(function(e){e.off(this._childMarkerEventHandlers,this),delete e.__parent},this),this._map&&this._generateInitialClusters(),this},getBounds:function(){var e=new L.LatLngBounds;this._topClusterLevel&&e.extend(this._topClusterLevel._bounds);for(var t=this._needsClustering.length-1;t>=0;t--)e.extend(this._needsClustering[t].getLatLng());return e.extend(this._nonPointGroup.getBounds()),e},eachLayer:function(e,t){var i=this._needsClustering.slice(),n=this._needsRemoving,s,a,r;for(this._topClusterLevel&&this._topClusterLevel.getAllChildMarkers(i),a=i.length-1;a>=0;a--){for(s=!0,r=n.length-1;r>=0;r--)if(n[r].layer===i[a]){s=!1;break}s&&e.call(t,i[a])}this._nonPointGroup.eachLayer(e,t)},getLayers:function(){var e=[];return this.eachLayer(function(t){e.push(t)}),e},getLayer:function(e){var t=null;return e=parseInt(e,10),this.eachLayer(function(i){L.stamp(i)===e&&(t=i)}),t},hasLayer:function(e){if(!e)return!1;var t,i=this._needsClustering;for(t=i.length-1;t>=0;t--)if(i[t]===e)return!0;for(i=this._needsRemoving,t=i.length-1;t>=0;t--)if(i[t].layer===e)return!1;return!!(e.__parent&&e.__parent._group===this)||this._nonPointGroup.hasLayer(e)},zoomToShowLayer:function(e,t){var i=this._map;typeof t!="function"&&(t=function(){});var n=function(){(i.hasLayer(e)||i.hasLayer(e.__parent))&&!this._inZoomAnimation&&(this._map.off("moveend",n,this),this.off("animationend",n,this),i.hasLayer(e)?t():e.__parent._icon&&(this.once("spiderfied",t,this),e.__parent.spiderfy()))};e._icon&&this._map.getBounds().contains(e.getLatLng())?t():e.__parent._zoom<Math.round(this._map._zoom)?(this._map.on("moveend",n,this),this._map.panTo(e.getLatLng())):(this._map.on("moveend",n,this),this.on("animationend",n,this),e.__parent.zoomToBounds())},onAdd:function(e){this._map=e;var t,i,n;if(!isFinite(this._map.getMaxZoom()))throw"Map has no maxZoom specified";for(this._featureGroup.addTo(e),this._nonPointGroup.addTo(e),this._gridClusters||this._generateInitialClusters(),this._maxLat=e.options.crs.projection.MAX_LATITUDE,t=0,i=this._needsRemoving.length;t<i;t++)n=this._needsRemoving[t],n.newlatlng=n.layer._latlng,n.layer._latlng=n.latlng;for(t=0,i=this._needsRemoving.length;t<i;t++)n=this._needsRemoving[t],this._removeLayer(n.layer,!0),n.layer._latlng=n.newlatlng;this._needsRemoving=[],this._zoom=Math.round(this._map._zoom),this._currentShownBounds=this._getExpandedVisibleBounds(),this._map.on("zoomend",this._zoomEnd,this),this._map.on("moveend",this._moveEnd,this),this._spiderfierOnAdd&&this._spiderfierOnAdd(),this._bindEvents(),i=this._needsClustering,this._needsClustering=[],this.addLayers(i,!0)},onRemove:function(e){e.off("zoomend",this._zoomEnd,this),e.off("moveend",this._moveEnd,this),this._unbindEvents(),this._map._mapPane.className=this._map._mapPane.className.replace(" leaflet-cluster-anim",""),this._spiderfierOnRemove&&this._spiderfierOnRemove(),delete this._maxLat,this._hideCoverage(),this._featureGroup.remove(),this._nonPointGroup.remove(),this._featureGroup.clearLayers(),this._map=null},getVisibleParent:function(e){for(var t=e;t&&!t._icon;)t=t.__parent;return t||null},_arraySplice:function(e,t){for(var i=e.length-1;i>=0;i--)if(e[i]===t)return e.splice(i,1),!0},_removeFromGridUnclustered:function(e,t){for(var i=this._map,n=this._gridUnclustered,s=Math.floor(this._map.getMinZoom());t>=s&&n[t].removeObject(e,i.project(e.getLatLng(),t));t--);},_childMarkerDragStart:function(e){e.target.__dragStart=e.target._latlng},_childMarkerMoved:function(e){if(!this._ignoreMove&&!e.target.__dragStart){var t=e.target._popup&&e.target._popup.isOpen();this._moveChild(e.target,e.oldLatLng,e.latlng),t&&e.target.openPopup()}},_moveChild:function(e,t,i){e._latlng=t,this.removeLayer(e),e._latlng=i,this.addLayer(e)},_childMarkerDragEnd:function(e){var t=e.target.__dragStart;delete e.target.__dragStart,t&&this._moveChild(e.target,t,e.target._latlng)},_removeLayer:function(e,t,i){var n=this._gridClusters,s=this._gridUnclustered,a=this._featureGroup,r=this._map,u=Math.floor(this._map.getMinZoom());t&&this._removeFromGridUnclustered(e,this._maxZoom);var o=e.__parent,h=o._markers,l;for(this._arraySplice(h,e);o&&(o._childCount--,o._boundsNeedUpdate=!0,!(o._zoom<u));)t&&o._childCount<=1?(l=o._markers[0]===e?o._markers[1]:o._markers[0],n[o._zoom].removeObject(o,r.project(o._cLatLng,o._zoom)),s[o._zoom].addObject(l,r.project(l.getLatLng(),o._zoom)),this._arraySplice(o.__parent._childClusters,o),o.__parent._markers.push(l),l.__parent=o.__parent,o._icon&&(a.removeLayer(o),i||a.addLayer(l))):o._iconNeedsUpdate=!0,o=o.__parent;delete e.__parent},_isOrIsParent:function(e,t){for(;t;){if(e===t)return!0;t=t.parentNode}return!1},fire:function(e,t,i){if(t&&t.layer instanceof L.MarkerCluster){if(t.originalEvent&&this._isOrIsParent(t.layer._icon,t.originalEvent.relatedTarget))return;e="cluster"+e}L.FeatureGroup.prototype.fire.call(this,e,t,i)},listens:function(e,t){return L.FeatureGroup.prototype.listens.call(this,e,t)||L.FeatureGroup.prototype.listens.call(this,"cluster"+e,t)},_defaultIconCreateFunction:function(e){var t=e.getChildCount(),i=" marker-cluster-";return t<10?i+="small":t<100?i+="medium":i+="large",new L.DivIcon({html:"<div><span>"+t+"</span></div>",className:"marker-cluster"+i,iconSize:new L.Point(40,40)})},_bindEvents:function(){var e=this._map,t=this.options.spiderfyOnMaxZoom,i=this.options.showCoverageOnHover,n=this.options.zoomToBoundsOnClick,s=this.options.spiderfyOnEveryZoom;(t||n||s)&&this.on("clusterclick clusterkeypress",this._zoomOrSpiderfy,this),i&&(this.on("clustermouseover",this._showCoverage,this),this.on("clustermouseout",this._hideCoverage,this),e.on("zoomend",this._hideCoverage,this))},_zoomOrSpiderfy:function(e){var t=e.layer,i=t;if(!(e.type==="clusterkeypress"&&e.originalEvent&&e.originalEvent.keyCode!==13)){for(;i._childClusters.length===1;)i=i._childClusters[0];i._zoom===this._maxZoom&&i._childCount===t._childCount&&this.options.spiderfyOnMaxZoom?t.spiderfy():this.options.zoomToBoundsOnClick&&t.zoomToBounds(),this.options.spiderfyOnEveryZoom&&t.spiderfy(),e.originalEvent&&e.originalEvent.keyCode===13&&this._map._container.focus()}},_showCoverage:function(e){var t=this._map;this._inZoomAnimation||(this._shownPolygon&&t.removeLayer(this._shownPolygon),e.layer.getChildCount()>2&&e.layer!==this._spiderfied&&(this._shownPolygon=new L.Polygon(e.layer.getConvexHull(),this.options.polygonOptions),t.addLayer(this._shownPolygon)))},_hideCoverage:function(){this._shownPolygon&&(this._map.removeLayer(this._shownPolygon),this._shownPolygon=null)},_unbindEvents:function(){var e=this.options.spiderfyOnMaxZoom,t=this.options.showCoverageOnHover,i=this.options.zoomToBoundsOnClick,n=this.options.spiderfyOnEveryZoom,s=this._map;(e||i||n)&&this.off("clusterclick clusterkeypress",this._zoomOrSpiderfy,this),t&&(this.off("clustermouseover",this._showCoverage,this),this.off("clustermouseout",this._hideCoverage,this),s.off("zoomend",this._hideCoverage,this))},_zoomEnd:function(){this._map&&(this._mergeSplitClusters(),this._zoom=Math.round(this._map._zoom),this._currentShownBounds=this._getExpandedVisibleBounds())},_moveEnd:function(){if(!this._inZoomAnimation){var e=this._getExpandedVisibleBounds();this._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds,Math.floor(this._map.getMinZoom()),this._zoom,e),this._topClusterLevel._recursivelyAddChildrenToMap(null,Math.round(this._map._zoom),e),this._currentShownBounds=e}},_generateInitialClusters:function(){var e=Math.ceil(this._map.getMaxZoom()),t=Math.floor(this._map.getMinZoom()),i=this.options.maxClusterRadius,n=i;typeof i!="function"&&(n=function(){return i}),this.options.disableClusteringAtZoom!==null&&(e=this.options.disableClusteringAtZoom-1),this._maxZoom=e,this._gridClusters={},this._gridUnclustered={};for(var s=e;s>=t;s--)this._gridClusters[s]=new L.DistanceGrid(n(s)),this._gridUnclustered[s]=new L.DistanceGrid(n(s));this._topClusterLevel=new this._markerCluster(this,t-1)},_addLayer:function(e,t){var i=this._gridClusters,n=this._gridUnclustered,s=Math.floor(this._map.getMinZoom()),a,r;for(this.options.singleMarkerMode&&this._overrideMarkerIcon(e),e.on(this._childMarkerEventHandlers,this);t>=s;t--){a=this._map.project(e.getLatLng(),t);var u=i[t].getNearObject(a);if(u){u._addChild(e),e.__parent=u;return}if(u=n[t].getNearObject(a),u){var o=u.__parent;o&&this._removeLayer(u,!1);var h=new this._markerCluster(this,t,u,e);i[t].addObject(h,this._map.project(h._cLatLng,t)),u.__parent=h,e.__parent=h;var l=h;for(r=t-1;r>o._zoom;r--)l=new this._markerCluster(this,r,l),i[r].addObject(l,this._map.project(u.getLatLng(),r));o._addChild(l),this._removeFromGridUnclustered(u,t);return}n[t].addObject(e,a)}this._topClusterLevel._addChild(e),e.__parent=this._topClusterLevel},_refreshClustersIcons:function(){this._featureGroup.eachLayer(function(e){e instanceof L.MarkerCluster&&e._iconNeedsUpdate&&e._updateIcon()})},_enqueue:function(e){this._queue.push(e),this._queueTimeout||(this._queueTimeout=setTimeout(L.bind(this._processQueue,this),300))},_processQueue:function(){for(var e=0;e<this._queue.length;e++)this._queue[e].call(this);this._queue.length=0,clearTimeout(this._queueTimeout),this._queueTimeout=null},_mergeSplitClusters:function(){var e=Math.round(this._map._zoom);this._processQueue(),this._zoom<e&&this._currentShownBounds.intersects(this._getExpandedVisibleBounds())?(this._animationStart(),this._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds,Math.floor(this._map.getMinZoom()),this._zoom,this._getExpandedVisibleBounds()),this._animationZoomIn(this._zoom,e)):this._zoom>e?(this._animationStart(),this._animationZoomOut(this._zoom,e)):this._moveEnd()},_getExpandedVisibleBounds:function(){if(this.options.removeOutsideVisibleBounds){if(L.Browser.mobile)return this._checkBoundsMaxLat(this._map.getBounds())}else return this._mapBoundsInfinite;return this._checkBoundsMaxLat(this._map.getBounds().pad(1))},_checkBoundsMaxLat:function(e){var t=this._maxLat;return t!==void 0&&(e.getNorth()>=t&&(e._northEast.lat=1/0),e.getSouth()<=-t&&(e._southWest.lat=-1/0)),e},_animationAddLayerNonAnimated:function(e,t){if(t===e)this._featureGroup.addLayer(e);else if(t._childCount===2){t._addToMap();var i=t.getAllChildMarkers();this._featureGroup.removeLayer(i[0]),this._featureGroup.removeLayer(i[1])}else t._updateIcon()},_extractNonGroupLayers:function(e,t){var i=e.getLayers(),n=0,s;for(t=t||[];n<i.length;n++){if(s=i[n],s instanceof L.LayerGroup){this._extractNonGroupLayers(s,t);continue}t.push(s)}return t},_overrideMarkerIcon:function(e){var t=e.options.icon=this.options.iconCreateFunction({getChildCount:function(){return 1},getAllChildMarkers:function(){return[e]}});return t}});L.MarkerClusterGroup.include({_mapBoundsInfinite:new L.LatLngBounds(new L.LatLng(-1/0,-1/0),new L.LatLng(1/0,1/0))}),L.MarkerClusterGroup.include({_noAnimation:{_animationStart:function(){},_animationZoomIn:function(e,t){this._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds,Math.floor(this._map.getMinZoom()),e),this._topClusterLevel._recursivelyAddChildrenToMap(null,t,this._getExpandedVisibleBounds()),this.fire("animationend")},_animationZoomOut:function(e,t){this._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds,Math.floor(this._map.getMinZoom()),e),this._topClusterLevel._recursivelyAddChildrenToMap(null,t,this._getExpandedVisibleBounds()),this.fire("animationend")},_animationAddLayer:function(e,t){this._animationAddLayerNonAnimated(e,t)}},_withAnimation:{_animationStart:function(){this._map._mapPane.className+=" leaflet-cluster-anim",this._inZoomAnimation++},_animationZoomIn:function(e,t){var i=this._getExpandedVisibleBounds(),n=this._featureGroup,s=Math.floor(this._map.getMinZoom()),a;this._ignoreMove=!0,this._topClusterLevel._recursively(i,e,s,function(r){var u=r._latlng,o=r._markers,h;for(i.contains(u)||(u=null),r._isSingleParent()&&e+1===t?(n.removeLayer(r),r._recursivelyAddChildrenToMap(null,t,i)):(r.clusterHide(),r._recursivelyAddChildrenToMap(u,t,i)),a=o.length-1;a>=0;a--)h=o[a],i.contains(h._latlng)||n.removeLayer(h)}),this._forceLayout(),this._topClusterLevel._recursivelyBecomeVisible(i,t),n.eachLayer(function(r){!(r instanceof L.MarkerCluster)&&r._icon&&r.clusterShow()}),this._topClusterLevel._recursively(i,e,t,function(r){r._recursivelyRestoreChildPositions(t)}),this._ignoreMove=!1,this._enqueue(function(){this._topClusterLevel._recursively(i,e,s,function(r){n.removeLayer(r),r.clusterShow()}),this._animationEnd()})},_animationZoomOut:function(e,t){this._animationZoomOutSingle(this._topClusterLevel,e-1,t),this._topClusterLevel._recursivelyAddChildrenToMap(null,t,this._getExpandedVisibleBounds()),this._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds,Math.floor(this._map.getMinZoom()),e,this._getExpandedVisibleBounds())},_animationAddLayer:function(e,t){var i=this,n=this._featureGroup;n.addLayer(e),t!==e&&(t._childCount>2?(t._updateIcon(),this._forceLayout(),this._animationStart(),e._setPos(this._map.latLngToLayerPoint(t.getLatLng())),e.clusterHide(),this._enqueue(function(){n.removeLayer(e),e.clusterShow(),i._animationEnd()})):(this._forceLayout(),i._animationStart(),i._animationZoomOutSingle(t,this._map.getMaxZoom(),this._zoom)))}},_animationZoomOutSingle:function(e,t,i){var n=this._getExpandedVisibleBounds(),s=Math.floor(this._map.getMinZoom());e._recursivelyAnimateChildrenInAndAddSelfToMap(n,s,t+1,i);var a=this;this._forceLayout(),e._recursivelyBecomeVisible(n,i),this._enqueue(function(){if(e._childCount===1){var r=e._markers[0];this._ignoreMove=!0,r.setLatLng(r.getLatLng()),this._ignoreMove=!1,r.clusterShow&&r.clusterShow()}else e._recursively(n,i,s,function(u){u._recursivelyRemoveChildrenFromMap(n,s,t+1)});a._animationEnd()})},_animationEnd:function(){this._map&&(this._map._mapPane.className=this._map._mapPane.className.replace(" leaflet-cluster-anim","")),this._inZoomAnimation--,this.fire("animationend")},_forceLayout:function(){L.Util.falseFn(document.body.offsetWidth)}}),L.markerClusterGroup=function(e){return new L.MarkerClusterGroup(e)};var y=L.MarkerCluster=L.Marker.extend({options:L.Icon.prototype.options,initialize:function(e,t,i,n){L.Marker.prototype.initialize.call(this,i?i._cLatLng||i.getLatLng():new L.LatLng(0,0),{icon:this,pane:e.options.clusterPane}),this._group=e,this._zoom=t,this._markers=[],this._childClusters=[],this._childCount=0,this._iconNeedsUpdate=!0,this._boundsNeedUpdate=!0,this._bounds=new L.LatLngBounds,i&&this._addChild(i),n&&this._addChild(n)},getAllChildMarkers:function(e,t){e=e||[];for(var i=this._childClusters.length-1;i>=0;i--)this._childClusters[i].getAllChildMarkers(e,t);for(var n=this._markers.length-1;n>=0;n--)t&&this._markers[n].__dragStart||e.push(this._markers[n]);return e},getChildCount:function(){return this._childCount},zoomToBounds:function(e){for(var t=this._childClusters.slice(),i=this._group._map,n=i.getBoundsZoom(this._bounds),s=this._zoom+1,a=i.getZoom(),r;t.length>0&&n>s;){s++;var u=[];for(r=0;r<t.length;r++)u=u.concat(t[r]._childClusters);t=u}n>s?this._group._map.setView(this._latlng,s):n<=a?this._group._map.setView(this._latlng,a+1):this._group._map.fitBounds(this._bounds,e)},getBounds:function(){var e=new L.LatLngBounds;return e.extend(this._bounds),e},_updateIcon:function(){this._iconNeedsUpdate=!0,this._icon&&this.setIcon(this)},createIcon:function(){return this._iconNeedsUpdate&&(this._iconObj=this._group.options.iconCreateFunction(this),this._iconNeedsUpdate=!1),this._iconObj.createIcon()},createShadow:function(){return this._iconObj.createShadow()},_addChild:function(e,t){this._iconNeedsUpdate=!0,this._boundsNeedUpdate=!0,this._setClusterCenter(e),e instanceof L.MarkerCluster?(t||(this._childClusters.push(e),e.__parent=this),this._childCount+=e._childCount):(t||this._markers.push(e),this._childCount++),this.__parent&&this.__parent._addChild(e,!0)},_setClusterCenter:function(e){this._cLatLng||(this._cLatLng=e._cLatLng||e._latlng)},_resetBounds:function(){var e=this._bounds;e._southWest&&(e._southWest.lat=1/0,e._southWest.lng=1/0),e._northEast&&(e._northEast.lat=-1/0,e._northEast.lng=-1/0)},_recalculateBounds:function(){var e=this._markers,t=this._childClusters,i=0,n=0,s=this._childCount,a,r,u,o;if(s!==0){for(this._resetBounds(),a=0;a<e.length;a++)u=e[a]._latlng,this._bounds.extend(u),i+=u.lat,n+=u.lng;for(a=0;a<t.length;a++)r=t[a],r._boundsNeedUpdate&&r._recalculateBounds(),this._bounds.extend(r._bounds),u=r._wLatLng,o=r._childCount,i+=u.lat*o,n+=u.lng*o;this._latlng=this._wLatLng=new L.LatLng(i/s,n/s),this._boundsNeedUpdate=!1}},_addToMap:function(e){e&&(this._backupLatlng=this._latlng,this.setLatLng(e)),this._group._featureGroup.addLayer(this)},_recursivelyAnimateChildrenIn:function(e,t,i){this._recursively(e,this._group._map.getMinZoom(),i-1,function(n){var s=n._markers,a,r;for(a=s.length-1;a>=0;a--)r=s[a],r._icon&&(r._setPos(t),r.clusterHide())},function(n){var s=n._childClusters,a,r;for(a=s.length-1;a>=0;a--)r=s[a],r._icon&&(r._setPos(t),r.clusterHide())})},_recursivelyAnimateChildrenInAndAddSelfToMap:function(e,t,i,n){this._recursively(e,n,t,function(s){s._recursivelyAnimateChildrenIn(e,s._group._map.latLngToLayerPoint(s.getLatLng()).round(),i),s._isSingleParent()&&i-1===n?(s.clusterShow(),s._recursivelyRemoveChildrenFromMap(e,t,i)):s.clusterHide(),s._addToMap()})},_recursivelyBecomeVisible:function(e,t){this._recursively(e,this._group._map.getMinZoom(),t,null,function(i){i.clusterShow()})},_recursivelyAddChildrenToMap:function(e,t,i){this._recursively(i,this._group._map.getMinZoom()-1,t,function(n){if(t!==n._zoom)for(var s=n._markers.length-1;s>=0;s--){var a=n._markers[s];i.contains(a._latlng)&&(e&&(a._backupLatlng=a.getLatLng(),a.setLatLng(e),a.clusterHide&&a.clusterHide()),n._group._featureGroup.addLayer(a))}},function(n){n._addToMap(e)})},_recursivelyRestoreChildPositions:function(e){for(var t=this._markers.length-1;t>=0;t--){var i=this._markers[t];i._backupLatlng&&(i.setLatLng(i._backupLatlng),delete i._backupLatlng)}if(e-1===this._zoom)for(var n=this._childClusters.length-1;n>=0;n--)this._childClusters[n]._restorePosition();else for(var s=this._childClusters.length-1;s>=0;s--)this._childClusters[s]._recursivelyRestoreChildPositions(e)},_restorePosition:function(){this._backupLatlng&&(this.setLatLng(this._backupLatlng),delete this._backupLatlng)},_recursivelyRemoveChildrenFromMap:function(e,t,i,n){var s,a;this._recursively(e,t-1,i-1,function(r){for(a=r._markers.length-1;a>=0;a--)s=r._markers[a],(!n||!n.contains(s._latlng))&&(r._group._featureGroup.removeLayer(s),s.clusterShow&&s.clusterShow())},function(r){for(a=r._childClusters.length-1;a>=0;a--)s=r._childClusters[a],(!n||!n.contains(s._latlng))&&(r._group._featureGroup.removeLayer(s),s.clusterShow&&s.clusterShow())})},_recursively:function(e,t,i,n,s){var a=this._childClusters,r=this._zoom,u,o;if(t<=r&&(n&&n(this),s&&r===i&&s(this)),r<t||r<i)for(u=a.length-1;u>=0;u--)o=a[u],o._boundsNeedUpdate&&o._recalculateBounds(),e.intersects(o._bounds)&&o._recursively(e,t,i,n,s)},_isSingleParent:function(){return this._childClusters.length>0&&this._childClusters[0]._childCount===this._childCount}});L.Marker.include({clusterHide:function(){var e=this.options.opacity;return this.setOpacity(0),this.options.opacity=e,this},clusterShow:function(){return this.setOpacity(this.options.opacity)}}),L.DistanceGrid=function(e){this._cellSize=e,this._sqCellSize=e*e,this._grid={},this._objectPoint={}},L.DistanceGrid.prototype={addObject:function(e,t){var i=this._getCoord(t.x),n=this._getCoord(t.y),s=this._grid,a=s[n]=s[n]||{},r=a[i]=a[i]||[],u=L.Util.stamp(e);this._objectPoint[u]=t,r.push(e)},updateObject:function(e,t){this.removeObject(e),this.addObject(e,t)},removeObject:function(e,t){var i=this._getCoord(t.x),n=this._getCoord(t.y),s=this._grid,a=s[n]=s[n]||{},r=a[i]=a[i]||[],u,o;for(delete this._objectPoint[L.Util.stamp(e)],u=0,o=r.length;u<o;u++)if(r[u]===e)return r.splice(u,1),o===1&&delete a[i],!0},eachObject:function(e,t){var i,n,s,a,r,u,o,h=this._grid;for(i in h){r=h[i];for(n in r)for(u=r[n],s=0,a=u.length;s<a;s++)o=e.call(t,u[s]),o&&(s--,a--)}},getNearObject:function(e){var t=this._getCoord(e.x),i=this._getCoord(e.y),n,s,a,r,u,o,h,l,d=this._objectPoint,_=this._sqCellSize,f=null;for(n=i-1;n<=i+1;n++)if(r=this._grid[n],r){for(s=t-1;s<=t+1;s++)if(u=r[s],u)for(a=0,o=u.length;a<o;a++)h=u[a],l=this._sqDist(d[L.Util.stamp(h)],e),(l<_||l<=_&&f===null)&&(_=l,f=h)}return f},_getCoord:function(e){var t=Math.floor(e/this._cellSize);return isFinite(t)?t:e},_sqDist:function(e,t){var i=t.x-e.x,n=t.y-e.y;return i*i+n*n}},function(){L.QuickHull={getDistant:function(e,t){var i=t[1].lat-t[0].lat,n=t[0].lng-t[1].lng;return n*(e.lat-t[0].lat)+i*(e.lng-t[0].lng)},findMostDistantPointFromBaseLine:function(e,t){var i=0,n=null,s=[],a,r,u;for(a=t.length-1;a>=0;a--){if(r=t[a],u=this.getDistant(r,e),u>0)s.push(r);else continue;u>i&&(i=u,n=r)}return{maxPoint:n,newPoints:s}},buildConvexHull:function(e,t){var i=[],n=this.findMostDistantPointFromBaseLine(e,t);return n.maxPoint?(i=i.concat(this.buildConvexHull([e[0],n.maxPoint],n.newPoints)),i=i.concat(this.buildConvexHull([n.maxPoint,e[1]],n.newPoints)),i):[e[0]]},getConvexHull:function(e){var t=!1,i=!1,n=!1,s=!1,a=null,r=null,u=null,o=null,h=null,l=null,d;for(d=e.length-1;d>=0;d--){var _=e[d];(t===!1||_.lat>t)&&(a=_,t=_.lat),(i===!1||_.lat<i)&&(r=_,i=_.lat),(n===!1||_.lng>n)&&(u=_,n=_.lng),(s===!1||_.lng<s)&&(o=_,s=_.lng)}i!==t?(l=r,h=a):(l=o,h=u);var f=[].concat(this.buildConvexHull([l,h],e),this.buildConvexHull([h,l],e));return f}}}(),L.MarkerCluster.include({getConvexHull:function(){var e=this.getAllChildMarkers(),t=[],i,n;for(n=e.length-1;n>=0;n--)i=e[n].getLatLng(),t.push(i);return L.QuickHull.getConvexHull(t)}}),L.MarkerCluster.include({_2PI:Math.PI*2,_circleFootSeparation:25,_circleStartAngle:0,_spiralFootSeparation:28,_spiralLengthStart:11,_spiralLengthFactor:5,_circleSpiralSwitchover:9,spiderfy:function(){if(!(this._group._spiderfied===this||this._group._inZoomAnimation)){var e=this.getAllChildMarkers(null,!0),t=this._group,i=t._map,n=i.latLngToLayerPoint(this._latlng),s;this._group._unspiderfy(),this._group._spiderfied=this,this._group.options.spiderfyShapePositions?s=this._group.options.spiderfyShapePositions(e.length,n):e.length>=this._circleSpiralSwitchover?s=this._generatePointsSpiral(e.length,n):(n.y+=10,s=this._generatePointsCircle(e.length,n)),this._animationSpiderfy(e,s)}},unspiderfy:function(e){this._group._inZoomAnimation||(this._animationUnspiderfy(e),this._group._spiderfied=null)},_generatePointsCircle:function(e,t){var i=this._group.options.spiderfyDistanceMultiplier*this._circleFootSeparation*(2+e),n=i/this._2PI,s=this._2PI/e,a=[],r,u;for(n=Math.max(n,35),a.length=e,r=0;r<e;r++)u=this._circleStartAngle+r*s,a[r]=new L.Point(t.x+n*Math.cos(u),t.y+n*Math.sin(u))._round();return a},_generatePointsSpiral:function(e,t){var i=this._group.options.spiderfyDistanceMultiplier,n=i*this._spiralLengthStart,s=i*this._spiralFootSeparation,a=i*this._spiralLengthFactor*this._2PI,r=0,u=[],o;for(u.length=e,o=e;o>=0;o--)o<e&&(u[o]=new L.Point(t.x+n*Math.cos(r),t.y+n*Math.sin(r))._round()),r+=s/n+o*5e-4,n+=a/r;return u},_noanimationUnspiderfy:function(){var e=this._group,t=e._map,i=e._featureGroup,n=this.getAllChildMarkers(null,!0),s,a;for(e._ignoreMove=!0,this.setOpacity(1),a=n.length-1;a>=0;a--)s=n[a],i.removeLayer(s),s._preSpiderfyLatlng&&(s.setLatLng(s._preSpiderfyLatlng),delete s._preSpiderfyLatlng),s.setZIndexOffset&&s.setZIndexOffset(0),s._spiderLeg&&(t.removeLayer(s._spiderLeg),delete s._spiderLeg);e.fire("unspiderfied",{cluster:this,markers:n}),e._ignoreMove=!1,e._spiderfied=null}}),L.MarkerClusterNonAnimated=L.MarkerCluster.extend({_animationSpiderfy:function(e,t){var i=this._group,n=i._map,s=i._featureGroup,a=this._group.options.spiderLegPolylineOptions,r,u,o,h;for(i._ignoreMove=!0,r=0;r<e.length;r++)h=n.layerPointToLatLng(t[r]),u=e[r],o=new L.Polyline([this._latlng,h],a),n.addLayer(o),u._spiderLeg=o,u._preSpiderfyLatlng=u._latlng,u.setLatLng(h),u.setZIndexOffset&&u.setZIndexOffset(1e6),s.addLayer(u);this.setOpacity(.3),i._ignoreMove=!1,i.fire("spiderfied",{cluster:this,markers:e})},_animationUnspiderfy:function(){this._noanimationUnspiderfy()}}),L.MarkerCluster.include({_animationSpiderfy:function(e,t){var i=this,n=this._group,s=n._map,a=n._featureGroup,r=this._latlng,u=s.latLngToLayerPoint(r),o=L.Path.SVG,h=L.extend({},this._group.options.spiderLegPolylineOptions),l=h.opacity,d,_,f,m,k,g;for(l===void 0&&(l=L.MarkerClusterGroup.prototype.options.spiderLegPolylineOptions.opacity),o?(h.opacity=0,h.className=(h.className||"")+" leaflet-cluster-spider-leg"):h.opacity=l,n._ignoreMove=!0,d=0;d<e.length;d++)_=e[d],g=s.layerPointToLatLng(t[d]),f=new L.Polyline([r,g],h),s.addLayer(f),_._spiderLeg=f,o&&(m=f._path,k=m.getTotalLength()+.1,m.style.strokeDasharray=k,m.style.strokeDashoffset=k),_.setZIndexOffset&&_.setZIndexOffset(1e6),_.clusterHide&&_.clusterHide(),a.addLayer(_),_._setPos&&_._setPos(u);for(n._forceLayout(),n._animationStart(),d=e.length-1;d>=0;d--)g=s.layerPointToLatLng(t[d]),_=e[d],_._preSpiderfyLatlng=_._latlng,_.setLatLng(g),_.clusterShow&&_.clusterShow(),o&&(f=_._spiderLeg,m=f._path,m.style.strokeDashoffset=0,f.setStyle({opacity:l}));this.setOpacity(.3),n._ignoreMove=!1,setTimeout(function(){n._animationEnd(),n.fire("spiderfied",{cluster:i,markers:e})},200)},_animationUnspiderfy:function(e){var t=this,i=this._group,n=i._map,s=i._featureGroup,a=e?n._latLngToNewLayerPoint(this._latlng,e.zoom,e.center):n.latLngToLayerPoint(this._latlng),r=this.getAllChildMarkers(null,!0),u=L.Path.SVG,o,h,l,d,_,f;for(i._ignoreMove=!0,i._animationStart(),this.setOpacity(1),h=r.length-1;h>=0;h--)o=r[h],o._preSpiderfyLatlng&&(o.closePopup(),o.setLatLng(o._preSpiderfyLatlng),delete o._preSpiderfyLatlng,f=!0,o._setPos&&(o._setPos(a),f=!1),o.clusterHide&&(o.clusterHide(),f=!1),f&&s.removeLayer(o),u&&(l=o._spiderLeg,d=l._path,_=d.getTotalLength()+.1,d.style.strokeDashoffset=_,l.setStyle({opacity:0})));i._ignoreMove=!1,setTimeout(function(){var m=0;for(h=r.length-1;h>=0;h--)o=r[h],o._spiderLeg&&m++;for(h=r.length-1;h>=0;h--)o=r[h],o._spiderLeg&&(o.clusterShow&&o.clusterShow(),o.setZIndexOffset&&o.setZIndexOffset(0),m>1&&s.removeLayer(o),n.removeLayer(o._spiderLeg),delete o._spiderLeg);i._animationEnd(),i.fire("unspiderfied",{cluster:t,markers:r})},200)}}),L.MarkerClusterGroup.include({_spiderfied:null,unspiderfy:function(){this._unspiderfy.apply(this,arguments)},_spiderfierOnAdd:function(){this._map.on("click",this._unspiderfyWrapper,this),this._map.options.zoomAnimation&&this._map.on("zoomstart",this._unspiderfyZoomStart,this),this._map.on("zoomend",this._noanimationUnspiderfy,this),L.Browser.touch||this._map.getRenderer(this)},_spiderfierOnRemove:function(){this._map.off("click",this._unspiderfyWrapper,this),this._map.off("zoomstart",this._unspiderfyZoomStart,this),this._map.off("zoomanim",this._unspiderfyZoomAnim,this),this._map.off("zoomend",this._noanimationUnspiderfy,this),this._noanimationUnspiderfy()},_unspiderfyZoomStart:function(){this._map&&this._map.on("zoomanim",this._unspiderfyZoomAnim,this)},_unspiderfyZoomAnim:function(e){L.DomUtil.hasClass(this._map._mapPane,"leaflet-touching")||(this._map.off("zoomanim",this._unspiderfyZoomAnim,this),this._unspiderfy(e))},_unspiderfyWrapper:function(){this._unspiderfy()},_unspiderfy:function(e){this._spiderfied&&this._spiderfied.unspiderfy(e)},_noanimationUnspiderfy:function(){this._spiderfied&&this._spiderfied._noanimationUnspiderfy()},_unspiderfyLayer:function(e){e._spiderLeg&&(this._featureGroup.removeLayer(e),e.clusterShow&&e.clusterShow(),e.setZIndexOffset&&e.setZIndexOffset(0),this._map.removeLayer(e._spiderLeg),delete e._spiderLeg)}}),L.MarkerClusterGroup.include({refreshClusters:function(e){return e?e instanceof L.MarkerClusterGroup?e=e._topClusterLevel.getAllChildMarkers():e instanceof L.LayerGroup?e=e._layers:e instanceof L.MarkerCluster?e=e.getAllChildMarkers():e instanceof L.Marker&&(e=[e]):e=this._topClusterLevel.getAllChildMarkers(),this._flagParentsIconsNeedUpdate(e),this._refreshClustersIcons(),this.options.singleMarkerMode&&this._refreshSingleMarkerModeMarkers(e),this},_flagParentsIconsNeedUpdate:function(e){var t,i;for(t in e)for(i=e[t].__parent;i;)i._iconNeedsUpdate=!0,i=i.__parent},_refreshSingleMarkerModeMarkers:function(e){var t,i;for(t in e)i=e[t],this.hasLayer(i)&&i.setIcon(this._overrideMarkerIcon(i))}}),L.Marker.include({refreshIconOptions:function(e,t){var i=this.options.icon;return L.setOptions(i,e),this.setIcon(i),t&&this.__parent&&this.__parent._group.refreshClusters(this),this}}),c.MarkerClusterGroup=p,c.MarkerCluster=y,Object.defineProperty(c,"__esModule",{value:!0})})})(G,G.exports);const z="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA0FJREFUWEe1l12IVGUYx3//WSGEoLqQUC8iCL0QAkNvQvBrzu5NqSkK2pzZjcBG1xtDrwxUqCs/CqJYC3SdM65hrCwlbDtntrzQCwlFEW+8CaIMLyQwSANnnjhndoYz68w5Z8bxXL7v8/94nuf9OqLHz/FsJ7B7Dv6N7+p8L1TqBZT17IDgWBRrcLDi6ni3fF0bGPTspMH+dkKCz8uuPu7GRGoDa87ZKwtrjAE7EgQuPMpQuPK+/k5jJJWBoQlbUauG4mvSkAJXMgMUZnbpTlJ8ogFn3AYZCMVfTyKbN/8bVQr+iMpxuFgDTtFGUCj+QpfijfD/MAp+XuOd8B0NZIt2SOLTHoVbYGZ8Usnrsw4L9+nhrGdfCfb2Q7zBYfB1xdXofM6WCmycsFdVZUywpZ/iERNTNkBhdpfuN8aaBjZ49tYAYb9XPw/xCOevVSj87OpGMBYaGCzZO2ah+NJEceNfMkzKuG0Z7liVZYi1gnXAy4n4esCfEoVyTpeUPWtHlOFwSuB1GaPlvK7Njx/y7I0anATeTckVhE3L8czSACS+KOfU9giO4rNF2yoxmYYzbEFKAzd9VytbhDw7LFgejFmNu5VhHWnMO559RH09JX5ySnYGYyQhcrPv6oeIQLuqXfZdrY/EBCegE8srxpu7IOvZOsEv7QAZWDLj6q9gLlvPvJltNN5qHG1UwvHsS2BfOz7fVVM3jYHffVevNTMr2STG1raZiYt+TtvCneXZboNT/TBw3Xe1KlLaCSB4DT31GUxVXL0XGqhv7R/7YYAF4qXpnB5204K4VnXbAmRsKufVzKbDzvnOd9WsjFOynzCG+lIBMyqVvFpWdHiAidVkeCC4XM7pdENssGgbTMx22gFdVyAkMj6Iu9ejYk7JxjGG+2sAbtVqbJwd1oO4vT33gpqJi+mtAgGjGPNz2hNHni3aLYk3n48BIAPDM66K7QSckhUx3NjTD+i9AnXmP+Zacbel70XbjriQJB7MP6uBoBXf+zm1/B+kvNRCf89uoH4DHn30hBNXP9Q/jmdXgbfTZN83A3NijxFPMF5MK97RwJBni2twrxuiHmNbru2WV3H4HDeCxbSoR/J4mLhnVb6NPl7+BxsEVCDQf2e/AAAAAElFTkSuQmCC",F={class:"point-cluster-container"},V={class:"controls"},j={__name:"MainMap",setup(w){const M=S(null),c=S(null),p=S(null),y=S(40),e=S([{id:1,lat:39.9042,lng:116.4074,name:"北京天安门",info:"中国首都标志性建筑"},{id:2,lat:39.9528,lng:116.4399,name:"北京故宫",info:"明清皇家宫殿"},{id:3,lat:31.2304,lng:121.4737,name:"上海外滩",info:"上海标志性景点"},{id:4,lat:31.1997,lng:121.5431,name:"上海东方明珠",info:"上海地标建筑"},{id:5,lat:22.5431,lng:114.0579,name:"深圳平安金融中心",info:"深圳第一高楼"},...t(50)]);function t(o){const h=[];for(let l=0;l<o;l++){const d=20+Math.random()*20,_=105+Math.random()*25;h.push({id:l+6,lat:d,lng:_,name:`随机站点位${l+1}`,info:`随机站点位信息${l+1}`})}return h}I(()=>{M.value&&(c.value=v.map(M.value,{crs:v.CRS.EPSG4326,center:[30,110],zoom:4,maxZoom:18}),i(),n())}),T(()=>{c.value&&(c.value.remove(),c.value=null),p.value&&(p.value.clearLayers(),p.value=null)});function i(){new v.supermap.TiandituTileLayer({layerType:"img",key:"1d109683f4d84198e37a38c442d68311"}).addTo(c.value),new v.supermap.TiandituTileLayer({layerType:"img",isLabel:!0,key:"1d109683f4d84198e37a38c442d68311"}).addTo(c.value)}function n(){p.value&&c.value.removeLayer(p.value),p.value=v.markerClusterGroup({showCoverageOnHover:!1,maxClusterRadius:y.value,spiderfyOnMaxZoom:!0,removeOutsideVisibleBounds:!0}),s(),c.value.addLayer(p.value)}function s(){p.value&&(p.value.clearLayers(),e.value.forEach(o=>{const h=v.marker([o.lat,o.lng],{icon:v.icon({iconUrl:z}),title:o.name});h.on("click",function(){a(o)}),p.value.addLayer(h)}))}function a(o){v.popup().setLatLng([o.lat,o.lng]).setContent(`
      <div class="point-popup">
        <h3>${o.name}</h3>
        <p>${o.info}</p>
        <p>坐标: (${o.lat.toFixed(4)}, ${o.lng.toFixed(4)})</p>
      </div>
    `).openOn(c.value)}function r(){e.value=[...e.value.slice(0,5),...t(50)],s()}function u(){n()}return N(y,()=>{n()}),(o,h)=>{const l=P("el-button"),d=P("el-option"),_=P("el-select");return U(),E("div",F,[O("div",{ref_key:"mapContainer",ref:M,class:"map"},null,512),O("div",V,[C(l,{onClick:r},{default:x(()=>h[1]||(h[1]=[b("刷新数据")])),_:1,__:[1]}),C(l,{onClick:u},{default:x(()=>h[2]||(h[2]=[b("修改聚合设置")])),_:1,__:[2]}),C(_,{modelValue:y.value,"onUpdate:modelValue":h[0]||(h[0]=f=>y.value=f),placeholder:"聚合半径"},{default:x(()=>[C(d,{label:"20px",value:"20"}),C(d,{label:"40px",value:"40"}),C(d,{label:"60px",value:"60"}),C(d,{label:"80px",value:"80"})]),_:1},8,["modelValue"])])])}}},R=Z(j,[["__scopeId","data-v-7f425b63"]]);export{R as default};
