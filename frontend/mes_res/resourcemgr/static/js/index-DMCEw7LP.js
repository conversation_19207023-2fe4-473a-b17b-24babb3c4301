import{T as E}from"./index-BCZFTY-Q.js";import te from"./StatBarChart-CwOVq5Q4.js";import{_ as M,r as v,x as ae,e as p,j as y,o as u,i as n,c as I,F as P,G as C,z as le,d as oe,K as ne,w as se,B as re,O as R,f as a,h as z,l as r,n as k,P as B,M as $,a6 as S}from"./index-Cl6Blbqy.js";import{d as de,o as ue,f as pe}from"./personMgr-Bb0gHMtY.js";import{i as ie}from"./common-Ce1gqYhs.js";/* empty css                                                              */import"./index-BciqUSOs.js";const ce={__name:"index",props:{modelValue:{type:[Number,String],default:null},startYear:{type:Number,default:()=>new Date().getFullYear()-50},endYear:{type:Number,default:()=>new Date().getFullYear()},step:{type:Number,default:1},placeholder:{type:String,default:"请选择年份"},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},sortDesc:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(x,{emit:D}){const l=x,j=D,w=v(l.modelValue!==null?Number(l.modelValue):null),V=ae(()=>{const s=[];for(let m=l.startYear;m<=l.endYear;m+=l.step)s.push(m);return l.sortDesc?s.reverse():s}),O=s=>{j("update:modelValue",s),j("change",s)};return((s,m)=>{let d=s();setInterval(()=>{const i=s();i!==d&&(m(i,d),d=i)},100)})(()=>l.modelValue,s=>{w.value=s!==null?Number(s):null}),(s,m)=>{const d=p("el-option"),N=p("el-select");return u(),y(N,{modelValue:w.value,"onUpdate:modelValue":m[0]||(m[0]=i=>w.value=i),placeholder:x.placeholder,clearable:x.clearable,disabled:x.disabled,onChange:O,class:"year-select"},{default:n(()=>[(u(!0),I(P,null,C(V.value,i=>(u(),y(d,{key:i,label:i.toString(),value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","clearable","disabled"])}}},me=M(ce,[["__scopeId","data-v-d2369404"]]),fe={class:"supplies-mgmt-container"},_e={class:"row-butons"},ge={class:"parameter-bottom"},be={class:"parameter-bottom"},ve=le({name:"Performanceassessment"}),ye=Object.assign(ve,{setup(x){const{proxy:D}=oe(),l=v({departmentIds:[],personIds:[],year:new Date().getFullYear(),timeCategory:"月"}),j=v([{value:"月",label:"月"},{value:"季度",label:"季度"}]),w=v({}),V=v(!0),O=[{label:"人员名称",prop:"personName"},{label:"单位名称",prop:"orgName"},{label:"年份",prop:"year"},{label:"时间值",prop:"timeValue"},{label:"综合绩效",prop:"totalPerformance"}],U=v([]),s=v([]),m=v([]),d=ne({currentPage:1,pageSize:5,total:0}),N=()=>{let e={...l.value};e.departmentIds=e.departmentIds.join(","),e.personIds=e.personIds.join(","),ue(S({...e})).then(t=>{const{data:_}=t;let g=[],h=[];_.forEach(f=>{const b=f.data;l.value.timeCategory==="月"?g=b.map(c=>`${c.year}-${c.month}`):g=b.map(c=>`${c.year}${c.quarter}`),h.push({name:f.departmentName,data:b.map(c=>c.totalPerformance)})}),w.value={xAxisData:g,seriesData:h}})},i=()=>{let e={...l.value};e.departmentIds=e.departmentIds.join(","),e.personIds=e.personIds.join(","),V.value=!0,pe(S({...e,pageSize:d.pageSize,pageNum:d.currentPage})).then(t=>{const{data:_}=t;U.value=_.data||[],d.total=_.totalRecords||0,V.value=!1})},Y=()=>{if(l.value.departmentIds.length===0){$.error("请至少选择一个运维单位，再进行查询！");return}d.currentPage=1,N(),i()},q=()=>{l.value.departmentIds=s.value.map(e=>e.value).splice(0,5),l.value.personIds=[],l.value.year=new Date().getFullYear(),l.value.timeCategory="月",Y()},A=()=>{let e={...l.value};e.departmentIds=e.departmentIds.join(","),e.personIds=e.personIds.join(","),D.download("resource/personmgrOrgPerformance/exportOrgPerformance",S(e),`单位绩效考核_${new Date().getTime()}.xlsx`),$.success("导出成功")},G=()=>{let e={...l.value};e.departmentIds=e.departmentIds.join(","),e.personIds=e.personIds.join(","),D.download("resource/personmgrPerformance/exportPersonPerformance",S(e),`人员绩效考核_${new Date().getTime()}.xlsx`),$.success("导出成功")},H=e=>{d.currentPage=e,i()},K=()=>{const{data:e}=ie();s.value=e.map(t=>({value:t.id,label:t.name})),l.value.departmentIds=s.value.map(t=>t.value).splice(0,5),Y()},J=e=>{de({orgId:e}).then(t=>{const{data:_}=t;m.value=_.map(g=>({label:g.personName,value:g.personId}))})};return se(()=>l.value.departmentIds,e=>{e&&(J(e.join(",")),l.value.personIds=[])},{immediate:!0,deep:!0}),re(()=>{K()}),(e,t)=>{const _=p("el-option"),g=p("el-select"),h=p("el-form-item"),f=p("el-col"),b=p("el-row"),c=p("el-button"),Q=p("el-form"),F=p("el-divider"),L=p("el-table-column"),W=p("el-table"),X=p("el-pagination"),T=R("hasPermi"),Z=R("loading");return u(),I("div",fe,[a(Q,{ref:"formRef","label-width":"100px"},{default:n(()=>[a(b,{justify:"space-between"},{default:n(()=>[a(f,{span:8},{default:n(()=>[a(h,{label:"运维单位"},{default:n(()=>[a(g,{modelValue:r(l).departmentIds,"onUpdate:modelValue":t[0]||(t[0]=o=>r(l).departmentIds=o),multiple:"","collapse-tags":"",placeholder:"请输入",style:{width:"300px"}},{default:n(()=>[(u(!0),I(P,null,C(r(s),o=>(u(),y(_,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(f,{span:8},{default:n(()=>[a(h,{label:"运维人员"},{default:n(()=>[a(g,{modelValue:r(l).personIds,"onUpdate:modelValue":t[1]||(t[1]=o=>r(l).personIds=o),multiple:"","collapse-tags":"",placeholder:"请输入",clearable:"",style:{width:"300px"}},{default:n(()=>[(u(!0),I(P,null,C(r(m),o=>(u(),y(_,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(f,{span:8},{default:n(()=>[a(h,{label:"统计年份"},{default:n(()=>[a(r(me),{modelValue:r(l).year,"onUpdate:modelValue":t[2]||(t[2]=o=>r(l).year=o),clearable:!1,style:{width:"300px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(b,{justify:"space-between"},{default:n(()=>[a(f,{span:8},{default:n(()=>[a(h,{label:"统计周期"},{default:n(()=>[a(g,{modelValue:r(l).timeCategory,"onUpdate:modelValue":t[3]||(t[3]=o=>r(l).timeCategory=o),placeholder:"请选择",filterable:"",style:{width:"300px"}},{default:n(()=>[(u(!0),I(P,null,C(r(j),o=>(u(),y(_,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(f,{span:8}),a(f,{span:8,style:{width:"400px",display:"flex","justify-content":"flex-end"}},{default:n(()=>[a(c,{type:"primary",onClick:Y},{default:n(()=>t[6]||(t[6]=[k("查询")])),_:1,__:[6]}),a(c,{onClick:q},{default:n(()=>t[7]||(t[7]=[k("重置")])),_:1,__:[7]})]),_:1})]),_:1})]),_:1},512),a(F),a(b,{gutter:10,justify:"space-between"},{default:n(()=>[a(r(E),{titleName:"单位绩效考核"}),z("div",_e,[B((u(),y(c,{type:"primary",icon:"Download",onClick:A},{default:n(()=>t[8]||(t[8]=[k("导出")])),_:1,__:[8]})),[[T,["system:supplies:export"]]])])]),_:1}),z("div",ge,[a(te,{echartData:r(w)},null,8,["echartData"])]),a(F),a(b,{gutter:10,justify:"space-between"},{default:n(()=>[a(f,{span:5},{default:n(()=>[a(r(E),{titleName:"人员绩效考核"})]),_:1}),a(f,{span:19,class:"row-butons"},{default:n(()=>[B((u(),y(c,{type:"primary",icon:"Download",onClick:G},{default:n(()=>t[9]||(t[9]=[k("导出")])),_:1,__:[9]})),[[T,["system:supplies:export"]]])]),_:1})]),_:1}),z("div",be,[B((u(),y(W,{data:r(U),style:{width:"100%"},height:"80%","show-overflow-tooltip":""},{default:n(()=>[a(L,{type:"selection",width:"55",align:"center"}),(u(),I(P,null,C(O,(o,ee)=>a(L,{key:ee,label:o.label,prop:o.prop},null,8,["label","prop"])),64))]),_:1},8,["data"])),[[Z,r(V)]]),a(X,{"current-page":d.currentPage,"onUpdate:currentPage":t[4]||(t[4]=o=>d.currentPage=o),"page-size":d.pageSize,"onUpdate:pageSize":t[5]||(t[5]=o=>d.pageSize=o),layout:"total, prev, pager, next, jumper",total:d.total,onCurrentChange:H},null,8,["current-page","page-size","total"])])])}}}),De=M(ye,[["__scopeId","data-v-2ab7394d"]]);export{De as default};
