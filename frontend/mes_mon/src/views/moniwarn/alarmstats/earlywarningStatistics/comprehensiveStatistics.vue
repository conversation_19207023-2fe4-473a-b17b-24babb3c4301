<!-- 综合统计 -->
<template>
  <div class="comprehensiveStatistics-container">
    <el-form ref="formRef" label-width="80px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="预警规则">
            <el-input
              v-model="queryParams.ruleName"
              placeholder="请输入预警规则"
              style="width: 300px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="预警等级">
            <el-select
              v-model="queryParams.alarmLevel"
              placeholder="请选择"
              filterable
              style="width: 300px"
            >
              <el-option
                v-for="dict in alarm_level"
                :key="dict.value"
                :value="dict.value"
                :label="dict.label"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="触发时间" prop="dateRange">
            <el-date-picker
              ref="alarmDataPicker"
              v-model="queryParams.dateRange"
              :append-to-body="true"
              value-format="YYYY-MM-DD"
              type="daterange"
              format="YYYY-MM-DD"
              popper-class="common-iw-s datepicker"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :clearable="false"
              :editable="false"
              @change="handleDateChange"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="介质类型">
            <el-select
              v-model="queryParams.mediumType"
              placeholder="请选择"
              filterable
              style="width: 300px"
            >
              <el-option
                v-for="dict in media_type"
                :key="dict.value"
                :value="dict.value"
                :label="dict.label"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="监控项目">
            <el-select
              v-model="queryParams.monitorClass"
              placeholder="请选择"
              filterable
              style="width: 300px"
            >
              <el-option
                v-for="dict in mon_item"
                :key="dict.value"
                :value="dict.value"
                :label="dict.label"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="区域">
            <el-tree-select
              v-model="queryParams.areaCode"
              :data="cityNameList"
              :render-after-expand="false"
              :props="{
                label: 'areaName',
                children: 'children',
                value: 'areaCode',
              }"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="站点">
            <el-tree-select
              v-model="queryParams.siteId"
              :data="siteList"
              :render-after-expand="false"
              :props="{
                label: 'name',
                children: 'children',
                value: 'siteCode',
              }"
              style="width: 300px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="16" style="display: flex; justify-content: flex-end">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider />

    <div class="comprehensiveStatistics-middle">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-box">
            <div class="table-title">报警总量</div>
            <TotalAlarmChart :data="totalWarnData" />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-box">
            <div class="table-title">报警处理情况</div>
            <AlarmHandleInfoChart :data="warnDealData" />
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="comprehensiveStatistics-bottom">
      <div class="table-btns">
        <el-button class="add-button" type="primary" @click="handleExport" :icon="Download">
          导出
        </el-button>
      </div>
      <div class="table-title">综合统计列表</div>
      <div class="table-content">
        <el-table :data="dataList" style="width: 100%" height="100%">
          <el-table-column label="站点" align="left" prop="siteName" />
          <el-table-column label="预警名称" align="left" prop="alarmName" show-overflow-tooltip />
          <el-table-column label="预警等级" align="left" prop="alarmLevelValue"></el-table-column>
          <el-table-column label="报警总量" align="left" prop="alarmTotalCount" />
          <el-table-column label="已处理数量" align="left" prop="alarmDealCount" />
          <el-table-column label="已处理率" align="left" prop="alarmDealRate" />
          <el-table-column label="及时处理数量" align="left" prop="alarmDealInTimeCount" />
          <el-table-column label="及时处理率" align="left" prop="alarmDealInTimeRate" />
        </el-table>
      </div>
      <div class="page-content">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from "vue";
import { Plus, Download, InfoFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import TotalAlarmChart from "./totalAlarmChart.vue";
import AlarmHandleInfoChart from "./alarmHandleInfoChart.vue";
import { getMonParam } from "@/api/moniwarn/monimgr/parameterquery";
import { getAreaTreeInfo, getRegionTreeInfo } from "@/api/common";
import {
  warnStatisticsList,
  exportWarnStatisticsList,
  warnDayStatisticsChart,
  warnDealStatisticsChart,
} from "@/api/moniwarn/alarmstats/earlywarningStatistics";
import { downloadFile } from "@/utils/index";

defineComponent({ TotalAlarmChart, AlarmHandleInfoChart });

const { proxy } = getCurrentInstance();
const { mon_item, media_type, alarm_level } = proxy.useBusDict(
  "mon_item",
  "media_type",
  "alarm_level",
);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  mediumType: "", // 介质类型
  monitorClass: "", // 监控项目
  alarmLevel: "", // 预警等级
  ruleName: "", // 规则名称
  areaCode: "", // 区域
  siteId: "", // 监测站点
  dateRange: [dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
});

// 配置列表数据
const dataList = ref([]);
const total = ref(0);
const totalWarnData = ref({});
const warnDealData = ref([]);

const siteList = ref([]);
// 监控参数列表
const moniParamList = ref([]);
// 城市名称列表
const cityNameList = ref([]);

const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const start = dayjs(val[0]);
    const end = dayjs(val[1]);
    const diffDays = end.diff(start, "day");

    if (diffDays > 30) {
      ElMessage.warning("日期区间不能超过30天");
      queryParams.dateRange = [
        dayjs().startOf("month").format("YYYY-MM-DD"),
        dayjs().format("YYYY-MM-DD"),
      ];
      // nextTick(() => {
      //   // 确保日期选择器更新
      //   queryParams.startDate = "";
      //   queryParams.endDate = "";
      // });
    }
  }
};

// 获取监控参数列表
const getMoniParamList = async () => {
  const { code, data } = await getMonParam();
  moniParamList.value = code === 200 ? data : [];
};

// 获取城市名称列表
const getCityNameList = async () => {
  const { code, data } = await getAreaTreeInfo();
  cityNameList.value = code === 200 ? data.children : [];
};

const getSiteOptions = async () => {
  const { code, data } = await getRegionTreeInfo();
  siteList.value = code === 200 ? data.children : [];
};

const getTableData = async () => {
  const params = {
    ...toRaw(queryParams),
    startTime: queryParams.dateRange ? queryParams.dateRange[0] : "",
    endTime: queryParams.dateRange ? queryParams.dateRange[1] : "",
  };
  warnStatisticsList(params).then((res) => {
    const { code, data } = res;
    if (code === 200) {
      dataList.value = data.data || [];
      total.value = data.totalRecords || 0;
    }
  });
};

const getWarnDayStatisticsChartData = async () => {
  const params = {
    ...toRaw(queryParams),
    startTime: queryParams.dateRange ? queryParams.dateRange[0] : "",
    endTime: queryParams.dateRange ? queryParams.dateRange[1] : "",
  };
  warnDayStatisticsChart(params).then((res) => {
    const { code, data } = res;
    if (code === 200) {
      totalWarnData.value = data || {};
    }
  });
};

const getWarnDealStatisticsChartData = async () => {
  const params = {
    ...toRaw(queryParams),
    startTime: queryParams.dateRange ? queryParams.dateRange[0] : "",
    endTime: queryParams.dateRange ? queryParams.dateRange[1] : "",
  };
  warnDealStatisticsChart(params).then((res) => {
    const { code, data } = res;
    if (code === 200) {
      const list = data || [];
      warnDealData.value = list.map((item) => ({
        name: item.statusName,
        value: item.count,
      }));
    }
  });
};

onMounted(async () => {
  getMoniParamList();
  getCityNameList();
  try {
    await getSiteOptions();
  } catch (error) {
    console.log("获取数据失败", error);
  }
  getTableData();
  getWarnDayStatisticsChartData();
  getWarnDealStatisticsChartData();
});

const handleSearch = () => {
  queryParams.pageNum = 1;
  getTableData();
  getWarnDayStatisticsChartData();
  getWarnDealStatisticsChartData();
};

const handleReset = () => {
  queryParams.pageNum = 1;
  queryParams.mediumType = "";
  queryParams.monitorClass = "";
  queryParams.alarmType = "";
  queryParams.ruleName = "";
  queryParams.areaCode = "";
  queryParams.dateRange = [
    dayjs().startOf("month").format("YYYY-MM-DD"),
    dayjs().format("YYYY-MM-DD"),
  ];
  queryParams.siteId = "";
  getTableData();
  getWarnDayStatisticsChartData();
  getWarnDealStatisticsChartData();
};

const handleCurrentChange = (currentPage) => {
  queryParams.pageNum = currentPage;
  getTableData();
};

const handleExport = () => {
  exportWarnStatisticsList({
    ...toRaw(queryParams),
    startTime: queryParams.dateRange ? queryParams.dateRange[0] : "",
    endTime: queryParams.dateRange ? queryParams.dateRange[1] : "",
  }).then((res) => {
    if (res) {
      // 获取请求返回的 headers
      downloadFile(res, "综合统计.xlsx");
    }
  });
};
</script>

<style scoped lang="scss">
.comprehensiveStatistics-container {
  display: flex;
  flex-direction: column;

  :deep(.el-divider) {
    margin: 0 0 16px 0;
  }

  .table-title {
    position: relative;
    height: 24px;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    font-weight: 500;
    padding-left: 10px;
    margin-bottom: 16px;
    width: 60%;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 16px;
      background: #1472ff;
    }
  }

  .comprehensiveStatistics-middle {
    margin: 10px 0 30px 0;
  }
  .comprehensiveStatistics-bottom {
    flex: 1;
    height: calc(100% - 200px);
    position: relative;
    .table-btns {
      position: absolute;
      right: 0;
      top: -4px;
      display: inline-flex;
    }

    .table-content {
      height: calc(100% - 24px - 16px - 56px);
    }
    .page-content {
      height: 56px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: end;
    }
  }
}
</style>
