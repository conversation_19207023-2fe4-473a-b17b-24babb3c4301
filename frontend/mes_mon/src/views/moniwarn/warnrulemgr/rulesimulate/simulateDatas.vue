<!-- 监控参数配置二级页面 -->
<template>
  <div class="warnrulemgr-rulesimulate-simulateDatas">
    <div class="warnrulemgr-simulateDatas-top">
      <div class="table-title">模拟基础信息</div>
      <el-descriptions>
        <el-descriptions-item label="介质类型:">
          <dict-tag :options="media_type" :value="baseInfo.mediumType" />
        </el-descriptions-item>
        <el-descriptions-item label="监控项目:">
          <dict-tag :options="mon_item" :value="baseInfo.monitorClass" />
        </el-descriptions-item>
        <el-descriptions-item label="预警规则分类:">
          <dict-tag :options="template_class_code" :value="baseInfo.templateClassCode" />
        </el-descriptions-item>
        <el-descriptions-item label="预警规则:"> {{ baseInfo.ruleName }} </el-descriptions-item>
        <el-descriptions-item label="模拟数据范围:">
          {{
            baseInfo.dataBeginTime && baseInfo.dataEndTime
              ? baseInfo.dataBeginTime + " 至 " + baseInfo.dataEndTime
              : ""
          }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-divider />
    <div class="warnrulemgr-simulateDatas-bottom">
      <div class="table-title">模拟报警数据</div>
      <div class="table-content">
        <el-table :data="dataList" style="width: 100%" height="100%">
          <el-table-column
            label="模拟报警编号"
            align="left"
            prop="simulaId"
            fixed
            show-overflow-tooltip
            width="120"
          />
          <el-table-column
            label="触发时间"
            align="left"
            prop="alarmTime"
            show-overflow-tooltip
            width="180"
          />
          <el-table-column
            label="报警名称"
            align="left"
            prop="alarmName"
            show-overflow-tooltip
            width="180"
          />
          <el-table-column label="预警等级" align="left" prop="alarmLevel" width="80">
            <template #default="scope">
              <dict-tag :options="alarm_level" :value="scope.row.alarmLevel" />
            </template>
          </el-table-column>
          <el-table-column
            label="预警是否升级"
            align="left"
            prop="isUp"
            show-overflow-tooltip
            width="120"
          >
            <template #default="scope">
              {{ scope.row.isUp === "1" ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            label="预警规则分类"
            align="left"
            prop="templateClassCode"
            width="150"
            show-overflow-tooltip
          >
            <template #default="scope">
              <dict-tag :options="template_class_code" :value="scope.row.templateClassCode" />
            </template>
          </el-table-column>
          <el-table-column
            label="报警内容"
            align="left"
            prop="alarmContent"
            show-overflow-tooltip
            width="200"
          />
          <el-table-column
            label="报警描述"
            align="left"
            prop="alarmDesc"
            show-overflow-tooltip
            width="200"
          />
          <el-table-column
            label="站点"
            align="left"
            prop="siteName"
            show-overflow-tooltip
            width="120"
          />
          <el-table-column
            label="省份"
            align="left"
            prop="provinceName"
            show-overflow-tooltip
            width="120"
          />
          <el-table-column
            label="城市"
            align="left"
            prop="cityName"
            show-overflow-tooltip
            width="120"
          />
          <el-table-column
            label="运维单位"
            align="left"
            prop="maintOrg"
            show-overflow-tooltip
            width="120"
          />
          <el-table-column label="操作" align="left" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleDetail(scope.row)"> 详情 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-content">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getRuleSimulateInfo, getInstSimulaList } from "@/api/moniwarn/warnrulemgr/rulesimulate";

const router = useRouter();
const route = useRoute();

const { proxy } = getCurrentInstance();
const {
  mon_item,
  media_type,
  alarm_type,
  alarm_level,
  site_type,
  trigger_type,
  up_alarm_level,
  template_class_code,
} = proxy.useBusDict(
  "mon_item",
  "media_type",
  "alarm_type",
  "alarm_level",
  "site_type",
  "trigger_type",
  "up_alarm_level",
  "template_class_code",
);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 配置列表数据
const dataList = ref([]);
const total = ref(0);
const baseInfo = ref({});

const getBaseInfo = async () => {
  const { code, data } = await getRuleSimulateInfo({
    simulaId: route.params.id,
  });
  baseInfo.value = code === 200 ? data : {};
};

const getTableData = async () => {
  const params = {
    ...toRaw(queryParams),
    simulaId: route.params.id,
  };
  getInstSimulaList(params).then((res) => {
    const { code, data } = res;
    if (code === 200) {
      dataList.value = data.data || [];
      total.value = data.totalRecords || 0;
    }
  });
};

onMounted(() => {
  getBaseInfo();
  getTableData();
});

const handleCurrentChange = (currentPage) => {
  queryParams.pageNum = currentPage;
  getTableData();
};

const handleDetail = (row) => {
  const id = row.instId;
  router.push("/moniwarn/rulesimulate/dataDetail/" + id);
};
</script>

<style scoped lang="scss">
.warnrulemgr-rulesimulate-simulateDatas {
  width: calc(100% - 48px);
  height: calc(100% - 48px);
  position: absolute;
  left: 24px;
  top: 24px;
  background: #fff;
  border-radius: 16px;
  padding: 24px 24px 0 24px;
  display: flex;
  flex-direction: column;

  .warnrulemgr-simulateDatas-top {
    padding-bottom: 10px;
  }

  :deep(.el-divider) {
    margin: 0 0 16px 0;
  }

  :deep(.el-descriptions__label) {
    color: #999;
  }

  :deep(.el-descriptions__content) {
    color: #333;
    display: inline-flex;
  }

  .table-title {
    position: relative;
    height: 24px;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    font-weight: 500;
    padding-left: 10px;
    margin-bottom: 16px;
    width: calc(100% - 80px);
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 16px;
      background: #1472ff;
    }
  }

  .warnrulemgr-simulateDatas-bottom {
    flex: 1;

    .table-content {
      height: calc(100% - 24px - 16px - 56px);
    }
    .page-content {
      height: 56px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: end;
    }
  }
}
</style>
