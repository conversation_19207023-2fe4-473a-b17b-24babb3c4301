<!-- 气类实时监控--调度监控 -->
<template>
  <div class="airRtMoni-schedulingMonitoring">
    <el-form ref="formRef" label-width="80px">
      <el-row>
        <!-- <el-col :span="8">
          <el-form-item label="数据类别">
            <el-select
              v-model="queryParams.basinId"
              placeholder="请选择"
              filterable
              clearable
              style="width: 300px"
            >
              <el-option
                v-for="item in basinList"
                :key="item.dictCode"
                :label="item.dictValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="数据时间" prop="dateRange">
            <el-date-picker
              ref="alarmDataPicker"
              v-model="queryParams.dateRange"
              :append-to-body="true"
              value-format="YYYY-MM-DD"
              type="daterange"
              format="YYYY-MM-DD"
              popper-class="common-iw-s datepicker"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :clearable="false"
              :editable="false"
              style="width: 300px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="16" style="display: flex; justify-content: flex-end">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-divider />
    <div class="airRtMoni-schedulingMonitoring-bottom">
      <div class="table-title">调度监控列表</div>
      <div class="table-content">
        <el-table :data="dataList" v-loading="loading" style="width: 100%" height="100%">
          <el-table-column label="任务编号" align="center" prop="taskCode" width="150" />
          <el-table-column label="任务名称" align="center" prop="taskName" show-overflow-tooltip />
          <el-table-column
            label="任务派发时间"
            align="center"
            prop="dispatchedTime"
            show-overflow-tooltip
          />
          <el-table-column
            label="任务开始时间"
            align="center"
            prop="startTime"
            show-overflow-tooltip
          />
          <el-table-column
            label="任务结束时间"
            align="center"
            prop="endTime"
            show-overflow-tooltip
          />
          <el-table-column
            label="任务状态"
            align="center"
            prop="taskStatus"
            width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ taskStatusMap[row.taskStatus] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="任务描述" align="center" prop="taskDesc" show-overflow-tooltip />
          <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
        </el-table>
      </div>
      <div class="page-content">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
          @change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, defineComponent } from "vue";
import { findAirTaskDispatchList } from "@/api/moniwarn/monimgr/airRtMoni";

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dataType: "", // 数据类型
  dateRange: [], // 数据时间
});

const taskStatusMap = ref({
  1: "待推送",
  2: "已推送",
  3: "进行中",
  4: "已完成",
  5: "已退回",
  6: "已中止",
});

// 配置列表数据
const dataList = ref([]);
const total = ref(0);
const loading = ref(false);

const getTableData = async () => {
  loading.value = true;
  const params = {
    ...toRaw(queryParams),
    startDate: queryParams.dateRange?.length ? queryParams.dateRange[0] : "",
    endDate: queryParams.dateRange?.length ? queryParams.dateRange[1] : "",
  };
  findAirTaskDispatchList(params)
    .then((res) => {
      loading.value = false;
      const { code, data } = res;
      if (code === 200) {
        dataList.value = data.data || [];
        total.value = data.totalRecords || 0;
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getTableData();
});

const handleSearch = () => {
  console.log(queryParams);
  queryParams.pageNum = 1;
  getTableData();
};

const handleReset = () => {
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.dataType = "";
  queryParams.dateRange = [];
  getTableData();
};

const handleCurrentChange = (currentPage, pageSize) => {
  queryParams.pageNum = currentPage;
  queryParams.pageSize = pageSize;
  getTableData();
};
</script>

<style scoped lang="scss">
.airRtMoni-schedulingMonitoring {
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.el-divider) {
    margin: 0 0 16px 0;
  }
  .airRtMoni-schedulingMonitoring-bottom {
    flex: 1;
    position: relative;
    .table-title {
      position: relative;
      height: 24px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      font-weight: 500;
      padding-left: 10px;
      margin-bottom: 16px;
      width: calc(100% - 80px);
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 16px;
        background: #1472ff;
      }
    }
    .table-content {
      height: calc(100vh - 415px);
    }
    .page-content {
      height: 56px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: end;
    }
  }
}
</style>
