# 质量计算结果接口测试示例

## 修复说明
✅ **已修复问题**: `sysUser.getTenantId()` 方法不存在的问题
- 所有 `setTenantId` 都设置为固定值 "0"
- 移除了对 `sysUser.getTenantId()` 的调用

## 测试用例

### 1. 单条新增测试
```bash
curl -X POST "http://localhost:8080/userTest/qualityResult/add" \
-H "Content-Type: application/json" \
-d '{
    "provinceCode": "110000",
    "cityCode": "110100", 
    "siteId": "SITE001",
    "siteCode": "WATER_MONITOR",
    "businessType": "WATER",
    "monitIndex": "PH",
    "accuracy": "95.5",
    "precision": "98.2",
    "effectivenessRate": "99.1",
    "captureRate": "97.8",
    "quactrlPassRate": "96.5",
    "tp": "day",
    "dt": "2025-07-19",
    "statYear": "2025"
}'
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": 1
}
```

### 2. 批量新增测试
```bash
curl -X POST "http://localhost:8080/userTest/qualityResult/batchAdd" \
-H "Content-Type: application/json" \
-d '[
    {
        "provinceCode": "110000",
        "cityCode": "110100",
        "siteId": "SITE001",
        "businessType": "WATER",
        "monitIndex": "PH",
        "accuracy": "95.5",
        "precision": "98.2",
        "tp": "day",
        "dt": "2025-07-19",
        "statYear": "2025"
    },
    {
        "provinceCode": "110000", 
        "cityCode": "110100",
        "siteId": "SITE002",
        "businessType": "AIR",
        "monitIndex": "PM2.5",
        "accuracy": "94.8",
        "precision": "97.1",
        "tp": "day",
        "dt": "2025-07-19",
        "statYear": "2025"
    }
]'
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "操作成功", 
    "data": 2
}
```

### 3. 查询列表测试
```bash
curl -X POST "http://localhost:8080/userTest/qualityResult/list" \
-H "Content-Type: application/json" \
-d '{
    "businessType": "WATER",
    "statYear": "2025"
}'
```

**预期响应**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "id": "uuid-generated-id",
            "provinceCode": "110000",
            "cityCode": "110100",
            "siteId": "SITE001",
            "businessType": "WATER",
            "monitIndex": "PH",
            "accuracy": "95.5",
            "precision": "98.2",
            "tenantId": "0",
            "status": "A",
            "createTime": "2025-07-19 10:30:00"
        }
    ],
    "total": 1
}
```

### 4. 获取详情测试
```bash
curl -X GET "http://localhost:8080/userTest/qualityResult/{id}"
```

### 5. 修改测试
```bash
curl -X PUT "http://localhost:8080/userTest/qualityResult/edit" \
-H "Content-Type: application/json" \
-d '{
    "id": "existing-id",
    "accuracy": "96.0",
    "precision": "98.5"
}'
```

### 6. 删除测试
```bash
curl -X DELETE "http://localhost:8080/userTest/qualityResult/id1,id2,id3"
```

## 自动设置的字段验证

### 新增时自动设置的字段:
- ✅ `id`: 自动生成UUID
- ✅ `createTime`: 当前时间
- ✅ `createBy`: 当前用户名或"system"
- ✅ `tenantId`: 固定值"0"
- ✅ `status`: 默认值"A"

### 修改时自动设置的字段:
- ✅ `updateTime`: 当前时间  
- ✅ `updateBy`: 当前用户名或"system"

## 错误处理测试

### 1. 空数据批量新增
```bash
curl -X POST "http://localhost:8080/userTest/qualityResult/batchAdd" \
-H "Content-Type: application/json" \
-d '[]'
```

**预期响应**:
```json
{
    "code": 500,
    "msg": "批量新增数据不能为空"
}
```

### 2. 空数据批量修改
```bash
curl -X PUT "http://localhost:8080/userTest/qualityResult/batchEdit" \
-H "Content-Type: application/json" \
-d '[]'
```

**预期响应**:
```json
{
    "code": 500,
    "msg": "批量修改数据不能为空"
}
```

## 数据库验证

插入数据后，可以通过以下SQL验证数据是否正确插入：

```sql
SELECT 
    id,
    province_code,
    city_code,
    site_id,
    business_type,
    monit_index,
    accuracy,
    precision,
    tenant_id,
    status,
    create_time,
    create_by
FROM tb_schedu_monitor_quality_result 
WHERE tenant_id = '0'
ORDER BY create_time DESC;
```

**预期结果**:
- `tenant_id` 字段应该都是 "0"
- `status` 字段应该都是 "A"
- `create_time` 应该是插入时的时间
- `create_by` 应该是当前用户名或"system"

## 注意事项

1. **租户ID修复**: 所有记录的 `tenant_id` 都会被设置为 "0"
2. **用户信息获取**: 如果无法获取当前登录用户，会使用 "system" 作为默认值
3. **状态管理**: 新增时如果没有指定状态，会自动设置为 "A"（有效）
4. **ID生成**: 系统会自动生成UUID作为主键，无需手动指定

## 完整的代码修复

修复的核心代码片段：

```java
// 修复前（有问题）
scheduMonitorQualityResult.setTenantId(sysUser.getTenantId()); // ❌ 方法不存在

// 修复后（正确）
scheduMonitorQualityResult.setTenantId("0"); // ✅ 设置固定值
```

这样修复后，所有的接口都可以正常工作，不会再出现 `getTenantId()` 方法不存在的错误。
