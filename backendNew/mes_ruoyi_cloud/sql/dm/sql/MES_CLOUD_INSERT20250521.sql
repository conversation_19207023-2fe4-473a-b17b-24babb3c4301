insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '性别男');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '性别女');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '性别未知');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '显示菜单');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '隐藏菜单');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '正常状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '停用状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '正常状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '停用状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '默认分组');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '系统分组');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '系统默认是');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '系统默认否');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '通知');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '公告');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2025-05-21 10:33:21', '', null, '正常状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '关闭状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '其他操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '新增操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '修改操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '删除操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '授权操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-05-21 10:33:21', '', null, '导出操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '导入操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '强退操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '生成操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '清空操作');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '正常状态');
insert into "MES_CLOUD"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-05-21 10:33:22', '', null, '停用状态');

insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-05-21 10:33:21', '', null, '用户性别列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-05-21 10:33:21', '', null, '菜单状态列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-05-21 10:33:21', '', null, '系统开关列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-05-21 10:33:21', '', null, '任务状态列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-05-21 10:33:21', '', null, '任务分组列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (6, '系统是否', 'sys_yes_no', '0', 'admin', '2025-05-21 10:33:21', '', null, '系统是否列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (7, '通知类型', 'sys_notice_type', '0', 'admin', '2025-05-21 10:33:21', '', null, '通知类型列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (8, '通知状态', 'sys_notice_status', '0', 'admin', '2025-05-21 10:33:21', '', null, '通知状态列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (9, '操作类型', 'sys_oper_type', '0', 'admin', '2025-05-21 10:33:21', '', null, '操作类型列表');
insert into "MES_CLOUD"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10, '系统状态', 'sys_common_status', '0', 'admin', '2025-05-21 10:33:21', '', null, '登录状态列表');

insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (100, 0, '0', '中国环境监测总站', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (101, 100, '0,100', '供应商', 10, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-05-21 10:33:20', 'admin', '2025-06-03 10:33:08');
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (103, 101, '0,100,101', '铁塔', 1, '胡庆云', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-05-21 10:33:20', 'admin', '2025-06-03 10:27:16');
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (105, 101, '0,100,101', '研景', 3, '卢彦林', '15222222222', '<EMAIL>', '0', '0', 'admin', '2025-05-21 10:33:20', 'admin', '2025-06-03 10:31:27');
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2025-05-21 10:33:20', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (200, 101, '0,100,101', '叙诚', 3, '甘振业', null, null, '0', '0', 'admin', '2025-06-03 10:32:29', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (201, 100, '0,100', '水室', 1, null, null, null, '0', '0', 'admin', '2025-06-03 10:32:57', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (202, 100, '0,100', '综合室', 2, null, null, null, '0', '0', 'admin', '2025-06-03 10:33:25', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (203, 100, '0,100', '土壤室', 3, null, null, null, '0', '0', 'admin', '2025-06-03 10:33:40', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (204, 100, '0,100', '大气室', 4, null, null, null, '0', '0', 'admin', '2025-06-03 10:33:51', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (205, 100, '0,100', '生态室', 5, null, null, null, '0', '0', 'admin', '2025-06-03 10:34:10', '', null);
insert into "MES_CLOUD"."SYS_DEPT" ("DEPT_ID", "PARENT_ID", "ANCESTORS", "DEPT_NAME", "ORDER_NUM", "LEADER", "PHONE", "EMAIL", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME") values (206, 100, '0,100', '站领导', 0, null, null, null, '0', '0', 'admin', '2025-06-03 10:34:22', '', null);

insert into "MES_CLOUD"."SYS_USER" ("USER_ID", "DEPT_ID", "USER_NAME", "NICK_NAME", "USER_TYPE", "EMAIL", "PHONENUMBER", "SEX", "AVATAR", "PASSWORD", "STATUS", "DEL_FLAG", "LOGIN_IP", "LOGIN_DATE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, 202, 'admin', '管理员账号', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$klAcdMsRYAyhz4U/BjR6cerRJHDu45wy5n8sNJ37PvvwF0Wh9asAS', '0', '0', '127.0.0.1', '2025-06-04 00:48:59', 'admin', '2025-05-21 10:33:20', '', '2025-06-04 00:44:32', '管理员');

insert into "MES_CLOUD"."SYS_EXPRESSION" ("ID", "NAME", "EXPRESSION", "CREATE_TIME", "UPDATE_TIME", "CREATE_BY", "UPDATE_BY", "STATUS", "REMARK") values (3, '流程发起人', '${INITIATOR}', '2023-11-04 00:00:45', null, null, null, 0, null);
insert into "MES_CLOUD"."SYS_EXPRESSION" ("ID", "NAME", "EXPRESSION", "CREATE_TIME", "UPDATE_TIME", "CREATE_BY", "UPDATE_BY", "STATUS", "REMARK") values (4, '审批流程指定接收人', '#{approval}', '2023-11-04 00:00:59', null, null, null, 0, null);

insert into "MES_CLOUD"."SYS_FORM" ("FORM_ID", "FORM_NAME", "FORM_CONTENT", "CREATE_TIME", "UPDATE_TIME", "CREATE_BY", "UPDATE_BY", "REMARK") values (12, '测试表单1', '{"list":[{"type":"BackgroundImage","label":"背景","options":{"imgurl":"","style":""},"key":"BackgroundImage_16990265256522","model":"BackgroundImage_16990265256522","labelWidth":-1,"width":"80%","span":24},{"type":"tableGrid","layout":true,"options":{"customStyle":"","customClass":"","bordered":true,"bright":true,"small":true,"hidden":false},"label":"网格布局","labelWidth":0,"width":"100%","span":24,"key":"tableGrid_16990265685142","model":"tableGrid_16990265685142","grids":{"list":[{"key":1699026568508,"list":[{"type":"input","options":{"defaultValue":"","type":"text","prepend":"","append":"","placeholder":"请输入","maxLength":0,"clearable":false,"hidden":false,"disabled":false},"label":"输入框","labelWidth":-1,"width":"100%","span":24,"model":"input_16990265768442","key":"input_16990265768442","rules":[{"required":false,"message":"必填项","trigger":["blur"]}]}],"colStart":1,"colEnd":1,"rowStart":1,"rowEnd":1,"show":true,"style":"height:100%;","class":""},{"key":1699026568508,"list":[{"type":"input","options":{"defaultValue":"","type":"text","prepend":"","append":"","placeholder":"请输入","maxLength":0,"clearable":false,"hidden":false,"disabled":false},"label":"输入框","labelWidth":-1,"width":"100%","span":24,"model":"input_16990265944432","key":"input_16990265944432","rules":[{"required":false,"message":"必填项","trigger":["blur"]}]}],"colStart":2,"colEnd":2,"rowStart":1,"rowEnd":1,"show":true,"style":"height:100%;","class":""},{"key":1699026568508,"list":[{"type":"input","options":{"defaultValue":"","type":"text","prepend":"","append":"","placeholder":"请输入","maxLength":0,"clearable":false,"hidden":false,"disabled":false},"label":"输入框","labelWidth":-1,"width":"100%","span":24,"model":"input_1699026579680","key":"input_1699026579680","rules":[{"required":false,"message":"必填项","trigger":["blur"]}]}],"colStart":1,"colEnd":1,"rowStart":2,"rowEnd":2,"show":true,"style":"height:100%;","class":""},{"key":1699026568508,"list":[{"type":"input","options":{"defaultValue":"","type":"text","prepend":"","append":"","placeholder":"请输入","maxLength":0,"clearable":false,"hidden":false,"disabled":false},"label":"输入框","labelWidth":-1,"width":"100%","span":24,"model":"input_16990265964342","key":"input_16990265964342","rules":[{"required":false,"message":"必填项","trigger":["blur"]}]}],"colStart":2,"colEnd":2,"rowStart":2,"rowEnd":2,"show":true,"style":"height:100%;","class":""}],"rowNum":2,"colNum":2}},{"type":"divider","options":{"direction":"horizontal","orientation":"center","hidden":false,"disabled":false},"label":"分割线","labelWidth":0,"width":"100%","span":24,"key":"divider_16990266074122","model":"divider_16990266074122"},{"type":"uploadImg","options":{"action":"","responseFileUrl":"","listType":"picture-card","limitSize":10,"defaultValue":[],"multiple":false,"limit":3,"hidden":false,"disabled":false,"uploadHidden":false,"headers":[]},"label":"上传图片","labelWidth":-1,"width":"100%","span":24,"model":"uploadImg_16990266251522","key":"uploadImg_16990266251522","rules":[{"required":false,"message":"必填项","trigger":["blur"]}]},{"type":"state","options":{"defaultValue":"","maxLevel":3,"oneByOne":false,"showAllPath":false,"separator":"/","showSearch":true,"hidden":false,"disabled":false},"label":"区划选择","labelWidth":-1,"width":"100%","span":24,"model":"state_16990266148272","key":"state_16990266148272","rules":[{"required":false,"message":"必填项","trigger":["blur","change"]}]}],"config":{"labelPosition":"left","labelWidth":100,"size":"mini","outputHidden":true,"hideRequiredMark":false,"syncLabelRequired":false,"customStyle":""}}', '2023-11-03 23:50:47', null, null, null, '测试');

insert into "MES_CLOUD"."SYS_JOB" ("JOB_ID", "JOB_NAME", "JOB_GROUP", "INVOKE_TARGET", "CRON_EXPRESSION", "MISFIRE_POLICY", "CONCURRENT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-05-21 10:33:22', '', null, '');
insert into "MES_CLOUD"."SYS_JOB" ("JOB_ID", "JOB_NAME", "JOB_GROUP", "INVOKE_TARGET", "CRON_EXPRESSION", "MISFIRE_POLICY", "CONCURRENT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(''ry'')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-05-21 10:33:22', '', null, '');
insert into "MES_CLOUD"."SYS_JOB" ("JOB_ID", "JOB_NAME", "JOB_GROUP", "INVOKE_TARGET", "CRON_EXPRESSION", "MISFIRE_POLICY", "CONCURRENT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(''ry'', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-05-21 10:33:22', '', null, '');

insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '首页', 0, 1, '', '', null, '', 1, 0, 'M', '0', '0', '', 'dashboard', 'admin', '2025-05-28 08:49:12', 'admin', '2025-05-28 08:52:50', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '监控预警', 0, 7, 'moniwarn', 'moniwarn', null, '', 1, 0, 'M', '0', '0', '', 'eye-open', 'admin', '2025-05-28 08:43:51', 'admin', '2025-05-28 08:58:00', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (3, '数据采集与处理', 0, 2, 'dataacquisition', 'dataacquisition', null, '', 1, 0, 'M', '0', '0', null, 'chart', 'admin', '2025-05-28 08:51:21', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (4, '数据审核', 0, 3, 'dataaudit', 'dataaudit', null, '', 1, 0, 'M', '0', '0', null, 'checkbox', 'admin', '2025-05-28 08:53:22', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (5, '智能调度', 0, 4, 'smartschedu', 'smartschedu', null, '', 1, 0, 'M', '0', '0', '', 'guide', 'admin', '2025-05-28 08:54:10', 'admin', '2025-05-28 08:54:37', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (6, '监测活动', 0, 5, 'moniactivitie', 'moniactivitie', null, '', 1, 0, 'M', '0', '0', null, 'component', 'admin', '2025-05-28 08:55:26', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (7, '资源使用管理', 0, 6, 'resourcemgr', 'resourcemgr', null, '', 1, 0, 'M', '0', '0', null, 'list', 'admin', '2025-05-28 08:56:10', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (8, '质量监督', 0, 8, 'quality', 'quality', null, '', 1, 0, 'M', '0', '0', null, 'bug', 'admin', '2025-05-28 08:57:13', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101, '系统管理', 1, 1, 'system', null, null, '', 1, 0, 'M', '0', '0', null, 'system', 'admin', '2022-11-18 09:53:56', 'admin', '2023-02-09 11:31:22', '系统管理目录');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1011, '菜单管理', 101, 1, 'menu', 'system/menu/index', null, '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2022-03-05 20:23:56', null, null, '菜单管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1012, '字典管理', 101, 2, 'dict', 'system/dict/index', null, '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2022-03-05 20:23:56', null, null, '字典管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1013, '参数设置', 101, 3, 'config', 'system/config/index', null, '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2022-03-05 20:23:56', null, null, '参数设置菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1014, '通知公告', 101, 4, 'notice', 'system/notice/index', null, '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2022-03-05 20:23:56', null, null, '通知公告菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1015, '日志管理', 101, 5, 'log', null, null, '', 1, 0, 'M', '0', '0', null, 'log', 'admin', '2022-03-05 20:23:56', null, null, '日志管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1016, '组织架构', 101, 6, 'organizationStructure', null, null, '', 1, 0, 'M', '0', '0', null, 'client', 'admin', '2022-09-08 09:43:52', 'A008211', '2023-05-10 17:13:10', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10111, '菜单查询', 1011, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10112, '菜单新增', 1011, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10113, '菜单修改', 1011, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10114, '菜单删除', 1011, 4, null, null, null, '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10121, '字典查询', 1012, 1, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10122, '字典新增', 1012, 2, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10123, '字典修改', 1012, 3, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10124, '字典删除', 1012, 4, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10125, '字典导出', 1012, 5, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10131, '参数查询', 1013, 1, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10132, '参数新增', 1013, 2, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10133, '参数修改', 1013, 3, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10134, '参数删除', 1013, 4, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10135, '参数导出', 1013, 5, '#', null, null, '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10151, '操作日志', 1015, 1, 'operlog', 'system/operlog/index', null, '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 'admin', '2022-03-05 20:23:56', null, null, '操作日志菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10152, '登录日志', 1015, 2, 'logininfor', 'system/logininfor/index', null, '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 'admin', '2022-03-05 20:23:56', null, null, '登录日志菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10161, '用户管理', 1016, 1, 'user', 'system/user/index', null, '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2022-03-05 20:23:56', 'admin', '2022-09-08 09:44:11', '用户管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10162, '角色管理', 1016, 2, 'role', 'system/role/index', null, '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2022-03-05 20:23:56', 'admin', '2022-09-08 09:44:21', '角色管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10163, '部门管理', 1016, 4, 'dept', 'system/dept/index', null, '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2022-03-05 20:23:56', 'admin', '2022-09-08 09:44:43', '部门管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10164, '岗位管理', 1016, 5, 'post', 'system/post/index', null, '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2022-03-05 20:23:56', 'admin', '2022-09-08 09:44:51', '岗位管理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101621, '角色查询', 10162, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101622, '角色新增', 10162, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101623, '角色修改', 10162, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101624, '角色删除', 10162, 4, null, null, null, '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101625, '角色导出', 10162, 5, null, null, null, '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101631, '部门查询', 10163, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101632, '部门新增', 10163, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101633, '部门修改', 10163, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (101634, '部门删除', 10163, 4, null, null, null, '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (103, '系统工具', 1, 3, 'tool', null, null, '', 1, 0, 'M', '0', '0', null, 'tool', 'admin', '2022-03-05 20:23:56', 'admin', '2022-11-03 16:06:13', '系统工具目录');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (102, '系统监控', 1, 2, 'monitor', null, null, '', 1, 0, 'M', '0', '0', null, 'monitor', 'admin', '2022-03-05 20:23:56', 'admin', '2022-12-29 11:31:12', '系统监控目录');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1021, '在线用户', 102, 1, 'online', 'monitor/online/index', null, '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2022-03-05 20:23:56', null, null, '在线用户菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1022, '定时任务', 102, 102, 'job', 'monitor/job/index', null, '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2022-03-05 20:23:56', null, null, '定时任务菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1023, 'Sentinel控制台', 102, 3, 'http://192.168.2.210:8718/#/dashboard/', null, null, '', 0, 0, 'C', '0', '0', 'monitor:sentinel:list', 'sentinel', 'admin', '2022-03-05 20:23:56', 'admin', '2022-11-18 20:48:57', '流量控制菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1024, 'Nacos控制台', 102, 4, 'http://127.0.0.1:8848/nacos/index.html', null, null, '', 0, 0, 'C', '0', '0', 'monitor:nacos:list', 'nacos', 'admin', '2022-03-05 20:23:56', 'admin', '2023-09-01 14:03:26', '服务治理菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10211, '在线查询', 1021, 1, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10212, '批量强退', 1021, 2, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10213, '单条强退', 1021, 3, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10221, '任务查询', 1022, 1, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10222, '任务新增', 1022, 2, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10223, '任务修改', 1022, 3, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10224, '任务删除', 1022, 4, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10225, '状态修改', 1022, 5, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10226, '任务导出', 1022, 7, '#', null, null, '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2022-03-05 20:23:56', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1025, 'Admin控制台', 102, 5, 'http://192.168.2.210:9100/login', null, null, '', 0, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2022-03-05 20:23:56', 'admin', '2022-11-19 08:15:01', '服务监控菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104, '工作流中心', 1, 1, 'workflowCenter', null, null, '', 1, 0, 'M', '0', '0', null, 'workflow', 'admin', '2022-10-11 10:51:02', 'admin', '2023-02-09 11:31:18', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1031, '代码生成', 103, 2, 'gen', 'tool/gen/index', null, '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2022-03-05 20:23:56', 'admin', '2023-11-03 22:48:39', '代码生成菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1032, '系统接口', 103, 3, 'http://192.168.2.210:8088/swagger-ui/index.html', null, null, '', 0, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2022-03-05 20:23:56', 'admin', '2022-06-24 13:24:52', '系统接口菜单');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1041, '流程管理', 104, 0, 'process-list', null, null, '', 1, 0, 'M', '0', '0', null, 'process-list', 'admin', '2022-10-11 14:35:47', 'admin', '2022-10-11 16:14:45', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1042, '任务管理', 104, 1, 'taskmanger', null, null, '', 1, 0, 'M', '0', '0', null, 'taskmanger', 'admin', '2022-10-11 16:02:10', 'admin', '2023-03-14 11:02:29', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10411, '流程定义', 1041, 1, 'definition', 'flowable/definition/index', null, '', 1, 0, 'C', '0', '0', 'flowable:definition:list', 'processdefinition', 'admin', '2022-10-11 10:54:09', 'admin', '2022-10-15 08:36:59', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10412, '表单配置', 1041, 2, 'form', 'flowable/task/form/index', null, '', 1, 1, 'C', '0', '0', 'flowable:form:list', 'tree-table', 'admin', '2022-10-11 14:37:41', 'admin', '2022-10-20 10:39:18', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10413, '流程监听', 1041, 3, 'listener', 'flowable/listener/index', null, '', 1, 0, 'C', '0', '0', 'flowable:listener:list', 'items', 'admin', '2023-11-03 23:00:50', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10414, '流程表达式', 1041, 4, 'flowExp', 'flowable/expression/index', null, '', 1, 0, 'C', '0', '0', 'flowable:expression:list', 'items', 'admin', '2023-11-03 23:01:41', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104111, '编辑', 10411, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:definition:edit', '#', 'admin', '2022-10-15 10:48:31', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104112, '挂起/激活', 10411, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:definition:state', '#', 'admin', '2022-10-15 10:49:06', 'admin', '2022-10-15 10:59:17', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104113, '配置表单', 10411, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:definition:configureform', '#', 'admin', '2022-10-15 10:49:54', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104114, '删除', 10411, 4, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:definition:del', '#', 'admin', '2022-10-15 10:51:48', 'admin', '2022-10-15 10:59:01', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104115, '定义导入', 10411, 5, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:definition:export', '#', 'admin', '2022-10-15 11:11:31', 'admin', '2022-10-15 11:11:42', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104121, '新增', 10412, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:form:add', '#', 'admin', '2022-10-15 11:17:49', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104122, '编辑', 10412, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:form:edit', '#', 'admin', '2022-10-15 11:19:01', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104123, '删除', 10412, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:form:del', '#', 'admin', '2022-10-15 11:19:17', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104131, '查询', 10413, 1, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:listener:query', '#', 'admin', '2023-11-03 23:02:14', 'admin', '2023-11-03 23:02:40', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104132, '新增', 10413, 2, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:listener:add', '#', 'admin', '2023-11-03 23:03:30', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104133, '导出', 10413, 3, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:listener:export', '#', 'admin', '2023-11-03 23:11:26', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104134, '编辑', 10413, 4, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:listener:edit', '#', 'admin', '2023-11-03 23:11:52', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104135, '删除', 10413, 5, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:listener:remove', '#', 'admin', '2023-11-03 23:12:13', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104141, '查询', 10414, 1, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:expression:query', '#', 'admin', '2023-11-03 23:03:06', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104142, '新增', 10414, 2, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:expression:add', '#', 'admin', '2023-11-03 23:12:46', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104143, '编辑', 10414, 3, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:expression:edit', '#', 'admin', '2023-11-03 23:13:05', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104144, '删除', 10414, 4, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:expression:remove', '#', 'admin', '2023-11-03 23:13:24', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104145, '导出', 10414, 5, '', null, null, '', 1, 0, 'F', '0', '0', 'flowable:expression:export', '#', 'admin', '2023-11-03 23:13:40', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10421, '我的流程', 1042, 1, 'process', 'flowable/task/process/index', null, '', 1, 0, 'C', '0', '0', 'flowable:task:myProcess', 'myprocess', 'admin', '2022-10-11 13:42:04', 'admin', '2022-12-05 11:22:50', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10422, '待办任务', 1042, 2, 'todo', 'flowable/task/todo/index', null, '', 1, 0, 'C', '0', '0', 'flowable:task:todoList', 'process-sent1', 'admin', '2022-10-11 13:54:38', 'admin', '2022-10-15 09:47:02', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (10423, '已办任务', 1042, 3, 'finished', 'flowable/task/finished/index', null, '', 1, 0, 'C', '0', '0', 'flowable:task:finishedList', 'process-sent2', 'admin', '2022-10-11 13:55:42', 'admin', '2022-10-15 09:47:18', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104211, '流程新增', 10421, 1, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:deployment:add', '#', 'admin', '2022-10-11 13:47:35', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104212, '取消申请', 10421, 2, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:task:stopProcess', '#', 'admin', '2022-10-11 13:47:59', 'admin', '2022-10-15 11:35:11', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104213, '流程删除', 10421, 3, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:instance:del', '#', 'admin', '2022-10-11 13:48:34', 'admin', '2022-10-15 11:40:56', null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (104214, '流程详情', 10421, 4, null, null, null, '', 1, 0, 'F', '0', '0', 'flowable:deployment:list', '#', 'admin', '2022-10-15 11:30:57', null, null, null);
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (105, '可视化管理', 1, 4, 'dataroom', null, null, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', '2023-10-13 23:22:51', 'admin', '2023-11-06 21:12:36', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1074, '监控预警', 2, 6, 'moniwarn', null, null, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-05-27 15:01:09', 'admin', '2025-05-27 15:57:26', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1075, '监控项目管理', 2, 1, 'monimanage', null, null, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-05-27 15:04:52', 'admin', '2025-05-27 16:43:47', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1076, '监控项目配置', 1075, 1, 'moniproject', 'moniwarn/moniManage/moniProject/index', '{"systemCode":"moniwarn"}', '', 1, 0, 'C', '0', '0', '', 'monitor', 'admin', '2025-05-27 15:08:07', 'admin', '2025-05-27 17:59:39', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1077, '项目分类配置', 1075, 2, 'category', null, null, '', 1, 0, 'C', '0', '0', '', 'items', 'admin', '2025-05-27 15:12:52', 'admin', '2025-05-27 15:19:24', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1078, '监控参数配置', 1075, 3, 'parameter', null, null, '', 1, 0, 'C', '0', '0', '', 'items', 'admin', '2025-05-27 15:13:52', 'admin', '2025-05-27 15:19:34', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1079, '监控参数综合查询', 1075, 4, 'parameterQuery', null, null, '', 1, 0, 'C', '0', '0', '', 'items', 'admin', '2025-05-27 15:15:25', 'admin', '2025-05-27 15:19:47', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1080, '水类参数实时监控', 1075, 5, 'waterParameters', null, null, '', 1, 0, 'C', '0', '0', '', 'items', 'admin', '2025-05-27 15:16:43', 'admin', '2025-05-27 15:19:57', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1081, '气类参数实时监控', 1075, 6, 'gasParameters', null, null, '', 1, 0, 'C', '0', '0', '', 'items', 'admin', '2025-05-27 15:17:41', 'admin', '2025-05-27 15:20:06', '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1082, '预警规则管理', 2, 2, 'warnRule', null, null, '', 1, 0, 'M', '0', '0', null, 'taskmanger', 'admin', '2025-05-27 15:18:56', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1083, '预警数据管理', 2, 3, 'warnData', null, null, '', 1, 0, 'M', '0', '0', null, 'documentation', 'admin', '2025-05-27 15:21:12', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1084, '审批管理', 2, 4, 'examineApprove', null, null, '', 1, 0, 'M', '0', '0', null, 'items', 'admin', '2025-05-27 15:23:13', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1085, '预警数据统计', 2, 5, 'warnStat', null, null, '', 1, 0, 'M', '0', '0', null, 'items', 'admin', '2025-05-27 15:23:44', '', null, '');
insert into "MES_CLOUD"."SYS_MENU" ("MENU_ID", "MENU_NAME", "PARENT_ID", "ORDER_NUM", "PATH", "COMPONENT", "QUERY", "ROUTE_NAME", "IS_FRAME", "IS_CACHE", "MENU_TYPE", "VISIBLE", "STATUS", "PERMS", "ICON", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1086, '监测大屏', 2, 6, 'moniScreen', null, null, '', 1, 0, 'M', '0', '0', null, 'items', 'admin', '2025-05-27 15:24:32', '', null, '');


insert into "MES_CLOUD"."SYS_NOTICE" ("NOTICE_ID", "NOTICE_TITLE", "NOTICE_TYPE", "NOTICE_CONTENT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '温馨提醒：2018-07-01 mes新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2025-05-21 10:33:22', '', null, '管理员');
insert into "MES_CLOUD"."SYS_NOTICE" ("NOTICE_ID", "NOTICE_TITLE", "NOTICE_TYPE", "NOTICE_CONTENT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '维护通知：2018-07-01 mes系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2025-05-21 10:33:22', '', null, '管理员');

insert into "MES_CLOUD"."SYS_POST" ("POST_ID", "POST_CODE", "POST_NAME", "POST_SORT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, 'code', '研发', 1, '0', 'admin', '2025-05-21 10:33:20', 'admin', '2025-06-03 10:35:51', '');
insert into "MES_CLOUD"."SYS_POST" ("POST_ID", "POST_CODE", "POST_NAME", "POST_SORT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, 'se', '项目经理', 2, '0', 'admin', '2025-05-21 10:33:20', '', null, '');

insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (2, '712e67d2-4889-11ee-9429-58112292f651', '测试123');
insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (3, '6b79974c-48c2-11ee-950a-58112292f651', 'test123');
insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (7, 'd3223c40-48c4-11ee-950a-58112292f651', '111111');
insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (8, 'f87ebffe-48c4-11ee-950a-58112292f651', '2222');
insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (475, '72f5ca08-6aa6-11ee-b216-00e012317744', '测试01');
insert into "MES_CLOUD"."SYS_PROCESS_TITLE" ("ID", "PROC_INS_ID", "PROCESS_TITLE") values (476, '0b0a2860-7a63-11ee-a44f-00e012317744', '测试流程001');

insert into "MES_CLOUD"."SYS_ROLE" ("ROLE_ID", "ROLE_NAME", "ROLE_KEY", "ROLE_SORT", "DATA_SCOPE", "MENU_CHECK_STRICTLY", "DEPT_CHECK_STRICTLY", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-05-21 10:33:21', '', null, '超级管理员');
insert into "MES_CLOUD"."SYS_ROLE" ("ROLE_ID", "ROLE_NAME", "ROLE_KEY", "ROLE_SORT", "DATA_SCOPE", "MENU_CHECK_STRICTLY", "DEPT_CHECK_STRICTLY", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2025-05-21 10:33:21', 'admin', '2025-06-03 10:48:32', '普通角色');
insert into "MES_CLOUD"."SYS_ROLE" ("ROLE_ID", "ROLE_NAME", "ROLE_KEY", "ROLE_SORT", "DATA_SCOPE", "MENU_CHECK_STRICTLY", "DEPT_CHECK_STRICTLY", "STATUS", "DEL_FLAG", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (100, '游客', 'visitor', 10, '1', 1, 1, '0', '0', 'admin', '2025-06-04 00:53:51', '', null, null);


insert into "MES_CLOUD"."SYS_ROLE_DEPT" ("ROLE_ID", "DEPT_ID") values (2, 100);
insert into "MES_CLOUD"."SYS_ROLE_DEPT" ("ROLE_ID", "DEPT_ID") values (2, 101);
insert into "MES_CLOUD"."SYS_ROLE_DEPT" ("ROLE_ID", "DEPT_ID") values (2, 105);

insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 1);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 101);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 1014);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 1041);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 1042);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10411);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10412);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10413);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10414);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10421);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10422);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 10423);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104111);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104112);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104113);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104114);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104115);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104121);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104122);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104123);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104131);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104132);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104133);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104134);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104135);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104141);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104142);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104143);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104144);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104145);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104211);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104212);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104213);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (2, 104214);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (100, 1);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (100, 101);
insert into "MES_CLOUD"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") values (100, 1014);

insert into "MES_CLOUD"."SYS_USER_POST" ("USER_ID", "POST_ID") values (1, 1);
insert into "MES_CLOUD"."SYS_USER_POST" ("USER_ID", "POST_ID") values (2, 2);

insert into "MES_CLOUD"."SYS_USER_ROLE" ("USER_ID", "ROLE_ID") values (1, 1);
insert into "MES_CLOUD"."SYS_USER_ROLE" ("USER_ID", "ROLE_ID") values (2, 2);

insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('batch.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('cfg.execution-related-entities-count', 'true', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('cfg.task-related-entities-count', 'true', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('common.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('entitylink.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('eventsubscription.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('identitylink.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('job.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('next.dbid', '1', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('schema.history', 'create(*******)', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('task.schema.version', '*******', 1);
insert into "MES_CLOUD"."ACT_GE_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('variable.schema.version', '*******', 1);

insert into "MES_CLOUD"."ACT_ID_PROPERTY" ("NAME_", "VALUE_", "REV_") values ('schema.version', '*******', 1);

insert into "MES_CLOUD"."FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") values ('1', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2025-05-21 14:37:12', 1, 'EXECUTED', '8:1b0c48c9cf7945be799d868a2626d687', 'createTable tableName=FLW_EVENT_DEPLOYMENT; createTable tableName=FLW_EVENT_RESOURCE; createTable tableName=FLW_EVENT_DEFINITION; createIndex indexName=ACT_IDX_EVENT_DEF_UNIQ, tableName=FLW_EVENT_DEFINITION; createTable tableName=FLW_CHANNEL_DEFIN...', '', null, '4.9.1', null, null, '7809432029');
insert into "MES_CLOUD"."FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") values ('2', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2025-05-21 14:37:12', 2, 'EXECUTED', '8:0ea825feb8e470558f0b5754352b9cda', 'addColumn tableName=FLW_CHANNEL_DEFINITION; addColumn tableName=FLW_CHANNEL_DEFINITION', '', null, '4.9.1', null, null, '7809432029');
insert into "MES_CLOUD"."FLW_EV_DATABASECHANGELOG" ("ID", "AUTHOR", "FILENAME", "DATEEXECUTED", "ORDEREXECUTED", "EXECTYPE", "MD5SUM", "DESCRIPTION", "COMMENTS", "TAG", "LIQUIBASE", "CONTEXTS", "LABELS", "DEPLOYMENT_ID") values ('3', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2025-05-21 14:37:12', 3, 'EXECUTED', '8:3c2bb293350b5cbe6504331980c9dcee', 'customChange', '', null, '4.9.1', null, null, '7809432029');

insert into "MES_CLOUD"."FLW_EV_DATABASECHANGELOGLOCK" ("ID", "LOCKED", "LOCKGRANTED", "LOCKEDBY") values (1, 0, null, null);

insert into "MES_CLOUD"."SYS_CONFIG" ("CONFIG_ID", "CONFIG_NAME", "CONFIG_KEY", "CONFIG_VALUE", "CONFIG_TYPE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-05-21 10:33:22', '', null, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
insert into "MES_CLOUD"."SYS_CONFIG" ("CONFIG_ID", "CONFIG_NAME", "CONFIG_KEY", "CONFIG_VALUE", "CONFIG_TYPE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-05-21 10:33:22', '', null, '初始化密码 123456');
insert into "MES_CLOUD"."SYS_CONFIG" ("CONFIG_ID", "CONFIG_NAME", "CONFIG_KEY", "CONFIG_VALUE", "CONFIG_TYPE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-05-21 10:33:22', '', null, '深色主题theme-dark，浅色主题theme-light');
insert into "MES_CLOUD"."SYS_CONFIG" ("CONFIG_ID", "CONFIG_NAME", "CONFIG_KEY", "CONFIG_VALUE", "CONFIG_TYPE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-05-21 10:33:22', '', null, '是否开启注册用户功能（true开启，false关闭）');
insert into "MES_CLOUD"."SYS_CONFIG" ("CONFIG_ID", "CONFIG_NAME", "CONFIG_KEY", "CONFIG_VALUE", "CONFIG_TYPE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") values (5, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-05-21 10:33:22', '', null, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

insert into "MES_CLOUD"."SYS_DEPLOY_FORM" ("ID", "FORM_ID", "DEPLOY_ID") values (19, 9, '7ce6e32c-48c4-11ee-950a-58112292f651');
insert into "MES_CLOUD"."SYS_DEPLOY_FORM" ("ID", "FORM_ID", "DEPLOY_ID") values (23, 11, '4885af14-6aa6-11ee-b216-00e012317744');
insert into "MES_CLOUD"."SYS_DEPLOY_FORM" ("ID", "FORM_ID", "DEPLOY_ID") values (24, 12, '4e07ed5c-7a62-11ee-a44f-00e012317744');
