<!DOCTYPE html>
<!-- saved from url=(0071)/index.html#requests -->
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="initial-scale=1, minimum-scale=1, width=device-width">
    <meta name="twitter:widgets:csp" content="on">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/prism.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/sourceSansPro.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/simple-line-icons.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/talend-icons-webfont.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/select2-4.0.3.min.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/fonts/api-tester/style.css">
    <!-- the following order is very important -->
    <link rel="stylesheet" type="text/css" href="rocketapi/dist/chromeapp.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/font-awesome.4.2.0.min.css">
    <link rel="stylesheet" type="text/css" href="rocketapi/css/font-awesome.3.2.1.enriched-for-client.css">
    <link rel="stylesheet" data-name="vs/editor/editor.main" href="rocketapi/monaco-editor/min/vs/editor/editor.main.css">

    <link rel="shortcut icon"
          href="data:image/svg+xml;base64, 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"
          type="image/x-icon"/>
    <title>NEW</title>

    <meta name="gwt:property" content="locale=en">
    <style>.GJ3SYAHDCH, .GJ3SYAHDEH {
        cursor: pointer;
        zoom: 1;
    }

    .GJ3SYAHDDH {
        background: #ffc;
    }

    .GJ3SYAHDFH {
        height: 26px;
        overflow: hidden;
        background: url("data:image/png;base64,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") -0px -0px repeat-x;
        background-color: #628cd5;
        color: white;
        height: auto;
        overflow: visible;
    }</style>

    <script type="text/javascript" src="https://s9.cnzz.com/z_stat.php?id=1280159263&web_id=1280159263"></script>
</head>

<body class="main chromeapp free-edition" id="console">
<div id="loader">
    <div class="tc-app-loader-container" role="status">
        <div class="tc-app-loader-icon">
            <div class="tc-app-loader">
                <div class="spinner-wrapper">
                    <div class="spinner-left">
                        <div class="circle"></div>
                    </div>
                    <div class="spinner-right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<input type="file" id="filePicker" style="display: none">


<!-- Google Analytics -->
<!-- @formatter:off -->
<!-- @formatter:on -->


<!-- ====================================== -->
<!-- Part that differs between dev and prod -->
<!-- ====================================== -->
<!-- Empty on that file tho -->


<div aria-hidden="true" style="display: none;"></div>
<div class="requests">

    <div th:replace="rocketapi/top::top-section"></div>

    <div class="h-splitter" aria-hidden="true" style="display: none;"></div>
    <section class="ui-lay-l left-pane flex-column" id="left-side" style="min-width:325px;">
        <div class="left-pane-header">
            <ul class="nav nav-pills selector">
                <li title="History" onclick="showHistory()" class=" history"><a> <i class="api-tester-icon api-tester-history"></i> 历史记录 </a></li>
                <li title="Repository" onclick="showRepository()" class=" active repository"><a> <i class="api-tester-icon api-tester-repository"></i> 数据服务 </a></li>
            </ul>
            <div class="left-pane-actions" style="position: absolute;left: 246px;">
                <button type="button" class="gwt-Button new-draft-action" style="    margin-top: 7px;" onclick="newRequest(this)" e2e-tag="btn-new-draft"
                        data-original-title="New draft request  (Alt + N)" title=""><i class="sli-icon-doc" style="padding-top:5px;"></i>
                </button>
            </div>
            <div class="hide-pane-l" style="padding: 13px 5px; cursor: pointer;position: absolute;right:5px;">
                <i class="icon-angle-left"></i>
            </div>
        </div>
        <div class="content">

            <div th:replace="rocketapi/history-api::history-api-section"></div>
            <div th:replace="rocketapi/history-example::history-example-section"></div>

            <div th:replace="rocketapi/repository::repository-section"></div>

        </div>
    </section>
    <div class="h-divider" style="left: 23%;">
        <div class="line"></div>
    </div>

    <div class="content-view" style="left: 23%;">
        <div id="editor-panel" class="ui-lay-m" >
            <div class="ui-lay-c" style="bottom: 265px;">
                <div class="ui-lay-cc" style="height:100%;">
                    <!--<div style="height: 13px;"></div>-->
                    <div th:replace="rocketapi/editor::editor-section"></div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="v-divider" aria-hidden="true" style="z-index: 10;bottom:252px;">
                <div class="line"></div>
            </div>
            <section class="v-splitter" title="Show bottom pane" style="bottom: 0px;"></section>
            <div th:replace="rocketapi/bottom::bottom-section"></div>
        </div>

        <div id="example-panel" class="ui-lay-m" style="overflow:scroll;">
            <div class="ui-lay-c">
                <div class="ui-lay-cc">
                    <!--<div style="height: 13px;"></div>-->
                    <div th:replace="rocketapi/example::example-section"></div>
                    <div th:replace="rocketapi/response::response-section"></div>
                </div>
            </div>
        </div>
    </div>

</div>

<!--save as-->
<div class="modal save-dialog in" id="save-dialog" aria-hidden="false" style="display: none">
    <div class="modal-header"><a class="close" onclick="cancelDialogGroup()" data-dismiss="modal">×</a>
        <h3>Save Request <small></small></h3></div>
    <div class="modal-body">
        <div class="form-horizontal">
            <div class="repository-path-selector">
                <div class="flex-container name-form">
                    <div class="left-label"> Name</div>
                    <div class="right-label"><input type="text" class="gwt-TextBox input-xlarge"></div>
                </div>
                <div class="validation-message"></div>
                <div class="path">
                    <div class="flex-container">
                        <div class="path-selector-header left-label"> Path</div>
                        <div class="flex-container path-selector-container right-label"><a class="r-btn" e2e-tag="drive-selector" title="My drive"><i
                                class="api-tester-icon api-tester-drive"></i> <span class="drive-name">My drive</span> <span class="caret"></span></a>
                            <ul class="dropdown-menu" e2e-tag="drives-list">
                                <li><a>My drive</a></li>
                            </ul>
                            <span class="colon">:</span>
                            <div class="path-buttons" e2e-tag="drive-path"><a class="r-btn active root-path" onclick="showDialogGroup()">/</a></div>
                        </div>
                    </div>
                </div>
                <div class="selector-table">
                    <ul class="nav nav-list local-drive">

                    </ul>
                </div>
                <!--<div class="new-path"><a class="r-btn" onclick="addGroup()"><i class="fa fa-plus"></i> <span> Create </span> </a>
                    <div class="new-container-form" aria-hidden="true" style="display: none;">
                        <div class="flex-container"><span class="left-label">Group</span> <input type="text" class="new-item-name input-xlarge">
                            <div class="create-form">
                                <button class="r-btn r-btn-small" onclick="confirmGroup()"><i class="fa fa-check"></i> <span> Add </span></button>
                                <button class="r-btn r-btn-small" onclick="cancelGroup()"><i class="fa fa-times-thin"></i> <span> Cancel </span></button>
                            </div>
                        </div>
                        <div class="validation-message"></div>
                    </div>
                </div>-->
            </div>
            <div class="alert hidden">The target request will be overwritten.</div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="r-btn" e2e-tag="modal-cancel" onclick="cancelDialogGroup()"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                        style="display: none;"></span></button>
        <button class="r-btn r-btn-primary" e2e-tag="modal-ok" onclick="confirmDialog('#editor-section')"><i class="fa fa-download"></i><span>Save</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                                               style="display: none;"></span></button>
    </div>
</div>

<!-- local setting -->
<div class="modal in" id="global-setting" aria-hidden="false" style="min-width: 700px;width:50%;margin: 0px auto; left: calc(50% - 350px);display: none">
    <div class="modal-header"><a class="close" onclick="hideGlobalConfig()" data-dismiss="modal">×</a>
        <h3>Local Setting <small></small></h3></div>
    <div class="modal-body" style="overflow: hidden;width: 100%;padding: 0px;height:450px;">

    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideGlobalConfig()"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                      style="display: none;"></span></button>
        <button class="r-btn r-btn-primary" onclick="saveGlobalConfig()" e2e-tag="modal-ok"><i class="fa fa-save"></i><span>Save</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                   style="display: none;"></span></button>
    </div>
</div>

<!-- config setting -->
<div class="modal in" id="yml-setting" aria-hidden="false" style="min-width: 700px;width:50%;margin: 0px auto; left: calc(50% - 350px);display: none">
    <div class="modal-header"><a class="close" onclick="hideYmlConfig()" data-dismiss="modal">×</a>
        <h3>rocket-api.yml <small></small></h3></div>
    <div class="modal-body" style="overflow: hidden;width: 100%;padding: 0px;height:450px;">

    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideYmlConfig()"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                       style="display: none;"></span></button>
        <button class="r-btn r-btn-primary" onclick="saveYmlGlobalConfig()" e2e-tag="modal-ok"><i class="fa fa-save"></i><span>Save</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                             style="display: none;"></span></button>
    </div>
</div>

<!-- datasource setting -->
<div class="modal in" id="datasource-setting" aria-hidden="false" style="min-width: 700px;width:50%;margin: 0px auto; left: calc(50% - 350px);">
    <div class="modal-header"><a class="close" onclick="hideDataSourceConfig()" data-dismiss="modal">×</a>
        <h3>Data Sources<small></small></h3></div>
    <div class="modal-body" style="overflow: hidden;width: 100%;padding: 0px;height:450px;">
        <div class="db-left">
            <div class="db-tool">
                <div class="btn-group ctrls dropdown-primary">
                    <a class="btn-mini dropdown-toggle db-add" data-toggle="dropdown"><i class="fa fa-plus"></i></a>
                    <ul class="pull-right dropdown-menu db-driver">
                    </ul>
                </div>
                <div class="fa fa-trash-o" onclick="removeDB()"></div>
                <div class="fa fa-files-o" onclick="copyDB()" aria-hidden="true"></div>
            </div>
            <div class="db-split">Data Sources</div>
            <div class="db-list">
                <div><span style="color:gray">Empty</span></div>
            </div>
        </div>
        <div class="db-right">
            <div class="properties control-group">
                <div class="db-driver-icon"><img  src="rocketapi/images/MySQL.png" ></div>
                <input type="hidden" name="id">
                <label class="control-label"> <span class="gwt-InlineHTML"> Name: </span> </label>
                <div class="controls">
                    <input type="text" class="gwt-TextBox input-xlarge" name="name" >
                </div>
                <label class="control-label"> <span class="gwt-InlineHTML"> Comment: </span> </label>
                <div class="controls">
                    <input type="text" class="gwt-TextBox input-xlarge" name="comment">
                </div>


                <label class="control-label"> <span class="gwt-InlineHTML"> User: </span> </label>
                <div class="controls" style="float:left">
                    <input type="text" class="gwt-TextBox input-xlarge" name="user" style="width: 80%;">
                </div>
                <label class="control-label"> <span class="gwt-InlineHTML"> Password: </span> </label>
                <div class="controls" style="float:left">
                    <input type="password" class="gwt-TextBox input-xlarge" name="password" style="width: 80%;">
                </div>
                <label class="control-label" style="clear:both"> <span class="gwt-InlineHTML"> URL: </span> </label>
                <div class="controls">
                    <input type="text" class="gwt-TextBox input-xlarge" name="url" style="    width: calc(100% - 100px);">
                </div>
            </div>
            <div class="db-properties-title">Properties:</div>
            <div class="db-properties">

            </div>

            <div class="db-status">
                <div><input type="hidden" name="driver"/>Driver: <a class="db-driver-info" style="cursor:default" title="com.wangxiaobao.com.MongoDriver">MongoDB</a></div>
                <div><a onclick="testConnect()">Test Connect</a><i class="db-connection-status fa fa-check"></i></span></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideDataSourceConfig()"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                           style="display: none;"></span></button>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="saveDataSourceConfig(false)"><i class="fa fa-check"></i><span>Apply</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                           style="display: none;"></span></button>
        <button class="r-btn r-btn-primary" onclick="saveDataSourceConfig(true)" e2e-tag="modal-ok"><i class="fa fa-save"></i><span>Save</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                                       style="display: none;"></span></button>
    </div>
</div>

<!--目录编辑-->
<div class="modal project-creation-dialog in" id="directory-editor" aria-hidden="false" style="display: none">
    <div class="modal-header"><a class="close" onclick="cancelDialog('#directory-editor')" data-dismiss="modal">×</a>
        <h3>Create a directory <small></small></h3>
    </div>
    <div class="modal-body">
        <div class="form-horizontal">
            <fieldset>
                <div class="control-group">
                    <label class="control-label"> <span class="gwt-InlineHTML"> Name </span> </label>
                    <div class="controls">
                        <input type="text" class="gwt-TextBox input-xlarge" name="name" placeholder="Enter name">
                        <input type="text" class="gwt-TextBox input-xlarge" style="display: none" name="parentId" >
                        <input type="text" class="gwt-TextBox input-xlarge" style="display: none" name="id" >
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label"> <span class="gwt-InlineHTML"> Path </span> </label>
                    <div class="controls">
                        <input type="text" class="gwt-TextBox input-xlarge" name="path" placeholder="Enter path">
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" onclick="cancelDialog('#directory-editor')"><i class="fa fa-remove"></i><span>Cancel</span></button>
        <button class="r-btn r-btn-primary" onclick="saveDirectory()"><i class="fa fa-plus"></i><span>Save</span></button>
    </div>
</div>


<!-- Remote Sync -->
<div class="modal in export-dialog " id="remote-sync" aria-hidden="false" style="display:none;min-width: 700px;width:50%;margin: 0px auto; left: calc(50% - 350px);">
    <div class="modal-header"><a class="close" onclick="hideRemoteSync()" data-dismiss="modal">×</a>
        <h3>Remote Release <small></small></h3></div>
    <div class="form-horizontal">
        <fieldset>
            <div class="control-group" style="margin-top: 20px">
                <label class="control-label file-label"><span> Filter </span></label>
                <div class="controls file-input"><input type="text" style="width: 50%" onkeyup="searchSelectApi('#remote-sync',this)" class="gwt-TextBox"></div>
            </div>
            <div class="control-group browser-repository">
                <div class="controls browser-and-error">
                    <div>
                        <div>
                            <ul class="tree">
                                <li class="level0 drive">
                                    <div class="tree-entry">
                                        <label class="checkbox">
                                            <input type="checkbox" value="on" tabindex="0"><span></span>
                                        </label>
                                        <a href="javascript:;" class="btn btn-link name">
                                            <i class="icon-caret-down"></i>
                                            <i class="node-icon node-icon-hidden"></i>
                                            <span class="gwt-InlineHTML node-text" data-content="(will be created)">My drive</span>
                                        </a>
                                    </div>
                                    <ul style="display: block;" class="api-list-body">
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="items-count">0 item selected</div>
                </div>
            </div>
            <div class="control-group">
                <label class="control-label file-label"><span> Remote URL </span></label>
                <div class="controls file-input"><input style="width: 50%"  type="text" class="remote-url gwt-TextBox"></div>
            </div>
            <div class="control-group">
                <label class="control-label file-label"><span> Secret Key </span></label>
                <div class="controls file-input"><input style="width: 50%"  type="text" class="secret-key gwt-TextBox"></div>
            </div>
        </fieldset>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideRemoteSync()">
            <i class="fa fa-remove"></i><span>Close</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-primary" e2e-tag="modal-cancel" title="增量同步" onclick="remoteSync(1)">
            <i class="fa fa-upload"></i><span>Increment</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-primary" onclick="remoteSync(0)" title="全量同步" e2e-tag="modal-ok">
            <i class="fa fa-upload"></i><span>ALL</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
    </div>
</div>

<!-- export -->
<div class="modal in export-dialog " id="export-dialog" aria-hidden="false" style="display:none;width:50%;left:25%;margin-left:0px;">
    <div class="modal-header"><a class="close" onclick="hideExport()" data-dismiss="modal">×</a>
        <h3>Export <small></small></h3></div>
    <div class="form-horizontal">
        <fieldset>
            <div class="control-group" style="margin-top: 20px">
                <label class="control-label file-label"><span> Filter </span></label>
                <div class="controls file-input"><input type="text" style="width: 50%" onkeyup="searchSelectApi('#export-dialog',this)" class="gwt-TextBox"></div>
            </div>
            <div class="control-group browser-repository">
                <div class="controls browser-and-error">
                    <div>
                        <div>
                            <ul class="tree">
                                <li class="level0 drive">
                                    <div class="tree-entry">
                                        <label class="checkbox">
                                            <input type="checkbox" value="on" tabindex="0"><span></span>
                                        </label>
                                        <a href="javascript:;" class="btn btn-link name">
                                            <i class="icon-caret-down"></i>
                                            <i class="node-icon node-icon-hidden"></i>
                                            <span class="gwt-InlineHTML node-text" data-content="(will be created)">My drive</span>
                                        </a>
                                    </div>
                                    <ul style="display: block;" class="api-list-body">
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="items-count">0 item selected</div>
                </div>
            </div>

            <form class="subform" action="" method="post">
                <div class="control-group">
                    <input name="apiInfoIds" type="hidden"/>
                    <input name="token" type="hidden"/>
                    <label class="control-label file-label"><span> Filename </span></label>
                    <div class="controls file-input"><input style="width: 20%" name="fileName" type="text" class="filename gwt-TextBox">.json</div>
                </div>
            </form>

        </fieldset>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideExport()">
            <i class="fa fa-remove"></i><span>Close</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-primary" e2e-tag="modal-cancel" title="导出" onclick="exportApiInfo()">
            <i class="fa fa-upload"></i><span>Export</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
    </div>
</div>

<!--import-->
<div class="modal in import-dialog" id="import-dialog" aria-hidden="false" style="display:none">
    <div class="modal-header">
        <a class="close" data-dismiss="modal" onclick="hideImport()">×</a>
        <h3>Import API <small></small></h3>
    </div>
    <div class="modal-body">
        <form id="import-form" style="padding: 0px">
            <div class="file-selector" style="height: 36px;">
                <input type="file" class="" name="file" id="g1366" style="display: block;width: auto;height: auto;opacity: inherit;z-index: auto" tabindex="-1">
            </div>
            <div>
                <label class="radio inline"><input type="radio" name="override" value="0" checked>增量导入</label>
                <label class="radio inline"><input type="radio" name="override" value="1">覆盖导入</label>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideImport()">
            <i class="fa fa-remove"></i><span>Close</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-primary" e2e-tag="modal-ok" onclick="importApi()" data-feature="import_postman2">
            <i class="fa fa-check"></i><span>Import</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button></div></div>

<div class="intercom-lightweight-app" aria-live="polite">
    <style id="intercom-lightweight-app-style">
        @keyframes intercom-lightweight-app-launcher {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes intercom-lightweight-app-gradient {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes intercom-lightweight-app-messenger {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        #remote-sync .modal-body{
            padding: 10px 0px;
            overflow: hidden;
            width: 100%;
            height:450px;
        }
        #remote-sync .modal-body .input>span{
            width:150px;
            display: block;
        }
        #remote-sync .modal-body .input input{
            width:80%;
        }
        #remote-sync .modal-body .input{
            width: 80%;
            margin:auto;
        }
        .intercom-lightweight-app {
            position: fixed;
            z-index: 2147483000;
            width: 0;
            height: 0;
            font-family: intercom-font, "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
        }

        .intercom-lightweight-app-gradient {
            position: fixed;
            z-index: 2147483001;
            width: 500px;
            height: 500px;
            bottom: 0;
            right: 0;
            pointer-events: none;
            background: radial-gradient(
                    ellipse at bottom right,
                    rgba(29, 39, 54, 0.16) 0%,
                    rgba(29, 39, 54, 0) 72%);
            animation: intercom-lightweight-app-gradient 200ms ease-out;
        }

        .intercom-lightweight-app-launcher {
            position: fixed;
            z-index: 2147483002;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #0675C1;
            cursor: pointer;
            box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06), 0 2px 32px 0 rgba(0, 0, 0, 0.16);
            animation: intercom-lightweight-app-launcher 250ms ease;
        }

        .intercom-lightweight-app-launcher:focus {
            outline: none;

        }

        .intercom-lightweight-app-launcher-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 60px;
            transition: transform 100ms linear, opacity 80ms linear;
        }

        .intercom-lightweight-app-launcher-icon-open {

            opacity: 1;
            transform: rotate(0deg) scale(1);

        }

        .intercom-lightweight-app-launcher-icon-open svg {
            width: 28px;
            height: 32px;
        }

        .intercom-lightweight-app-launcher-icon-open svg path {
            fill: rgb(255, 255, 255);
        }

        .intercom-lightweight-app-launcher-icon-self-serve {

            opacity: 1;
            transform: rotate(0deg) scale(1);

        }

        .intercom-lightweight-app-launcher-icon-self-serve svg {
            height: 56px;
        }

        .intercom-lightweight-app-launcher-icon-self-serve svg path {
            fill: rgb(255, 255, 255);
        }

        .intercom-lightweight-app-launcher-custom-icon-open {
            max-height: 36px;
            max-width: 36px;

            opacity: 1;
            transform: rotate(0deg) scale(1);

        }

        .intercom-lightweight-app-launcher-icon-minimize {

            opacity: 0;
            transform: rotate(-60deg) scale(0);

        }

        .intercom-lightweight-app-launcher-icon-minimize svg {
            width: 16px;
        }

        .intercom-lightweight-app-launcher-icon-minimize svg path {
            fill: rgb(255, 255, 255);
        }

        .intercom-lightweight-app-messenger {
            position: fixed;
            z-index: 2147483002;
            overflow: hidden;
            background-color: white;
            animation: intercom-lightweight-app-messenger 250ms ease-out;

            width: 376px;
            height: calc(100% - 40px);
            max-height: 704px;
            min-height: 250px;
            right: 20px;
            bottom: 20px;
            box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);
            border-radius: 8px;

        }

        .intercom-lightweight-app-messenger-header {
            height: 75px;
            background: linear-gradient(
                    135deg,
                    rgb(35, 97, 146) 0%,
                    rgb(15, 42, 64) 100%
            );
        }

        @media print {
            .intercom-lightweight-app {
                display: none;
            }
        }

        .not-select {
            -moz-user-select: none; /*火狐*/
            -webkit-user-select: none; /*webkit浏览器*/
            -ms-user-select: none; /*IE10*/
            -khtml-user-select: none; /*早期浏览器*/
            user-select: none;
        }

        .code-body {
            border: 1px solid #ccc;
        }

        .CodeMirror {
            border: 1px solid #eee;
            height: auto;
            min-height: 300px;
        }

        #example-section .CodeMirror-scroll {
            height: 250px;
            overflow-y: auto;
            overflow-x: auto;
        }

        #editor-section .CodeMirror-scroll {
            min-height: 500px;
            height: auto;
            overflow-y: auto;
            overflow-x: auto;
        }

        .cm-s-default .cm-keyword {
            color: blue;
        }

        .authenticated .btn-group {
            position: absolute;
            right: 0px;
        }

        #uri-db {
            margin-right: 10px;
        }

        .draft-ribbon {
            cursor: pointer;
        }

        .response-body-as-text {
            font-size: 12px;
        }

        #editor-btns > div {
            float: left;
            display: block;
        }

        #editor-btns > .item {
            padding: 2px 10px;
            cursor: pointer;
        }

        #editor-btns > .item:hover {
            background-color: #333340;
            color: white;

        }

        #editor-btns > .run-btn, #editor-btns > .debug-btn {
            float: right;
            border: none;
        }

        #editor-btns > .run-btn:hover, #editor-btns > .debug-btn:hover {
            background-color: #4458a7;
        }

        #repository .fa-file-o:before{
            content: "\f016";
            font-family: inherit;
            line-height: 32px;
        }

        /* drak theam*/
        #editor-panel .pane-request, #editor-panel input, #editor-panel button, #editor-panel .dropdown-menu, #bottom-side, #bottom-side .bottom-pane-selector,
        #editor-panel .ui-lay-c .btn-send.btn-primary,
        #editor-panel .ui-lay-c .btn-send.btn-primary:hover,
        #editor-panel .dropdown-menu > li > a,
        #editor-panel .request-common .split-dropdown .add-on,
        #editor-panel .v-divider,
        #editor-panel .v-splitter,
        .h-splitter,
        .ui-lay-l,
        #repository .authenticated .request,
        #repository .authenticated .service,
        #repository .authenticated .drive-name,
        .requests .left-pane .left-pane-header .selector .active > a,
        #save-dialog,#save-dialog input, .move-dialog, #save-dialog .modal-footer, #save-dialog .repository-path-selector .selector-table,
        #save-dialog .repository-path-selector .selector-table .nav-list > li > a,
        #save-dialog .r-btn {
            color: #ccc;
            background-color: #3c3f41;
            border-color: #585858;
        }

        .requests .left-pane .left-pane-header .selector a:hover, .selection-tree .left-pane-header .selector a:hover,
        .requests .left-pane .left-pane-header .selector .active > a:hover,
        #save-dialog .repository-path-selector .selector-table .nav-list > li > a:hover,
        #save-dialog .r-btn:hover {
            background-color: #2b2b2b;
        }


        #editor-panel .bottom-tab  {
            background-color: #2b2b2b;
        }

        .nav-list > li > a, .nav-list .nav-header {
            text-shadow: none;
        }

        #editor-panel .v-splitter:hover, .h-splitter:hover,
        #editor-panel .nav-tabs > .active > a,
        #editor-panel .nav-tabs > .active > a:hover,
        #editor-panel .nav-tabs > .active > a:focus,
        #bottom-side .bottom-pane-selector li > a:hover {
            background-color: #1e1e1e;
            border: 0px solid #ddd;
            bottom: 0px;
        }

        #editor-panel .nav-tabs > li > a {
            border: 0px;
        }

        #editor-panel .pane-request .draft-ribbon .draft-ribbon-text {
            color: #ccc;
            background-color: #888;
        }

        #editor-panel .pane-request .draft-ribbon .draft-ribbon-text:hover {
            background-color: #4d66b5;
            color: #ccc;
        }

        #editor-panel .ui-lay-c, #editor-panel {
            background-color: #232425;
        }

        #editor-panel .nav-tabs {
            border-bottom: 1px solid #232425;
        }

        .h-divider {
            background-color: #888888;
        }

        #editor-panel .code-body {
            border: 1px solid #525252;
        }

        #editor-panel .editor-btns {
            border-bottom: 1px solid #525252;
        }

        #editor-panel .run-btn {
            border-right: 1px solid #525252;
        }

        #editor-panel .ui-lay-c .btn-send.btn-primary {
            border-color: #525252 !important;
        }

        #example-section {
            margin: 15px 15px 15px 1em;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;
        }

        /*diff panel*/
        .diff-body  .item:hover{
            text-decoration: underline ;
        }
        .diff-body  .item{
            float: right;
            margin-left:10px;
            cursor: pointer;
            color: #e8be6a;
        }
        .m-left{
            float:left;
        }
        .el-time a{
            color: #e8be6a;
        }
    </style>
</div>

<!--登录框-->
<div id="loginDialog" class="modal login-dialog in" aria-hidden="false" style="display: none">
    <div class="modal-header"><a class="close" onclick="hideLoginDialog()" data-dismiss="modal">×</a>
        <h3>Sign in <small></small></h3></div>
    <div class="modal-body">
        <div class="form-horizontal">
            <fieldset>
                <div class="control-group">
                    <label class="control-label"> <span class="gwt-InlineHTML"> username </span> </label>
                    <div class="controls">
                        <input type="text" class="username" id="username" maxlength="256" placeholder="Enter username" >
                        <div class="error-message"></div>
                    </div>
                    <label class="control-label" style="margin-top:15px;"> <span class="gwt-InlineHTML"> password </span> </label>
                    <div class="controls" style="margin-top:20px;">
                        <input type="password" class="password" id="password" maxlength="256" autocomplete="new-password" placeholder="Enter password">
                        <div class="error-message"></div>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="modal-footer">
        <div class="error-message login-error-message" style="float: left;"></div>
        <button class="r-btn" e2e-tag="modal-cancel" onclick="hideLoginDialog()"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                      style="display: none;"></span></button>
        <button class="r-btn r-btn-primary" onclick="login()" e2e-tag="modal-ok"><i class="fa fa-plus"></i><span>Login</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                   style="display: none;"></span></button>
    </div>
</div>

<!--消息框-->
<div id="msgModal" class="modal modal-dialog" style="display: none">
    <div class="modal-header"><h3>Prompt message</h3></div>
    <div class="modal-body">
        msg
    </div>
    <div class="modal-footer">
        <button class="r-btn r-btn-primary" onclick="closeModal()">Close</button>
    </div>
</div>
<!--确认框-->
<div id="confirmModal" class="modal in" aria-hidden="false" style="display: none">
    <div class="modal-header"><a class="close" onclick="closeConfirmModal()" data-dismiss="modal">×</a>
        <h3>Confirmation <small></small></h3>
    </div>
    <div class="modal-body">
        <div class="form-horizontal">
            <div class="gwt-HTML"></div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="r-btn" onclick="closeConfirmModal()" e2e-tag="modal-cancel"><i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                                        style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-danger" e2e-tag="modal-ok"><i class="fa fa-trash"></i><span>Confirm</span><span class="r-btn-indicator" aria-hidden="true"
                                                                                                                   style="display: none;"></span></button>
    </div>
</div>
<!--输入确认框-->
<div class="modal rename-dialog in" id="rename-dialog" aria-hidden="false" style="display: none">
    <div class="modal-header"><a class="close" onclick="cancelDialog('#rename-dialog')" data-dismiss="modal">×</a>
        <h3>Rename Group <small></small></h3></div>
    <div class="modal-body">
        <div class="form-horizontal">
            <div class="repository-path-selector">
                <div class="flex-container name-form">
                    <div class="left-label"> Name</div>
                    <div class="right-label">
                        <input type="text" class="newname gwt-TextBox input-xlarge">
                        <input type="text" style="display: none" class="oldname gwt-TextBox input-xlarge">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="r-btn" e2e-tag="modal-cancel" onclick="cancelDialog('#rename-dialog')">
            <i class="fa fa-remove"></i><span>Cancel</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
        <button class="r-btn r-btn-primary" e2e-tag="modal-ok" onclick="confirmRenameDialog('#rename-dialog')">
            <i class="fa fa-download"></i><span>Save</span><span class="r-btn-indicator" aria-hidden="true" style="display: none;"></span>
        </button>
    </div>
</div>
<div id="modal-backdrop" class="modal-backdrop  in" style="display: none;"></div>

<!--请求发送状态-->
<div class="notification-container">
    <div id="notification" class="notification-area info" style="display: none">
        <div id="spinner"><i class="fa fa-spinner fa-spin"></i></div>
        <div id="notification-message">Sending request</div>
    </div>
    <div id="alt-notification" class="alt-notification-area" style="display: none"></div>
</div>

<script  type="text/javascript">
    let query = window.location.search.substring(1);
    let vars = query.split("&");
    let urlParam = {};
    for (let i=0;i<vars.length;i++) {
        let pair = vars[i].split("=");
        urlParam[pair[0]] = pair[1];
    }

    let baseUrl = window.location.href.indexOf("?") == -1?window.location.href:window.location.href.substring(0,window.location.href.indexOf("?"));
    let currApi = urlParam.id;
    let currPage = urlParam.page;
</script>


<script type="text/javascript" src="rocketapi/js/jquery-1.12.2.min.js"></script>
<script type="text/javascript" src="rocketapi/js/jquery.form.js"></script>
<script type="text/javascript" src="rocketapi/js/bootstrap-2.3.2.min.js"></script>
<script type="text/javascript" src="rocketapi/js/bootstrap-select.min.js"></script>

<script>var require = {paths: {'vs': 'rocketapi/monaco-editor/min/vs'}};</script>
<script src="rocketapi/monaco-editor/min/vs/loader.js"></script>
<script src="rocketapi/monaco-editor/min/vs/editor/editor.main.nls.js"></script>
<script src="rocketapi/monaco-editor/min/vs/editor/editor.main.js"></script>
<script src="rocketapi/monaco-editor/min/vs/rocket-language.js"></script>
<script src="rocketapi/monaco-editor/min/vs/basic-languages/yaml/yaml.js"></script>
<script type="text/javascript" src="rocketapi/js/tester_boot.js"></script>
</body>
</html>