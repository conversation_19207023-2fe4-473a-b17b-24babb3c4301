<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.moniwarn.mapper.primary.ResSiteMapper">
    
    <resultMap type="com.mes.moniwarn.domain.ResSite" id="TbResSiteResult">
        <result property="id"    column="ID"    />
        <result property="siteNumber"    column="SITE_NUMBER"    />
        <result property="siteName"    column="SITE_NAME"    />
        <result property="siteCode"    column="SITE_CODE"    />
        <result property="formerCodes"    column="FORMER_CODES"    />
        <result property="monitoringElement"    column="MONITORING_ELEMENT"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="siteType"    column="SITE_TYPE"    />
        <result property="siteStatus"    column="SITE_STATUS"    />
        <result property="siteBatch"    column="SITE_BATCH"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="operationUnit"    column="OPERATION_UNIT"    />
        <result property="packageId"    column="PACKAGE_ID"    />
        <result property="officialLongitude"    column="OFFICIAL_LONGITUDE"    />
        <result property="officialLatitude"    column="OFFICIAL_LATITUDE"    />
        <result property="constructionTime"    column="CONSTRUCTION_TIME"    />
        <result property="constructionUnit"    column="CONSTRUCTION_UNIT"    />
        <result property="ministryAddress"    column="MINISTRY_ADDRESS"    />
        <result property="actualAddress"    column="ACTUAL_ADDRESS"    />
        <result property="networkTime"    column="NETWORK_TIME"    />
        <result property="waterQualityTarget"    column="WATER_QUALITY_TARGET"    />
        <result property="riverBasin"    column="RIVER_BASIN"    />
        <result property="waterBody"    column="WATER_BODY"    />
        <result property="riverLevel"    column="RIVER_LEVEL"    />
        <result property="inflowWaterBody"    column="INFLOW_WATER_BODY"    />
        <result property="sectionAttribute"    column="SECTION_ATTRIBUTE"    />
        <result property="sectionDirection"    column="SECTION_DIRECTION"    />
        <result property="assessmentProvince"    column="ASSESSMENT_PROVINCE"    />
        <result property="assessmentCity"    column="ASSESSMENT_CITY"    />
        <result property="hasSalinity"    column="HAS_SALINITY"    />
        <result property="samplingPoints"    column="SAMPLING_POINTS"    />
        <result property="samplingMethod"    column="SAMPLING_METHOD"    />
        <result property="isSelfTesting"    column="IS_SELF_TESTING"    />
        <result property="hasAutomaticStation"    column="HAS_AUTOMATIC_STATION"    />
        <result property="stationConstructionTime"    column="STATION_CONSTRUCTION_TIME"    />
        <result property="constructionProvince"    column="CONSTRUCTION_PROVINCE"    />
        <result property="stationClassification"    column="STATION_CLASSIFICATION"    />
        <result property="siteIntroduction"    column="SITE_INTRODUCTION"    />
        <result property="upstream"    column="UPSTREAM"    />
        <result property="downstream"    column="DOWNSTREAM"    />
        <result property="remarks"    column="REMARKS"    />
        <result property="tenantId"    column="TENANT_ID"    />
        <result property="createdBy"    column="CREATED_BY"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatedBy"    column="UPDATED_BY"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.RegionSiteCoutVO" id="RegionSiteCountResult">
        <result property="regionCode"    column="region_code"    />
        <result property="count"    column="count"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.ResSiteVO" id="ResSiteVOResult" extends="TbResSiteResult">
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="packageName"    column="package_name"    />
    </resultMap>

    <sql id="selectTbResSiteVo">
        select id, site_number, site_name, site_code, former_codes, monitoring_element, longitude, latitude, site_type, site_status, site_batch, province, city, operation_unit, package_id, official_longitude, official_latitude, construction_time, construction_unit, ministry_address, actual_address, network_time, water_quality_target, river_basin, water_body, river_level, inflow_water_body, section_attribute, section_direction, assessment_province, assessment_city, has_salinity, sampling_points, sampling_method, is_self_testing, has_automatic_station, station_construction_time, construction_province, station_classification, site_introduction, upstream, downstream, remarks, tenant_id, created_by, create_time, updated_by, update_time from tb_res_site
    </sql>

    <select id="selectPackageSiteInfos" resultMap="ResSiteVOResult">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name,
            a.package_id, d.dict_value AS package_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
                 LEFT JOIN tb_sys_dict d on d.class_code = 'package_id' and a.package_id = d.dict_code
        WHERE
            package_id IS NOT NULL
          AND province IS NOT NULL
          AND city IS NOT NULL
        ORDER BY
            package_id,
            province,
            city
    </select>

    <select id="selectRegionSiteInfos" resultMap="ResSiteVOResult">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
        WHERE
            province IS NOT NULL
          AND city IS NOT NULL
        ORDER BY
            province,
            city
    </select>

    <select id="selectSiteVOList" resultMap="ResSiteVOResult" parameterType="com.mes.moniwarn.domain.dto.ResSiteQueryDto">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
        <where>
            <if test="id != null  and id != ''"> and a.id = #{id}</if>
            <if test="siteIdArr != null  and siteIdArr != ''">
                 and a.id in
                    <foreach collection="siteIdArr" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            </if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and monitoring_element = #{monitoringElement}</if>
            <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
        </where>
    </select>

    <select id="getAllTbResSiteList" resultMap="TbResSiteResult">
        <include refid="selectTbResSiteVo"/> where SITE_STATUS = '1'
    </select>

    <select id="selectTbResSiteList" parameterType="com.mes.moniwarn.domain.ResSite" resultMap="TbResSiteResult">
        <include refid="selectTbResSiteVo"/>
        <where>  
            <if test="siteNumber != null  and siteNumber != ''"> and SITE_NUMBER = #{siteNumber}</if>
            <if test="siteName != null  and siteName != ''"> and SITE_NAME like concat('%', #{siteName}, '%')</if>
            <if test="siteCode != null  and siteCode != ''"> and SITE_CODE = #{siteCode}</if>
            <if test="formerCodes != null  and formerCodes != ''"> and FORMER_CODES = #{formerCodes}</if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and MONITORING_ELEMENT = #{monitoringElement}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="siteType != null  and siteType != ''"> and SITE_TYPE = #{siteType}</if>
            <if test="siteStatus != null  and siteStatus != ''"> and SITE_STATUS = #{siteStatus}</if>
            <if test="siteBatch != null  and siteBatch != ''"> and SITE_BATCH = #{siteBatch}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="operationUnit != null  and operationUnit != ''"> and OPERATION_UNIT = #{operationUnit}</if>
            <if test="packageId != null  and packageId != ''"> and PACKAGE_ID = #{packageId}</if>
            <if test="officialLongitude != null  and officialLongitude != ''"> and OFFICIAL_LONGITUDE = #{officialLongitude}</if>
            <if test="officialLatitude != null  and officialLatitude != ''"> and OFFICIAL_LATITUDE = #{officialLatitude}</if>
            <if test="constructionTime != null  and constructionTime != ''"> and CONSTRUCTION_TIME = #{constructionTime}</if>
            <if test="constructionUnit != null  and constructionUnit != ''"> and CONSTRUCTION_UNIT = #{constructionUnit}</if>
            <if test="ministryAddress != null  and ministryAddress != ''"> and MINISTRY_ADDRESS = #{ministryAddress}</if>
            <if test="actualAddress != null  and actualAddress != ''"> and ACTUAL_ADDRESS = #{actualAddress}</if>
            <if test="networkTime != null  and networkTime != ''"> and NETWORK_TIME = #{networkTime}</if>
            <if test="waterQualityTarget != null  and waterQualityTarget != ''"> and WATER_QUALITY_TARGET = #{waterQualityTarget}</if>
            <if test="riverBasin != null  and riverBasin != ''"> and RIVER_BASIN = #{riverBasin}</if>
            <if test="waterBody != null  and waterBody != ''"> and WATER_BODY = #{waterBody}</if>
            <if test="riverLevel != null  and riverLevel != ''"> and RIVER_LEVEL = #{riverLevel}</if>
            <if test="inflowWaterBody != null  and inflowWaterBody != ''"> and INFLOW_WATER_BODY = #{inflowWaterBody}</if>
            <if test="sectionAttribute != null  and sectionAttribute != ''"> and SECTION_ATTRIBUTE = #{sectionAttribute}</if>
            <if test="sectionDirection != null  and sectionDirection != ''"> and SECTION_DIRECTION = #{sectionDirection}</if>
            <if test="assessmentProvince != null  and assessmentProvince != ''"> and ASSESSMENT_PROVINCE = #{assessmentProvince}</if>
            <if test="assessmentCity != null  and assessmentCity != ''"> and ASSESSMENT_CITY = #{assessmentCity}</if>
            <if test="hasSalinity != null  and hasSalinity != ''"> and HAS_SALINITY = #{hasSalinity}</if>
            <if test="samplingPoints != null  and samplingPoints != ''"> and SAMPLING_POINTS = #{samplingPoints}</if>
            <if test="samplingMethod != null  and samplingMethod != ''"> and SAMPLING_METHOD = #{samplingMethod}</if>
            <if test="isSelfTesting != null  and isSelfTesting != ''"> and IS_SELF_TESTING = #{isSelfTesting}</if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and HAS_AUTOMATIC_STATION = #{hasAutomaticStation}</if>
            <if test="stationConstructionTime != null  and stationConstructionTime != ''"> and STATION_CONSTRUCTION_TIME = #{stationConstructionTime}</if>
            <if test="constructionProvince != null  and constructionProvince != ''"> and CONSTRUCTION_PROVINCE = #{constructionProvince}</if>
            <if test="stationClassification != null  and stationClassification != ''"> and STATION_CLASSIFICATION = #{stationClassification}</if>
            <if test="siteIntroduction != null  and siteIntroduction != ''"> and SITE_INTRODUCTION = #{siteIntroduction}</if>
            <if test="UPSTREAM != null  and UPSTREAM != ''"> and UPSTREAM = #{UPSTREAM}</if>
            <if test="DOWNSTREAM != null  and DOWNSTREAM != ''"> and DOWNSTREAM = #{DOWNSTREAM}</if>
            <if test="REMARKS != null  and REMARKS != ''"> and REMARKS = #{REMARKS}</if>
            <if test="tenantId != null  and tenantId != ''"> and TENANT_ID = #{tenantId}</if>
            <if test="createdBy != null  and createdBy != ''"> and CREATED_BY = #{createdBy}</if>
            <if test="createTime != null  and createTime != ''"> and CREATE_TIME = #{createTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and UPDATED_BY = #{updatedBy}</if>
            <if test="updateTime != null  and updateTime != ''"> and UPDATE_TIME = #{updateTime}</if>
        </where>
    </select>

    <select id="selectRegionSiteCoutList" resultMap="RegionSiteCountResult">
        select province as region_code, count(*) as count
        from tb_res_site
        where province is not null
        group by province
        union all
        select city as region_code, count(*) as count
        from tb_res_site
        where city is not null
        group by city
        union all
        select '000000' as region_code, count(*) as count
        from tb_res_site
    </select>
    
    <select id="selectResSiteInfoList" parameterType="com.mes.moniwarn.domain.ResSite" resultMap="ResSiteVOResult">
        select id as site_id,site_name,site_code,site_type,province,city
        from tb_res_site
        <where>
            <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
        </where>
    </select>
    
    <select id="selectTbResSiteByID" parameterType="String" resultMap="TbResSiteResult">
        <include refid="selectTbResSiteVo"/>
        where ID = #{ID}
    </select>

    <insert id="insertTbResSite" parameterType="com.mes.moniwarn.domain.ResSite">
        insert into tb_res_site
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ID != null">ID,</if>
            <if test="siteNumber != null and siteNumber != ''">SITE_NUMBER,</if>
            <if test="siteName != null and siteName != ''">SITE_NAME,</if>
            <if test="siteCode != null">SITE_CODE,</if>
            <if test="formerCodes != null">FORMER_CODES,</if>
            <if test="monitoringElement != null and monitoringElement != ''">MONITORING_ELEMENT,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="siteType != null">SITE_TYPE,</if>
            <if test="siteStatus != null">SITE_STATUS,</if>
            <if test="siteBatch != null">SITE_BATCH,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="operationUnit != null">OPERATION_UNIT,</if>
            <if test="packageId != null">PACKAGE_ID,</if>
            <if test="officialLongitude != null">OFFICIAL_LONGITUDE,</if>
            <if test="officialLatitude != null">OFFICIAL_LATITUDE,</if>
            <if test="constructionTime != null">CONSTRUCTION_TIME,</if>
            <if test="constructionUnit != null">CONSTRUCTION_UNIT,</if>
            <if test="ministryAddress != null">MINISTRY_ADDRESS,</if>
            <if test="actualAddress != null">ACTUAL_ADDRESS,</if>
            <if test="networkTime != null">NETWORK_TIME,</if>
            <if test="waterQualityTarget != null">WATER_QUALITY_TARGET,</if>
            <if test="riverBasin != null">RIVER_BASIN,</if>
            <if test="waterBody != null">WATER_BODY,</if>
            <if test="riverLevel != null">RIVER_LEVEL,</if>
            <if test="inflowWaterBody != null">INFLOW_WATER_BODY,</if>
            <if test="sectionAttribute != null">SECTION_ATTRIBUTE,</if>
            <if test="sectionDirection != null">SECTION_DIRECTION,</if>
            <if test="assessmentProvince != null">ASSESSMENT_PROVINCE,</if>
            <if test="assessmentCity != null">ASSESSMENT_CITY,</if>
            <if test="hasSalinity != null">HAS_SALINITY,</if>
            <if test="samplingPoints != null">SAMPLING_POINTS,</if>
            <if test="samplingMethod != null">SAMPLING_METHOD,</if>
            <if test="isSelfTesting != null">IS_SELF_TESTING,</if>
            <if test="hasAutomaticStation != null">HAS_AUTOMATIC_STATION,</if>
            <if test="stationConstructionTime != null">STATION_CONSTRUCTION_TIME,</if>
            <if test="constructionProvince != null">CONSTRUCTION_PROVINCE,</if>
            <if test="stationClassification != null">STATION_CLASSIFICATION,</if>
            <if test="siteIntroduction != null">SITE_INTRODUCTION,</if>
            <if test="UPSTREAM != null">UPSTREAM,</if>
            <if test="DOWNSTREAM != null">DOWNSTREAM,</if>
            <if test="REMARKS != null">REMARKS,</if>
            <if test="tenantId != null and tenantId != ''">TENANT_ID,</if>
            <if test="createdBy != null">CREATED_BY,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatedBy != null">UPDATED_BY,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ID != null">#{ID},</if>
            <if test="siteNumber != null and siteNumber != ''">#{siteNumber},</if>
            <if test="siteName != null and siteName != ''">#{siteName},</if>
            <if test="siteCode != null">#{siteCode},</if>
            <if test="formerCodes != null">#{formerCodes},</if>
            <if test="monitoringElement != null and monitoringElement != ''">#{monitoringElement},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="siteType != null">#{siteType},</if>
            <if test="siteStatus != null">#{siteStatus},</if>
            <if test="siteBatch != null">#{siteBatch},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="operationUnit != null">#{operationUnit},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="officialLongitude != null">#{officialLongitude},</if>
            <if test="officialLatitude != null">#{officialLatitude},</if>
            <if test="constructionTime != null">#{constructionTime},</if>
            <if test="constructionUnit != null">#{constructionUnit},</if>
            <if test="ministryAddress != null">#{ministryAddress},</if>
            <if test="actualAddress != null">#{actualAddress},</if>
            <if test="networkTime != null">#{networkTime},</if>
            <if test="waterQualityTarget != null">#{waterQualityTarget},</if>
            <if test="riverBasin != null">#{riverBasin},</if>
            <if test="waterBody != null">#{waterBody},</if>
            <if test="riverLevel != null">#{riverLevel},</if>
            <if test="inflowWaterBody != null">#{inflowWaterBody},</if>
            <if test="sectionAttribute != null">#{sectionAttribute},</if>
            <if test="sectionDirection != null">#{sectionDirection},</if>
            <if test="assessmentProvince != null">#{assessmentProvince},</if>
            <if test="assessmentCity != null">#{assessmentCity},</if>
            <if test="hasSalinity != null">#{hasSalinity},</if>
            <if test="samplingPoints != null">#{samplingPoints},</if>
            <if test="samplingMethod != null">#{samplingMethod},</if>
            <if test="isSelfTesting != null">#{isSelfTesting},</if>
            <if test="hasAutomaticStation != null">#{hasAutomaticStation},</if>
            <if test="stationConstructionTime != null">#{stationConstructionTime},</if>
            <if test="constructionProvince != null">#{constructionProvince},</if>
            <if test="stationClassification != null">#{stationClassification},</if>
            <if test="siteIntroduction != null">#{siteIntroduction},</if>
            <if test="UPSTREAM != null">#{UPSTREAM},</if>
            <if test="DOWNSTREAM != null">#{DOWNSTREAM},</if>
            <if test="REMARKS != null">#{REMARKS},</if>
            <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTbResSite" parameterType="com.mes.moniwarn.domain.ResSite">
        update tb_res_site
        <trim prefix="SET" suffixOverrides=",">
            <if test="siteNumber != null and siteNumber != ''">SITE_NUMBER = #{siteNumber},</if>
            <if test="siteName != null and siteName != ''">SITE_NAME = #{siteName},</if>
            <if test="siteCode != null">SITE_CODE = #{siteCode},</if>
            <if test="formerCodes != null">FORMER_CODES = #{formerCodes},</if>
            <if test="monitoringElement != null and monitoringElement != ''">MONITORING_ELEMENT = #{monitoringElement},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="siteType != null">SITE_TYPE = #{siteType},</if>
            <if test="siteStatus != null">SITE_STATUS = #{siteStatus},</if>
            <if test="siteBatch != null">SITE_BATCH = #{siteBatch},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="operationUnit != null">OPERATION_UNIT = #{operationUnit},</if>
            <if test="packageId != null">PACKAGE_ID = #{packageId},</if>
            <if test="officialLongitude != null">OFFICIAL_LONGITUDE = #{officialLongitude},</if>
            <if test="officialLatitude != null">OFFICIAL_LATITUDE = #{officialLatitude},</if>
            <if test="constructionTime != null">CONSTRUCTION_TIME = #{constructionTime},</if>
            <if test="constructionUnit != null">CONSTRUCTION_UNIT = #{constructionUnit},</if>
            <if test="ministryAddress != null">MINISTRY_ADDRESS = #{ministryAddress},</if>
            <if test="actualAddress != null">ACTUAL_ADDRESS = #{actualAddress},</if>
            <if test="networkTime != null">NETWORK_TIME = #{networkTime},</if>
            <if test="waterQualityTarget != null">WATER_QUALITY_TARGET = #{waterQualityTarget},</if>
            <if test="riverBasin != null">RIVER_BASIN = #{riverBasin},</if>
            <if test="waterBody != null">WATER_BODY = #{waterBody},</if>
            <if test="riverLevel != null">RIVER_LEVEL = #{riverLevel},</if>
            <if test="inflowWaterBody != null">INFLOW_WATER_BODY = #{inflowWaterBody},</if>
            <if test="sectionAttribute != null">SECTION_ATTRIBUTE = #{sectionAttribute},</if>
            <if test="sectionDirection != null">SECTION_DIRECTION = #{sectionDirection},</if>
            <if test="assessmentProvince != null">ASSESSMENT_PROVINCE = #{assessmentProvince},</if>
            <if test="assessmentCity != null">ASSESSMENT_CITY = #{assessmentCity},</if>
            <if test="hasSalinity != null">HAS_SALINITY = #{hasSalinity},</if>
            <if test="samplingPoints != null">SAMPLING_POINTS = #{samplingPoints},</if>
            <if test="samplingMethod != null">SAMPLING_METHOD = #{samplingMethod},</if>
            <if test="isSelfTesting != null">IS_SELF_TESTING = #{isSelfTesting},</if>
            <if test="hasAutomaticStation != null">HAS_AUTOMATIC_STATION = #{hasAutomaticStation},</if>
            <if test="stationConstructionTime != null">STATION_CONSTRUCTION_TIME = #{stationConstructionTime},</if>
            <if test="constructionProvince != null">CONSTRUCTION_PROVINCE = #{constructionProvince},</if>
            <if test="stationClassification != null">STATION_CLASSIFICATION = #{stationClassification},</if>
            <if test="siteIntroduction != null">SITE_INTRODUCTION = #{siteIntroduction},</if>
            <if test="UPSTREAM != null">UPSTREAM = #{UPSTREAM},</if>
            <if test="DOWNSTREAM != null">DOWNSTREAM = #{DOWNSTREAM},</if>
            <if test="REMARKS != null">REMARKS = #{REMARKS},</if>
            <if test="tenantId != null and tenantId != ''">TENANT_ID = #{tenantId},</if>
            <if test="createdBy != null">CREATED_BY = #{createdBy},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatedBy != null">UPDATED_BY = #{updatedBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
        </trim>
        where ID = #{ID}
    </update>

    <delete id="deleteTbResSiteByID" parameterType="String">
        delete from tb_res_site where ID = #{ID}
    </delete>

    <delete id="deleteTbResSiteByIDs" parameterType="String">
        delete from tb_res_site where ID in 
        <foreach item="ID" collection="array" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </delete>
</mapper>