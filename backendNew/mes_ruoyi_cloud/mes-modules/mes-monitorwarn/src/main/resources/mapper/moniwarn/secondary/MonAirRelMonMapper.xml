<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.moniwarn.mapper.secondary.MonAirRelMonMapper">
    
    <resultMap type="com.mes.moniwarn.domain.vo.AirRelMonVO" id="MonAirRelResult">
        <result property="stationCode"    column="station_code"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="monitorCode"    column="monitor_code"    />
        <result property="siteStatus"    column="site_status"    />
        <result property="siteName"    column="site_name"    />
        <result property="riverBasin"    column="river_basin"    />
        <result property="assessmentCity"    column="assessment_city"    />
        <result property="t100"    column="t100"    />
        <result property="t101"    column="t101"    />
        <result property="t102"    column="t102"    />
        <result property="t103"    column="t103"    />
        <result property="t104"    column="t104"    />
        <result property="t105"    column="t105"    />
        <result property="t106"    column="t106"    />
        <result property="t107"    column="t107"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.WaterMonRecordVO" id="MonWaterRecordResult">
        <result property="stationCode"    column="station_code"    />
        <result property="siteName"    column="site_name"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="monitorCode"    column="monitor_code"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="monitorValue"    column="monitor_value"    />
        <result property="valueUnit"    column="value_unit"    />
        <result property="standardValue"    column="standard_value"    />
        <result property="spanValue"    column="span_value"    />
        <result property="dataFlag"    column="data_flag"    />
        <result property="riverBasin"    column="river_basin"    />
        <result property="assessmentProvince"    column="assessment_province"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.GasDevMonVO" id="MonAirDevResult">
        <result property="stationCode"    column="station_code"    />
        <result property="siteName"    column="site_name"    />
        <result property="monitorCode"    column="monitor_code"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="monitorValue"    column="monitor_value"    />
        <result property="valueUnit"    column="value_unit"    />
        <result property="standardValue"    column="standard_value"    />
        <result property="spanValue"    column="span_value"    />
        <result property="dataFlag"    column="data_flag"    />
        <result property="riverBasin"    column="river_basin"    />
        <result property="assessmentProvince"    column="assessment_province"    />
        <result property="brand"    column="brand"    />
        <result property="series"    column="series"    />
        <result property="fieldName"    column="field_name"    />
        <result property="fieldValue"    column="field_value"    />
        <result property="fieldUnit"    column="field_unit"    />
        <result property="uppLowLimit"    column="upp_low_limit"    />
        <result property="dataFlag"    column="data_flag"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.WaterTaskDispatchVO" id="MonWaterTaskDispatchResult">
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="dispatchedTime"    column="dispatched_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="taskDesc"    column="task_desc"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.WaterSourceMonitorVO" id="MonWaterSourceMonitorResult">
        <result property="deviceBrandId"    column="device_brand_id"    />
        <result property="deviceModelCode"    column="device_model_code"    />
        <result property="deviceModelName"    column="device_model_name"    />
        <result property="parameterName"    column="parameter_name"    />
        <result property="rangeMin"    column="range_min"    />
        <result property="rangeMax"    column="range_max"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.vo.WaterSourceMonitorInboundVO" id="MonWaterSourceMonitorInboundResult">
        <result property="suppliesType"    column="supplies_type"    />
        <result property="category"    column="category"    />
        <result property="suppliesCode"    column="supplies_code"    />
        <result property="name"    column="name"    />
        <result property="modelCode"    column="model_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="stockTime"    column="stock_time"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="currentStatus"    column="current_status"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.dto.MonParamsChildDto" id="MonParamsChildResult">
        <result property="stationCode"    column="station_code"    />
        <result property="monitorCode"    column="monitor_code"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="paramName"    column="paramName"    />
        <result property="totalCount"    column="totalCount"    />
        <result property="todayCount"    column="todayCount"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.dto.MonParamsSecondDto" id="MonParamsSecondResult">
        <result property="stationCode"    column="station_code"    />
        <result property="siteName"    column="site_name"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="monitorCode"    column="monitor_code"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="monitorValue"    column="monitor_value"    />
        <result property="valueUnit"    column="value_unit"    />
    </resultMap>

    <select id="findAirRealMonitorList" resultMap="MonAirRelResult">
        WITH ranked_data AS (
        SELECT
        *,
        ROW_NUMBER() OVER (
        PARTITION BY station_code , monitor_code
        ORDER BY monitor_time DESC
        ) AS rn
        FROM tb_air_monitor_jr_data_1h_month_${yearMonth}
        WHERE 1=1
        <if test="siteCodeList != null  and siteCodeList.size() > 0"> and station_code IN
            <foreach item="code" collection="siteCodeList"
                     open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        )
        SELECT t2.station_code, max(t2.monitor_time) as monitor_time,FIRST_VALUE( t2.w100) AS t100,
        FIRST_VALUE(t2.w101) AS t101, FIRST_VALUE(t2.w102) AS t102, FIRST_VALUE(t2.w103) AS t103,
        FIRST_VALUE(t2.w104) AS t104, FIRST_VALUE(t2.w105) AS t105, FIRST_VALUE(t2.w106) AS t106,
        FIRST_VALUE(t2.w107) AS t107  FROM (
        SELECT * FROM (
        SELECT t1.station_code, t1.monitor_time, t1.monitor_code, t1.monitor_value
        FROM (
        SELECT
        id, station_code, monitor_time, monitor_code, monitor_name,
        monitor_value, data_flag, create_time
        FROM
        ranked_data
        WHERE
        rn = 1
        ) t1
        order by t1.monitor_time DESC
        )
        PIVOT (
        FIRST_VALUE(monitor_value)
        FOR monitor_code IN ('100' AS w100, '101' AS w101, '102' AS w102, '103' AS w103, '104' AS w104, '105' AS w105, '106' AS w106, '107' AS w107)
        )
        ORDER BY monitor_time DESC
        ) t2
        GROUP BY t2.station_code
    </select>

    <select id="findGasHistoryMonitorList" resultMap="MonAirRelResult">
        SELECT * FROM (
                          SELECT
                              t1.station_code, t1.monitor_time, t1.monitor_code, t1.monitor_value
                          FROM
                              ${tableName} t1
                          WHERE 1=1
                            <if test="siteCode != null  and siteCode != ''"> and t1.station_code = #{siteCode}</if>
                            <if test="monitorCode != null  and monitorCode != ''"> and t1.monitor_code = #{monitorCode}</if>
                            <if test="startDate != null  and startDate != ''"> and t1.monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')</if>
                            <if test="endDate != null  and endDate != ''"> and t1.monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1' DAY - INTERVAL '1' SECOND</if>
                          ORDER BY
                              t1.monitor_time desc
                      )
                          PIVOT (
                                 FIRST_VALUE(monitor_value)
        FOR monitor_code IN ('100' AS t100, '101' AS t101, '102' AS t102, '103' AS t103, '104' AS t104, '105' AS t105, '106' AS t106, '107' AS t107)
                )
        ORDER BY monitor_time DESC
    </select>

    <select id="findGasDataMonRecordList" resultMap="MonWaterRecordResult">
        SELECT
        tw.station_code, tw.data_time as monitor_time, tw.factor as monitor_code, tw.act as monitor_value,
        tw.aim as standard_value, tw.zero_value as span_value,tw.data_value as data_flag
        FROM ${tableName} tw
        WHERE 1=1
        <if test="siteCode != null  and siteCode != ''"> and tw.station_code = #{siteCode}</if>
        <if test="monitorCodes != null  and monitorCodes.length > 0"> and tw.factor IN
            <foreach item="code" collection="monitorCodes"
                     open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="findDevRunRelMonList" resultMap="MonAirDevResult">
        WITH ranked_data AS (
            SELECT
                *,
                ROW_NUMBER() OVER (PARTITION BY station_code,field_name ORDER BY monitor_time DESC) AS rn
            FROM
                tb_air_device_operation_data_1h_latest
        )
        SELECT t1.* from (
             SELECT
                 station_code, monitor_time, monitor_name,brand,series,field_name,field_value, field_unit,upp_low_limit, data_flag, create_time
             FROM
                 ranked_data
             WHERE
                 rn = 1) t1
        WHERE 1=1
        <if test="siteCode != null  and siteCode != ''"> and t1.station_code = #{siteCode}</if>
        <if test="monitorCodes != null  and monitorCodes.length > 0"> and t1.monitor_name IN
            <foreach item="code" collection="monitorCodes"
                     open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="brand != null  and brand != ''"> and t1.brand = #{brand}</if>
    </select>

    <select id="findDevRunHistoryMonList" resultMap="MonAirDevResult">
        select t1.station_code, t1.monitor_time, t1.brand, t1.field_unit, t1.upp_low_limit, t1.data_flag,
        t1.monitor_name, t1.series,t1.field_name, t1.field_value, t1.create_time
        from tb_air_device_operation_1h_${siteCode} t1
        WHERE 1=1
        <if test="monitorCodes != null  and monitorCodes.length > 0"> and t1.monitor_name IN
            <foreach item="code" collection="monitorCodes"
                     open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="startDate != null  and startDate != ''"> and t1.monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')</if>
        <if test="endDate != null  and endDate != ''"> and t1.monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1' DAY - INTERVAL '1' SECOND</if>
        <if test="brand != null  and brand != ''"> and t1.brand = #{brand}</if>
    </select>

    <select id="findMonitorClassChildList" resultMap="MonParamsChildResult">
        SELECT
        station_code,
        monitor_code,
        monitor_name as paramName,
        COUNT(*) AS totalCount,
        SUM(CASE
        WHEN monitor_time >= TRUNC(SYSDATE) AND monitor_time &lt; TRUNC(SYSDATE) + 1
        THEN 1
        ELSE 0
        END) AS todayCount
        FROM
        ${tableName}
        WHERE 1=1
        <if test="siteCode != null  and siteCode != ''"> and station_code = #{siteCode}</if>
        <if test="startDate != null  and startDate != ''"> and monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')</if>
        <if test="endDate != null  and endDate != ''"> and monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1' DAY - INTERVAL '1' SECOND</if>
        <if test="paramCodes != null  and paramCodes.size()>0">
            AND monitor_code IN
            <foreach item="code" collection="paramCodes"
                     open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        GROUP BY
        station_code,
        monitor_code,
        monitor_name;
    </select>

    <select id="findSecondDataHourList" resultMap="MonParamsSecondResult">
        SELECT station_code, monitor_time , monitor_code ,monitor_name ,monitor_value
        FROM ${tableName}
        WHERE 1=1
        <if test="siteCode != null  and siteCode != ''"> and station_code = #{siteCode}</if>
        <if test="startDate != null  and startDate != ''"> and monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')</if>
        <if test="endDate != null  and endDate != ''"> and monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1' DAY - INTERVAL '1' SECOND</if>
        <if test="paramsCode != null  and paramsCode != ''"> AND monitor_code = #{paramsCode} </if>
    </select>

    <select id="getAirStationHistoryMonitorHourList" resultType="java.lang.Float">
        SELECT
            NVL(FIRST_VALUE(t.monitor_value), 0) AS monitor_value
        FROM (
                 SELECT
                     TO_DATE(#{startDate}, 'YYYY-MM-DD') + (ROWNUM - 1) / 24 AS hour_time
                 FROM
                     DUAL
                     CONNECT BY
        LEVEL &lt;= (TO_DATE(#{endDate}, 'YYYY-MM-DD') - TO_DATE(#{startDate}, 'YYYY-MM-DD') + 1) * 24
                 ORDER BY
                     hour_time
             ) h
                 LEFT JOIN (
            SELECT
                monitor_time,
                monitor_value
            FROM
                tb_air_monitor_jr_data_1h_month_${yearMonth}
            WHERE
                station_code = #{siteCode}
              AND monitor_code = #{monitorCode}
              AND monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
              AND monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')+ INTERVAL '1' DAY - INTERVAL '1' SECOND
        ) t
                           ON h.hour_time = t.monitor_time
        GROUP BY h.hour_time
    </select>

    <select id="getAirStationHistoryMonitorDayList" resultType="java.lang.Float">
        SELECT
            NVL(FIRST_VALUE(t.monitor_value), 0) AS monitor_value
        FROM (
                 SELECT
                     TO_DATE(#{startDate}, 'YYYY-MM-DD') + (ROWNUM - 1)  AS hour_time
                 FROM
                     DUAL
                     CONNECT BY
        LEVEL &lt;= (TO_DATE(#{endDate}, 'YYYY-MM-DD') - TO_DATE(#{startDate}, 'YYYY-MM-DD') + 1)
                 ORDER BY
                     hour_time
             ) h
                 LEFT JOIN (
            SELECT
                monitor_time,
                monitor_value
            FROM
                tb_air_monitor_jr_data_day
            WHERE
                station_code = #{siteCode}
              AND monitor_code = #{monitorCode}
              AND monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
              AND monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')+ INTERVAL '1' DAY - INTERVAL '1' SECOND
        ) t
                           ON h.hour_time = t.monitor_time
        GROUP BY h.hour_time
    </select>

    <select id="getAirStationDevHistoryMonitorList" resultType="java.lang.Float">
        SELECT
            NVL(FIRST_VALUE(t.field_value), 0) AS monitor_value
        FROM (
                 SELECT
                     TO_DATE(#{startDate}, 'YYYY-MM-DD') + (ROWNUM - 1) / 24 AS hour_time
                 FROM
                     DUAL
                     CONNECT BY
        LEVEL &lt;= (TO_DATE(#{endDate}, 'YYYY-MM-DD') - TO_DATE(#{startDate}, 'YYYY-MM-DD') + 1) * 24
                 ORDER BY
                     hour_time
             ) h
                 LEFT JOIN (
            SELECT
                monitor_time,
                field_value
            FROM
                tb_air_device_operation_1h_${siteCode}
            WHERE
                monitor_name = #{monitorCode}
              AND field_name = #{fieldCode}
              AND monitor_time >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
              AND monitor_time &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')+ INTERVAL '1' DAY - INTERVAL '1' SECOND
        ) t
                           ON h.hour_time = t.monitor_time
        GROUP BY h.hour_time
    </select>
</mapper>