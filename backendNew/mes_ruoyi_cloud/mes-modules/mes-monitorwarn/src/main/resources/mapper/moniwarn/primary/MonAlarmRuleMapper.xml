<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.moniwarn.mapper.primary.MonAlarmRuleMapper">
    
    <resultMap type="com.mes.moniwarn.domain.MonAlarmRule" id="MonAlarmRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="version"    column="version"    />
        <result property="mediumType"    column="medium_type"    />
        <result property="monitorClass"    column="monitor_class"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleRemark"    column="rule_remark"    />
        <result property="effTime"    column="eff_time"    />
        <result property="expTime"    column="exp_time"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="templateId"    column="template_id"    />
        <result property="templateInst"    column="template_inst"    />
        <result property="manageRoleId"    column="manage_role_id"    />
        <result property="mergeAlarm"    column="merge_alarm"    />
        <result property="mergeAlarmUnit"    column="merge_alarm_unit"    />
        <result property="isAudit"    column="is_audit"    />
        <result property="upRuleName"    column="up_rule_name"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="triggerThreshold"    column="trigger_threshold"    />
        <result property="upAlarmLevel"    column="up_alarm_level"    />
        <result property="upEffTime"    column="up_eff_time"    />
        <result property="upExpTime"    column="up_exp_time"    />
        <result property="upRemark"    column="up_remark"    />
        <result property="applyRemark"    column="apply_remark"    />
        <result property="status"    column="status"    />
        <result property="statusTime"    column="status_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdByName"    column="created_by_name"    />
        <result property="templateUiInst"    column="template_ui_inst"    />
        <result property="srcRuleId"    column="src_rule_id"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.MonAlarmRuleApply" id="MonAlarmRuleApplyResult">
        <result property="applyId"    column="apply_id"    />
        <result property="applyType"    column="apply_type"    />
        <result property="applyStaffId"    column="apply_staff_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="applyStatusTime"    column="apply_status_time"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="workflowId"    column="workflow_id"    />
        <result property="applyStaffName"    column="apply_staff_name"    />
        <result property="applyRemark"    column="apply_remark"    />
    </resultMap>

    <resultMap type="com.mes.moniwarn.domain.dto.MonAlarmRuleApplyDto" id="MonAlarmRuleApplyDtoResult">
        <result property="applyId"    column="apply_id"    />
        <result property="applyType"    column="apply_type"    />
        <result property="applyStaffId"    column="apply_staff_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="applyStatusTime"    column="apply_status_time"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="workflowId"    column="workflow_id"    />
        <result property="applyStaffName"    column="apply_staff_name"    />
        <result property="applyRemark"    column="apply_remark"    />
        <result property="status"    column="status"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="srcRuleId"    column="src_rule_id"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectMonAlarmRuleVo">
        select rule_id, rule_code, version, medium_type, monitor_class, rule_name, rule_remark, eff_time, exp_time, alarm_type, alarm_level, template_id, template_inst, manage_role_id, merge_alarm, merge_alarm_unit, is_audit, up_rule_name, trigger_type, trigger_threshold, up_alarm_level, up_eff_time, up_exp_time, up_remark, apply_remark, status, status_time, create_time, update_time, tenant_id, created_by, created_by_name, template_ui_inst, src_rule_id from tb_mon_alarm_rule where 1 = 1 
    </sql>

    <select id="findMonAlarmRuleVersionList" resultMap="MonAlarmRuleResult">
        SELECT * FROM (
          SELECT
              t1.rule_id,
              t1.rule_code,
              t1.version,
              t1.medium_type,
              t1.monitor_class,
              t1.rule_name,
              t1.rule_remark,
              DATE_FORMAT(t1.eff_time, '%Y-%m-%d %H:%i:%s') as eff_time,
              DATE_FORMAT(t1.exp_time, '%Y-%m-%d %H:%i:%s') as exp_time,
              t1.alarm_type,
              t1.alarm_level,
              t1.template_id,
              t1.template_inst,
              t1.manage_role_id,
              t1.merge_alarm,
              t1.is_audit,
              t1.up_rule_name,
              t1.trigger_type,
              t1.trigger_threshold,
              t1.up_alarm_level,
              DATE_FORMAT(t1.up_eff_time, '%Y-%m-%d %H:%i:%s') as up_eff_time,
              DATE_FORMAT(t1.up_exp_time, '%Y-%m-%d %H:%i:%s') as up_exp_time,
              t1.up_remark,
              t1.status,
              DATE_FORMAT(t1.status_time, '%Y-%m-%d %H:%i:%s') as status_time,
              DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as create_time,
              DATE_FORMAT(t1.update_time, '%Y-%m-%d %H:%i:%s') as update_time,
              t1.tenant_id,
              t1.created_by,
              t1.created_by_name,
              rt.template_class_code,
              rt.template_name,
              ROW_NUMBER() OVER (PARTITION BY t1.rule_code ORDER BY RAND()) AS rn
          FROM
              tb_mon_alarm_rule t1
            LEFT JOIN tb_mon_rule_template rt on rt.template_id = t1.template_id

          WHERE t1.status in ('A','P','D')
            <if test="ruleCode != null  and ruleCode != ''"> and t1.rule_code = #{ruleCode}</if>
            <if test="mediumType != null  and mediumType != ''"> and t1.medium_type = #{mediumType}</if>
            <if test="monitorClass != null  and monitorClass != ''"> and t1.monitor_class = #{monitorClass}</if>
            <if test="alarmType != null  and alarmType != ''"> and t1.alarm_type = #{alarmType}</if>
            <if test="status != null  and status != ''"> and t1.status = #{status}</if>
            <if test="ruleName != null  and ruleName != ''"> and t1.rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="templateClassCode != null  and templateClassCode != ''"> and rt.template_class_code = #{templateClassCode}</if>
        ORDER BY t1.update_time desc
        ) t WHERE t.rn = 1
    </select>

    <select id="findMonAlarmRuleList" resultMap="MonAlarmRuleResult">
        SELECT
        t1.rule_id,
        t1.rule_code,
        t1.version,
        t1.medium_type,
        t1.monitor_class,
        t1.rule_name,
        t1.rule_remark,
        DATE_FORMAT(t1.eff_time, '%Y-%m-%d %H:%i:%s') as eff_time,
        DATE_FORMAT(t1.exp_time, '%Y-%m-%d %H:%i:%s') as exp_time,
        t1.alarm_type,
        t1.alarm_level,
        t1.template_id,
        t1.template_inst,
        t1.manage_role_id,
        t1.merge_alarm,
        t1.is_audit,
        t1.up_rule_name,
        t1.trigger_type,
        t1.trigger_threshold,
        t1.up_alarm_level,
        DATE_FORMAT(t1.up_eff_time, '%Y-%m-%d %H:%i:%s') as up_eff_time,
        DATE_FORMAT(t1.up_exp_time, '%Y-%m-%d %H:%i:%s') as up_exp_time,
        t1.up_remark,
        t1.status,
        DATE_FORMAT(t1.status_time, '%Y-%m-%d %H:%i:%s') as status_time,
        DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as create_time,
        DATE_FORMAT(t1.update_time, '%Y-%m-%d %H:%i:%s') as update_time,
        t1.tenant_id
        FROM
        tb_mon_alarm_rule t1
        WHERE t1.status in ('A','P','D')
        <if test="ruleCode != null  and ruleCode != ''"> and t1.rule_code = #{ruleCode}</if>
        <if test="mediumType != null  and mediumType != ''"> and t1.medium_type = #{mediumType}</if>
        <if test="monitorClass != null  and monitorClass != ''"> and t1.monitor_class = #{monitorClass}</if>
        <if test="alarmType != null  and alarmType != ''"> and t1.alarm_type = #{alarmType}</if>
        <if test="status != null  and status != ''"> and t1.status = #{status}</if>
        <if test="ruleName != null  and ruleName != ''"> and t1.rule_name like concat('%', #{ruleName}, '%')</if>
        <if test="templateClassCode != null  and templateClassCode != ''"> and t1.template_id in (select rt.template_id from tb_mon_rule_template rt
            where rt.status = 'A' and rt.template_class_code = #{templateClassCode})</if>
        <if test="effTime != null  and effTime != ''"> and t1.eff_time >= #{effTime}</if>
        <if test="expTime != null  and expTime != ''"> and t1.exp_time &lt;= #{expTime}</if>
        ORDER BY t1.status_time desc
    </select>

    <select id="findMonAlarmRuleHistoryList" resultMap="MonAlarmRuleApplyDtoResult">
        select ta.apply_id, ta.apply_type, ta.apply_staff_id, ta.apply_staff_name,
        DATE_FORMAT(ta.apply_time, '%Y-%m-%d %H:%i:%s') as apply_time,
        DATE_FORMAT(ta.apply_status_time, '%Y-%m-%d %H:%i:%s') as apply_status_time,
        ta.apply_status, ta.rule_id, ta.workflow_id, ta.apply_remark,
        t.rule_code, t.version, t.status, t.src_rule_id
        from tb_mon_alarm_rule_apply ta
        left join tb_mon_alarm_rule t on t.rule_id = ta.rule_id
        where 1=1
        <if test="ruleCode != null and ruleCode != ''"> and t.rule_code = #{ruleCode}</if>
        <if test="alarmType != null and alarmType != ''"> and ta.alarm_type = #{alarmType}</if>
        <if test="startTime != null and startTime != ''"> and ta.apply_time >= #{startTime}</if>
        <if test="endTime != null and endTime != ''"> and ta.apply_time &lt;= #{endTime}</if>
        <if test="applyStaffName != null and applyStaffName != ''"> and ta.apply_staff_name like concat('%',#{applyStaffName},'%')</if>
        ORDER BY ta.apply_time desc
    </select>

    <select id="selectMonAlarmRuleList" parameterType="com.mes.moniwarn.domain.MonAlarmRule" resultMap="MonAlarmRuleResult">
        <include refid="selectMonAlarmRuleVo"/>
        <where>  
            <if test="ruleCode != null  and ruleCode != ''"> and rule_code = #{ruleCode}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="mediumType != null  and mediumType != ''"> and medium_type = #{mediumType}</if>
            <if test="monitorClass != null  and monitorClass != ''"> and monitor_class = #{monitorClass}</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleRemark != null  and ruleRemark != ''"> and rule_remark = #{ruleRemark}</if>
            <if test="effTime != null  and effTime != ''"> and eff_time = #{effTime}</if>
            <if test="expTime != null  and expTime != ''"> and exp_time = #{expTime}</if>
            <if test="alarmType != null  and alarmType != ''"> and alarm_type = #{alarmType}</if>
            <if test="alarmLevel != null  and alarmLevel != ''"> and alarm_level = #{alarmLevel}</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="templateInst != null  and templateInst != ''"> and template_inst = #{templateInst}</if>
            <if test="manageRoleId != null  and manageRoleId != ''"> and manage_role_id = #{manageRoleId}</if>
            <if test="mergeAlarm != null  and mergeAlarm != ''"> and merge_alarm = #{mergeAlarm}</if>
            <if test="mergeAlarmUnit != null  and mergeAlarmUnit != ''"> and merge_alarm_unit = #{mergeAlarmUnit}</if>
            <if test="isAudit != null  and isAudit != ''"> and is_audit = #{isAudit}</if>
            <if test="upRuleName != null  and upRuleName != ''"> and up_rule_name like concat('%', #{upRuleName}, '%')</if>
            <if test="triggerType != null  and triggerType != ''"> and trigger_type = #{triggerType}</if>
            <if test="triggerThreshold != null  and triggerThreshold != ''"> and trigger_threshold = #{triggerThreshold}</if>
            <if test="upAlarmLevel != null  and upAlarmLevel != ''"> and up_alarm_level = #{upAlarmLevel}</if>
            <if test="upEffTime != null  and upEffTime != ''"> and up_eff_time = #{upEffTime}</if>
            <if test="upExpTime != null  and upExpTime != ''"> and up_exp_time = #{upExpTime}</if>
            <if test="upRemark != null  and upRemark != ''"> and up_remark = #{upRemark}</if>
            <if test="applyRemark != null  and applyRemark != ''"> and apply_remark = #{applyRemark}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="statusTime != null  and statusTime != ''"> and status_time = #{statusTime}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdByName != null  and createdByName != ''"> and created_by_name like concat('%', #{createdByName}, '%')</if>
            <if test="templateUiInst != null  and templateUiInst != ''"> and template_ui_inst = #{templateUiInst}</if>
            <if test="srcRuleId != null  and srcRuleId != ''"> and src_rule_id = #{srcRuleId}</if>
        </where>
    </select>
    
    <select id="selectMonAlarmRuleByRuleId" parameterType="Integer" resultMap="MonAlarmRuleResult">
        <include refid="selectMonAlarmRuleVo"/>
        and rule_id = #{ruleId}
    </select>

    <select id="getMonAlarmRuleMaxVersion" resultType="java.lang.String">
        SELECT MAX(ar.version) version FROM tb_mon_alarm_rule ar WHERE ar.rule_code = #{ruleCode}
    </select>

    <insert id="insertMonAlarmRule" parameterType="com.mes.moniwarn.domain.MonAlarmRule">
        insert into tb_mon_alarm_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="ruleCode != null and ruleCode != ''">rule_code,</if>
            <if test="version != null and version != ''">version,</if>
            <if test="mediumType != null and mediumType != ''">medium_type,</if>
            <if test="monitorClass != null and monitorClass != ''">monitor_class,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleRemark != null">rule_remark,</if>
            <if test="effTime != null">eff_time,</if>
            <if test="expTime != null">exp_time,</if>
            <if test="alarmType != null and alarmType != ''">alarm_type,</if>
            <if test="alarmLevel != null and alarmLevel != ''">alarm_level,</if>
            <if test="templateId != null">template_id,</if>
            <if test="templateInst != null">template_inst,</if>
            <if test="manageRoleId != null">manage_role_id,</if>
            <if test="mergeAlarm != null">merge_alarm,</if>
            <if test="mergeAlarmUnit != null">merge_alarm_unit,</if>
            <if test="isAudit != null">is_audit,</if>
            <if test="upRuleName != null">up_rule_name,</if>
            <if test="triggerType != null">trigger_type,</if>
            <if test="triggerThreshold != null">trigger_threshold,</if>
            <if test="upAlarmLevel != null">up_alarm_level,</if>
            <if test="upEffTime != null">up_eff_time,</if>
            <if test="upExpTime != null">up_exp_time,</if>
            <if test="upRemark != null">up_remark,</if>
            <if test="applyRemark != null">apply_remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="statusTime != null">status_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdByName != null">created_by_name,</if>
            <if test="templateUiInst != null">template_ui_inst,</if>
            <if test="srcRuleId != null">src_rule_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="ruleCode != null and ruleCode != ''">#{ruleCode},</if>
            <if test="version != null and version != ''">#{version},</if>
            <if test="mediumType != null and mediumType != ''">#{mediumType},</if>
            <if test="monitorClass != null and monitorClass != ''">#{monitorClass},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleRemark != null">#{ruleRemark},</if>
            <if test="effTime != null">#{effTime},</if>
            <if test="expTime != null">#{expTime},</if>
            <if test="alarmType != null and alarmType != ''">#{alarmType},</if>
            <if test="alarmLevel != null and alarmLevel != ''">#{alarmLevel},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="templateInst != null">#{templateInst},</if>
            <if test="manageRoleId != null">#{manageRoleId},</if>
            <if test="mergeAlarm != null">#{mergeAlarm},</if>
            <if test="mergeAlarmUnit != null">#{mergeAlarmUnit},</if>
            <if test="isAudit != null">#{isAudit},</if>
            <if test="upRuleName != null">#{upRuleName},</if>
            <if test="triggerType != null">#{triggerType},</if>
            <if test="triggerThreshold != null">#{triggerThreshold},</if>
            <if test="upAlarmLevel != null">#{upAlarmLevel},</if>
            <if test="upEffTime != null">#{upEffTime},</if>
            <if test="upExpTime != null">#{upExpTime},</if>
            <if test="upRemark != null">#{upRemark},</if>
            <if test="applyRemark != null">#{applyRemark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="statusTime != null">#{statusTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdByName != null">#{createdByName},</if>
            <if test="templateUiInst != null">#{templateUiInst},</if>
            <if test="srcRuleId != null">#{srcRuleId},</if>
         </trim>
    </insert>

    <update id="updateMonAlarmRule" parameterType="com.mes.moniwarn.domain.MonAlarmRule">
        update tb_mon_alarm_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="version != null and version != ''">version = #{version},</if>
            <if test="mediumType != null and mediumType != ''">medium_type = #{mediumType},</if>
            <if test="monitorClass != null and monitorClass != ''">monitor_class = #{monitorClass},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleRemark != null">rule_remark = #{ruleRemark},</if>
            <if test="effTime != null">eff_time = #{effTime},</if>
            <if test="expTime != null">exp_time = #{expTime},</if>
            <if test="alarmType != null and alarmType != ''">alarm_type = #{alarmType},</if>
            <if test="alarmLevel != null and alarmLevel != ''">alarm_level = #{alarmLevel},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="templateInst != null">template_inst = #{templateInst},</if>
            <if test="manageRoleId != null">manage_role_id = #{manageRoleId},</if>
            <if test="mergeAlarm != null">merge_alarm = #{mergeAlarm},</if>
            <if test="mergeAlarmUnit != null">merge_alarm_unit = #{mergeAlarmUnit},</if>
            <if test="isAudit != null">is_audit = #{isAudit},</if>
            <if test="upRuleName != null">up_rule_name = #{upRuleName},</if>
            <if test="triggerType != null">trigger_type = #{triggerType},</if>
            <if test="triggerThreshold != null">trigger_threshold = #{triggerThreshold},</if>
            <if test="upAlarmLevel != null">up_alarm_level = #{upAlarmLevel},</if>
            <if test="upEffTime != null">up_eff_time = #{upEffTime},</if>
            <if test="upExpTime != null">up_exp_time = #{upExpTime},</if>
            <if test="upRemark != null">up_remark = #{upRemark},</if>
            <if test="applyRemark != null">apply_remark = #{applyRemark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="statusTime != null">status_time = #{statusTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createdByName != null">created_by_name = #{createdByName},</if>
            <if test="templateUiInst != null">template_ui_inst = #{templateUiInst},</if>
            <if test="srcRuleId != null">src_rule_id = #{srcRuleId},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteMonAlarmRuleByRuleId" parameterType="Integer">
        delete from tb_mon_alarm_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteMonAlarmRuleByRuleIds" parameterType="Integer">
        delete from tb_mon_alarm_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>


    <update id="updateMonAlarmRuleByRuleCode" parameterType="com.mes.moniwarn.domain.MonAlarmRule">
        update tb_mon_alarm_rule
           set status = #{status},
               status_time = #{statusTime},
               update_time = #{updateTime}
         where rule_code = #{ruleCode} and status = 'A' and rule_id != #{ruleId}
    </update>

    <select id="getNextRuleCode" resultType="java.lang.Integer">
        SELECT seq_mon_rule_code.NEXTVAL as ruleCode FROM DUAL
    </select>

    <delete id="deleteCommonRuleRelaTableByRuleId">
        delete from ${tableName} where rule_id = #{ruleId}
    </delete>

    <insert id="insertCommonRuleRelaTableByRuleId">
        insert into ${tableName} (rule_id, ${columnName})
        SELECT
        A.rule_id, A.rela_id
        FROM (
        <foreach collection="relaIds" item="relaId" separator="UNION ALL">
            SELECT
            #{ruleId} AS rule_id,
            #{relaId} AS rela_id
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <insert id="insertMonAlarmRuleApply" parameterType="com.mes.moniwarn.domain.dto.MonAlarmRuleDto">
        INSERT INTO tb_mon_alarm_rule_apply (
            apply_staff_id,
            apply_staff_name,
            apply_type,
            apply_time,
            apply_remark,
            apply_status,
            apply_status_time,
            rule_id,
            tenant_id
        ) VALUES (
             #{applyStaffId},
             #{applyStaffName},
             #{applyType},
             CURRENT_TIMESTAMP,
             #{applyRemark},
             #{ruleApplyStatus},
             CURRENT_TIMESTAMP,
             #{ruleId},
             #{tenantId}
         )
    </insert>

    <select id="getMonAlarmRuleMaxRuleId" resultType="java.lang.Integer">
        SELECT MAX(rule_id) FROM tb_mon_alarm_rule
    </select>

    <select id="getMonAlarmRuleApplyMaxApplyId" resultType="java.lang.Integer">
        SELECT MAX(apply_id) FROM tb_mon_alarm_rule_apply
    </select>
</mapper>