package com.mes.moniwarn.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 预警规则对象 tb_mon_alarm_rule
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class MonAlarmRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID（自增主键） */
    private Integer ruleId;

    /** 规则编码，取自序列seq_mon_rule_code */
    @Excel(name = "规则编码，取自序列seq_mon_rule_code")
    private String ruleCode;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 介质类型，A：气；W：水 */
    @Excel(name = "介质类型，A：气；W：水")
    private String mediumType;

    /** 监控项目 */
    @Excel(name = "监控项目")
    private String monitorClass;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 规则备注 */
    @Excel(name = "规则备注")
    private String ruleRemark;

    /** 生效时间 */
    @Excel(name = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effTime;

    /** 失效时间 */
    @Excel(name = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expTime;

    /** 预警类型，G：普通预警；S：特殊预警 */
    @Excel(name = "预警类型，G：普通预警；S：特殊预警")
    private String alarmType;

    /** 预警等级（1-4级）,1：紧急；2：重要；3：中等；4：一般 */
    @Excel(name = "预警等级", readConverterExp = "1=-4级")
    private String alarmLevel;

    /** 规则模板ID */
    @Excel(name = "规则模板ID")
    private Integer templateId;

    /** 规则模板实例 */
    @Excel(name = "规则模板实例")
    private String templateInst;

    /** 管理小组 */
    @Excel(name = "管理小组")
    private String manageRoleId;

    /** 预警合并时间范围，如数值24 */
    @Excel(name = "预警合并时间范围，如数值24")
    private Integer mergeAlarm;

    /** 预警合并时间范围-单位，H：小时；M：分钟 */
    @Excel(name = "预警合并时间范围-单位，H：小时；M：分钟")
    private String mergeAlarmUnit;

    /** 是否审核（流程处置中，是否需要审核），0：否；1：是 */
    @Excel(name = "是否审核", readConverterExp = "流=程处置中，是否需要审核")
    private String isAudit;

    /** 升级规则名称 */
    @Excel(name = "升级规则名称")
    private String upRuleName;

    /** 触发条件类型，T：时间触发；F：频次触发 */
    @Excel(name = "触发条件类型，T：时间触发；F：频次触发")
    private String triggerType;

    /** 触发阈值，单位：小时 或 次 */
    @Excel(name = "触发阈值，单位：小时 或 次")
    private Integer triggerThreshold;

    /** 目标预警等级，1：紧急；2：重要；3：中等；4：一般 */
    @Excel(name = "目标预警等级，1：紧急；2：重要；3：中等；4：一般")
    private String upAlarmLevel;

    /** 升级规则生效时间 */
    @Excel(name = "升级规则生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upEffTime;

    /** 升级规则失效时间 */
    @Excel(name = "升级规则失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upExpTime;

    /** 升级备注 */
    @Excel(name = "升级备注")
    private String upRemark;

    /** 变更说明，当保存为草稿时，也会填写变更说明 */
    @Excel(name = "变更说明，当保存为草稿时，也会填写变更说明")
    private String applyRemark;

    /** 规则状态，A：在用；P：审批中；O：已覆盖；X：已删除；D：草稿 */
    @Excel(name = "规则状态，A：在用；P：审批中；O：已覆盖；X：已删除；D：草稿")
    private String status;

    /** 状态时间 */
    @Excel(name = "状态时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusTime;

    /** 租户ID */
    @Excel(name = "租户ID")
    private Integer tenantId;

    /** 创建者账号 */
    @Excel(name = "创建者账号")
    private String createdBy;

    /** 创建者姓名 */
    @Excel(name = "创建者姓名")
    private String createdByName;

    /** 规则模板UI实例 */
    @Excel(name = "规则模板UI实例")
    private String templateUiInst;

    /** 来源规则ID */
    @Excel(name = "来源规则ID")
    private Integer srcRuleId;

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleCode() 
    {
        return ruleCode;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public void setMediumType(String mediumType) {
        this.mediumType = mediumType;
    }

    public String getMediumType() {
        return mediumType;
    }

    public void setMonitorClass(String monitorClass) {
        this.monitorClass = monitorClass;
    }

    public String getMonitorClass() {
        return monitorClass;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleRemark(String ruleRemark) {
        this.ruleRemark = ruleRemark;
    }

    public String getRuleRemark() {
        return ruleRemark;
    }

    public void setEffTime(Date effTime) {
        this.effTime = effTime;
    }

    public Date getEffTime() {
        return effTime;
    }

    public void setExpTime(Date expTime) {
        this.expTime = expTime;
    }

    public Date getExpTime() {
        return expTime;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateInst(String templateInst) {
        this.templateInst = templateInst;
    }

    public String getTemplateInst() {
        return templateInst;
    }

    public void setManageRoleId(String manageRoleId) {
        this.manageRoleId = manageRoleId;
    }

    public String getManageRoleId() {
        return manageRoleId;
    }

    public void setMergeAlarm(Integer mergeAlarm) {
        this.mergeAlarm = mergeAlarm;
    }

    public Integer getMergeAlarm() {
        return mergeAlarm;
    }

    public void setMergeAlarmUnit(String mergeAlarmUnit) {
        this.mergeAlarmUnit = mergeAlarmUnit;
    }

    public String getMergeAlarmUnit() {
        return mergeAlarmUnit;
    }

    public void setIsAudit(String isAudit) {
        this.isAudit = isAudit;
    }

    public String getIsAudit() {
        return isAudit;
    }

    public void setUpRuleName(String upRuleName) {
        this.upRuleName = upRuleName;
    }

    public String getUpRuleName() {
        return upRuleName;
    }

    public void setTriggerType(String triggerType) {
        this.triggerType = triggerType;
    }

    public String getTriggerType() {
        return triggerType;
    }

    public void setTriggerThreshold(Integer triggerThreshold) {
        this.triggerThreshold = triggerThreshold;
    }

    public Integer getTriggerThreshold() {
        return triggerThreshold;
    }

    public void setUpAlarmLevel(String upAlarmLevel) {
        this.upAlarmLevel = upAlarmLevel;
    }

    public String getUpAlarmLevel() {
        return upAlarmLevel;
    }

    public void setUpEffTime(Date upEffTime) {
        this.upEffTime = upEffTime;
    }

    public Date getUpEffTime() {
        return upEffTime;
    }

    public void setUpExpTime(Date upExpTime) {
        this.upExpTime = upExpTime;
    }

    public Date getUpExpTime() {
        return upExpTime;
    }

    public void setUpRemark(String upRemark) {
        this.upRemark = upRemark;
    }

    public String getUpRemark() {
        return upRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    public Date getStatusTime()
    {
        return statusTime;
    }

    public void setTenantId(Integer tenantId)
    {
        this.tenantId = tenantId;
    }

    public Integer getTenantId()
    {
        return tenantId;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setCreatedByName(String createdByName) 
    {
        this.createdByName = createdByName;
    }

    public String getCreatedByName() 
    {
        return createdByName;
    }

    public void setTemplateUiInst(String templateUiInst) 
    {
        this.templateUiInst = templateUiInst;
    }

    public String getTemplateUiInst() 
    {
        return templateUiInst;
    }

    public void setSrcRuleId(Integer srcRuleId)
    {
        this.srcRuleId = srcRuleId;
    }

    public Integer getSrcRuleId()
    {
        return srcRuleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("ruleCode", getRuleCode())
            .append("version", getVersion())
            .append("mediumType", getMediumType())
            .append("monitorClass", getMonitorClass())
            .append("ruleName", getRuleName())
            .append("ruleRemark", getRuleRemark())
            .append("effTime", getEffTime())
            .append("expTime", getExpTime())
            .append("alarmType", getAlarmType())
            .append("alarmLevel", getAlarmLevel())
            .append("templateId", getTemplateId())
            .append("templateInst", getTemplateInst())
            .append("manageRoleId", getManageRoleId())
            .append("mergeAlarm", getMergeAlarm())
            .append("mergeAlarmUnit", getMergeAlarmUnit())
            .append("isAudit", getIsAudit())
            .append("upRuleName", getUpRuleName())
            .append("triggerType", getTriggerType())
            .append("triggerThreshold", getTriggerThreshold())
            .append("upAlarmLevel", getUpAlarmLevel())
            .append("upEffTime", getUpEffTime())
            .append("upExpTime", getUpExpTime())
            .append("upRemark", getUpRemark())
            .append("applyRemark", getApplyRemark())
            .append("status", getStatus())
            .append("statusTime", getStatusTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("tenantId", getTenantId())
            .append("createdBy", getCreatedBy())
            .append("createdByName", getCreatedByName())
            .append("templateUiInst", getTemplateUiInst())
            .append("srcRuleId", getSrcRuleId())
            .toString();
    }
}
