package com.mes.moniwarn.controller;

import com.mes.common.core.web.controller.BaseController;
import com.mes.moniwarn.config.aspect.OperationLogEndpoint;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.enums.OperationTypeEnum;
import com.mes.moniwarn.service.IHomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门户-首页Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/home")
public class HomeController extends BaseController {
    @Autowired
    private IHomeService homeService;

    /**
     *  报警状态统计
     *  @param mediumType 介质类型
     *  @param dateType 查询类型 day：按天 week：按周 month：按月
     *  @param startTime 开始时间
     *  @param endTime 结束时间
     *  @return LcResult
     *  @Version 1.0
     */
    @GetMapping("/findAlarmStatusStatistics")
    @OperationLogEndpoint(module = "首页-报警状态统计", operationType = OperationTypeEnum.QUERY, operationContent = "报警状态统计")
    public LcResult findAlarmStatusStatistics(@RequestParam(name = "mediumType", required = false) String mediumType,
                                 @RequestParam(name = "dateType", required = false) String dateType,
                                 @RequestParam(name = "startTime", required = false) String startTime,
                                 @RequestParam(name = "endTime", required = false) String endTime) {

        return LcResult.success(homeService.findAlarmStatusStatistics(mediumType, dateType, startTime, endTime));
    }

    /**
     * 报警站点统计
     *  @param mediumType 介质类型
     *  @param dateType 查询类型 day：按天 week：按周 month：按月
     *  @param startTime 开始时间
     *  @param endTime 结束时间
     *  @return LcResult
     *  @Version 1.0
     */
    @GetMapping("/findAlarmSiteStatistics")
    @OperationLogEndpoint(module = "首页-报警站点统计", operationType = OperationTypeEnum.QUERY, operationContent = "报警站点统计")
    public LcResult findAlarmSiteStatistics(@RequestParam(name = "mediumType", required = false) String mediumType,
                                 @RequestParam(name = "dateType", required = false) String dateType,
                                 @RequestParam(name = "startTime", required = false) String startTime,
                                 @RequestParam(name = "endTime", required = false) String endTime) {

        return LcResult.success(homeService.findAlarmSiteStatistics(mediumType, dateType, startTime, endTime));
    }

    /**
     *  报警原因统计
     *  @param mediumType 介质类型
     *  @param dateType 查询类型 day：按天 week：按周 month：按月
     *  @param startTime 开始时间
     *  @param endTime 结束时间
     *  @return LcResult
     *  @Version 1.0
     */
    @GetMapping("/findAlarmReasonStatistics")
    @OperationLogEndpoint(module = "首页-报警状态统计", operationType = OperationTypeEnum.QUERY, operationContent = "报警状态统计")
    public LcResult findAlarmReasonStatistics(@RequestParam(name = "mediumType", required = false) String mediumType,
                                              @RequestParam(name = "dateType", required = false) String dateType,
                                              @RequestParam(name = "startTime", required = false) String startTime,
                                              @RequestParam(name = "endTime", required = false) String endTime) {

        return LcResult.success(homeService.findAlarmReasonStatistics(mediumType, dateType, startTime, endTime));
    }

    /**
     * 报警数据统计
     * @param mediumType 介质类型
     * @param dateType 查询类型 day：按天 week：按周 month：按月
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return LcResult
     * @Version 1.0
     */
    @GetMapping("/findAlarmLevelStatistics")
    @OperationLogEndpoint(module = "首页-报警数据统计", operationType = OperationTypeEnum.QUERY, operationContent = "报警数据统计")
    public LcResult findAlarmLevelStatistics(@RequestParam(name = "mediumType", required = false) String mediumType,
                                             @RequestParam(name = "dateType", required = false) String dateType,
                                             @RequestParam(name = "startTime", required = false) String startTime,
                                             @RequestParam(name = "endTime", required = false) String endTime) {
        return LcResult.success(homeService.findAlarmLevelStatistics(mediumType, dateType, startTime, endTime));
    }


}
