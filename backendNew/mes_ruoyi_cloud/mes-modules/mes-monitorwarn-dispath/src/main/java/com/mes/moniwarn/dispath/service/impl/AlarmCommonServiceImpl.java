package com.mes.moniwarn.dispath.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mes.moniwarn.dispath.domain.MonRuleTemplate;
import com.mes.moniwarn.dispath.mapper.primary.MonRuleTemplateMapper;
import com.mes.moniwarn.dispath.service.AlarmCommonService;

/**
 * 告警通用接口
 * 
 * <AUTHOR>
 */
@Service
public class AlarmCommonServiceImpl implements AlarmCommonService {
    private static final Logger log = LoggerFactory.getLogger(AlarmCommonServiceImpl.class);

    @Autowired
    private MonRuleTemplateMapper monRuleTemplateMapper;

    @Override
    public Map<String, Object> getTemplateDefault(Integer templateId) {
        MonRuleTemplate template = monRuleTemplateMapper.queryTemplateById(templateId);
        if (template != null) {
            Map<String, Object> returnMap = new HashMap<String, Object>();
            List<Object> defaultArray = new ArrayList<Object>();
            String templateContent = template.getTemplateContent();
            String templateIndex = template.getTemplateIndex();
            String templateDetail = template.getTemplateDetail();
            JSONArray templateContentArray = JSONArray.parseArray(templateContent);
            JSONArray templateIndexArray = JSONArray.parseArray(templateIndex);
            for (int i = 0; i < templateContentArray.size(); i++) {
                JSONObject rule = templateContentArray.getJSONObject(i);
                JSONObject index = templateIndexArray.getJSONObject(i);

                Set<String> keySet = index.keySet();
                for (String key : keySet) {
                    JSONArray indexArray = index.getJSONArray(key);
                    JSONArray ruleArray = rule.getJSONArray(key);
                    log.info(key);
                    if ("~".equals(indexArray.getString(0))) {
                        defaultArray.add(ruleArray);
                    }
                    else {
                        for (Object aIndex : indexArray) {
                            defaultArray.add(ruleArray.get((Integer) aIndex));
                        }
                    }
                }
            }
            returnMap.put("defaultValue", defaultArray);
            returnMap.put("templateDetail", templateDetail);
            return returnMap;
        }
        return null;
    }

    @Override
    public Map<String, Object> getRule(JSONObject param) {
        Integer templateId = param.getInteger("templateId");
        JSONArray params = param.getJSONArray("params");
        MonRuleTemplate template = monRuleTemplateMapper.queryTemplateById(templateId);
        if (template != null) {
            Map<String, Object> returnMap = new HashMap<String, Object>();
            List<Object> defaultArray = new ArrayList<Object>();
            String templateContent = template.getTemplateContent();
            String templateIndex = template.getTemplateIndex();
            String url = template.getTemplateUrl();
            JSONArray templateContentArray = JSONArray.parseArray(templateContent);
            JSONArray templateIndexArray = JSONArray.parseArray(templateIndex);
            int paramIndex = 0;
            for (int i = 0; i < templateContentArray.size(); i++) {
                JSONObject rule = templateContentArray.getJSONObject(i);
                JSONObject index = templateIndexArray.getJSONObject(i);

                Set<String> keySet = index.keySet();
                for (String key : keySet) {
                    JSONArray indexArray = index.getJSONArray(key);
                    JSONArray ruleArray = rule.getJSONArray(key);
                    if ("~".equals(indexArray.getString(0))) {
                        rule.put(key, params.get(paramIndex));
                        paramIndex++;
                    }
                    else {
                        for (Object aIndex : indexArray) {
                            ruleArray.set((Integer) aIndex, params.get(paramIndex));
                            paramIndex++;
                            defaultArray.add(ruleArray.get((Integer) aIndex));
                        }
                    }

                }
            }
            Map<String, Object> handle = new HashMap<String, Object>();
            handle.put("rules", templateContentArray);
            handle.put("ruleCode", template.getTemplateCode());
            returnMap.put("handle", handle);
            returnMap.put("url", url);
            return returnMap;
        }
        return param;
    }

    @Override
    public List<MonRuleTemplate> getTemplateList(String mediumType, String monitorClass, String templateClassCode) {
        List<MonRuleTemplate> templateList = monRuleTemplateMapper.getTemplateList(mediumType, monitorClass,
            templateClassCode);
        return templateList;
    }
}
