package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.dto.QuantityTargetCompleteQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.CompletePlanStaticVO;
import com.mes.smartdispath.domain.vo.PlanSourceStaticVO;
import com.mes.smartdispath.domain.vo.PlanTypeStaticVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;

/**
 * 调度计划信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduPlanInfoMapper {
    /**
     * 根据时间查询完成计划数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<CompletePlanStaticVO> selectCompletePlanCountByTime(@Param("startTime") String startTime,
        @Param("endTime") String endTime);

    /**
     * 查询计划类型统计
     *
     * @param queryDto
     * @return
     */
    public List<PlanTypeStaticVO> selectPlanCompleteCount(QuantityTargetCompleteQueryDTO queryDto);

    /**
     * 调度计划来源统计
     *
     * @param queryDTO
     * @return
     */
    List<PlanSourceStaticVO> selectPlanSourceStatic(ScheduPlanInfoQueryDTO queryDTO);

    /**
     * 查询调度任务关联的计划信息列表
     *
     * @param scheduPlanInfo 调度计划信息
     * @return 调度计划信息集合
     */
    public List<ScheduPlanInfoVO> selectTaskLinkPlanInfoList(ScheduPlanInfoQueryDTO scheduPlanInfo);

    /**
     * 查询计划的完成情况
     *
     * @param activityTypeArr
     * @param businessType
     * @return
     */
    public List<PlanTypeStaticVO> selectPlanCompleteCount(@Param("activityTypeArr") List<String> activityTypeArr,
        @Param("businessType") String businessType);

    /**
     * 查询站点的完成统计
     * 
     * @param queryDto
     * @return
     */
    public List<CompletePlanStaticVO> selectSiteCompletePlanStatic(QuantityTargetCompleteQueryDTO queryDto);

    /**
     * 查询汇总的完成统计-全国
     *
     * @param queryDto
     * @return
     */
    public List<CompletePlanStaticVO> selectNationwideCollectCompletePlanStatic(
        QuantityTargetCompleteQueryDTO queryDto);

    /**
     * 查询汇总的完成统计-省份
     *
     * @param queryDto
     * @return
     */
    public List<CompletePlanStaticVO> selectProvinceCollectCompletePlanStatic(QuantityTargetCompleteQueryDTO queryDto);

    /**
     * 计划类型统计
     * 
     * @param queryDto
     * @return 计划类型统计列表
     */
    public List<PlanTypeStaticVO> selectPlanTypeStatic(TaskStaticQueryDTO queryDto);

    /**
     * 查询调度计划信息
     * 
     * @param scheduPlanInfo 调度计划信息
     * @return 调度计划信息
     */
    public ScheduPlanInfoVO selectScheduPlanInfoById(ScheduPlanInfoQueryDTO scheduPlanInfo);

    /**
     * 查询调度计划信息列表
     * 
     * @param scheduPlanInfo 调度计划信息
     * @return 调度计划信息集合
     */
    public List<ScheduPlanInfoVO> selectScheduPlanInfoList(ScheduPlanInfoQueryDTO scheduPlanInfo);

    /**
     * 查询需要审批的调度计划信息列表
     *
     * @param scheduPlanInfo 调度计划信息
     * @return 调度计划信息集合
     */
    public List<ScheduPlanInfoVO> selectApprovalScheduPlanInfoList(ScheduPlanInfoQueryDTO scheduPlanInfo);

    /**
     * 新增调度计划信息
     * 
     * @param scheduPlanInfo 调度计划信息
     * @return 结果
     */
    public int insertScheduPlanInfo(ScheduPlanInfo scheduPlanInfo);

    /**
     * 批量新增调度计划信息
     *
     * @param scheduPlanInfoList 调度计划信息List
     * @return 结果
     */
    public int batchInsertScheduPlanInfo(List<ScheduPlanInfo> scheduPlanInfoList);

    /**
     * 修改调度计划信息
     * 
     * @param scheduPlanInfo 调度计划信息
     * @return 结果
     */
    public int updateScheduPlanInfo(ScheduPlanInfo scheduPlanInfo);

    /**
     * 通过planCode修改调度计划信息
     *
     * @param scheduPlanInfo 调度计划信息
     * @return 结果
     */
    public int updateScheduPlanInfoByPlanCode(ScheduPlanInfo scheduPlanInfo);

    /**
     * 修改多个计划信息
     *
     * @param scheduPlanInfo 计划信息
     * @return 结果
     */
    public int updateScheduPlanInfoByIdArr(ScheduPlanInfoQueryDTO scheduPlanInfo);

    /**
     * 批量修改调度计划信息
     *
     * @param scheduPlanInfoList 调度计划信息List
     * @return 结果
     */
    public int batchUpdateScheduPlanInfo(List<ScheduPlanInfo> scheduPlanInfoList);

    /**
     * 批量修改调度计划执行状态信息
     *
     * @param scheduPlanInfoList 调度计划信息List
     * @return 结果
     */
    public int batchUpdateScheduPlanInfoExecuteStatus(List<ScheduPlanInfo> scheduPlanInfoList);

    /**
     * 通过planCode批量修改调度计划信息
     *
     * @param scheduPlanInfoList 调度计划信息List
     * @return 结果
     */
    public int batchUpdateScheduPlanInfoByPlanCode(List<ScheduPlanInfo> scheduPlanInfoList);

    /**
     * 删除调度计划信息
     * 
     * @param id 调度计划信息主键
     * @return 结果
     */
    public int deleteScheduPlanInfoById(String id);

    /**
     * 批量删除调度计划信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduPlanInfoByIds(String[] ids);

    /**
     * 删除计划和伴生计划
     *
     * @param scheduPlanInfo 调度计划信息
     * @return 结果
     */
    public int deletePlanInfoAndAssoPlan(ScheduPlanInfo scheduPlanInfo);
}
