package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ResPersonBasic;

/**
 * 人员查询DTO
 *
 * @Author: li.haoyang @Date： 2025/7/28
 */
public class ResPersonBasicQueryDTO extends ResPersonBasic {
    /**
     * 站点id
     */
    private String siteId;

    /**
     * 站点id数组
     */
    private List<String> siteIdArr;

    /**
     * 人员状态数组
     */
    private List<String> statusArr;

    /**
     * 部门id数组
     */
    private List<String> deptIdArr;

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public List<String> getStatusArr() {
        return statusArr;
    }

    public void setStatusArr(List<String> statusArr) {
        this.statusArr = statusArr;
    }

    public List<String> getDeptIdArr() {
        return deptIdArr;
    }

    public void setDeptIdArr(List<String> deptIdArr) {
        this.deptIdArr = deptIdArr;
    }
}
