package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.EnumConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;
import com.mes.smartdispath.domain.ScheduMonitorQuantityTarget;
import com.mes.smartdispath.domain.ScheduTargetFile;
import com.mes.smartdispath.domain.SysAttachInfo;
import com.mes.smartdispath.domain.SysAttachInfoRel;
import com.mes.smartdispath.domain.dto.MonitorQualityResultQueryDTO;
import com.mes.smartdispath.domain.dto.QuantityTargetCompleteQueryDTO;
import com.mes.smartdispath.domain.dto.ResSiteQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileQueryDTO;
import com.mes.smartdispath.domain.vo.CompletePlanStaticVO;
import com.mes.smartdispath.domain.vo.ResSiteVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQuantityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduTargetFileVO;
import com.mes.smartdispath.mapper.ResSiteMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQualityResultMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQualityTargetMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQuantityTargetMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTargetFileMapper;
import com.mes.smartdispath.mapper.SysAttachInfoMapper;
import com.mes.smartdispath.mapper.SysAttachInfoRelMapper;
import com.mes.smartdispath.service.ITargetMgrService;
import com.mes.system.api.model.LoginUser;

import cn.hutool.core.lang.Assert;

/**
 * 目标管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class TargetMgrServiceImpl implements ITargetMgrService {
    private static final Logger log = LoggerFactory.getLogger(TargetMgrServiceImpl.class);

    @Autowired
    private ScheduMonitorQuantityTargetMapper scheduMonitorQuantityTargetMapper;

    @Autowired
    private ResSiteMapper resSiteMapper;

    @Autowired
    private ScheduTargetFileMapper scheduTargetFileMapper;

    @Autowired
    private SysAttachInfoMapper sysAttachInfoMapper;

    @Autowired
    private SysAttachInfoRelMapper sysAttachInfoRelMapper;

    @Autowired
    private ScheduMonitorQualityTargetMapper scheduMonitorQualityTargetMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ScheduMonitorQualityResultMapper scheduMonitorQualityResultMapper;

    /**
     * 数量目标查询
     *
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    @Override
    public List<ScheduMonitorQuantityTargetVO> qryNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getSiteId())) {
            queryDto.setSiteIdArr(Arrays.asList(StringUtils.split(queryDto.getSiteId(), ",")));
        }
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(queryDto.getBusinessType())
            && StringUtils.isNotEmpty(queryDto.getIsAutosite())) {
            queryDto.setHasAutomaticStation(
                EnumConstant.IS_AUTOSITE_YES.equals(queryDto.getIsAutosite()) ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                    : EnumConstant.HAS_AUTOMATIC_STATION_NO);
        }
        if (StringUtils.isNotEmpty(queryDto.getPackageId())) {
            queryDto.setPackageIdArr(Arrays.asList(StringUtils.split(queryDto.getPackageId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduMonitorQuantityTargetVO> list = scheduMonitorQuantityTargetMapper
            .selectScheduMonitorQuantityTargetList(queryDto);
        if (list.isEmpty()) {
            return list;
        }
        // 补充已完成的数量
        Set<String> siteIdSet = new HashSet<>();
        Set<String> activityTypeSet = new HashSet<>();
        Set<String> activitySubtypeSet = new HashSet<>();
        Set<String> statYearSet = new HashSet<>();
        for (ScheduMonitorQuantityTargetVO vo : list) {
            siteIdSet.add(vo.getSiteId());
            activityTypeSet.add(vo.getActivityType());
            activitySubtypeSet.add(vo.getActivitySubtype());
            statYearSet.add(vo.getStatYear());
        }
        List<String> siteIdArr = new ArrayList<>(siteIdSet);
        List<String> activityTypeArr = new ArrayList<>(activityTypeSet);
        List<String> activitySubtypeArr = new ArrayList<>(activitySubtypeSet);
        List<String> statYearArr = new ArrayList<>(statYearSet);
        QuantityTargetCompleteQueryDTO completeQueryDto = new QuantityTargetCompleteQueryDTO();
        completeQueryDto.setSiteIdArr(siteIdArr);
        completeQueryDto.setActivityTypeArr(activityTypeArr);
        completeQueryDto.setActivitySubtypeArr(activitySubtypeArr);
        completeQueryDto.setStatYearArr(statYearArr);
        List<CompletePlanStaticVO> completePlanStaticList = scheduPlanInfoMapper
            .selectSiteCompletePlanStatic(completeQueryDto);
        // 转成字典，id-拼接除了数量以外的字段
        Map<String, Integer> countMap = completePlanStaticList.stream()
            .collect(Collectors.toMap(
                completePlanStatic -> completePlanStatic.getSiteId() + completePlanStatic.getActivityType()
                    + completePlanStatic.getActivitySubtype() + completePlanStatic.getYear(),
                CompletePlanStaticVO::getCompleteCount));
        list.forEach(target -> {
            String key = target.getSiteId() + target.getActivityType() + target.getActivitySubtype()
                + target.getStatYear();
            Integer count = countMap.get(key);
            target.setCompleteNum(null != count ? count : 0);
        });
        return list;
    }

    /**
     * 数量目标汇总查询
     *
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    @Override
    public List<ScheduMonitorQuantityTargetVO> qryCollectNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getProvinceCode())) {
            List<String> getProvinceCodeArr = Arrays.asList(StringUtils.split(queryDto.getProvinceCode(), ","));
            if (getProvinceCodeArr.contains(CommonConstant.NATIONWIDE_REGION_CODE)) {
                return qryNationwideCollectNumTargetList(queryDto);
            }
        }
        return qryProvinceCollectNumTargetList(queryDto);
    }

    /**
     * 全国-数量目标汇总查询
     *
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    private List<ScheduMonitorQuantityTargetVO> qryNationwideCollectNumTargetList(
        ScheduMonitorQuantityTargetQueryDTO queryDto) {
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(queryDto.getBusinessType())
            && StringUtils.isNotEmpty(queryDto.getIsAutosite())) {
            queryDto.setHasAutomaticStation(
                EnumConstant.IS_AUTOSITE_YES.equals(queryDto.getIsAutosite()) ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                    : EnumConstant.HAS_AUTOMATIC_STATION_NO);
        }
        if (StringUtils.isNotEmpty(queryDto.getPackageId())) {
            queryDto.setPackageIdArr(Arrays.asList(StringUtils.split(queryDto.getPackageId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduMonitorQuantityTargetVO> list = scheduMonitorQuantityTargetMapper
            .selectNationwideCollectScheduMonitorQuantityTargetList(queryDto);
        if (list.isEmpty()) {
            return list;
        }
        // 补充已完成的数量
        Set<String> siteTypeSet = new HashSet<>();
        Set<String> activityTypeSet = new HashSet<>();
        Set<String> activitySubtypeSet = new HashSet<>();
        Set<String> statYearSet = new HashSet<>();
        for (ScheduMonitorQuantityTargetVO vo : list) {
            siteTypeSet.add(vo.getSiteType());
            activityTypeSet.add(vo.getActivityType());
            activitySubtypeSet.add(vo.getActivitySubtype());
            statYearSet.add(vo.getStatYear());
        }
        List<String> siteTypeArr = new ArrayList<>(siteTypeSet);
        List<String> activityTypeArr = new ArrayList<>(activityTypeSet);
        List<String> activitySubtypeArr = new ArrayList<>(activitySubtypeSet);
        List<String> statYearArr = new ArrayList<>(statYearSet);
        QuantityTargetCompleteQueryDTO completeQueryDto = new QuantityTargetCompleteQueryDTO();
        completeQueryDto.setSiteTypeArr(siteTypeArr);
        completeQueryDto.setActivityTypeArr(activityTypeArr);
        completeQueryDto.setActivitySubtypeArr(activitySubtypeArr);
        completeQueryDto.setStatYearArr(statYearArr);
        List<CompletePlanStaticVO> completePlanStaticList = scheduPlanInfoMapper
            .selectNationwideCollectCompletePlanStatic(completeQueryDto);
        // 转成字典，id-拼接除了数量以外的字段
        Map<String, Integer> countMap = completePlanStaticList.stream()
            .collect(
                Collectors
                    .toMap(
                        completePlanStatic -> completePlanStatic.getSiteType()
                            + completePlanStatic.getHasAutomaticStation() + completePlanStatic.getActivityType()
                            + completePlanStatic.getActivitySubtype() + completePlanStatic.getYear(),
                        CompletePlanStaticVO::getCompleteCount));
        list.forEach(target -> {
            String key = target.getSiteType() + target.getHasAutomaticStation() + target.getActivityType()
                + target.getActivitySubtype() + target.getStatYear();
            Integer count = countMap.get(key);
            target.setCompleteNum(null != count ? count : 0);
        });
        return list;
    }

    /**
     * 省份-数量目标汇总查询
     *
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    private List<ScheduMonitorQuantityTargetVO> qryProvinceCollectNumTargetList(
        ScheduMonitorQuantityTargetQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getProvinceCode())) {
            queryDto.setProvinceCodeArr(Arrays.asList(StringUtils.split(queryDto.getProvinceCode(), ",")));
        }
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(queryDto.getBusinessType())
            && StringUtils.isNotEmpty(queryDto.getIsAutosite())) {
            queryDto.setHasAutomaticStation(
                EnumConstant.IS_AUTOSITE_YES.equals(queryDto.getIsAutosite()) ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                    : EnumConstant.HAS_AUTOMATIC_STATION_NO);
        }
        if (StringUtils.isNotEmpty(queryDto.getPackageId())) {
            queryDto.setPackageIdArr(Arrays.asList(StringUtils.split(queryDto.getPackageId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduMonitorQuantityTargetVO> list = scheduMonitorQuantityTargetMapper
            .selectProvinceCollectScheduMonitorQuantityTargetList(queryDto);
        if (list.isEmpty()) {
            return list;
        }
        // 补充已完成的数量
        Set<String> provinceCodeSet = new HashSet<>();
        Set<String> siteTypeSet = new HashSet<>();
        Set<String> activityTypeSet = new HashSet<>();
        Set<String> activitySubtypeSet = new HashSet<>();
        Set<String> statYearSet = new HashSet<>();
        for (ScheduMonitorQuantityTargetVO vo : list) {
            provinceCodeSet.add(vo.getProvinceCode());
            siteTypeSet.add(vo.getSiteType());
            activityTypeSet.add(vo.getActivityType());
            activitySubtypeSet.add(vo.getActivitySubtype());
            statYearSet.add(vo.getStatYear());
        }
        List<String> provinceCodeArr = new ArrayList<>(provinceCodeSet);
        List<String> siteTypeArr = new ArrayList<>(siteTypeSet);
        List<String> activityTypeArr = new ArrayList<>(activityTypeSet);
        List<String> activitySubtypeArr = new ArrayList<>(activitySubtypeSet);
        List<String> statYearArr = new ArrayList<>(statYearSet);
        QuantityTargetCompleteQueryDTO completeQueryDto = new QuantityTargetCompleteQueryDTO();
        completeQueryDto.setProvinceCodeArr(provinceCodeArr);
        completeQueryDto.setSiteTypeArr(siteTypeArr);
        completeQueryDto.setActivityTypeArr(activityTypeArr);
        completeQueryDto.setActivitySubtypeArr(activitySubtypeArr);
        completeQueryDto.setStatYearArr(statYearArr);
        List<CompletePlanStaticVO> completePlanStaticList = scheduPlanInfoMapper
            .selectProvinceCollectCompletePlanStatic(completeQueryDto);
        // 转成字典，id-拼接除了数量以外的字段
        Map<String, Integer> countMap = completePlanStaticList.stream()
            .collect(Collectors.toMap(
                completePlanStatic -> completePlanStatic.getProvinceCode() + completePlanStatic.getSiteType()
                    + completePlanStatic.getHasAutomaticStation() + completePlanStatic.getActivityType()
                    + completePlanStatic.getActivitySubtype() + completePlanStatic.getYear(),
                CompletePlanStaticVO::getCompleteCount));
        list.forEach(target -> {
            String key = target.getProvinceCode() + target.getSiteType() + target.getHasAutomaticStation()
                + target.getActivityType() + target.getActivitySubtype() + target.getStatYear();
            Integer count = countMap.get(key);
            target.setCompleteNum(null != count ? count : 0);
        });
        return list;
    }

    /**
     * 数量目标新增/编辑
     *
     * @param scheduMonitorQuantityTarget 数量目标信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveNumTarget(ScheduMonitorQuantityTargetQueryDTO scheduMonitorQuantityTarget) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isEmpty(scheduMonitorQuantityTarget.getId())) {
            // 新增逻辑
            log.info("新增数量目标记录");
            // 查询出站点数据（前台可以不传具体站点，后台根据站点类型、业务类型、省、市等参数查询出具体站点）
            ResSiteQueryDTO queryParam = new ResSiteQueryDTO();
            if (StringUtils.isNotEmpty(scheduMonitorQuantityTarget.getSiteId())) {
                queryParam.setSiteIdArr(Arrays.asList(StringUtils.split(scheduMonitorQuantityTarget.getSiteId(), ",")));
            }
            queryParam.setMonitoringElement(scheduMonitorQuantityTarget.getBusinessType());
            queryParam.setSiteType(scheduMonitorQuantityTarget.getSiteType());
            queryParam.setProvince(scheduMonitorQuantityTarget.getProvinceCode());
            queryParam.setCity(scheduMonitorQuantityTarget.getCityCode());
            if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(scheduMonitorQuantityTarget.getBusinessType())) {
                queryParam.setHasAutomaticStation(
                    EnumConstant.IS_AUTOSITE_YES.equals(scheduMonitorQuantityTarget.getIsAutosite())
                        ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                        : EnumConstant.HAS_AUTOMATIC_STATION_NO);
            }
            List<ResSiteVO> siteList = resSiteMapper.selectSiteVOList(queryParam);
            if (siteList.isEmpty()) {
                log.info("未查询到符合条件的站点数据");
                Assert.isTrue(false, "未匹配到符合条件的站点数据，请修改");
                return 0;
            }
            // 查询是不是已存在要保存的站点相关指标
            List<String> siteIdList = siteList.stream().map(ResSiteVO::getSiteId).collect(Collectors.toList());
            ScheduMonitorQuantityTargetQueryDTO queryCountDto = new ScheduMonitorQuantityTargetQueryDTO();
            queryCountDto.setSiteIdArr(siteIdList);
            queryCountDto.setBusinessType(scheduMonitorQuantityTarget.getBusinessType());
            queryCountDto.setActivityType(scheduMonitorQuantityTarget.getActivityType());
            queryCountDto.setActivitySubtype(scheduMonitorQuantityTarget.getActivitySubtype());
            queryCountDto.setStatYear(scheduMonitorQuantityTarget.getStatYear());
            int countCheck = scheduMonitorQuantityTargetMapper.selectScheduMonitorQuantityTargetCount(queryCountDto);
            if (countCheck > 0) {
                Assert.isTrue(false, "已存在相同业务分类和监测活动类别的站点目标值，请修改");
            }
            // 批量插入
            List<ScheduMonitorQuantityTarget> targetList = new ArrayList<>();
            siteList.forEach(site -> {
                ScheduMonitorQuantityTarget target = new ScheduMonitorQuantityTarget();
                BeanUtils.copyProperties(scheduMonitorQuantityTarget, target);
                target.setSiteId(site.getSiteId());
                target.setSiteName(site.getSiteName());
                target.setSiteType(site.getSiteType());
                target.setProvinceCode(site.getProvince());
                target.setProvinceName(site.getProvinceName());
                target.setCityCode(site.getCity());
                target.setCityName(site.getCityName());
                target.setCreateBy(loginUser.getUsername());
                target.setUpdateBy(loginUser.getUsername());
                targetList.add(target);
            });
            return scheduMonitorQuantityTargetMapper.batchInsertScheduMonitorQuantityTarget(targetList);
        }
        else {
            ScheduMonitorQuantityTarget targetInfo = new ScheduMonitorQuantityTarget();
            BeanUtils.copyProperties(scheduMonitorQuantityTarget, targetInfo);
            // 编辑逻辑
            log.info("修改监测目标记录，id:{}", targetInfo.getId());
            targetInfo.setUpdateBy(loginUser.getUsername());
            return scheduMonitorQuantityTargetMapper.updateScheduMonitorQuantityTarget(targetInfo);
        }
    }

    /**
     * 数量目标删除
     *
     * @param id 目标信息id
     * @return 结果
     */
    @Override
    public int delNumTarget(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ScheduMonitorQuantityTarget scheduMonitorQuantityTarget = new ScheduMonitorQuantityTarget();
        scheduMonitorQuantityTarget.setId(id);
        scheduMonitorQuantityTarget.setStatus(CommonConstant.DATA_STATUS_X);
        scheduMonitorQuantityTarget.setUpdateBy(loginUser.getUsername());
        return scheduMonitorQuantityTargetMapper.updateScheduMonitorQuantityTarget(scheduMonitorQuantityTarget);
    }

    /**
     * 数量目标文件列表
     *
     * @param queryParam
     * @return 结果
     */
    @Override
    public List<ScheduTargetFileVO> qryNumTargetFileList(ScheduTargetFileQueryDTO queryParam) {
        queryParam.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        return scheduTargetFileMapper.selectScheduTargetFileList(queryParam);
    }

    /**
     * 数量目标文件新增/编辑
     *
     * @param scheduTargetFileDTO
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveNumTargetFile(ScheduTargetFileDTO scheduTargetFileDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        scheduTargetFileDTO.setUpdateBy(loginUser.getUsername());
        if (StringUtils.isEmpty(scheduTargetFileDTO.getId())) {
            // 新增逻辑
            log.info("新增数量目标文件记录");
            scheduTargetFileDTO.setCreateBy(loginUser.getUsername());
            // 先保存附件信息
            String attachId = UUID.randomUUID().toString().replaceAll("-", "");
            SysAttachInfo attachInfo = new SysAttachInfo();
            BeanUtils.copyProperties(scheduTargetFileDTO, attachInfo);
            attachInfo.setAttachId(attachId);
            sysAttachInfoMapper.insertSysAttachInfo(attachInfo);
            // 保存目标文件信息
            int count = scheduTargetFileMapper.insertScheduTargetFile(scheduTargetFileDTO);
            // 保存关联关系
            SysAttachInfoRel attachInfoRel = new SysAttachInfoRel();
            BeanUtils.copyProperties(scheduTargetFileDTO, attachInfoRel);
            attachInfoRel.setAttachId(attachId);
            attachInfoRel.setBusinessId(scheduTargetFileDTO.getId());
            attachInfoRel.setTbMark(CommonConstant.ATTACH_SOURCE_TB_SCHEDU_TARGET_FILE);
            sysAttachInfoRelMapper.insertSysAttachInfoRel(attachInfoRel);
            return count;
        }
        else {
            // 编辑逻辑
            log.info("修改数量目标文件记录，id:{}", scheduTargetFileDTO.getId());
            return scheduTargetFileMapper.updateScheduTargetFile(scheduTargetFileDTO);
        }
    }

    /**
     * 数量目标文件删除
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    @Override
    public int delNumTargetFile(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ScheduTargetFile scheduTargetFile = new ScheduTargetFile();
        scheduTargetFile.setId(id);
        scheduTargetFile.setStatus(CommonConstant.DATA_STATUS_X);
        scheduTargetFile.setUpdateBy(loginUser.getUsername());
        return scheduTargetFileMapper.updateScheduTargetFile(scheduTargetFile);
    }

    /**
     * 预览数量目标文件查询
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    @Override
    public ScheduTargetFileDTO qryTargetFileAttachInfo(String id) {
        return scheduTargetFileMapper.selectTargetFileWithAttachInfoById(id,
            CommonConstant.ATTACH_SOURCE_TB_SCHEDU_TARGET_FILE);
    }

    /**
     * 质量目标查询
     *
     * @param queryParam
     * @return 结果
     */
    @Override
    public List<ScheduMonitorQualityTargetVO> qryQualityTargetList(ScheduMonitorQualityTargetQueryDTO queryParam) {
        if (StringUtils.isNotEmpty(queryParam.getCityCode())) {
            queryParam.setCityCodeArr(Arrays.asList(StringUtils.split(queryParam.getCityCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryParam.getSiteId())) {
            queryParam.setSiteIdArr(Arrays.asList(StringUtils.split(queryParam.getSiteId(), ",")));
        }
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(queryParam.getBusinessType())
            && StringUtils.isNotEmpty(queryParam.getIsAutosite())) {
            queryParam.setHasAutomaticStation(
                EnumConstant.IS_AUTOSITE_YES.equals(queryParam.getIsAutosite()) ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                    : EnumConstant.HAS_AUTOMATIC_STATION_NO);
        }
        if (StringUtils.isNotEmpty(queryParam.getPackageId())) {
            queryParam.setPackageIdArr(Arrays.asList(StringUtils.split(queryParam.getPackageId(), ",")));
        }
        queryParam.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryParam.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduMonitorQualityTargetVO> list = scheduMonitorQualityTargetMapper
            .selectScheduMonitorQualityTargetList(queryParam);
        if (list.isEmpty()) {
            return list;
        }
        // 补充结果
        MonitorQualityResultQueryDTO queryDTO = new MonitorQualityResultQueryDTO();
        Set<String> statYearSet = new HashSet<>();
        Set<String> provinceCodeSet = new HashSet<>();
        Set<String> cityCodeSet = new HashSet<>();
        Set<String> siteIdSet = new HashSet<>();
        Set<String> monitIndexSet = new HashSet<>();
        for (ScheduMonitorQualityTargetVO vo : list) {
            statYearSet.add(vo.getStatYear());
            provinceCodeSet.add(vo.getProvinceCode());
            cityCodeSet.add(vo.getCityCode());
            siteIdSet.add(vo.getSiteId());
            monitIndexSet.add(vo.getMonitIndex());
        }
        List<String> statYearArr = new ArrayList<>(statYearSet);
        List<String> provinceCodeArr = new ArrayList<>(provinceCodeSet);
        List<String> cityCodeArr = new ArrayList<>(cityCodeSet);
        List<String> siteIdArr = new ArrayList<>(siteIdSet);
        List<String> monitIndexArr = new ArrayList<>(monitIndexSet);
        queryDTO.setStatYearArr(statYearArr);
        queryDTO.setProvinceCodeArr(provinceCodeArr);
        queryDTO.setCityCodeArr(cityCodeArr);
        queryDTO.setSiteIdArr(siteIdArr);
        queryDTO.setMonitIndexArr(monitIndexArr);
        List<ScheduMonitorQualityResult> qualityResultList = scheduMonitorQualityResultMapper
            .selectCollectMonitorQualityResultList(queryDTO);
        // 转字典, id-拼接除了结果值以外的字段
        Map<String, ScheduMonitorQualityResult> resultMap = qualityResultList.stream()
            .collect(Collectors.toMap(
                vo -> vo.getStatYear() + vo.getProvinceCode() + vo.getCityCode() + vo.getSiteId() + vo.getMonitIndex(),
                Function.identity(), (o1, o2) -> o1));
        for (ScheduMonitorQualityTargetVO vo : list) {
            String key = vo.getStatYear() + vo.getProvinceCode() + vo.getCityCode() + vo.getSiteId()
                + vo.getMonitIndex();
            if (resultMap.containsKey(key)) {
                ScheduMonitorQualityResult result = resultMap.get(key);
                vo.setAccuracyResult(result.getAccuracy());
                vo.setPrecisionResult(result.getPrecision());
                vo.setEffectivenessRateResult(result.getEffectivenessRate());
                vo.setCaptureRateResult(result.getCaptureRate());
                vo.setQuactrlPassRateResult(result.getQuactrlPassRate());
            }
        }
        return list;
    }

    /**
     * 质量目标新增/编辑
     *
     * @param scheduMonitorQualityTarget
     * @return 结果
     */
    @Override
    public int saveQualityTarget(ScheduMonitorQualityTargetQueryDTO scheduMonitorQualityTarget) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isEmpty(scheduMonitorQualityTarget.getId())) {
            // 新增逻辑
            log.info("新增质量目标记录");
            // 查询出站点数据（前台可以不传具体站点，后台根据站点类型、业务类型、省、市等参数查询出具体站点）
            ResSiteQueryDTO queryParam = new ResSiteQueryDTO();
            if (StringUtils.isNotEmpty(scheduMonitorQualityTarget.getSiteId())) {
                queryParam.setSiteIdArr(Arrays.asList(StringUtils.split(scheduMonitorQualityTarget.getSiteId(), ",")));
            }
            queryParam.setMonitoringElement(scheduMonitorQualityTarget.getBusinessType());
            queryParam.setSiteType(scheduMonitorQualityTarget.getSiteType());
            queryParam.setProvince(scheduMonitorQualityTarget.getProvinceCode());
            queryParam.setCity(scheduMonitorQualityTarget.getCityCode());
            if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(scheduMonitorQualityTarget.getBusinessType())) {
                queryParam.setHasAutomaticStation(
                    EnumConstant.IS_AUTOSITE_YES.equals(scheduMonitorQualityTarget.getIsAutosite())
                        ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                        : EnumConstant.HAS_AUTOMATIC_STATION_NO);
            }
            List<ResSiteVO> siteList = resSiteMapper.selectSiteVOList(queryParam);
            if (siteList.isEmpty()) {
                log.info("未查询到符合条件的站点数据");
                Assert.isTrue(false, "未匹配到符合条件的站点数据，请修改");
                return 0;
            }
            // 查询是不是已存在要保存的站点相关指标
            List<String> siteIdList = siteList.stream().map(ResSiteVO::getSiteId).collect(Collectors.toList());
            ScheduMonitorQualityTargetQueryDTO queryCountDto = new ScheduMonitorQualityTargetQueryDTO();
            queryCountDto.setSiteIdArr(siteIdList);
            queryCountDto.setBusinessType(scheduMonitorQualityTarget.getBusinessType());
            queryCountDto.setMonitIndex(scheduMonitorQualityTarget.getMonitIndex());
            queryCountDto.setStatYear(scheduMonitorQualityTarget.getStatYear());
            int countCheck = scheduMonitorQualityTargetMapper.selectScheduMonitorQualityTargetCount(queryCountDto);
            if (countCheck > 0) {
                Assert.isTrue(false, "已存在相同业务分类和监测参数的站点目标值，请修改");
            }
            // 批量插入
            List<ScheduMonitorQualityTarget> targetList = new ArrayList<>();
            siteList.forEach(site -> {
                ScheduMonitorQualityTarget target = new ScheduMonitorQualityTarget();
                BeanUtils.copyProperties(scheduMonitorQualityTarget, target);
                target.setSiteId(site.getSiteId());
                target.setSiteName(site.getSiteName());
                target.setSiteType(site.getSiteType());
                target.setProvinceCode(site.getProvince());
                target.setProvinceName(site.getProvinceName());
                target.setCityCode(site.getCity());
                target.setCityName(site.getCityName());
                target.setCreateBy(loginUser.getUsername());
                target.setUpdateBy(loginUser.getUsername());
                targetList.add(target);
            });
            return scheduMonitorQualityTargetMapper.batchInsertScheduMonitorQualityTarget(targetList);
        }
        else {
            // 编辑逻辑
            log.info("修改质量目标记录，id:{}", scheduMonitorQualityTarget.getId());
            scheduMonitorQualityTarget.setUpdateBy(loginUser.getUsername());
            return scheduMonitorQualityTargetMapper.updateScheduMonitorQualityTarget(scheduMonitorQualityTarget);
        }
    }

    /**
     * 质量目标删除
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    @Override
    public int delQualityTarget(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ScheduMonitorQualityTarget scheduMonitorQualityTarget = new ScheduMonitorQualityTarget();
        scheduMonitorQualityTarget.setId(id);
        scheduMonitorQualityTarget.setStatus(CommonConstant.DATA_STATUS_X);
        scheduMonitorQualityTarget.setUpdateBy(loginUser.getUsername());
        return scheduMonitorQualityTargetMapper.updateScheduMonitorQualityTarget(scheduMonitorQualityTarget);
    }
}
