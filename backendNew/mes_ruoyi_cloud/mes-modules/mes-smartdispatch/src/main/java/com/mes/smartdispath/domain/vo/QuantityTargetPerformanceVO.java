package com.mes.smartdispath.domain.vo;

/**
 * @Author: li.haoyang
 * @Description：数量目标完成情况 @Date： 2025/7/14
 */
public class QuantityTargetPerformanceVO {
    /**
     * 当前月周计划目标
     */
    private Integer curMonthWeeklyTarget;

    /**
     * 当前月月计划目标
     */
    private Integer curMonthMonthlyTarget;

    /**
     * 当前月总目标
     */
    private Integer curMonthTotalTarget;

    /**
     * 上月周计划目标
     */
    private Integer lastMonthWeeklyTarget;

    /**
     * 上月月计划目标
     */
    private Integer lastMonthMonthlyTarget;

    /**
     * 上月总目标
     */
    private Integer lastMonthTotalTarget;

    /**
     * 当前月周计划完成情况
     */
    private Integer curMonthWeeklyPerformance;

    /**
     * 当前月月计划完成情况
     */
    private Integer curMonthMonthlyPerformance;

    /**
     * 当前月总计划完成情况
     */
    private Integer curMonthTotalPerformance;

    /**
     * 上月周计划完成情况
     */
    private Integer lastMonthWeeklyPerformance;

    /**
     * 上月月计划完成情况
     */
    private Integer lastMonthMonthlyPerformance;

    /**
     * 上月总计划完成情况
     */
    private Integer lastMonthTotalPerformance;

    public QuantityTargetPerformanceVO() {
        this.curMonthWeeklyTarget = 0;
        this.curMonthMonthlyTarget = 0;
        this.curMonthTotalTarget = 0;
        this.lastMonthWeeklyTarget = 0;
        this.lastMonthMonthlyTarget = 0;
        this.lastMonthTotalTarget = 0;
        this.curMonthWeeklyPerformance = 0;
        this.curMonthMonthlyPerformance = 0;
        this.curMonthTotalPerformance = 0;
        this.lastMonthWeeklyPerformance = 0;
        this.lastMonthMonthlyPerformance = 0;
        this.lastMonthTotalPerformance = 0;
    }

    public Integer getCurMonthWeeklyTarget() {
        return curMonthWeeklyTarget;
    }

    public void setCurMonthWeeklyTarget(Integer curMonthWeeklyTarget) {
        this.curMonthWeeklyTarget = curMonthWeeklyTarget;
    }

    public Integer getCurMonthMonthlyTarget() {
        return curMonthMonthlyTarget;
    }

    public void setCurMonthMonthlyTarget(Integer curMonthMonthlyTarget) {
        this.curMonthMonthlyTarget = curMonthMonthlyTarget;
    }

    public Integer getCurMonthTotalTarget() {
        return curMonthTotalTarget;
    }

    public void setCurMonthTotalTarget(Integer curMonthTotalTarget) {
        this.curMonthTotalTarget = curMonthTotalTarget;
    }

    public Integer getLastMonthWeeklyTarget() {
        return lastMonthWeeklyTarget;
    }

    public void setLastMonthWeeklyTarget(Integer lastMonthWeeklyTarget) {
        this.lastMonthWeeklyTarget = lastMonthWeeklyTarget;
    }

    public Integer getLastMonthMonthlyTarget() {
        return lastMonthMonthlyTarget;
    }

    public void setLastMonthMonthlyTarget(Integer lastMonthMonthlyTarget) {
        this.lastMonthMonthlyTarget = lastMonthMonthlyTarget;
    }

    public Integer getLastMonthTotalTarget() {
        return lastMonthTotalTarget;
    }

    public void setLastMonthTotalTarget(Integer lastMonthTotalTarget) {
        this.lastMonthTotalTarget = lastMonthTotalTarget;
    }

    public Integer getCurMonthWeeklyPerformance() {
        return curMonthWeeklyPerformance;
    }

    public void setCurMonthWeeklyPerformance(Integer curMonthWeeklyPerformance) {
        this.curMonthWeeklyPerformance = curMonthWeeklyPerformance;
    }

    public Integer getCurMonthMonthlyPerformance() {
        return curMonthMonthlyPerformance;
    }

    public void setCurMonthMonthlyPerformance(Integer curMonthMonthlyPerformance) {
        this.curMonthMonthlyPerformance = curMonthMonthlyPerformance;
    }

    public Integer getCurMonthTotalPerformance() {
        return curMonthTotalPerformance;
    }

    public void setCurMonthTotalPerformance(Integer curMonthTotalPerformance) {
        this.curMonthTotalPerformance = curMonthTotalPerformance;
    }

    public Integer getLastMonthWeeklyPerformance() {
        return lastMonthWeeklyPerformance;
    }

    public void setLastMonthWeeklyPerformance(Integer lastMonthWeeklyPerformance) {
        this.lastMonthWeeklyPerformance = lastMonthWeeklyPerformance;
    }

    public Integer getLastMonthMonthlyPerformance() {
        return lastMonthMonthlyPerformance;
    }

    public void setLastMonthMonthlyPerformance(Integer lastMonthMonthlyPerformance) {
        this.lastMonthMonthlyPerformance = lastMonthMonthlyPerformance;
    }

    public Integer getLastMonthTotalPerformance() {
        return lastMonthTotalPerformance;
    }

    public void setLastMonthTotalPerformance(Integer lastMonthTotalPerformance) {
        this.lastMonthTotalPerformance = lastMonthTotalPerformance;
    }
}
