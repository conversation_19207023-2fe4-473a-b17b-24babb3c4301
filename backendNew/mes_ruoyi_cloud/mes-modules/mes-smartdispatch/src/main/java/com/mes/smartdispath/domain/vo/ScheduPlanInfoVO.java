package com.mes.smartdispath.domain.vo;

import com.mes.smartdispath.domain.ScheduPlanExtend;
import com.mes.smartdispath.domain.ScheduPlanInfo;

/**
 * @Author: li.haoyang
 * @Description：计划信息VO @Date： 2025/7/14
 */
public class ScheduPlanInfoVO extends ScheduPlanInfo {
    /**
     * 站点类型名称
     */
    private String siteTypeName;

    /**
     * 是否自动站
     */
    private String hasAutomaticStation;

    /**
     * 站点的运维单位
     */
    private String operationUnit;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 活动大类名称
     */
    private String activityTypeName;

    /**
     * 活动小类名称
     */
    private String activitySubtypeName;

    /**
     * 审批意见
     */
    private String approvalOpinion;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批时间
     */
    private String approvalTime;

    /**
     * 关联的任务ID
     */
    private String taskId;

    /**
     * 是否重要活动
     */
    private String isImportant;

    /**
     * 计划扩展信息
     */
    private ScheduPlanExtend planExtendInfo;

    public String getSiteTypeName() {
        return siteTypeName;
    }

    public void setSiteTypeName(String siteTypeName) {
        this.siteTypeName = siteTypeName;
    }

    public String getHasAutomaticStation() {
        return hasAutomaticStation;
    }

    public void setHasAutomaticStation(String hasAutomaticStation) {
        this.hasAutomaticStation = hasAutomaticStation;
    }

    public String getOperationUnit() {
        return operationUnit;
    }

    public void setOperationUnit(String operationUnit) {
        this.operationUnit = operationUnit;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getActivityTypeName() {
        return activityTypeName;
    }

    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }

    public String getActivitySubtypeName() {
        return activitySubtypeName;
    }

    public void setActivitySubtypeName(String activitySubtypeName) {
        this.activitySubtypeName = activitySubtypeName;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getIsImportant() {
        return isImportant;
    }

    public void setIsImportant(String isImportant) {
        this.isImportant = isImportant;
    }

    public ScheduPlanExtend getPlanExtendInfo() {
        return planExtendInfo;
    }

    public void setPlanExtendInfo(ScheduPlanExtend planExtendInfo) {
        this.planExtendInfo = planExtendInfo;
    }
}
