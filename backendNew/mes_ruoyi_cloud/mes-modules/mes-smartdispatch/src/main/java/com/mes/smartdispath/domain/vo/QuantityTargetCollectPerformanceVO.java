package com.mes.smartdispath.domain.vo;

/**
 * 汇总的数量目标完成情况
 *
 * @Author: li.haoyang @Date： 2025/7/14
 */
public class QuantityTargetCollectPerformanceVO {
    /**
     * 年度水目标
     */
    private Integer annualWaterTarget;

    /**
     * 年度气目标
     */
    private Integer annualAirTarget;

    /**
     * 年度总目标
     */
    private Integer annualTotalTarget;

    /**
     * 本月水目标
     */
    private Integer curMonthWaterTarget;

    /**
     * 本月气目标
     */
    private Integer curMonthAirTarget;

    /**
     * 本月总目标
     */
    private Integer curMonthTotalTarget;

    /**
     * 年度水目标完成情况
     */
    private Integer annualWaterPerformance;

    /**
     * 年度气目标完成情况
     */
    private Integer annualAirPerformance;

    /**
     * 年度总目标完成情况
     */
    private Integer annualTotalPerformance;

    /**
     * 本月水目标完成情况
     */
    private Integer curMonthWaterPerformance;

    /**
     * 本月气目标完成情况
     */
    private Integer curMonthAirPerformance;

    /**
     * 本月总目标完成情况
     */
    private Integer curMonthTotalPerformance;

    public QuantityTargetCollectPerformanceVO() {
        this.annualWaterTarget = 0;
        this.annualAirTarget = 0;
        this.annualTotalTarget = 0;
        this.curMonthWaterTarget = 0;
        this.curMonthAirTarget = 0;
        this.curMonthTotalTarget = 0;
        this.annualWaterPerformance = 0;
        this.annualAirPerformance = 0;
        this.annualTotalPerformance = 0;
        this.curMonthWaterPerformance = 0;
        this.curMonthAirPerformance = 0;
        this.curMonthTotalPerformance = 0;
    }

    public Integer getAnnualWaterTarget() {
        return annualWaterTarget;
    }

    public void setAnnualWaterTarget(Integer annualWaterTarget) {
        this.annualWaterTarget = annualWaterTarget;
    }

    public Integer getAnnualAirTarget() {
        return annualAirTarget;
    }

    public void setAnnualAirTarget(Integer annualAirTarget) {
        this.annualAirTarget = annualAirTarget;
    }

    public Integer getAnnualTotalTarget() {
        return annualTotalTarget;
    }

    public void setAnnualTotalTarget(Integer annualTotalTarget) {
        this.annualTotalTarget = annualTotalTarget;
    }

    public Integer getCurMonthWaterTarget() {
        return curMonthWaterTarget;
    }

    public void setCurMonthWaterTarget(Integer curMonthWaterTarget) {
        this.curMonthWaterTarget = curMonthWaterTarget;
    }

    public Integer getCurMonthAirTarget() {
        return curMonthAirTarget;
    }

    public void setCurMonthAirTarget(Integer curMonthAirTarget) {
        this.curMonthAirTarget = curMonthAirTarget;
    }

    public Integer getCurMonthTotalTarget() {
        return curMonthTotalTarget;
    }

    public void setCurMonthTotalTarget(Integer curMonthTotalTarget) {
        this.curMonthTotalTarget = curMonthTotalTarget;
    }

    public Integer getAnnualWaterPerformance() {
        return annualWaterPerformance;
    }

    public void setAnnualWaterPerformance(Integer annualWaterPerformance) {
        this.annualWaterPerformance = annualWaterPerformance;
    }

    public Integer getAnnualAirPerformance() {
        return annualAirPerformance;
    }

    public void setAnnualAirPerformance(Integer annualAirPerformance) {
        this.annualAirPerformance = annualAirPerformance;
    }

    public Integer getAnnualTotalPerformance() {
        return annualTotalPerformance;
    }

    public void setAnnualTotalPerformance(Integer annualTotalPerformance) {
        this.annualTotalPerformance = annualTotalPerformance;
    }

    public Integer getCurMonthWaterPerformance() {
        return curMonthWaterPerformance;
    }

    public void setCurMonthWaterPerformance(Integer curMonthWaterPerformance) {
        this.curMonthWaterPerformance = curMonthWaterPerformance;
    }

    public Integer getCurMonthAirPerformance() {
        return curMonthAirPerformance;
    }

    public void setCurMonthAirPerformance(Integer curMonthAirPerformance) {
        this.curMonthAirPerformance = curMonthAirPerformance;
    }

    public Integer getCurMonthTotalPerformance() {
        return curMonthTotalPerformance;
    }

    public void setCurMonthTotalPerformance(Integer curMonthTotalPerformance) {
        this.curMonthTotalPerformance = curMonthTotalPerformance;
    }
}
