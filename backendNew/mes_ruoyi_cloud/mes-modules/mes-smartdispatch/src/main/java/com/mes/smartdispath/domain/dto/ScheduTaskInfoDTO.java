package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.ScheduTaskExtend;
import com.mes.smartdispath.domain.ScheduTaskInfo;

/**
 * 任务信息DTO
 * 
 * @Author: li.haoyang @Date： 2025/7/28
 */
public class ScheduTaskInfoDTO extends ScheduTaskInfo {
    /**
     * 计划信息
     */
    private List<ScheduPlanInfo> planInfos;

    /**
     * 任务扩展信息
     */
    private List<ScheduTaskExtend> extendInfos;

    /**
     * 实验室地址
     */
    private String laboratoryAddress;

    /**
     * 实验室名称
     */
    private String laboratoryName;

    /**
     * 实验室code
     */
    private String laboratoryCode;

    /**
     * 任务交割地址，如采样与送样任务
     */
    private String deliveryAddress;

    /**
     * 任务更新标识，1-更新
     */
    private String taskUpdateFlag;

    /**
     * 任务扩展更新标识，1-更新
     */
    private String extendUpdateFlag;

    public List<ScheduPlanInfo> getPlanInfos() {
        return planInfos;
    }

    public void setPlanInfos(List<ScheduPlanInfo> planInfos) {
        this.planInfos = planInfos;
    }

    public List<ScheduTaskExtend> getExtendInfos() {
        return extendInfos;
    }

    public void setExtendInfos(List<ScheduTaskExtend> extendInfos) {
        this.extendInfos = extendInfos;
    }

    public String getLaboratoryAddress() {
        return laboratoryAddress;
    }

    public void setLaboratoryAddress(String laboratoryAddress) {
        this.laboratoryAddress = laboratoryAddress;
    }

    public String getLaboratoryName() {
        return laboratoryName;
    }

    public void setLaboratoryName(String laboratoryName) {
        this.laboratoryName = laboratoryName;
    }

    public String getLaboratoryCode() {
        return laboratoryCode;
    }

    public void setLaboratoryCode(String laboratoryCode) {
        this.laboratoryCode = laboratoryCode;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getTaskUpdateFlag() {
        return taskUpdateFlag;
    }

    public void setTaskUpdateFlag(String taskUpdateFlag) {
        this.taskUpdateFlag = taskUpdateFlag;
    }

    public String getExtendUpdateFlag() {
        return extendUpdateFlag;
    }

    public void setExtendUpdateFlag(String extendUpdateFlag) {
        this.extendUpdateFlag = extendUpdateFlag;
    }
}
