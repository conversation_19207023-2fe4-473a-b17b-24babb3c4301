package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduPlanInfo;

/**
 * 计划信息查询DTO
 *
 * @Author: li.haoyang @Date： 2025/7/11
 */
public class ScheduPlanInfoQueryDTO extends ScheduPlanInfo {
    /**
     * id数组
     */
    private List<String> idArr;

    /**
     * 执行时间区间
     */
    private String startTime;

    /**
     * 执行时间区间
     */
    private String endTime;

    /**
     * 城市code数组
     */
    private List<String> cityCodeArr;

    /**
     * 审批状态数组
     */
    private List<String> approvalStatusArr;

    /**
     * 业务类型字典编码
     */
    private String businessTypeDictClassCode;

    /**
     * 站点类型字典编码
     */
    private String siteTypeDictClassCode;

    /**
     * 任务id数组
     */
    private List<String> taskIdArr;

    /**
     * 站点id数组
     */
    private List<String> siteIdArr;

    /**
     * 调度状态数组
     */
    private List<String> scheduStatusArr;

    public ScheduPlanInfoQueryDTO() {
        this.businessTypeDictClassCode = SysDictConstant.BUSINESS_TYPE_CLASS_CODE;
        this.siteTypeDictClassCode = SysDictConstant.SITE_TYPE_CLASS_CODE;
    }

    public List<String> getIdArr() {
        return idArr;
    }

    public void setIdArr(List<String> idArr) {
        this.idArr = idArr;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<String> getCityCodeArr() {
        return cityCodeArr;
    }

    public void setCityCodeArr(List<String> cityCodeArr) {
        this.cityCodeArr = cityCodeArr;
    }

    public List<String> getApprovalStatusArr() {
        return approvalStatusArr;
    }

    public void setApprovalStatusArr(List<String> approvalStatusArr) {
        this.approvalStatusArr = approvalStatusArr;
    }

    public String getBusinessTypeDictClassCode() {
        return businessTypeDictClassCode;
    }

    public void setBusinessTypeDictClassCode(String businessTypeDictClassCode) {
        this.businessTypeDictClassCode = businessTypeDictClassCode;
    }

    public String getSiteTypeDictClassCode() {
        return siteTypeDictClassCode;
    }

    public void setSiteTypeDictClassCode(String siteTypeDictClassCode) {
        this.siteTypeDictClassCode = siteTypeDictClassCode;
    }

    public List<String> getTaskIdArr() {
        return taskIdArr;
    }

    public void setTaskIdArr(List<String> taskIdArr) {
        this.taskIdArr = taskIdArr;
    }

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public List<String> getScheduStatusArr() {
        return scheduStatusArr;
    }

    public void setScheduStatusArr(List<String> scheduStatusArr) {
        this.scheduStatusArr = scheduStatusArr;
    }
}
