package com.mes.smartdispath.config.aspect;

import java.lang.reflect.Method;

import javax.annotation.Resource;
import javax.servlet.ServletRequestWrapper;
import javax.servlet.ServletResponseWrapper;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.config.exception.BizException;
import com.mes.smartdispath.domain.PubOperationLog;
import com.mes.smartdispath.service.IPubOperationLogService;
import com.mes.smartdispath.utils.IPUtils;
import com.mes.system.api.model.LoginUser;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Aspect
@Slf4j
@Component
@Order(1)
@EnableAspectJAutoProxy(proxyTargetClass = true)
@RequiredArgsConstructor
public class OperationLogEndpointAspect extends BaseAspectSupport {

    @Autowired
    private IPubOperationLogService pubOperationLogService;

    @Resource
    private HttpServletRequest request;

    @Pointcut("execution(* com.mes.smartdispath.controller.*.*(..)) && @annotation(com.mes.smartdispath.config.aspect.OperationLogEndpoint)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Method targetMethod = resolveMethod(point);
        OperationLogEndpoint annotation = targetMethod.getAnnotation(OperationLogEndpoint.class);
        String error = null;

        PubOperationLog operationLog = new PubOperationLog();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = "admin";
        String ipAddr = IPUtils.getIpAddr(request);
        String oprateLogType = annotation.operationType().getValue();
        String oprateLogModule = annotation.module();
        String oprateLogContent = annotation.operationContent() + "，" + getFrontParam(point.getArgs());

        if (null != loginUser) {
            username = loginUser.getUsername();
        }
        operationLog.setName(username);
        operationLog.setIp(ipAddr);
        operationLog.setType(oprateLogType);
        operationLog.setModule(oprateLogModule);
        operationLog.setContent(oprateLogContent);
        operationLog.setState("A");
        operationLog.setCreateBy(username);

        // 记录操作日志
        pubOperationLogService.insertPubOperationLog(operationLog);

        Object result = null; // 用于存储目标方法的返回值
        try {
            // 执行目标方法
            result = point.proceed();
        }
        catch (Throwable throwable) {
            // 处理异常
            error = throwable.getMessage();
            throw new BizException(error); // 根据需要抛出自定义异常
        }

        return result; // 返回目标方法的结果
    }

    /**
     * 前端入参
     *
     * @param objects
     * @return
     */
    private String getFrontParam(Object[] objects) {
        if (objects == null || objects.length == 0) {
            return "";
        }
        try {
            // 过滤不应序列化的对象类型
            for (Object obj : objects) {
                if (obj == null) {
                    continue;
                }
                if (obj instanceof HttpServletRequest || obj instanceof HttpServletResponse
                    || obj instanceof ServletRequestWrapper || obj instanceof ServletResponseWrapper) {
                    // 这些对象不序列化，跳过或返回空
                    return "";
                }
            }

            // 如果只有一个参数且是字符串，直接返回字符串
            if (objects.length == 1 && objects[0] instanceof String) {
                return (String) objects[0];
            }

            // 否则序列化所有参数为JSON字符串
            // 如果想序列化多个参数，可以序列化整个数组，或者自己拼接
            return JSONObject.toJSONString(objects);
        }
        catch (Exception e) {
            // 序列化失败时避免抛异常导致业务异常
            return "";
        }
    }
}
