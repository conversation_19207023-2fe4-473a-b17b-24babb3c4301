package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ResDevice;
import com.mes.smartdispath.domain.ScheduApprovalRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.ScheduPlanAssorule;
import com.mes.smartdispath.domain.ScheduPlanExtend;
import com.mes.smartdispath.domain.ScheduPlanHis;
import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.ScheduPlanRule;
import com.mes.smartdispath.domain.dto.ApprovalDTO;
import com.mes.smartdispath.domain.dto.ResDeviceQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduApprovalRecordQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanAssoruleQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanAssoruleVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoDetailVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduPlanRuleVO;
import com.mes.smartdispath.enums.BusinessTypeShortPrefixEnum;
import com.mes.smartdispath.mapper.ResDeviceMapper;
import com.mes.smartdispath.mapper.ResSiteInspectionItemMapper;
import com.mes.smartdispath.mapper.ResSiteMapper;
import com.mes.smartdispath.mapper.ScheduApprovalRecordMapper;
import com.mes.smartdispath.mapper.ScheduPlanAssoruleMapper;
import com.mes.smartdispath.mapper.ScheduPlanExtendMapper;
import com.mes.smartdispath.mapper.ScheduPlanHisMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduPlanRuleMapper;
import com.mes.smartdispath.service.IGenericMgrService;
import com.mes.smartdispath.service.IPlanMgrService;
import com.mes.smartdispath.service.ISavePlanService;
import com.mes.smartdispath.utils.DateUtils;
import com.mes.system.api.model.LoginUser;

/**
 * 计划管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class PlanMgrServiceImpl implements IPlanMgrService {
    private static final Logger log = LoggerFactory.getLogger(PlanMgrServiceImpl.class);

    @Autowired
    private ScheduPlanRuleMapper scheduPlanRuleMapper;

    @Autowired
    private ScheduApprovalRecordMapper scheduApprovalRecordMapper;

    @Autowired
    private ScheduPlanAssoruleMapper scheduPlanAssoruleMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ScheduPlanHisMapper scheduPlanHisMapper;

    @Autowired
    private ResDeviceMapper resDeviceMapper;

    @Autowired
    private ScheduPlanExtendMapper scheduPlanExtendMapper;

    @Autowired
    private IGenericMgrService genericMgrService;

    @Autowired
    private ResSiteMapper resSiteMapper;

    @Autowired
    private ResSiteInspectionItemMapper resSiteInspectionItemMapper;

    /**
     * 断面采样的监测活动大类code
     */
    @Value("${activityType.sectionSampling}")
    private String sectionSamplingActivityType;

    @Autowired
    private ISavePlanService savePlanService;

    /**
     * 计划生成规则查询
     * 
     * @param queryDto 查询参数
     * @return 计划生成规则列表
     */
    @Override
    public List<ScheduPlanRuleVO> qryPlanRule(ScheduPlanRuleQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getApprovalStatus())) {
            queryDto.setApprovalStatusArr(Arrays.asList(StringUtils.split(queryDto.getApprovalStatus(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getSiteId())) {
            queryDto.setSiteIdArr(Arrays.asList(StringUtils.split(queryDto.getSiteId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduPlanRuleVO> result = scheduPlanRuleMapper.selectScheduPlanRuleList(queryDto);
        // 补充审批信息
        if (!result.isEmpty()) {
            List<String> planRuleIds = result.stream().map(ScheduPlanRuleVO::getId).collect(Collectors.toList());
            ScheduApprovalRecordQueryDTO approvalQueryDto = new ScheduApprovalRecordQueryDTO();
            approvalQueryDto.setBusinessIdArr(planRuleIds);
            approvalQueryDto.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_RULE);
            List<ScheduApprovalRecord> approvalRecords = scheduApprovalRecordMapper
                .selectBizApprovalRecordList(approvalQueryDto);
            // 转成审批结果字典
            Map<String, ScheduApprovalRecord> approvalMap = approvalRecords.stream()
                .collect(Collectors.toMap(ScheduApprovalRecord::getBusinessId, item -> item, (item1, item2) -> item1));
            for (ScheduPlanRuleVO planRule : result) {
                ScheduApprovalRecord approvalRecord = approvalMap.get(planRule.getId());
                if (approvalRecord != null) {
                    planRule.setApprovalOpinion(approvalRecord.getApprovalOpinion());
                    planRule.setApprover(approvalRecord.getApprover());
                    planRule.setApprovalTime(approvalRecord.getApprovalTime());
                }
            }
        }
        return result;
    }

    /**
     * 计划生成规则新增/编辑
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePlanRule(ScheduPlanRuleDTO dto) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date curDateTime = DateUtils.getCurDateTime();
        dto.setUpdateBy(loginUser.getUsername());
        dto.setUpdateTime(curDateTime);
        String id = null;
        String approvalStatus = null != dto.getSubmitFlag()
            && StringUtils.equals(dto.getSubmitFlag(), CommonConstant.SUBMIT_FLAG_1)
                ? CommonConstant.APPROVAL_STATUS_PENDING
                : null;
        dto.setApprovalStatus(approvalStatus);
        String businessTypeShortPrefix = BusinessTypeShortPrefixEnum.getShortPrefixByType(dto.getBusinessType());
        String curDate = DateUtils.getCurDateWithoutHyphen();
        ScheduPlanRule scheduPlanRule = new ScheduPlanRule();
        BeanUtils.copyProperties(dto, scheduPlanRule);
        int count = 0;
        if (StringUtils.isEmpty(dto.getId())) {
            // 新增逻辑
            log.info("新增计划规则");
            String ruleCode = CommonConstant.PLAN_RULE_CODE_PREFIX + businessTypeShortPrefix + curDate + "-"
                + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
            scheduPlanRule.setRuleCode(ruleCode);
            scheduPlanRule.setCreateBy(loginUser.getUsername());
            scheduPlanRule.setCreateTime(curDateTime);
            scheduPlanRule.setStatus(CommonConstant.DATA_STATUS_A);
            count = scheduPlanRuleMapper.insertScheduPlanRule(scheduPlanRule);
            id = scheduPlanRule.getId();
        }
        else {
            // 编辑逻辑
            log.info("修改计划规则，id:{}", dto.getId());
            id = dto.getId();
            count = scheduPlanRuleMapper.updateScheduPlanRule(scheduPlanRule);
        }
        // 如果是提交，那么生成计划规则相关的伴生计划规则、审批信息等
        if (StringUtils.equals(dto.getSubmitFlag(), CommonConstant.SUBMIT_FLAG_1)) {
            log.info("提交计划规则，id:{}", id);
            List<ScheduMonitorActivityInfo> effectiveActivityList = genericMgrService
                .getEffectiveActivityList(dto.getSiteId(), dto.getActivityType(), dto.getActivitySubtype());
            if (!effectiveActivityList.isEmpty()) {
                List<ScheduPlanAssorule> scheduPlanAssoruleList = new ArrayList<>();
                for (ScheduMonitorActivityInfo activityInfo : effectiveActivityList) {
                    ScheduPlanAssorule scheduPlanAssorule = new ScheduPlanAssorule();
                    scheduPlanAssorule.setMainRuleId(id);
                    scheduPlanAssorule.setRuleCode(CommonConstant.PLAN_RULE_CODE_PREFIX + businessTypeShortPrefix
                        + curDate + "-" + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8));
                    scheduPlanAssorule
                        .setRuleName(dto.getRuleName() + "-" + activityInfo.getActivityTypeName() + "-伴生计划规则");
                    scheduPlanAssorule.setBusinessType(dto.getBusinessType());
                    scheduPlanAssorule.setSiteType(dto.getSiteType());
                    scheduPlanAssorule.setActivityType(activityInfo.getActivityTypeCode());
                    scheduPlanAssorule.setActivitySubtype(activityInfo.getActivitySubtypeCode());
                    scheduPlanAssorule.setUpdateTime(curDateTime);
                    scheduPlanAssorule.setUpdateTime(curDateTime);
                    scheduPlanAssoruleList.add(scheduPlanAssorule);
                }
                // 批量保存
                int assoruleCount = scheduPlanAssoruleMapper.batchInsertScheduPlanAssorule(scheduPlanAssoruleList);
                log.info("保存伴生规则条数:{}", assoruleCount);
                // 更新主规则的是否有伴生计划字段
                ScheduPlanRule updateRule = new ScheduPlanRule();
                updateRule.setId(id);
                updateRule.setIsAssoplan(CommonConstant.IS_ASSO_RULE_FLAG_1);
                scheduPlanRuleMapper.updateScheduPlanRule(updateRule);
            }
            // 保存审核信息到审核表
            ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
            scheduApprovalRecord.setBusinessId(id);
            scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_RULE);
            scheduApprovalRecord.setApprovalStatus(approvalStatus);
            scheduApprovalRecord.setUpdateBy(loginUser.getUsername());
            scheduApprovalRecord.setUpdateTime(curDateTime);
            scheduApprovalRecord.setCreateBy(loginUser.getUsername());
            scheduApprovalRecord.setCreateTime(curDateTime);
            scheduApprovalRecord.setStatus(CommonConstant.DATA_STATUS_A);
            int approvalCount = scheduApprovalRecordMapper.insertScheduApprovalRecord(scheduApprovalRecord);
            log.info("保存审核信息数量:{}", approvalCount);
        }
        return count;
    }

    /**
     * 计划生成规则删除
     *
     * @param id
     * @return
     */
    @Override
    public int delPlanRule(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 更新审批表的数据状态
        ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
        scheduApprovalRecord.setBusinessId(id);
        scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_RULE);
        scheduApprovalRecord.setStatus(CommonConstant.DATA_STATUS_X);
        scheduApprovalRecord.setUpdateBy(loginUser.getUsername());
        scheduApprovalRecordMapper.updateByBizId(scheduApprovalRecord);

        // 删除伴生规则
        ScheduPlanAssorule scheduPlanAssorule = new ScheduPlanAssorule();
        scheduPlanAssorule.setMainRuleId(id);
        scheduPlanAssorule.setStatus(CommonConstant.DATA_STATUS_X);
        scheduPlanAssoruleMapper.updateByMainRuleId(scheduPlanAssorule);

        // 删除主规则
        ScheduPlanRule scheduPlanRule = new ScheduPlanRule();
        scheduPlanRule.setId(id);
        scheduPlanRule.setStatus(CommonConstant.DATA_STATUS_X);
        scheduPlanRule.setUpdateBy(loginUser.getUsername());
        return scheduPlanRuleMapper.updateScheduPlanRule(scheduPlanRule);
    }

    /**
     * 计划生成规则审批【支持批量】
     * 
     * @param approvalDTO 审批参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int verifyPlanRule(ApprovalDTO approvalDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<String> planIdArr = Arrays.asList(StringUtils.split(approvalDTO.getId(), ","));
        // 批量新增审批记录
        List<ScheduApprovalRecord> approvalRecordList = new ArrayList<>();
        for (String planId : planIdArr) {
            ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
            scheduApprovalRecord.setBusinessId(planId);
            scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_RULE);
            scheduApprovalRecord.setApprover(loginUser.getUsername());
            scheduApprovalRecord.setApprovalStatus(approvalDTO.getApprovalStatus());
            scheduApprovalRecord.setApprovalOpinion(approvalDTO.getApprovalOpinion());
            scheduApprovalRecord.setCreateBy(loginUser.getUsername());
            scheduApprovalRecord.setUpdateBy(loginUser.getUsername());
        }
        scheduApprovalRecordMapper.batchInsertScheduApprovalRecord(approvalRecordList);
        // 更新计划主表的审批状态
        ScheduPlanRuleQueryDTO updateDto = new ScheduPlanRuleQueryDTO();
        updateDto.setIdArr(planIdArr);
        updateDto.setApprovalStatus(approvalDTO.getApprovalStatus());
        updateDto.setUpdateBy(loginUser.getUsername());
        return scheduPlanRuleMapper.updateScheduPlanRuleByIdArr(updateDto);
    }

    /**
     * 关联伴生计划规则查询
     * 
     * @param mainRuleId 主规则id
     * @return 伴生计划规则列表
     */
    @Override
    public List<ScheduPlanAssoruleVO> qryAssoPlanRule(String mainRuleId) {
        ScheduPlanAssoruleQueryDTO queryDto = new ScheduPlanAssoruleQueryDTO();
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setMainRuleId(mainRuleId);
        return scheduPlanAssoruleMapper.selectScheduPlanAssoruleListByMainRuleId(queryDto);
    }

    /**
     * 计划信息查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    @Override
    public List<ScheduPlanInfoVO> qryPlanList(ScheduPlanInfoQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getSiteId())) {
            queryDto.setSiteIdArr(Arrays.asList(StringUtils.split(queryDto.getSiteId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduPlanInfoVO> result = scheduPlanInfoMapper.selectScheduPlanInfoList(queryDto);
        // 补充审批信息
        addApprovalInfo(result);
        return result;
    }

    /**
     * 计划生成规则新增/编辑
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePlanInfo(ScheduPlanInfoDTO dto) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return savePlanService.savePlan(dto, loginUser.getUsername());
    }

    /**
     * 计划信息删除
     *
     * @param id
     * @return
     */
    @Override
    public int delPlanInfo(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 更新审批表的数据状态
        ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
        scheduApprovalRecord.setBusinessId(id);
        scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_INFO);
        scheduApprovalRecord.setStatus(CommonConstant.DATA_STATUS_X);
        scheduApprovalRecord.setUpdateBy(loginUser.getUsername());
        scheduApprovalRecordMapper.updateByBizId(scheduApprovalRecord);

        // 更新任务历史表
        ScheduPlanHis scheduPlanHis = new ScheduPlanHis();
        scheduPlanHis.setPlanId(id);
        scheduPlanHis.setStatus(CommonConstant.DATA_STATUS_X);
        scheduPlanHis.setUpdateBy(loginUser.getUsername());
        scheduPlanHisMapper.updateScheduPlanHisByPlanId(scheduPlanHis);

        // 需要更新主计划和伴生计划的状态
        ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
        scheduPlanInfo.setId(id);
        scheduPlanInfo.setMainPlanId(id);
        scheduPlanInfo.setStatus(CommonConstant.DATA_STATUS_X);
        scheduPlanInfo.setUpdateBy(loginUser.getUsername());
        return scheduPlanInfoMapper.deletePlanInfoAndAssoPlan(scheduPlanInfo);
    }

    /**
     * 计划信息审批列表查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    @Override
    public List<ScheduPlanInfoVO> qryPlanApprovalList(ScheduPlanInfoQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getApprovalStatus())) {
            queryDto.setApprovalStatusArr(Arrays.asList(StringUtils.split(queryDto.getApprovalStatus(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getSiteId())) {
            queryDto.setSiteIdArr(Arrays.asList(StringUtils.split(queryDto.getSiteId(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduPlanInfoVO> result = scheduPlanInfoMapper.selectApprovalScheduPlanInfoList(queryDto);
        // 补充审批信息
        addApprovalInfo(result);
        return result;
    }

    /**
     * 计划信息审核【支持批量】
     *
     * @param approvalDTO 审批参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int verifyPlanInfo(ApprovalDTO approvalDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<String> planIdArr = Arrays.asList(StringUtils.split(approvalDTO.getId(), ","));
        // 批量新增审批记录
        List<ScheduApprovalRecord> approvalRecordList = new ArrayList<>();
        for (String planId : planIdArr) {
            ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
            scheduApprovalRecord.setBusinessId(planId);
            scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_INFO);
            scheduApprovalRecord.setApprover(loginUser.getUsername());
            scheduApprovalRecord.setApprovalStatus(approvalDTO.getApprovalStatus());
            scheduApprovalRecord.setApprovalOpinion(approvalDTO.getApprovalOpinion());
            scheduApprovalRecord.setCreateBy(loginUser.getUsername());
            scheduApprovalRecord.setUpdateBy(loginUser.getUsername());
        }
        scheduApprovalRecordMapper.batchInsertScheduApprovalRecord(approvalRecordList);
        // 更新计划主表的审批状态
        ScheduPlanInfoQueryDTO updateDto = new ScheduPlanInfoQueryDTO();
        updateDto.setIdArr(planIdArr);
        updateDto.setApprovalStatus(approvalDTO.getApprovalStatus());
        updateDto.setUpdateBy(loginUser.getUsername());
        return scheduPlanInfoMapper.updateScheduPlanInfoByIdArr(updateDto);
    }

    /**
     * 计划新增时查询站点关联的设备信息
     *
     * @param queryDTO
     * @return 设备信息列表
     */
    @Override
    public List<ResDevice> qrySiteLinkDevice(ResDeviceQueryDTO queryDTO) {
        return resDeviceMapper.selectSiteLinkDevice(queryDTO);
    }

    /**
     * 计划信息详情查询
     *
     * @param id
     * @return 计划详情
     */
    @Override
    public ScheduPlanInfoDetailVO qryPlanDetail(String id) {
        ScheduPlanInfoQueryDTO queryDto = new ScheduPlanInfoQueryDTO();
        queryDto.setId(id);
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        ScheduPlanInfoVO scheduPlanInfoVO = scheduPlanInfoMapper.selectScheduPlanInfoById(queryDto);
        if (null == scheduPlanInfoVO) {
            return null;
        }
        ScheduPlanInfoDetailVO result = new ScheduPlanInfoDetailVO();
        BeanUtils.copyProperties(scheduPlanInfoVO, result);
        // 查询拓展信息
        ScheduPlanExtend scheduPlanExtend = scheduPlanExtendMapper.selectScheduPlanExtendByPlanId(id);
        result.setExtendInfo(scheduPlanExtend);
        // 查询设备信息
        if (StringUtils.isNotEmpty(scheduPlanInfoVO.getDevcModel())) {
            ResDevice resDevice = resDeviceMapper.selectResDeviceById(scheduPlanInfoVO.getDevcModel());
            result.setDeviceInfo(resDevice);
        }
        return result;
    }

    /**
     * 补充审批信息
     *
     * @param planInfoList
     */
    private void addApprovalInfo(List<ScheduPlanInfoVO> planInfoList) {
        if (!planInfoList.isEmpty()) {
            List<String> planInfoIds = planInfoList.stream().map(ScheduPlanInfoVO::getId).collect(Collectors.toList());
            ScheduApprovalRecordQueryDTO approvalQueryDto = new ScheduApprovalRecordQueryDTO();
            approvalQueryDto.setBusinessIdArr(planInfoIds);
            approvalQueryDto.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_INFO);
            List<ScheduApprovalRecord> approvalRecords = scheduApprovalRecordMapper
                .selectBizApprovalRecordList(approvalQueryDto);
            // 转成审批结果字典
            Map<String, ScheduApprovalRecord> approvalMap = approvalRecords.stream()
                .collect(Collectors.toMap(ScheduApprovalRecord::getBusinessId, item -> item, (item1, item2) -> item1));
            for (ScheduPlanInfoVO planInfo : planInfoList) {
                ScheduApprovalRecord approvalRecord = approvalMap.get(planInfo.getId());
                if (approvalRecord != null) {
                    planInfo.setApprovalOpinion(approvalRecord.getApprovalOpinion());
                    planInfo.setApprover(approvalRecord.getApprover());
                    planInfo.setApprovalTime(approvalRecord.getApprovalTime());
                }
            }
        }
    }
}
