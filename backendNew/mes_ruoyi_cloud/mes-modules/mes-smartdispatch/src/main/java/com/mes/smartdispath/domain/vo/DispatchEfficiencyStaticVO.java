package com.mes.smartdispath.domain.vo;

import java.util.List;

/**
 * 大屏的调度效率统计VO
 *
 * @Author: li.haoyang @Date： 2025/7/22
 */
public class DispatchEfficiencyStaticVO {
    /**
     * 当月任务平均计划数
     */
    private Double curMonthTaskAvgPlanCount;

    /**
     * 当月单人平均每天要参与的计划数
     */
    private Double curMonthPersonAvgPlanCount;

    /**
     * 月任务平均计划数列表
     */
    private List<ChartBasicVO> taskAvgPlanCountList;

    /**
     * 月单人平均每天要参与的计划数列表
     */
    private List<ChartBasicVO> personAvgPlanCountList;

    /**
     * 平均月任务平均计划数
     */
    private Double avgTaskAvgPlanCount;

    /**
     * 平均单人平均每天要参与的计划数
     */
    private Double avgPersonAvgPlanCount;

    public Double getCurMonthTaskAvgPlanCount() {
        return curMonthTaskAvgPlanCount;
    }

    public void setCurMonthTaskAvgPlanCount(Double curMonthTaskAvgPlanCount) {
        this.curMonthTaskAvgPlanCount = curMonthTaskAvgPlanCount;
    }

    public Double getCurMonthPersonAvgPlanCount() {
        return curMonthPersonAvgPlanCount;
    }

    public void setCurMonthPersonAvgPlanCount(Double curMonthPersonAvgPlanCount) {
        this.curMonthPersonAvgPlanCount = curMonthPersonAvgPlanCount;
    }

    public List<ChartBasicVO> getTaskAvgPlanCountList() {
        return taskAvgPlanCountList;
    }

    public void setTaskAvgPlanCountList(List<ChartBasicVO> taskAvgPlanCountList) {
        this.taskAvgPlanCountList = taskAvgPlanCountList;
    }

    public List<ChartBasicVO> getPersonAvgPlanCountList() {
        return personAvgPlanCountList;
    }

    public void setPersonAvgPlanCountList(List<ChartBasicVO> personAvgPlanCountList) {
        this.personAvgPlanCountList = personAvgPlanCountList;
    }

    public Double getAvgTaskAvgPlanCount() {
        return avgTaskAvgPlanCount;
    }

    public void setAvgTaskAvgPlanCount(Double avgTaskAvgPlanCount) {
        this.avgTaskAvgPlanCount = avgTaskAvgPlanCount;
    }

    public Double getAvgPersonAvgPlanCount() {
        return avgPersonAvgPlanCount;
    }

    public void setAvgPersonAvgPlanCount(Double avgPersonAvgPlanCount) {
        this.avgPersonAvgPlanCount = avgPersonAvgPlanCount;
    }
}
