package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.activity.api.RemoteOrderService;
import com.mes.activity.api.domain.param.CreateAirOrderParam;
import com.mes.activity.api.domain.param.CreateWaterOrderParam;
import com.mes.smartdispath.constant.PlanConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.constant.TaskConstant;
import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.ScheduTaskExtend;
import com.mes.smartdispath.domain.ScheduTaskInfo;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskExtendQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.service.IPushTaskService;

/**
 * 推送任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class PushTaskServiceImpl implements IPushTaskService {
    private static final Logger log = LoggerFactory.getLogger(PushTaskServiceImpl.class);

    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduTaskExtendMapper scheduTaskExtendMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private RemoteOrderService remoteOrderService;

    /**
     * 推送任务到活动
     *
     * @param queryDto 任务查询参数
     * @return
     */
    @Override
    public int pushTaskToActivity(ScheduTaskInfoQueryDTO queryDto, String operationUser) {
        log.info("推送任务到活动侧开始，查询参数：{}", queryDto);
        List<ScheduTaskInfoVO> taskInfos = scheduTaskInfoMapper.selectScheduTaskInfoList(queryDto);
        if (taskInfos.isEmpty()) {
            log.info("未查询到任务信息");
            return 0;
        }
        List<String> taskIdArr = taskInfos.stream().map(ScheduTaskInfoVO::getId).collect(Collectors.toList());
        // 计划信息
        ScheduPlanInfoQueryDTO planQueryDTO = new ScheduPlanInfoQueryDTO();
        planQueryDTO.setTaskIdArr(taskIdArr);
        planQueryDTO.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        planQueryDTO.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        List<ScheduPlanInfoVO> planInfos = scheduPlanInfoMapper.selectTaskLinkPlanInfoList(planQueryDTO);
        log.info("过滤前计划数：{}", planInfos.size());
        // 过滤掉已中止的计划（可能会因为天气原因批量中止计划）
        planInfos = planInfos.stream()
            .filter(planInfo -> !planInfo.getScheduStatus().equals(PlanConstant.EXEC_STATUS_STOP))
            .collect(Collectors.toList());
        log.info("过滤后计划数：{}", planInfos.size());
        // 按照任务id分组计划信息
        Map<String, List<ScheduPlanInfoVO>> planInfoDict = planInfos.stream()
            .collect(Collectors.groupingBy(ScheduPlanInfoVO::getTaskId));
        // 查询任务扩展信息
        ScheduTaskExtendQueryDTO extendQueryDTO = new ScheduTaskExtendQueryDTO();
        extendQueryDTO.setTaskIdArr(taskIdArr);
        List<ScheduTaskExtend> extendList = scheduTaskExtendMapper.selectScheduTaskExtendList(extendQueryDTO);
        // 按照任务id分组任务扩展信息
        Map<String, List<ScheduTaskExtend>> extendDict = extendList.stream()
            .collect(Collectors.groupingBy(ScheduTaskExtend::getTaskId));
        // 按照水业务和气业务分类存储到两个数组中
        List<CreateWaterOrderParam> waterTaskInfos = new ArrayList<>();
        List<CreateAirOrderParam> gasTaskInfos = new ArrayList<>();
        // 过滤下无法匹配到计划的任务
        log.info("过滤前任务数：{}", taskInfos.size());
        taskInfos = taskInfos.stream().filter(taskInfo -> planInfoDict.containsKey(taskInfo.getId()))
            .collect(Collectors.toList());
        log.info("过滤后任务数：{}", taskInfos.size());
        if (taskInfos.isEmpty()) {
            log.info("无需要推送的任务");
            return 0;
        }
        return mockPushToActivity(taskInfos, planInfos, operationUser);
        // for (ScheduTaskInfoVO taskInfo : taskInfos) {
        // if (taskInfo.getBusinessType().equals(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE)) {
        // // 拼接参数
        // }
        // else if (taskInfo.getBusinessType().equals(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE)) {
        // // 拼接参数
        // }
        // }
        // // 推送任务信息到活动侧
        // List<CreateOrderRespVo> respVoList = new ArrayList<>();
        // List<String> taskCodeArr = new ArrayList<>();
        // // 水业务任务
        // if (!waterTaskInfos.isEmpty()) {
        // log.info("水业务任务推送参数：{}", waterTaskInfos);
        // R<List<CreateOrderRespVo>> waterRespVo = remoteOrderService
        // .createWaterRoutineInspectionOrder(waterTaskInfos);
        // log.info("水业务任务推送结果：{}", waterRespVo);
        // if (waterRespVo.getCode() == R.SUCCESS) {
        // respVoList.addAll(waterRespVo.getData());
        // taskCodeArr.addAll(
        // waterTaskInfos.stream().map(CreateWaterOrderParam::getTaskCode).collect(Collectors.toList()));
        // }
        // }
        // // 气业务任务
        // if (!gasTaskInfos.isEmpty()) {
        // log.info("气业务任务推送参数：{}", gasTaskInfos);
        // R<List<CreateOrderRespVo>> gasRespVo = remoteOrderService.createAirRoutineInspectionOrder(gasTaskInfos);
        // log.info("气业务任务推送结果：{}", gasRespVo);
        // if (gasRespVo.getCode() == R.SUCCESS) {
        // respVoList.addAll(gasRespVo.getData());
        // taskCodeArr
        // .addAll(gasTaskInfos.stream().map(CreateAirOrderParam::getTaskCode).collect(Collectors.toList()));
        // }
        // }
        // // 处理返回结果
        // if (respVoList.isEmpty()) {
        // log.info("推送任务到活动侧失败，未查询到返回结果");
        // return 0;
        // }
        // // 更新任务表的状态
        // List<ScheduTaskInfo> scheduTaskInfoList = new ArrayList<>();
        // taskCodeArr.forEach(taskCode -> {
        // ScheduTaskInfo scheduTaskInfo = new ScheduTaskInfo();
        // scheduTaskInfo.setTaskCode(taskCode);
        // scheduTaskInfo.setTaskStatus(TaskConstant.TASK_STATUS_PUSHED);
        // scheduTaskInfo.setUpdateBy(operationUser);
        // scheduTaskInfoList.add(scheduTaskInfo);
        // });
        // int updateTaskStatusCount = scheduTaskInfoMapper.batchUpdateScheduTaskInfoByTaskCode(scheduTaskInfoList);
        // // 更新计划的工单号
        // List<ScheduPlanInfo> scheduPlanInfoLis = new ArrayList<>();
        // respVoList.forEach(respVo -> {
        // respVo.getPlanList().forEach(plan -> {
        // ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
        // scheduPlanInfo.setPlanCode(plan.getPlanCode());
        // scheduPlanInfo.setExecuteStatus(PlanConstant.EXEC_STATUS_RUNNING);
        // scheduPlanInfo.setOrderNo(respVo.getOrderNo());
        // scheduPlanInfo.setUpdateBy(operationUser);
        // scheduPlanInfoLis.add(scheduPlanInfo);
        // });
        // });
        // int updatePlanStatusCount = scheduPlanInfoMapper.batchUpdateScheduPlanInfoByPlanCode(scheduPlanInfoLis);
        // log.info("推送任务到活动侧成功，更新任务状态数量：{}, 更新计划状态数量：{}", updateTaskStatusCount, updatePlanStatusCount);
        // return updateTaskStatusCount;
    }

    private int mockPushToActivity(List<ScheduTaskInfoVO> taskInfos, List<ScheduPlanInfoVO> planInfos,
        String operationUser) {
        // 更新任务表的状态
        List<ScheduTaskInfo> scheduTaskInfoList = new ArrayList<>();
        taskInfos.forEach(taskInfo -> {
            ScheduTaskInfo scheduTaskInfo = new ScheduTaskInfo();
            scheduTaskInfo.setTaskCode(taskInfo.getTaskCode());
            scheduTaskInfo.setTaskStatus(TaskConstant.TASK_STATUS_PUSHED);
            scheduTaskInfo.setUpdateBy(operationUser);
            scheduTaskInfoList.add(scheduTaskInfo);
        });
        int updateTaskStatusCount = scheduTaskInfoMapper.batchUpdateScheduTaskInfoByTaskCode(scheduTaskInfoList);
        // TODO, 任务状态更新以后，需要保存一些历史数据
        // 更新计划的工单号
        List<ScheduPlanInfo> scheduPlanInfoLis = new ArrayList<>();
        planInfos.forEach(planInfo -> {
            ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
            scheduPlanInfo.setPlanCode(planInfo.getPlanCode());
            scheduPlanInfo.setExecuteStatus(PlanConstant.EXEC_STATUS_RUNNING);
            scheduPlanInfo.setOrderNo("ACH" + UUID.randomUUID().toString().replace("-", ""));
            scheduPlanInfo.setUpdateBy(operationUser);
            scheduPlanInfoLis.add(scheduPlanInfo);
        });
        int updatePlanStatusCount = scheduPlanInfoMapper.batchUpdateScheduPlanInfoByPlanCode(scheduPlanInfoLis);
        // TODO, 计划状态更新以后，需要保存一些历史数据
        log.info("推送任务到活动侧成功，更新任务状态数量：{}, 更新计划状态数量：{}", updateTaskStatusCount, updatePlanStatusCount);
        return updateTaskStatusCount;
    }
}
