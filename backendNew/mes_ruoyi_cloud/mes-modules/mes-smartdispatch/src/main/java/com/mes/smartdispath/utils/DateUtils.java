package com.mes.smartdispath.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间工具类
 * 
 * @Author: li.haoyang @Date： 2025/7/25
 */
public final class DateUtils {
    /**
     * 获取当前日期，格式：yyyyMMdd
     *
     * @return
     */
    public static String getCurDateWithoutHyphen() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("YYYYMMdd"));
    }

    /**
     * 获取当前日期时间，格式：yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getCurFormatDateTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss"));
    }

    public static Date getCurDateTime() {
        LocalDateTime now = LocalDateTime.now();
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }
}
