package com.mes.smartdispath.enums;

import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.SysDictConstant;

/**
 * 业务分类前缀枚举
 * 
 * @Author: li.haoyang @Date： 2025/7/25
 */
public enum BusinessTypeShortPrefixEnum {
    WATER(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE, CommonConstant.BUSINESS_TYPE_SHORT_PREFIX_WATER), AIR(
        SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE, CommonConstant.BUSINESS_TYPE_SHORT_PREFIX_AIR);

    private final String type;

    private final String shortPrefix;

    BusinessTypeShortPrefixEnum(String type, String shortPrefix) {
        this.type = type;
        this.shortPrefix = shortPrefix;
    }

    public String getType() {
        return type;
    }

    public String getShortPrefix() {
        return shortPrefix;
    }

    // 根据type获取shortPrefix
    public static String getShortPrefixByType(String type) {
        for (BusinessTypeShortPrefixEnum e : values()) {
            if (e.getType().equalsIgnoreCase(type)) {
                return e.getShortPrefix();
            }
        }
        return "";
    }
}
