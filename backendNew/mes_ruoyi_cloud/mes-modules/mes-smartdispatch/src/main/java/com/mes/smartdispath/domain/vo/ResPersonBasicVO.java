package com.mes.smartdispath.domain.vo;

import com.mes.smartdispath.domain.ResPersonBasic;

/**
 * 人员信息VO
 *
 * @Author: li.haoyang @Date： 2025/7/28
 */
public class ResPersonBasicVO extends ResPersonBasic {
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 人员状态
     */
    private String statusName;

    /**
     * 证书名称
     */
    private String certificateName;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }
}
