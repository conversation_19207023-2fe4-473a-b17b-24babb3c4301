package com.mes.smartdispath.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.mes.common.core.constant.ServiceNameConstants;
import com.mes.common.core.domain.R;
import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.openApi.AddTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.UpdatePlanExtendApiDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.feign.factory.RemoteSmartdispatchInnerFallbackFactory;

@FeignClient(contextId = "remoteSmartdispatchInnerService", value = ServiceNameConstants.SMARTDISPATCH,
    fallbackFactory = RemoteSmartdispatchInnerFallbackFactory.class)
public interface RemoteSmartdispatchInnerService {
    @GetMapping("/openapi/getPlanInfoList")
    R<List<ScheduPlanInfoVO>> getPlanInfoList(ScheduPlanInfoQueryDTO queryDto);

    @PostMapping("/openapi/addTaskInfo")
    R<Boolean> addTaskInfo(@RequestBody AddTaskInfoOpenApiDTO dto);

    @PostMapping("/openapi/batchUpdatePlanExtend")
    R<Boolean> batchUpdatePlanExtend(@RequestBody UpdatePlanExtendApiDTO dto);

    @GetMapping("/openapi/getAlgorithmInfoList")
    R<List<ScheduAlgorithmInfo>> getAlgorithmInfoList();

    @GetMapping("/openapi/getAlgruleConfigList")
    R<List<ScheduAlgruleConfigVO>> getAlgruleConfigList();

    @PostMapping("/openapi/batchAddAlgorithmInvokeRecord")
    R<Boolean> batchAddAlgorithmInvokeRecord(@RequestBody List<ScheduAlgorithmInvokeRecord> dto);
}
