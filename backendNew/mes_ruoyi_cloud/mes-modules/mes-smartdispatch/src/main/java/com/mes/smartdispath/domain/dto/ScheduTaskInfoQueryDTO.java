package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduTaskInfo;

/**
 * 任务信息查询DTO
 *
 * @Author: li.haoyang @Date： 2025/7/16
 */
public class ScheduTaskInfoQueryDTO extends ScheduTaskInfo {
    /**
     * id数组
     */
    private List<String> idArr;

    /**
     * 业务类型字典编码
     */
    private String businessTypeDictClassCode;

    /**
     * 开始调度时间
     */
    private String startDispatchedTime;

    /**
     * 结束调度时间
     */
    private String endDispatchedTime;

    /**
     * 任务状态数组
     */
    private List<String> taskStatusArr;

    /**
     * 审核状态数组
     */
    private List<String> approvalStatusArr;

    /**
     * 开始时间（根据任务的开始时间过滤参数）
     */
    private String startTaskTime;

    /**
     * 结束时间（根据任务的开始时间过滤参数）
     */
    private String endTaskTime;

    public ScheduTaskInfoQueryDTO() {
        this.businessTypeDictClassCode = SysDictConstant.BUSINESS_TYPE_CLASS_CODE;
    }

    public List<String> getIdArr() {
        return idArr;
    }

    public void setIdArr(List<String> idArr) {
        this.idArr = idArr;
    }

    public String getBusinessTypeDictClassCode() {
        return businessTypeDictClassCode;
    }

    public void setBusinessTypeDictClassCode(String businessTypeDictClassCode) {
        this.businessTypeDictClassCode = businessTypeDictClassCode;
    }

    public String getStartDispatchedTime() {
        return startDispatchedTime;
    }

    public void setStartDispatchedTime(String startDispatchedTime) {
        this.startDispatchedTime = startDispatchedTime;
    }

    public String getEndDispatchedTime() {
        return endDispatchedTime;
    }

    public void setEndDispatchedTime(String endDispatchedTime) {
        this.endDispatchedTime = endDispatchedTime;
    }

    public List<String> getTaskStatusArr() {
        return taskStatusArr;
    }

    public void setTaskStatusArr(List<String> taskStatusArr) {
        this.taskStatusArr = taskStatusArr;
    }

    public List<String> getApprovalStatusArr() {
        return approvalStatusArr;
    }

    public void setApprovalStatusArr(List<String> approvalStatusArr) {
        this.approvalStatusArr = approvalStatusArr;
    }

    public String getStartTaskTime() {
        return startTaskTime;
    }

    public void setStartTaskTime(String startTaskTime) {
        this.startTaskTime = startTaskTime;
    }

    public String getEndTaskTime() {
        return endTaskTime;
    }

    public void setEndTaskTime(String endTaskTime) {
        this.endTaskTime = endTaskTime;
    }
}
