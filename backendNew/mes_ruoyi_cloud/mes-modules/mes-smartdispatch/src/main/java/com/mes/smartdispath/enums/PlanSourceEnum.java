package com.mes.smartdispath.enums;

/**
 * @Author: li.haoyang
 * @Description：计划来源枚举 @Date： 2025/7/21
 */
public enum PlanSourceEnum {
    RULE_GENERATION("1", "规则生成"), MONITORING_WARNING("2", "监测预警"), DATA_AUDIT("3", "数据审核"), RESOURCE_MANAGEMENT("4",
        "资源管理"), QUALITY_MANAGEMENT("5", "质量管理"), MONITORING_ACTIVITY("6", "监测活动"), MANUAL_REPORT("7", "人工填报");

    private final String code;

    private final String chineseName;

    PlanSourceEnum(String code, String chineseName) {
        this.code = code;
        this.chineseName = chineseName;
    }

    public String getCode() {
        return code;
    }

    public String getChineseName() {
        return chineseName;
    }

    /**
     * 根据枚举名获取中文名称
     */
    public static String toChinese(String name) {
        for (PlanSourceEnum source : PlanSourceEnum.values()) {
            if (source.name().equalsIgnoreCase(name)) {
                return source.getChineseName();
            }
        }
        return null;
    }

    /**
     * 根据code获取中文名称
     */
    public static String toChineseByCode(String code) {
        for (PlanSourceEnum source : PlanSourceEnum.values()) {
            if (source.getCode().equals(code)) {
                return source.getChineseName();
            }
        }
        return null;
    }
}
