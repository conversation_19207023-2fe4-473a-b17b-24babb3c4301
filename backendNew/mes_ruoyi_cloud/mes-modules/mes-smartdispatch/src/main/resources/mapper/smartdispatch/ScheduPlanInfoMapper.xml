<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduPlanInfoMapper">

    <resultMap type="com.mes.smartdispath.domain.ScheduPlanInfo" id="ScheduPlanInfoResult">
        <result property="id" column="id"/>
        <result property="planCode" column="plan_code"/>
        <result property="planName" column="plan_name"/>
        <result property="scheduStatus" column="schedu_status"/>
        <result property="executeStatus" column="execute_status"/>
        <result property="planSource" column="plan_source"/>
        <result property="sourceId" column="source_id"/>
        <result property="planPriority" column="plan_priority"/>
        <result property="provinceName" column="province_name"/>
        <result property="provinceCode" column="province_code"/>
        <result property="cityName" column="city_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="businessType" column="business_type"/>
        <result property="siteType" column="site_type"/>
        <result property="siteName" column="site_name"/>
        <result property="siteId" column="site_id"/>
        <result property="activityType" column="activity_type"/>
        <result property="activitySubtype" column="activity_subtype"/>
        <result property="isCompanion" column="is_companion"/>
        <result property="mainPlanId" column="main_plan_id"/>
        <result property="planStartTime" column="plan_start_time"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="remark" column="remark"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="devcModel" column="devc_model"/>
        <result property="orderNo" column="order_no"/>
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.ScheduPlanInfoVO" id="PlanInfoVOResult"
               extends="ScheduPlanInfoResult">
        <result property="siteTypeName" column="site_type_name"/>
        <result property="hasAutomaticStation" column="has_automatic_station"/>
        <result property="operationUnit" column="operation_unit"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="activityTypeName" column="activity_type_name"/>
        <result property="activitySubtypeName" column="activity_subtype_name"/>
        <result property="approvalOpinion" column="approval_opinion"/>
        <result property="approver" column="approver"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="taskId" column="task_id"/>
        <result property="isImportant" column="is_important"/>
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.PlanTypeStaticVO" id="PlanTypeStaticResult">
        <result property="activityType" column="activity_type"/>
        <result property="activityTypeName" column="activity_type_name"/>
        <result property="totalCount" column="total_count"/>
        <result property="completeCount" column="complete_count"/>
        <result property="exceptionCount" column="exception_count"/>
        <result property="curMonthCompleteCount" column="cur_month_complete_count"/>
        <result property="lastMonthCompleteCount" column="last_month_complete_count"/>
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.CompletePlanStaticVO" id="QuantityTargetCompleteVOResult">
        <result property="siteId" column="site_id"/>
        <result property="provinceCode" column="province_code"/>
        <result property="siteType" column="site_type"/>
        <result property="businessType" column="business_type"/>
        <result property="hasAutomaticStation" column="has_automatic_station"/>
        <result property="activityType" column="activity_type"/>
        <result property="activitySubtype" column="activity_subtype"/>
        <result property="completeCount" column="complete_count"/>
        <result property="year" column="year"/>
    </resultMap>

    <resultMap id="PlanSourceStaticResult" type="com.mes.smartdispath.domain.vo.PlanSourceStaticVO">
        <result property="planSource" column="plan_source"/>
        <result property="planCount" column="plan_count"/>
    </resultMap>

    <sql id="selectScheduPlanInfoVo">
        select id,
               plan_code,
               plan_name,
               schedu_status,
               execute_status,
               plan_source,
               source_id,
               plan_priority,
               province_name,
               province_code,
               city_name,
               city_code,
               business_type,
               site_type,
               site_name,
               site_id,
               activity_type,
               activity_subtype,
               is_companion,
               main_plan_id,
               plan_start_time,
               plan_end_time,
               remark,
               approval_status,
               create_time,
               create_by,
               update_time,
               update_by,
               status,
               tenant_id,
               devc_model,
               order_no
        from tb_schedu_plan_info
    </sql>

    <select id="selectCompletePlanCountByTime" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="QuantityTargetCompleteVOResult">
        SELECT business_type,
            COUNT(*) AS complete_count
            FROM tb_schedu_plan_info
        WHERE status = 'A' AND execute_status = '5'
          and plan_start_time >= #{startTime}
          and plan_start_time <![CDATA[<=]]> #{endTime}
        GROUP BY business_type
    </select>

    <select id="selectPlanSourceStatic" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="PlanSourceStaticResult">
        SELECT plan_source, COUNT(*) AS plan_count
        FROM tb_schedu_plan_info
        WHERE status = 'A'
            <if test="businessType != null  and businessType != ''">and business_type = #{businessType}</if>
            <if test="startTime != null  and startTime != ''">and plan_start_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''">and plan_start_time <![CDATA[<=]]> #{endTime}</if>
        GROUP BY plan_source
    </select>

    <select id="selectTaskLinkPlanInfoList" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="PlanInfoVOResult">
        SELECT a.id, a.plan_code, a.plan_name, a.schedu_status, a.execute_status,
        a.plan_source, a.source_id, a.plan_priority, a.province_code, a.province_name,
        a.city_code, a.city_name, a.site_name, a.site_id, a.site_type,
        d.dict_value AS site_type_name,
        a.business_type,
        e.dict_value AS business_type_name,
        a.activity_type,
        f.activity_type_name AS activity_type_name,
        a.activity_subtype,
        f.activity_subtype_name AS activity_subtype_name,
        a.is_companion, a.main_plan_id, a.plan_start_time, a.plan_end_time, a.remark,
        a.approval_status, a.devc_model,
        a.create_by, a.create_time,
        a.update_by, a.update_time,
        b.has_automatic_station,
        b.operation_unit,
        c.task_id
        FROM tb_schedu_plan_info a
        left join tb_res_site b on a.site_id = b.id
        left join tb_schedu_task_plan_rel c on a.id = c.plan_id and c.status = 'A'
        left join tb_sys_dict d on d.class_code = #{siteTypeDictClassCode} AND d.state = 'A' AND
        d.dict_code = a.site_type AND d.parent_dict_code = a.business_type
        left join tb_sys_dict e on e.class_code = #{businessTypeDictClassCode} AND e.state = 'A' AND
        e.dict_code = a.business_type
        left join tb_sys_dict f on f.activity_type_code = a.activity_type and f.activity_subtype_code =
        a.activity_subtype
        WHERE a.status = 'A'
            and c.task_id in
                <foreach collection="taskIdArr" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
    </select>

    <select id="selectPlanCompleteCount" resultMap="PlanTypeStaticResult">
        SELECT
            activity_type,
            COUNT(CASE
                WHEN plan_start_time >= TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM')
                    AND plan_start_time <![CDATA[<=]]> TRUNC(SYSDATE, 'MM')
                THEN 1
                END) AS last_month_complete_count,
            COUNT(CASE
                WHEN plan_start_time >= TRUNC(SYSDATE, 'MM')
                    AND plan_start_time <![CDATA[<=]]> TRUNC(ADD_MONTHS(SYSDATE, 1), 'MM')
                THEN 1
                END) AS cur_month_complete_count
        FROM tb_schedu_plan_info
        WHERE activity_type IN
            <foreach collection="activityTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND business_type = #{businessType}
            AND execute_status = '5'
            AND status = 'A'
            AND plan_start_time >= TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM')
            AND plan_start_time <![CDATA[<=]]> TRUNC(ADD_MONTHS(SYSDATE, 1), 'MM')
            AND is_companion = 0
        GROUP BY activity_type
    </select>

    <select id="selectSiteCompletePlanStatic"
            parameterType="com.mes.smartdispath.domain.dto.QuantityTargetCompleteQueryDTO"
            resultMap="QuantityTargetCompleteVOResult">
        SELECT site_id, activity_type, activity_subtype,
            EXTRACT(YEAR FROM plan_start_time) AS year,
            COUNT(*) AS complete_count
            FROM tb_schedu_plan_info
        WHERE status = 'A'
            AND execute_status = '5'
            AND site_id in
            <foreach collection="siteIdArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND activity_type in
            <foreach collection="activityTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND activity_subtype in
            <foreach collection="activitySubtypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND EXTRACT(YEAR FROM plan_start_time) in
            <foreach collection="statYearArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY site_id, activity_type, activity_subtype,
        EXTRACT(YEAR FROM plan_start_time)
    </select>

    <select id="selectNationwideCollectCompletePlanStatic"
            parameterType="com.mes.smartdispath.domain.dto.QuantityTargetCompleteQueryDTO"
            resultMap="QuantityTargetCompleteVOResult">
        SELECT
        EXTRACT(YEAR FROM t1.plan_start_time) AS year,
        t1.site_type,
        t1.business_type,
        t1.activity_type,
        t1.activity_subtype,
        t2.has_automatic_station,
        COUNT(*) AS complete_count
        FROM tb_schedu_plan_info t1
        LEFT JOIN tb_res_site t2 ON t1.site_id = t2.id
        WHERE t1.status = 'A'
        AND t1.execute_status = '5'
        AND t1.is_companion = 0
        <if test="null != siteTypeArr">
            AND t1.site_type in
            <foreach collection="siteTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != activityTypeArr">
            AND t1.activity_type in
            <foreach collection="activityTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != activitySubtypeArr">
            AND t1.activity_subtype in
            <foreach collection="activitySubtypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != statYearArr">
            AND EXTRACT(YEAR FROM t1.plan_start_time) in
            <foreach collection="statYearArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY EXTRACT(YEAR FROM t1.plan_start_time),
        t1.site_type,
        t1.business_type,
        t1.activity_type,
        t1.activity_subtype,
        t2.has_automatic_station
    </select>

    <select id="selectProvinceCollectCompletePlanStatic"
            parameterType="com.mes.smartdispath.domain.dto.QuantityTargetCompleteQueryDTO"
            resultMap="QuantityTargetCompleteVOResult">
        SELECT
            EXTRACT(YEAR FROM t1.plan_start_time) AS year,
            t1.province_code,
            t1.site_type,
            t1.business_type,
            t1.activity_type,
            t1.activity_subtype,
            t2.has_automatic_station,
            COUNT(*) AS complete_count
        FROM tb_schedu_plan_info t1
        LEFT JOIN tb_res_site t2 ON t1.site_id = t2.id
        WHERE t1.status = 'A'
        AND t1.execute_status = '5'
        AND t1.is_companion = 0
        <if test="null != provinceCodeArr">
            AND t1.province_code in
            <foreach collection="provinceCodeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != siteTypeArr">
            AND t1.site_type in
            <foreach collection="siteTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != activityTypeArr">
            AND t1.activity_type in
            <foreach collection="activityTypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != activitySubtypeArr">
            AND t1.activity_subtype in
            <foreach collection="activitySubtypeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != statYearArr">
            AND EXTRACT(YEAR FROM t1.plan_start_time) in
            <foreach collection="statYearArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY EXTRACT(YEAR FROM t1.plan_start_time),
            t1.province_code,
            t1.site_type,
            t1.business_type,
            t1.activity_type,
            t1.activity_subtype,
            t2.has_automatic_station
    </select>


    <select id="selectPlanTypeStatic" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO"
            resultMap="PlanTypeStaticResult">
        SELECT
        c.activity_type,
        d.activity_type_name,
        COUNT(DISTINCT c.id) AS total_count,
        COUNT(DISTINCT CASE WHEN c.execute_status = 5 THEN c.id END) AS complete_count,
        COUNT(DISTINCT CASE WHEN c.execute_status IN (6,7) THEN c.id END) AS exception_count
        FROM
        tb_schedu_plan_info c
        INNER JOIN tb_schedu_task_plan_rel a
        ON c.id = a.plan_id
        INNER JOIN tb_schedu_task_info b
        ON a.task_id = b.id
        LEFT JOIN tb_schedu_task_extend e
        ON b.id = e.task_id
        LEFT JOIN tb_schedu_monitor_activity_info d
        ON c.activity_type = d.activity_type_code
        <where>
            <if test="businessType != null  and businessType != ''">and c.business_type = #{businessType}</if>
            <if test="startDate != null  and startDate != ''">and b.start_time >= #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and b.start_time <![CDATA[<=]]>  #{endDate}</if>
            <if test="province != null  and province != ''">and c.province_code = #{province}</if>
            <if test="city != null  and city != ''">and c.city_code = #{city}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''">and e.maintain_unit_code =
                #{maintainUnitCode}
            </if>
        </where>
        GROUP BY
        c.activity_type,
        d.activity_type_name
        ORDER BY
        total_count DESC
    </select>

    <select id="selectScheduPlanInfoList" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="PlanInfoVOResult">
        SELECT a.id, a.plan_code, a.plan_name, a.schedu_status, a.execute_status,
        a.plan_source, a.source_id, a.plan_priority, a.province_code, a.province_name,
        a.city_code, a.city_name, a.site_name, a.site_id, a.site_type,
        c.dict_value AS site_type_name,
        a.business_type,
        d.dict_value AS business_type_name,
        a.activity_type,
        e.activity_type_name AS activity_type_name,
        a.activity_subtype,
        e.activity_subtype_name AS activity_subtype_name,
        a.is_companion, a.main_plan_id, a.plan_start_time, a.plan_end_time, a.remark,
        a.approval_status, a.devc_model,
        a.create_by, a.create_time,
        a.update_by, a.update_time,
        b.has_automatic_station,
        b.operation_unit,
        e.is_important
        FROM tb_schedu_plan_info a
        left join tb_res_site b on a.site_id = b.id
        left join tb_sys_dict c on c.class_code = #{siteTypeDictClassCode} AND c.state = 'A' AND c.dict_code = a.site_type AND c.parent_dict_code = a.business_type
        left join tb_sys_dict d on d.class_code = #{businessTypeDictClassCode} AND d.state = 'A' AND d.dict_code = a.business_type
        left join tb_schedu_monitor_activity_info e on e.activity_type_code = a.activity_type AND e.activity_subtype_code = a.activity_subtype
        WHERE a.status = 'A'
        <if test="planCode != null  and planCode != ''">and a.plan_code = #{planCode}</if>
        <if test="planName != null  and planName != ''">and a.plan_name like concat('%', #{planName}, '%')</if>
        <if test="scheduStatus != null  and scheduStatus != '' and null == scheduStatusArr">and a.schedu_status = #{scheduStatus}</if>
        <if test="scheduStatusArr != null  and scheduStatusArr != ''">
          and a.schedu_status in
            <foreach collection="scheduStatusArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="executeStatus != null  and executeStatus != ''">and a.execute_status = #{executeStatus}</if>
        <if test="planSource != null  and planSource != ''">and a.plan_source = #{planSource}</if>
        <if test="sourceId != null  and sourceId != ''">and a.source_id = #{sourceId}</if>
        <if test="planPriority != null  and planPriority != ''">and a.plan_priority = #{planPriority}</if>
        <if test="provinceName != null  and provinceName != ''">and a.province_name like concat('%', #{provinceName}, '%')</if>
        <if test="provinceCode != null  and provinceCode != ''">and a.province_code = #{provinceCode}</if>
        <if test="cityName != null  and cityName != ''">and a.city_name like concat('%', #{cityName}, '%')</if>
        <if test="cityCode != null  and cityCode != '' and null == cityCodeArr">and a.city_code = #{cityCode}</if>
        <if test="cityCodeArr != null  and cityCodeArr != ''">
            and a.city_code in
            <foreach collection="cityCodeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType != null  and businessType != ''">and a.business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''">and a.site_type = #{siteType}</if>
        <if test="siteName != null  and siteName != ''">and a.site_name like concat('%', #{siteName}, '%')</if>
        <if test="siteId != null  and siteId != '' and null == siteIdArr">and a.site_id = #{siteId}</if>
        <if test="siteIdArr != null  and siteIdArr != ''">
          and a.site_id in
            <foreach collection="siteIdArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activityType != null  and activityType != ''">and a.activity_type = #{activityType}</if>
        <if test="activitySubtype != null  and activitySubtype != ''">and a.activity_subtype = #{activitySubtype}</if>
        <if test="isCompanion != null  and isCompanion != ''">and a.is_companion = #{isCompanion}</if>
        <if test="mainPlanId != null  and mainPlanId != ''">and a.main_plan_id = #{mainPlanId}</if>
        <if test="planStartTime != null  and planStartTime != ''">and a.plan_start_time = #{planStartTime}</if>
        <if test="planEndTime != null  and planEndTime != ''">and a.plan_end_time = #{planEndTime}</if>
        <if test="approvalStatus != null  and approvalStatus != ''">and a.approval_status = #{approvalStatus}</if>
        <if test="tenantId != null  and tenantId != ''">and a.tenant_id = #{tenantId}</if>
        <if test="devcModel != null  and devcModel != ''">and a.devc_model = #{devcModel}</if>
        <if test="orderNo != null  and orderNo != ''">and a.order_no = #{orderNo}</if>
        <if test="startTime != null  and startTime != ''">and a.plan_start_time >= #{startTime}</if>
        <if test="endTime != null  and endTime != ''">and a.plan_start_time <![CDATA[<=]]> #{endTime}</if>
        ORDER by a.create_time desc, a.province_code, a.city_code, a.site_type, a.site_id, a.activity_type, a.activity_subtype
    </select>

    <select id="selectApprovalScheduPlanInfoList" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="PlanInfoVOResult">
        SELECT a.id, a.plan_code, a.plan_name, a.schedu_status, a.execute_status,
        a.plan_source, a.source_id, a.plan_priority, a.province_code, a.province_name,
        a.city_code, a.city_name, a.site_name, a.site_id, a.site_type,
        ( SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{siteTypeDictClassCode} AND b.state = 'A' AND
        b.dict_code = a.site_type AND b.parent_dict_code = a.business_type) AS site_type_name,
        a.business_type,
        ( SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND
        b.dict_code = a.business_type) AS business_type_name,
        a.activity_type,
        ( SELECT b.activity_type_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_type_code =
        a.activity_type LIMIT 1) AS activity_type_name,
        a.activity_subtype,
        ( SELECT b.activity_subtype_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_subtype_code =
        a.activity_subtype LIMIT 1) AS activity_subtype_name,
        a.is_companion, a.main_plan_id, a.plan_start_time, a.plan_end_time, a.remark,
        a.approval_status, a.devc_model,
        a.create_by, a.create_time,
        a.update_by, a.update_time,
        b.has_automatic_station
        FROM tb_schedu_plan_info a
        left join tb_res_site b on a.site_id = b.id
        WHERE (
        a.plan_source = '7'
        OR
        (a.plan_source = '2' AND a.plan_priority = '4')
        )
        and a.is_companion = 0
        and a.status = 'A'
        <if test="planCode != null  and planCode != ''">and a.plan_code = #{planCode}</if>
        <if test="planName != null  and planName != ''">and a.plan_name like concat('%', #{planName}, '%')</if>
        <if test="scheduStatus != null  and scheduStatus != ''">and a.schedu_status = #{scheduStatus}</if>
        <if test="executeStatus != null  and executeStatus != ''">and a.execute_status = #{executeStatus}</if>
        <if test="planSource != null  and planSource != ''">and a.plan_source = #{planSource}</if>
        <if test="sourceId != null  and sourceId != ''">and a.source_id = #{sourceId}</if>
        <if test="planPriority != null  and planPriority != ''">and a.plan_priority = #{planPriority}</if>
        <if test="provinceName != null  and provinceName != ''">and a.province_name like concat('%', #{provinceName}, '%')</if>
        <if test="provinceCode != null  and provinceCode != ''">and a.province_code = #{provinceCode}</if>
        <if test="cityName != null  and cityName != ''">and a.city_name like concat('%', #{cityName}, '%')</if>
        <if test="cityCode != null  and cityCode != '' and null == cityCodeArr">and a.city_code = #{cityCode}</if>
        <if test="cityCodeArr != null  and cityCodeArr != ''">
            and a.city_code in
            <foreach collection="cityCodeArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType != null  and businessType != ''">and a.business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''">and a.site_type = #{siteType}</if>
        <if test="siteName != null  and siteName != ''">and a.site_name like concat('%', #{siteName}, '%')</if>
        <if test="siteId != null  and siteId != '' and null == siteIdArr">and a.site_id = #{siteId}</if>
        <if test="siteIdArr != null  and siteIdArr != ''">
            and a.site_id in
            <foreach collection="siteIdArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activityType != null  and activityType != ''">and a.activity_type = #{activityType}</if>
        <if test="activitySubtype != null  and activitySubtype != ''">and a.activity_subtype = #{activitySubtype}</if>
        <if test="mainPlanId != null  and mainPlanId != ''">and a.main_plan_id = #{mainPlanId}</if>
        <if test="planStartTime != null  and planStartTime != ''">and a.plan_start_time = #{planStartTime}</if>
        <if test="planEndTime != null  and planEndTime != ''">and a.plan_end_time = #{planEndTime}</if>
        <if test="approvalStatus != null  and approvalStatus != ''">and a.approval_status = #{approvalStatus}</if>
        <if test="tenantId != null  and tenantId != ''">and a.tenant_id = #{tenantId}</if>
        <if test="devcModel != null  and devcModel != ''">and a.devc_model = #{devcModel}</if>
        <if test="orderNo != null  and orderNo != ''">and a.order_no = #{orderNo}</if>
        <if test="startTime != null  and startTime != ''">and a.plan_start_time >= #{startTime}</if>
        <if test="endTime != null  and endTime != ''">and a.plan_start_time <![CDATA[<=]]> #{endTime}</if>
    </select>

    <select id="selectScheduPlanInfoById" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO"
            resultMap="PlanInfoVOResult">
        SELECT a.id,
               a.plan_code,
               a.plan_name,
               a.schedu_status,
               a.execute_status,
               a.plan_source,
               a.source_id,
               a.plan_priority,
               a.province_code,
               a.province_name,
               a.city_code,
               a.city_name,
               a.site_name,
               a.site_id,
               a.site_type,
               (SELECT b.dict_value
                FROM tb_sys_dict b
                WHERE b.class_code = #{siteTypeDictClassCode}
                  AND b.state = 'A'
                  AND b.dict_code = a.site_type
                  AND b.parent_dict_code = a.business_type) AS site_type_name,
               a.business_type,
               (SELECT b.dict_value
                FROM tb_sys_dict b
                WHERE b.class_code = #{businessTypeDictClassCode}
                  AND b.state = 'A'
                  AND b.dict_code = a.business_type)        AS business_type_name,
               a.activity_type,
               (SELECT b.activity_type_name
                FROM tb_schedu_monitor_activity_info b
                WHERE b.activity_type_code = a.activity_type   LIMIT 1) AS activity_type_name,
               a.activity_subtype,
               ( SELECT b.activity_subtype_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_subtype_code = a.activity_subtype LIMIT 1) AS activity_subtype_name,
               a.is_companion, a.main_plan_id, a.plan_start_time, a.plan_end_time, a.remark,
               a.approval_status, a.devc_model,
               a.create_by, a.create_time,
               a.update_by, a.update_time,
               b.has_automatic_station
        FROM tb_schedu_plan_info a
            left join tb_res_site b
        on a.site_id = b.id
        where id = #{id}
    </select>

    <insert id="insertScheduPlanInfo" parameterType="com.mes.smartdispath.domain.ScheduPlanInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_plan_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="planCode != null and planCode != ''">plan_code,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="scheduStatus != null">schedu_status,</if>
            <if test="executeStatus != null">execute_status,</if>
            <if test="planSource != null and planSource != ''">plan_source,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="planPriority != null and planPriority != ''">plan_priority,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="cityName != null">city_name,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="businessType != null">business_type,</if>
            <if test="siteType != null">site_type,</if>
            <if test="siteName != null">site_name,</if>
            <if test="siteId != null">site_id,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="activitySubtype != null">activity_subtype,</if>
            <if test="isCompanion != null and isCompanion != ''">is_companion,</if>
            <if test="mainPlanId != null">main_plan_id,</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time,</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="devcModel != null">devc_model,</if>
            <if test="orderNo != null">order_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="planCode != null and planCode != ''">#{planCode},</if>
            <if test="planName != null and planName != ''">#{planName},</if>
            <if test="scheduStatus != null">#{scheduStatus},</if>
            <if test="executeStatus != null">#{executeStatus},</if>
            <if test="planSource != null and planSource != ''">#{planSource},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="planPriority != null and planPriority != ''">#{planPriority},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="siteType != null">#{siteType},</if>
            <if test="siteName != null">#{siteName},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="activitySubtype != null">#{activitySubtype},</if>
            <if test="isCompanion != null and isCompanion != ''">#{isCompanion},</if>
            <if test="mainPlanId != null">#{mainPlanId},</if>
            <if test="planStartTime != null and planStartTime != ''">#{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">#{planEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="devcModel != null">#{devcModel},</if>
            <if test="orderNo != null">#{orderNo},</if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduPlanInfo" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_plan_info (
        plan_code,
        plan_name,
        schedu_status,
        execute_status,
        plan_source,
        source_id,
        plan_priority,
        province_name,
        province_code,
        city_name,
        city_code,
        business_type,
        site_type,
        site_name,
        site_id,
        activity_type,
        activity_subtype,
        is_companion,
        main_plan_id,
        plan_start_time,
        plan_end_time,
        remark,
        approval_status,
        create_time,
        create_by,
        update_time,
        update_by,
        status,
        tenant_id,
        devc_model,
        order_no
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.planCode},
            #{item.planName},
            #{item.scheduStatus},
            #{item.executeStatus},
            #{item.planSource},
            #{item.sourceId},
            #{item.planPriority},
            #{item.provinceName},
            #{item.provinceCode},
            #{item.cityName},
            #{item.cityCode},
            #{item.businessType},
            #{item.siteType},
            #{item.siteName},
            #{item.siteId},
            #{item.activityType},
            #{item.activitySubtype},
            #{item.isCompanion},
            #{item.mainPlanId},
            #{item.planStartTime},
            #{item.planEndTime},
            #{item.remark},
            #{item.approvalStatus},
            #{item.createTime},
            #{item.createBy},
            #{item.updateTime},
            #{item.updateBy},
            #{item.status},
            #{item.tenantId},
            #{item.devcModel},
            #{item.orderNo}
            )
        </foreach>
    </insert>

    <update id="updateScheduPlanInfo" parameterType="com.mes.smartdispath.domain.ScheduPlanInfo">
        update tb_schedu_plan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="scheduStatus != null">schedu_status = #{scheduStatus},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="planSource != null and planSource != ''">plan_source = #{planSource},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="planPriority != null and planPriority != ''">plan_priority = #{planPriority},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="isCompanion != null and isCompanion != ''">is_companion = #{isCompanion},</if>
            <if test="mainPlanId != null">main_plan_id = #{mainPlanId},</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="devcModel != null">devc_model = #{devcModel},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateScheduPlanInfoByPlanCode" parameterType="com.mes.smartdispath.domain.ScheduPlanInfo">
        update tb_schedu_plan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="scheduStatus != null">schedu_status = #{scheduStatus},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="planSource != null and planSource != ''">plan_source = #{planSource},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="planPriority != null and planPriority != ''">plan_priority = #{planPriority},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="isCompanion != null and isCompanion != ''">is_companion = #{isCompanion},</if>
            <if test="mainPlanId != null">main_plan_id = #{mainPlanId},</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="devcModel != null">devc_model = #{devcModel},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            update_time = now(),
        </trim>
        where plan_code = #{planCode}
    </update>

    <update id="updateScheduPlanInfoByIdArr" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO">
        update tb_schedu_plan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="scheduStatus != null">schedu_status = #{scheduStatus},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="planSource != null and planSource != ''">plan_source = #{planSource},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="planPriority != null and planPriority != ''">plan_priority = #{planPriority},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="isCompanion != null and isCompanion != ''">is_companion = #{isCompanion},</if>
            <if test="mainPlanId != null">main_plan_id = #{mainPlanId},</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="devcModel != null">devc_model = #{devcModel},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            update_time = now(),
        </trim>
        where id in
        <foreach item="item" collection="idArr" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.planCode != null and item.planCode != ''">
                    plan_code = #{item.planCode},
                </if>
                <if test="item.planName != null and item.planName != ''">
                    plan_name = #{item.planName},
                </if>
                <if test="item.scheduStatus != null">
                    schedu_status = #{item.scheduStatus},
                </if>
                <if test="item.executeStatus != null">
                    execute_status = #{item.executeStatus},
                </if>
                <if test="item.planSource != null and item.planSource != ''">
                    plan_source = #{item.planSource},
                </if>
                <if test="item.sourceId != null">
                    source_id = #{item.sourceId},
                </if>
                <if test="item.planPriority != null and item.planPriority != ''">
                    plan_priority = #{item.planPriority},
                </if>
                <if test="item.provinceName != null">
                    province_name = #{item.provinceName},
                </if>
                <if test="item.provinceCode != null">
                    province_code = #{item.provinceCode},
                </if>
                <if test="item.cityName != null">
                    city_name = #{item.cityName},
                </if>
                <if test="item.cityCode != null">
                    city_code = #{item.cityCode},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.siteType != null">
                    site_type = #{item.siteType},
                </if>
                <if test="item.siteName != null">
                    site_name = #{item.siteName},
                </if>
                <if test="item.siteId != null">
                    site_id = #{item.siteId},
                </if>
                <if test="item.activityType != null">
                    activity_type = #{item.activityType},
                </if>
                <if test="item.activitySubtype != null">
                    activity_subtype = #{item.activitySubtype},
                </if>
                <if test="item.isCompanion != null and item.isCompanion != ''">
                    is_companion = #{item.isCompanion},
                </if>
                <if test="item.mainPlanId != null">
                    main_plan_id = #{item.mainPlanId},
                </if>
                <if test="item.planStartTime != null and item.planStartTime != ''">
                    plan_start_time = #{item.planStartTime},
                </if>
                <if test="item.planEndTime != null and item.planEndTime != ''">
                    plan_end_time = #{item.planEndTime},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark},
                </if>
                <if test="item.approvalStatus != null">
                    approval_status = #{item.approvalStatus},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.devcModel != null">
                    devc_model = #{item.devcModel},
                </if>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanInfoExecuteStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_info
            SET execute_status = #{item.executeStatus},
                update_time = #{item.updateTime},
                update_by = #{item.updateBy}
            where id = #{item.id}
        </foreach>
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanInfoByPlanCode" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.planName != null and item.planName != ''">
                    plan_name = #{item.planName},
                </if>
                <if test="item.scheduStatus != null">
                    schedu_status = #{item.scheduStatus},
                </if>
                <if test="item.executeStatus != null">
                    execute_status = #{item.executeStatus},
                </if>
                <if test="item.planSource != null and item.planSource != ''">
                    plan_source = #{item.planSource},
                </if>
                <if test="item.sourceId != null">
                    source_id = #{item.sourceId},
                </if>
                <if test="item.planPriority != null and item.planPriority != ''">
                    plan_priority = #{item.planPriority},
                </if>
                <if test="item.provinceName != null">
                    province_name = #{item.provinceName},
                </if>
                <if test="item.provinceCode != null">
                    province_code = #{item.provinceCode},
                </if>
                <if test="item.cityName != null">
                    city_name = #{item.cityName},
                </if>
                <if test="item.cityCode != null">
                    city_code = #{item.cityCode},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.siteType != null">
                    site_type = #{item.siteType},
                </if>
                <if test="item.siteName != null">
                    site_name = #{item.siteName},
                </if>
                <if test="item.siteId != null">
                    site_id = #{item.siteId},
                </if>
                <if test="item.activityType != null">
                    activity_type = #{item.activityType},
                </if>
                <if test="item.activitySubtype != null">
                    activity_subtype = #{item.activitySubtype},
                </if>
                <if test="item.isCompanion != null and item.isCompanion != ''">
                    is_companion = #{item.isCompanion},
                </if>
                <if test="item.mainPlanId != null">
                    main_plan_id = #{item.mainPlanId},
                </if>
                <if test="item.planStartTime != null and item.planStartTime != ''">
                    plan_start_time = #{item.planStartTime},
                </if>
                <if test="item.planEndTime != null and item.planEndTime != ''">
                    plan_end_time = #{item.planEndTime},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark},
                </if>
                <if test="item.approvalStatus != null">
                    approval_status = #{item.approvalStatus},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.devcModel != null">
                    devc_model = #{item.devcModel},
                </if>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo},
                </if>
                update_by = now(),
            </trim>
            where plan_code = #{item.planCode}
        </foreach>
    </update>

    <delete id="deleteScheduPlanInfoById" parameterType="String">
        delete
        from tb_schedu_plan_info
        where id = #{id}
    </delete>

    <delete id="deleteScheduPlanInfoByIds" parameterType="String">
        delete from tb_schedu_plan_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deletePlanInfoAndAssoPlan" parameterType="com.mes.smartdispath.domain.ScheduPlanInfo">
        update tb_schedu_plan_info
        SET status      = #{status},
            update_by   = #{updateBy},
            update_time = now()
        where id = #{id}
           or main_plan_id = #{id}
    </update>
</mapper>