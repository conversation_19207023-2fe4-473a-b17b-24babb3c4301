<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.resource.mapper.ResSiteSurroundingMapper">

    <resultMap type="com.mes.resource.domain.ResSiteSurrounding" id="ResSiteSurroundingResult">
        <result property="id" column="id"/>
        <result property="siteId" column="site_id"/>
        <result property="environmentType" column="environment_type"/>
        <result property="positionType" column="position_type"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="distance" column="distance"/>
        <result property="description" column="description"/>
        <result property="remarks" column="remarks"/>
        <result property="createdBy" column="created_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectResSiteSurroundingVo">
        select id,
               site_id,
               environment_type,
               position_type,
               longitude,
               latitude,
               distance,
               description,
               remarks,
               created_by,
               create_time,
               updated_by,
               update_time
        from tb_res_site_surrounding
    </sql>

    <select id="selectResSiteSurroundingList" parameterType="com.mes.resource.domain.ResSiteSurrounding"
            resultMap="ResSiteSurroundingResult">
        <include refid="selectResSiteSurroundingVo"/>
        <where>
            <if test="siteId != null  and siteId != ''">and site_id = #{siteId}</if>
            <if test="environmentType != null  and environmentType != ''">and environment_type = #{environmentType}</if>
            <if test="positionType != null  and positionType != ''">and position_type = #{positionType}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="distance != null  and distance != ''">and distance = #{distance}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
            <if test="createdBy != null  and createdBy != ''">and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''">and updated_by = #{updatedBy}</if>
        </where>
    </select>

    <select id="selectResSiteSurroundingById" parameterType="Long" resultMap="ResSiteSurroundingResult">
        <include refid="selectResSiteSurroundingVo"/>
        where id = #{id}
    </select>

    <insert id="insertResSiteSurrounding" parameterType="com.mes.resource.domain.ResSiteSurrounding" useGeneratedKeys="true" keyProperty="id">
        insert into tb_res_site_surrounding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="siteId != null and siteId != ''">site_id,</if>
            <if test="environmentType != null and environmentType != ''">environment_type,</if>
            <if test="positionType != null and positionType != ''">position_type,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="distance != null">distance,</if>
            <if test="description != null">description,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by,</if>
            <if test="updateTime != null ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="siteId != null and siteId != ''">#{siteId},</if>
            <if test="environmentType != null and environmentType != ''">#{environmentType},</if>
            <if test="positionType != null and positionType != ''">#{positionType},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="distance != null">#{distance},</if>
            <if test="description != null">#{description},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updatedBy != null and updatedBy != ''">#{updatedBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateResSiteSurrounding" parameterType="com.mes.resource.domain.ResSiteSurrounding">
        update tb_res_site_surrounding
        <trim prefix="SET" suffixOverrides=",">
            <if test="siteId != null and siteId != ''">site_id = #{siteId},</if>
            <if test="environmentType != null and environmentType != ''">environment_type = #{environmentType},</if>
            <if test="positionType != null and positionType != ''">position_type = #{positionType},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="distance != null">distance = #{distance},</if>
            <if test="description != null">description = #{description},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResSiteSurroundingById" parameterType="Long">
        delete
        from tb_res_site_surrounding
        where id = #{id}
    </delete>

    <delete id="deleteResSiteSurroundingByIds" parameterType="Long">
        delete from tb_res_site_surrounding where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findSiteSurroundingList" parameterType="String"
            resultType="com.mes.resource.domain.dto.SiteSurroundingDto">
        SELECT t1.id                                            as id,
               t1.site_id                                       as siteId,
               t1.environment_type                              as environmentType,
               t1.position_type                                 as positionType,
               t1.longitude                                     as longitude,
               t1.latitude                                      as latitude,
               t1.distance                                      as distance,
               t1.description                                   as description,
               t1.remarks                                       as remarks,
               t1.created_by                                    as createdBy,
               t2_et.dict_value                                 as environmentTypeName,
               t2_pt.dict_value                                 as positionTypeName,
               DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               DATE_FORMAT(t1.update_time, '%Y-%m-%d %H:%i:%s') as updateTime
        FROM tb_res_site_surrounding t1
                 LEFT JOIN tb_sys_dict t2_et
                           ON t2_et.class_code = 'environment_type' AND t2_et.dict_code = t1.environment_type
                 LEFT JOIN tb_sys_dict t2_pt
                           ON t2_pt.class_code = 'position_type' AND t2_pt.dict_code = t1.position_type
        WHERE site_id = #{siteId}
    </select>
</mapper>