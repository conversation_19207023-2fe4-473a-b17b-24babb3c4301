<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.resource.mapper.ResDeviceAntiInterferenceMapper">

    <resultMap type="com.mes.resource.domain.ResDeviceAntiInterference" id="ResDeviceAntiInterferenceResult">
            <result property="id" column="id"/>
            <result property="siteId" column="site_id"/>
            <result property="deviceId" column="device_id"/>
            <result property="testItem" column="test_item"/>
            <result property="antiType" column="anti_type"/>
            <result property="recordTime" column="record_time"/>
            <result property="startTime" column="start_time"/>
            <result property="endTime" column="end_time"/>
            <result property="limitValue" column="limit_value"/>
            <result property="pipelineLength" column="pipeline_length"/>
            <result property="systemPreprocess" column="system_preprocess"/>
            <result property="instrumentPreprocess" column="instrument_preprocess"/>
            <result property="waterTurbidityRange" column="water_turbidity_range"/>
            <result property="settlingTime" column="settling_time"/>
            <result property="hasTurbidityCompensation" column="has_turbidity_compensation"/>
            <result property="doublePumpWater" column="double_pump_water"/>
            <result property="samplerNormal" column="sampler_normal"/>
            <result property="createdBy" column="created_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updatedBy" column="updated_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectResDeviceAntiInterferenceVo">
        select id, site_id, device_id, test_item, anti_type, record_time, start_time, end_time, limit_value, pipeline_length, system_preprocess, instrument_preprocess, water_turbidity_range, settling_time, has_turbidity_compensation, double_pump_water, sampler_normal, created_by, create_time, updated_by, update_time
        from tb_res_device_anti_interference
    </sql>

    <select id="selectResDeviceAntiInterferenceList" parameterType="com.mes.resource.domain.ResDeviceAntiInterference"
            resultMap="ResDeviceAntiInterferenceResult">
        <include refid="selectResDeviceAntiInterferenceVo"/>
        <where>
                        <if test="siteId != null  and siteId != ''">
                            and site_id = #{siteId}
                        </if>
                        <if test="deviceId != null  and deviceId != ''">
                            and device_id = #{deviceId}
                        </if>
                        <if test="testItem != null  and testItem != ''">
                            and test_item = #{testItem}
                        </if>
                        <if test="antiType != null  and antiType != ''">
                            and anti_type = #{antiType}
                        </if>
                        <if test="recordTime != null  and recordTime != ''">
                            and record_time = #{recordTime}
                        </if>
                        <if test="startTime != null  and startTime != ''">
                            and start_time = #{startTime}
                        </if>
                        <if test="endTime != null  and endTime != ''">
                            and end_time = #{endTime}
                        </if>
                        <if test="limitValue != null  and limitValue != ''">
                            and limit_value = #{limitValue}
                        </if>
                        <if test="pipelineLength != null  and pipelineLength != ''">
                            and pipeline_length = #{pipelineLength}
                        </if>
                        <if test="systemPreprocess != null  and systemPreprocess != ''">
                            and system_preprocess = #{systemPreprocess}
                        </if>
                        <if test="instrumentPreprocess != null  and instrumentPreprocess != ''">
                            and instrument_preprocess = #{instrumentPreprocess}
                        </if>
                        <if test="waterTurbidityRange != null  and waterTurbidityRange != ''">
                            and water_turbidity_range = #{waterTurbidityRange}
                        </if>
                        <if test="settlingTime != null  and settlingTime != ''">
                            and settling_time = #{settlingTime}
                        </if>
                        <if test="hasTurbidityCompensation != null  and hasTurbidityCompensation != ''">
                            and has_turbidity_compensation = #{hasTurbidityCompensation}
                        </if>
                        <if test="doublePumpWater != null  and doublePumpWater != ''">
                            and double_pump_water = #{doublePumpWater}
                        </if>
                        <if test="samplerNormal != null  and samplerNormal != ''">
                            and sampler_normal = #{samplerNormal}
                        </if>
                        <if test="createdBy != null  and createdBy != ''">
                            and created_by = #{createdBy}
                        </if>
                        <if test="updatedBy != null  and updatedBy != ''">
                            and updated_by = #{updatedBy}
                        </if>
        </where>
    </select>

    <select id="selectResDeviceAntiInterferenceById" parameterType="String"
            resultMap="ResDeviceAntiInterferenceResult">
            <include refid="selectResDeviceAntiInterferenceVo"/>
            where id = #{id}
    </select>

    <insert id="insertResDeviceAntiInterference" parameterType="com.mes.resource.domain.ResDeviceAntiInterference">
        insert into tb_res_device_anti_interference
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,
                    </if>
                    <if test="siteId != null and siteId != ''">site_id,
                    </if>
                    <if test="deviceId != null and deviceId != ''">device_id,
                    </if>
                    <if test="testItem != null">test_item,
                    </if>
                    <if test="antiType != null">anti_type,
                    </if>
                    <if test="recordTime != null">record_time,
                    </if>
                    <if test="startTime != null">start_time,
                    </if>
                    <if test="endTime != null">end_time,
                    </if>
                    <if test="limitValue != null">limit_value,
                    </if>
                    <if test="pipelineLength != null">pipeline_length,
                    </if>
                    <if test="systemPreprocess != null">system_preprocess,
                    </if>
                    <if test="instrumentPreprocess != null">instrument_preprocess,
                    </if>
                    <if test="waterTurbidityRange != null">water_turbidity_range,
                    </if>
                    <if test="settlingTime != null">settling_time,
                    </if>
                    <if test="hasTurbidityCompensation != null">has_turbidity_compensation,
                    </if>
                    <if test="doublePumpWater != null">double_pump_water,
                    </if>
                    <if test="samplerNormal != null">sampler_normal,
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by,
                    </if>
                    <if test="createTime != null and createTime != ''">create_time,
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by,
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="siteId != null and siteId != ''">#{siteId},
                    </if>
                    <if test="deviceId != null and deviceId != ''">#{deviceId},
                    </if>
                    <if test="testItem != null">#{testItem},
                    </if>
                    <if test="antiType != null">#{antiType},
                    </if>
                    <if test="recordTime != null">#{recordTime},
                    </if>
                    <if test="startTime != null">#{startTime},
                    </if>
                    <if test="endTime != null">#{endTime},
                    </if>
                    <if test="limitValue != null">#{limitValue},
                    </if>
                    <if test="pipelineLength != null">#{pipelineLength},
                    </if>
                    <if test="systemPreprocess != null">#{systemPreprocess},
                    </if>
                    <if test="instrumentPreprocess != null">#{instrumentPreprocess},
                    </if>
                    <if test="waterTurbidityRange != null">#{waterTurbidityRange},
                    </if>
                    <if test="settlingTime != null">#{settlingTime},
                    </if>
                    <if test="hasTurbidityCompensation != null">#{hasTurbidityCompensation},
                    </if>
                    <if test="doublePumpWater != null">#{doublePumpWater},
                    </if>
                    <if test="samplerNormal != null">#{samplerNormal},
                    </if>
                    <if test="createdBy != null and createdBy != ''">#{createdBy},
                    </if>
                    <if test="createTime != null and createTime != ''">#{createTime},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">#{updatedBy},
                    </if>
                    <if test="updateTime != null and updateTime != ''">#{updateTime},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResDeviceAntiInterference" parameterType="java.util.List">
        insert into tb_res_device_anti_interference (
                id,
                site_id,
                device_id,
                test_item,
                anti_type,
                record_time,
                start_time,
                end_time,
                limit_value,
                pipeline_length,
                system_preprocess,
                instrument_preprocess,
                water_turbidity_range,
                settling_time,
                has_turbidity_compensation,
                double_pump_water,
                sampler_normal,
                created_by,
                create_time,
                updated_by,
                update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.id},
                #{item.siteId},
                #{item.deviceId},
                #{item.testItem},
                #{item.antiType},
                #{item.recordTime},
                #{item.startTime},
                #{item.endTime},
                #{item.limitValue},
                #{item.pipelineLength},
                #{item.systemPreprocess},
                #{item.instrumentPreprocess},
                #{item.waterTurbidityRange},
                #{item.settlingTime},
                #{item.hasTurbidityCompensation},
                #{item.doublePumpWater},
                #{item.samplerNormal},
                #{item.createdBy},
                #{item.createTime},
                #{item.updatedBy},
                #{item.updateTime}
        )
        </foreach>
    </insert>

    <update id="updateResDeviceAntiInterference" parameterType="com.mes.resource.domain.ResDeviceAntiInterference">
        update tb_res_device_anti_interference
        <trim prefix="SET" suffixOverrides=",">
                    <if test="siteId != null and siteId != ''">site_id =
                        #{siteId},
                    </if>
                    <if test="deviceId != null and deviceId != ''">device_id =
                        #{deviceId},
                    </if>
                    <if test="testItem != null">test_item =
                        #{testItem},
                    </if>
                    <if test="antiType != null">anti_type =
                        #{antiType},
                    </if>
                    <if test="recordTime != null">record_time =
                        #{recordTime},
                    </if>
                    <if test="startTime != null">start_time =
                        #{startTime},
                    </if>
                    <if test="endTime != null">end_time =
                        #{endTime},
                    </if>
                    <if test="limitValue != null">limit_value =
                        #{limitValue},
                    </if>
                    <if test="pipelineLength != null">pipeline_length =
                        #{pipelineLength},
                    </if>
                    <if test="systemPreprocess != null">system_preprocess =
                        #{systemPreprocess},
                    </if>
                    <if test="instrumentPreprocess != null">instrument_preprocess =
                        #{instrumentPreprocess},
                    </if>
                    <if test="waterTurbidityRange != null">water_turbidity_range =
                        #{waterTurbidityRange},
                    </if>
                    <if test="settlingTime != null">settling_time =
                        #{settlingTime},
                    </if>
                    <if test="hasTurbidityCompensation != null">has_turbidity_compensation =
                        #{hasTurbidityCompensation},
                    </if>
                    <if test="doublePumpWater != null">double_pump_water =
                        #{doublePumpWater},
                    </if>
                    <if test="samplerNormal != null">sampler_normal =
                        #{samplerNormal},
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by =
                        #{createdBy},
                    </if>
                    <if test="createTime != null and createTime != ''">create_time =
                        #{createTime},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by =
                        #{updatedBy},
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time =
                        #{updateTime},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResDeviceAntiInterference" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_device_anti_interference
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.siteId != null and item.siteId != ''">
                            site_id = #{item.siteId},
                        </if>
                        <if test="item.deviceId != null and item.deviceId != ''">
                            device_id = #{item.deviceId},
                        </if>
                        <if test="item.testItem != null">
                            test_item = #{item.testItem},
                        </if>
                        <if test="item.antiType != null">
                            anti_type = #{item.antiType},
                        </if>
                        <if test="item.recordTime != null">
                            record_time = #{item.recordTime},
                        </if>
                        <if test="item.startTime != null">
                            start_time = #{item.startTime},
                        </if>
                        <if test="item.endTime != null">
                            end_time = #{item.endTime},
                        </if>
                        <if test="item.limitValue != null">
                            limit_value = #{item.limitValue},
                        </if>
                        <if test="item.pipelineLength != null">
                            pipeline_length = #{item.pipelineLength},
                        </if>
                        <if test="item.systemPreprocess != null">
                            system_preprocess = #{item.systemPreprocess},
                        </if>
                        <if test="item.instrumentPreprocess != null">
                            instrument_preprocess = #{item.instrumentPreprocess},
                        </if>
                        <if test="item.waterTurbidityRange != null">
                            water_turbidity_range = #{item.waterTurbidityRange},
                        </if>
                        <if test="item.settlingTime != null">
                            settling_time = #{item.settlingTime},
                        </if>
                        <if test="item.hasTurbidityCompensation != null">
                            has_turbidity_compensation = #{item.hasTurbidityCompensation},
                        </if>
                        <if test="item.doublePumpWater != null">
                            double_pump_water = #{item.doublePumpWater},
                        </if>
                        <if test="item.samplerNormal != null">
                            sampler_normal = #{item.samplerNormal},
                        </if>
                        <if test="item.createdBy != null and item.createdBy != ''">
                            created_by = #{item.createdBy},
                        </if>
                        <if test="item.createTime != null and item.createTime != ''">
                            create_time = #{item.createTime},
                        </if>
                        <if test="item.updatedBy != null and item.updatedBy != ''">
                            updated_by = #{item.updatedBy},
                        </if>
                        <if test="item.updateTime != null and item.updateTime != ''">
                            update_time = #{item.updateTime},
                        </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteResDeviceAntiInterferenceById" parameterType="String">
        delete
        from tb_res_device_anti_interference where id = #{id}
    </delete>

    <delete id="deleteResDeviceAntiInterferenceByIds" parameterType="String">
        delete from tb_res_device_anti_interference where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAssociatedDataList" resultType="com.mes.resource.domain.dto.ResDeviceAntiInterferenceDto">
        select
        t1.id AS id,
        t1.site_id AS siteId,
        t1.device_id AS deviceId,
        t1.test_item AS testItem,
        t1.anti_type AS antiType,
        t1.record_time AS recordTime,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.limit_value AS limitValue,
        t1.pipeline_length AS pipelineLength,
        t1.system_preprocess AS systemPreprocess,
        t1.instrument_preprocess AS instrumentPreprocess,
        t1.water_turbidity_range AS waterTurbidityRange,
        t1.settling_time AS settlingTime,
        t1.has_turbidity_compensation AS hasTurbidityCompensation,
        t1.double_pump_water AS doublePumpWater,
        t1.sampler_normal AS samplerNormal,
        t1.created_by AS createdBy,
        t1.create_time AS createTime,
        t1.updated_by AS updatedBy,
        t1.update_time AS updateTime,
        t2.device_name as deviceName,
        t2.device_brand_id as deviceBrandId,
        t2.is_primary_device as isPrimaryDevice,
        t3.dept_name as deviceBrandIdName
        from tb_res_device_anti_interference t1
        left join tb_res_device t2 on t1.device_id = t2.id
        left join tb_res_dept t3 on t2.device_brand_id = t3.dept_id
        <where>
            <if test="siteId != null and siteId != ''"> t1.site_id = #{siteId}
            </if>
        </where>
    </select>
</mapper>