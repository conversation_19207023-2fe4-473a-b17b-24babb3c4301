<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.resource.mapper.ResLaboratoryMapper">

    <resultMap type="com.mes.resource.domain.ResLaboratory" id="ResLaboratoryResult">
            <result property="id" column="id"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="monitoringElement" column="monitoring_element"/>
            <result property="laboratoryName" column="laboratory_name"/>
            <result property="laboratoryCode" column="laboratory_code"/>
            <result property="address" column="address"/>
            <result property="legalPerson" column="legal_person"/>
            <result property="contactPerson" column="contact_person"/>
            <result property="contactPhone" column="contact_phone"/>
            <result property="contactPosition" column="contact_position"/>
            <result property="projectLeader" column="project_leader"/>
            <result property="projectLeaderPhone" column="project_leader_phone"/>
            <result property="projectLeaderPosition" column="project_leader_position"/>
            <result property="certificateName" column="certificate_name"/>
            <result property="certificateIssuer" column="certificate_issuer"/>
            <result property="certificateNumber" column="certificate_number"/>
            <result property="issueDate" column="issue_date"/>
            <result property="expiryDate" column="expiry_date"/>
            <result property="totalArea" column="total_area"/>
            <result property="equipmentCount" column="equipment_count"/>
            <result property="technicianCount" column="technician_count"/>
            <result property="technicalLeader" column="technical_leader"/>
            <result property="qualityLeader" column="quality_leader"/>
            <result property="status" column="status"/>
            <result property="createdBy" column="created_by"/>
            <result property="updatedBy" column="updated_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectResLaboratoryVo">
        select id, tenant_id, monitoring_element, laboratory_name, laboratory_code, address, legal_person, contact_person, contact_phone, contact_position, project_leader, project_leader_phone, project_leader_position, certificate_name, certificate_issuer, certificate_number, issue_date, expiry_date, total_area, equipment_count, technician_count, technical_leader, quality_leader, status, created_by, updated_by, create_time, update_time
        from tb_res_laboratory
    </sql>

    <select id="selectResLaboratoryList" parameterType="com.mes.resource.domain.ResLaboratory"
            resultMap="ResLaboratoryResult">
        <include refid="selectResLaboratoryVo"/>
        <where>
                        <if test="tenantId != null  and tenantId != ''">
                            and tenant_id = #{tenantId}
                        </if>
                        <if test="monitoringElement != null  and monitoringElement != ''">
                            and monitoring_element = #{monitoringElement}
                        </if>
                        <if test="laboratoryName != null  and laboratoryName != ''">
                            and laboratory_name like concat('%', #{laboratoryName}, '%')
                        </if>
                        <if test="laboratoryCode != null  and laboratoryCode != ''">
                            and laboratory_code = #{laboratoryCode}
                        </if>
                        <if test="address != null  and address != ''">
                            and address = #{address}
                        </if>
                        <if test="legalPerson != null  and legalPerson != ''">
                            and legal_person = #{legalPerson}
                        </if>
                        <if test="contactPerson != null  and contactPerson != ''">
                            and contact_person = #{contactPerson}
                        </if>
                        <if test="contactPhone != null  and contactPhone != ''">
                            and contact_phone = #{contactPhone}
                        </if>
                        <if test="contactPosition != null  and contactPosition != ''">
                            and contact_position = #{contactPosition}
                        </if>
                        <if test="projectLeader != null  and projectLeader != ''">
                            and project_leader = #{projectLeader}
                        </if>
                        <if test="projectLeaderPhone != null  and projectLeaderPhone != ''">
                            and project_leader_phone = #{projectLeaderPhone}
                        </if>
                        <if test="projectLeaderPosition != null  and projectLeaderPosition != ''">
                            and project_leader_position = #{projectLeaderPosition}
                        </if>
                        <if test="certificateName != null  and certificateName != ''">
                            and certificate_name like concat('%', #{certificateName}, '%')
                        </if>
                        <if test="certificateIssuer != null  and certificateIssuer != ''">
                            and certificate_issuer = #{certificateIssuer}
                        </if>
                        <if test="certificateNumber != null  and certificateNumber != ''">
                            and certificate_number = #{certificateNumber}
                        </if>
                        <if test="issueDate != null  and issueDate != ''">
                            and issue_date = #{issueDate}
                        </if>
                        <if test="expiryDate != null  and expiryDate != ''">
                            and expiry_date = #{expiryDate}
                        </if>
                        <if test="totalArea != null  and totalArea != ''">
                            and total_area = #{totalArea}
                        </if>
                        <if test="equipmentCount != null  and equipmentCount != ''">
                            and equipment_count = #{equipmentCount}
                        </if>
                        <if test="technicianCount != null  and technicianCount != ''">
                            and technician_count = #{technicianCount}
                        </if>
                        <if test="technicalLeader != null  and technicalLeader != ''">
                            and technical_leader = #{technicalLeader}
                        </if>
                        <if test="qualityLeader != null  and qualityLeader != ''">
                            and quality_leader = #{qualityLeader}
                        </if>
                        <if test="status != null  and status != ''">
                            and status = #{status}
                        </if>
                        <if test="createdBy != null  and createdBy != ''">
                            and created_by = #{createdBy}
                        </if>
                        <if test="updatedBy != null  and updatedBy != ''">
                            and updated_by = #{updatedBy}
                        </if>
        </where>
    </select>

    <select id="selectResLaboratoryById" parameterType="String"
            resultMap="ResLaboratoryResult">
            <include refid="selectResLaboratoryVo"/>
            where id = #{id}
    </select>

    <insert id="insertResLaboratory" parameterType="com.mes.resource.domain.ResLaboratory">
        insert into tb_res_laboratory
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,
                    </if>
                    <if test="tenantId != null and tenantId != ''">tenant_id,
                    </if>
                    <if test="monitoringElement != null and monitoringElement != ''">monitoring_element,
                    </if>
                    <if test="laboratoryName != null and laboratoryName != ''">laboratory_name,
                    </if>
                    <if test="laboratoryCode != null and laboratoryCode != ''">laboratory_code,
                    </if>
                    <if test="address != null and address != ''">address,
                    </if>
                    <if test="legalPerson != null and legalPerson != ''">legal_person,
                    </if>
                    <if test="contactPerson != null and contactPerson != ''">contact_person,
                    </if>
                    <if test="contactPhone != null and contactPhone != ''">contact_phone,
                    </if>
                    <if test="contactPosition != null">contact_position,
                    </if>
                    <if test="projectLeader != null">project_leader,
                    </if>
                    <if test="projectLeaderPhone != null">project_leader_phone,
                    </if>
                    <if test="projectLeaderPosition != null">project_leader_position,
                    </if>
                    <if test="certificateName != null">certificate_name,
                    </if>
                    <if test="certificateIssuer != null">certificate_issuer,
                    </if>
                    <if test="certificateNumber != null">certificate_number,
                    </if>
                    <if test="issueDate != null">issue_date,
                    </if>
                    <if test="expiryDate != null">expiry_date,
                    </if>
                    <if test="totalArea != null">total_area,
                    </if>
                    <if test="equipmentCount != null">equipment_count,
                    </if>
                    <if test="technicianCount != null">technician_count,
                    </if>
                    <if test="technicalLeader != null">technical_leader,
                    </if>
                    <if test="qualityLeader != null">quality_leader,
                    </if>
                    <if test="status != null and status != ''">status,
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by,
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by,
                    </if>
                    <if test="createTime != null and createTime != ''">create_time,
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="tenantId != null and tenantId != ''">#{tenantId},
                    </if>
                    <if test="monitoringElement != null and monitoringElement != ''">#{monitoringElement},
                    </if>
                    <if test="laboratoryName != null and laboratoryName != ''">#{laboratoryName},
                    </if>
                    <if test="laboratoryCode != null and laboratoryCode != ''">#{laboratoryCode},
                    </if>
                    <if test="address != null and address != ''">#{address},
                    </if>
                    <if test="legalPerson != null and legalPerson != ''">#{legalPerson},
                    </if>
                    <if test="contactPerson != null and contactPerson != ''">#{contactPerson},
                    </if>
                    <if test="contactPhone != null and contactPhone != ''">#{contactPhone},
                    </if>
                    <if test="contactPosition != null">#{contactPosition},
                    </if>
                    <if test="projectLeader != null">#{projectLeader},
                    </if>
                    <if test="projectLeaderPhone != null">#{projectLeaderPhone},
                    </if>
                    <if test="projectLeaderPosition != null">#{projectLeaderPosition},
                    </if>
                    <if test="certificateName != null">#{certificateName},
                    </if>
                    <if test="certificateIssuer != null">#{certificateIssuer},
                    </if>
                    <if test="certificateNumber != null">#{certificateNumber},
                    </if>
                    <if test="issueDate != null">#{issueDate},
                    </if>
                    <if test="expiryDate != null">#{expiryDate},
                    </if>
                    <if test="totalArea != null">#{totalArea},
                    </if>
                    <if test="equipmentCount != null">#{equipmentCount},
                    </if>
                    <if test="technicianCount != null">#{technicianCount},
                    </if>
                    <if test="technicalLeader != null">#{technicalLeader},
                    </if>
                    <if test="qualityLeader != null">#{qualityLeader},
                    </if>
                    <if test="status != null and status != ''">#{status},
                    </if>
                    <if test="createdBy != null and createdBy != ''">#{createdBy},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">#{updatedBy},
                    </if>
                    <if test="createTime != null and createTime != ''">#{createTime},
                    </if>
                    <if test="updateTime != null and updateTime != ''">#{updateTime},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResLaboratory" parameterType="java.util.List">
        insert into tb_res_laboratory (
                id,
                tenant_id,
                monitoring_element,
                laboratory_name,
                laboratory_code,
                address,
                legal_person,
                contact_person,
                contact_phone,
                contact_position,
                project_leader,
                project_leader_phone,
                project_leader_position,
                certificate_name,
                certificate_issuer,
                certificate_number,
                issue_date,
                expiry_date,
                total_area,
                equipment_count,
                technician_count,
                technical_leader,
                quality_leader,
                status,
                created_by,
                updated_by,
                create_time,
                update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.id},
                #{item.tenantId},
                #{item.monitoringElement},
                #{item.laboratoryName},
                #{item.laboratoryCode},
                #{item.address},
                #{item.legalPerson},
                #{item.contactPerson},
                #{item.contactPhone},
                #{item.contactPosition},
                #{item.projectLeader},
                #{item.projectLeaderPhone},
                #{item.projectLeaderPosition},
                #{item.certificateName},
                #{item.certificateIssuer},
                #{item.certificateNumber},
                #{item.issueDate},
                #{item.expiryDate},
                #{item.totalArea},
                #{item.equipmentCount},
                #{item.technicianCount},
                #{item.technicalLeader},
                #{item.qualityLeader},
                #{item.status},
                #{item.createdBy},
                #{item.updatedBy},
                #{item.createTime},
                #{item.updateTime}
        )
        </foreach>
    </insert>

    <update id="updateResLaboratory" parameterType="com.mes.resource.domain.ResLaboratory">
        update tb_res_laboratory
        <trim prefix="SET" suffixOverrides=",">
                    <if test="tenantId != null and tenantId != ''">tenant_id =
                        #{tenantId},
                    </if>
                    <if test="monitoringElement != null and monitoringElement != ''">monitoring_element =
                        #{monitoringElement},
                    </if>
                    <if test="laboratoryName != null and laboratoryName != ''">laboratory_name =
                        #{laboratoryName},
                    </if>
                    <if test="laboratoryCode != null and laboratoryCode != ''">laboratory_code =
                        #{laboratoryCode},
                    </if>
                    <if test="address != null and address != ''">address =
                        #{address},
                    </if>
                    <if test="legalPerson != null and legalPerson != ''">legal_person =
                        #{legalPerson},
                    </if>
                    <if test="contactPerson != null and contactPerson != ''">contact_person =
                        #{contactPerson},
                    </if>
                    <if test="contactPhone != null and contactPhone != ''">contact_phone =
                        #{contactPhone},
                    </if>
                    <if test="contactPosition != null">contact_position =
                        #{contactPosition},
                    </if>
                    <if test="projectLeader != null">project_leader =
                        #{projectLeader},
                    </if>
                    <if test="projectLeaderPhone != null">project_leader_phone =
                        #{projectLeaderPhone},
                    </if>
                    <if test="projectLeaderPosition != null">project_leader_position =
                        #{projectLeaderPosition},
                    </if>
                    <if test="certificateName != null">certificate_name =
                        #{certificateName},
                    </if>
                    <if test="certificateIssuer != null">certificate_issuer =
                        #{certificateIssuer},
                    </if>
                    <if test="certificateNumber != null">certificate_number =
                        #{certificateNumber},
                    </if>
                    <if test="issueDate != null">issue_date =
                        #{issueDate},
                    </if>
                    <if test="expiryDate != null">expiry_date =
                        #{expiryDate},
                    </if>
                    <if test="totalArea != null">total_area =
                        #{totalArea},
                    </if>
                    <if test="equipmentCount != null">equipment_count =
                        #{equipmentCount},
                    </if>
                    <if test="technicianCount != null">technician_count =
                        #{technicianCount},
                    </if>
                    <if test="technicalLeader != null">technical_leader =
                        #{technicalLeader},
                    </if>
                    <if test="qualityLeader != null">quality_leader =
                        #{qualityLeader},
                    </if>
                    <if test="status != null and status != ''">status =
                        #{status},
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by =
                        #{createdBy},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by =
                        #{updatedBy},
                    </if>
                    <if test="createTime != null and createTime != ''">create_time =
                        #{createTime},
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time =
                        #{updateTime},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResLaboratory" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_laboratory
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.tenantId != null and item.tenantId != ''">
                            tenant_id = #{item.tenantId},
                        </if>
                        <if test="item.monitoringElement != null and item.monitoringElement != ''">
                            monitoring_element = #{item.monitoringElement},
                        </if>
                        <if test="item.laboratoryName != null and item.laboratoryName != ''">
                            laboratory_name = #{item.laboratoryName},
                        </if>
                        <if test="item.laboratoryCode != null and item.laboratoryCode != ''">
                            laboratory_code = #{item.laboratoryCode},
                        </if>
                        <if test="item.address != null and item.address != ''">
                            address = #{item.address},
                        </if>
                        <if test="item.legalPerson != null and item.legalPerson != ''">
                            legal_person = #{item.legalPerson},
                        </if>
                        <if test="item.contactPerson != null and item.contactPerson != ''">
                            contact_person = #{item.contactPerson},
                        </if>
                        <if test="item.contactPhone != null and item.contactPhone != ''">
                            contact_phone = #{item.contactPhone},
                        </if>
                        <if test="item.contactPosition != null">
                            contact_position = #{item.contactPosition},
                        </if>
                        <if test="item.projectLeader != null">
                            project_leader = #{item.projectLeader},
                        </if>
                        <if test="item.projectLeaderPhone != null">
                            project_leader_phone = #{item.projectLeaderPhone},
                        </if>
                        <if test="item.projectLeaderPosition != null">
                            project_leader_position = #{item.projectLeaderPosition},
                        </if>
                        <if test="item.certificateName != null">
                            certificate_name = #{item.certificateName},
                        </if>
                        <if test="item.certificateIssuer != null">
                            certificate_issuer = #{item.certificateIssuer},
                        </if>
                        <if test="item.certificateNumber != null">
                            certificate_number = #{item.certificateNumber},
                        </if>
                        <if test="item.issueDate != null">
                            issue_date = #{item.issueDate},
                        </if>
                        <if test="item.expiryDate != null">
                            expiry_date = #{item.expiryDate},
                        </if>
                        <if test="item.totalArea != null">
                            total_area = #{item.totalArea},
                        </if>
                        <if test="item.equipmentCount != null">
                            equipment_count = #{item.equipmentCount},
                        </if>
                        <if test="item.technicianCount != null">
                            technician_count = #{item.technicianCount},
                        </if>
                        <if test="item.technicalLeader != null">
                            technical_leader = #{item.technicalLeader},
                        </if>
                        <if test="item.qualityLeader != null">
                            quality_leader = #{item.qualityLeader},
                        </if>
                        <if test="item.status != null and item.status != ''">
                            status = #{item.status},
                        </if>
                        <if test="item.createdBy != null and item.createdBy != ''">
                            created_by = #{item.createdBy},
                        </if>
                        <if test="item.updatedBy != null and item.updatedBy != ''">
                            updated_by = #{item.updatedBy},
                        </if>
                        <if test="item.createTime != null and item.createTime != ''">
                            create_time = #{item.createTime},
                        </if>
                        <if test="item.updateTime != null and item.updateTime != ''">
                            update_time = #{item.updateTime},
                        </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteResLaboratoryById" parameterType="String">
        delete
        from tb_res_laboratory where id = #{id}
    </delete>

    <delete id="deleteResLaboratoryByIds">
        delete from tb_res_laboratory where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id,jdbcType=NUMERIC}
        </foreach>
    </delete>

    <select id="page" resultType="com.mes.resource.domain.dto.LaboratoryMonitorDto">
        SELECT
        lab.laboratory_name AS laboratoryName,
        lab.monitoring_element AS monitoringElement,
        t2.dict_value as monitoringElementName,
        item.testing_method AS testingMethod,
        item.method_basis AS methodBasis,
        ability.update_month AS updateMonth,
        ability.update_remark AS updateRemark,
        LISTAGG(item.item_name, ', ') WITHIN GROUP (ORDER BY item.item_name) AS monitorItems,
        COUNT(item.id) AS itemCount
        FROM
            tb_res_laboratory lab
        LEFT JOIN tb_res_laboratory_ability ability ON lab.id = ability.laboratory_id
        LEFT JOIN tb_res_monitor_item item ON ability.ability_type = item.id
        LEFT JOIN tb_sys_dict t2 ON lab.monitoring_element = t2.dict_code AND t2.class_code = 'monitoring_element'
        <where>
            lab.status = 1
            AND item.status = 'A'
            <if test="monitorItems != null  and monitorItems != ''">
                and item.item_name like concat('%', #{monitorItems}, '%')
            </if>
            <if test="monitoringElement != null  and monitoringElement != ''">
                and lab.monitoring_element = #{monitoringElement}
            </if>
            <if test="laboratoryName != null  and laboratoryName != ''">
                and lab.laboratory_name like concat('%', #{laboratoryName}, '%')
            </if>
        </where>
        GROUP BY
            lab.laboratory_name,
            lab.monitoring_element,
            t2.dict_value,
            item.testing_method,
            item.method_basis,
            ability.update_month,
            ability.update_remark
    </select>
</mapper>