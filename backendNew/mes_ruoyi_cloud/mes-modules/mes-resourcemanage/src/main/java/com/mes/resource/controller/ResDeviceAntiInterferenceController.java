package com.mes.resource.controller;

import com.mes.common.core.utils.poi.ExcelUtil;
import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.resource.domain.ResDeviceAntiInterference;
import com.mes.resource.domain.dto.ResDeviceAntiInterferenceDto;
import com.mes.resource.service.IResDeviceAntiInterferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仪器抗干扰能力Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/deviceAntiInterference")
public class ResDeviceAntiInterferenceController extends BaseController {
    @Autowired
    private IResDeviceAntiInterferenceService resDeviceAntiInterferenceService;

    /**
     * 查询仪器抗干扰能力列表
     *
     * @RequiresPermissions("resource:interference:list")
     */
    @GetMapping("/list")
    public TableDataInfo list(ResDeviceAntiInterference resDeviceAntiInterference) {
        startPage();
        List<ResDeviceAntiInterference> list = resDeviceAntiInterferenceService.selectResDeviceAntiInterferenceList(
            resDeviceAntiInterference);
        return getDataTable(list);
    }

    /**
     * 查询仪器抗干扰能力列表分页
     *
     * @RequiresPermissions("resource:interference:list")
     */
    @GetMapping("/page")
    public TableDataInfo page(ResDeviceAntiInterferenceDto resDeviceAntiInterferenceDto) {
        startPage();
        List<ResDeviceAntiInterferenceDto> list = resDeviceAntiInterferenceService.associatedDataList(resDeviceAntiInterferenceDto);
        return getDataTable(list);
    }



    /**
     * 导出仪器抗干扰能力列表
     *
     * @RequiresPermissions("resource:interference:export")
     * @Log(title = "仪器抗干扰能力", businessType = BusinessType.EXPORT)
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, ResDeviceAntiInterferenceDto resDeviceAntiInterferenceDto) {
        List<ResDeviceAntiInterferenceDto> list = resDeviceAntiInterferenceService.associatedDataList(
            resDeviceAntiInterferenceDto);
        ExcelUtil<ResDeviceAntiInterferenceDto> util = new ExcelUtil<>(ResDeviceAntiInterferenceDto.class);
        util.exportExcel(response, list, "仪器抗干扰能力数据", false, false);
    }

    /**
     * 获取仪器抗干扰能力详细信息
     *
     * @RequiresPermissions("resource:interference:query")
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(resDeviceAntiInterferenceService.selectResDeviceAntiInterferenceById(id));
    }

    /**
     * 新增仪器抗干扰能力
     *
     * @RequiresPermissions("resource:interference:add")
     */
    //@Log(title = "仪器抗干扰能力", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ResDeviceAntiInterference resDeviceAntiInterference) {
        return toAjax(resDeviceAntiInterferenceService.insertResDeviceAntiInterference(resDeviceAntiInterference));
    }

    /**
     * 批量新增仪器抗干扰能力
     *
     * @RequiresPermissions("resource:interference:add")
     */
    //@Log(title = "仪器抗干扰能力", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@RequestBody List<ResDeviceAntiInterference> resDeviceAntiInterferenceList) {
        return toAjax(
            resDeviceAntiInterferenceService.batchInsertResDeviceAntiInterference(resDeviceAntiInterferenceList));
    }

    /**
     * 修改仪器抗干扰能力
     *
     * @RequiresPermissions("resource:interference:edit")
     */
    //@Log(title = "仪器抗干扰能力", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ResDeviceAntiInterference resDeviceAntiInterference) {
        return toAjax(resDeviceAntiInterferenceService.updateResDeviceAntiInterference(resDeviceAntiInterference));
    }

    /**
     * 批量修改仪器抗干扰能力
     *
     * @RequiresPermissions("resource:interference:edit")
     */
    //@Log(title = "仪器抗干扰能力", businessType = BusinessType.UPDATE)
    @PostMapping("/batchEdit")
    public AjaxResult batchEdit(@RequestBody List<ResDeviceAntiInterference> resDeviceAntiInterferenceList) {
        return toAjax(
            resDeviceAntiInterferenceService.batchUpdateResDeviceAntiInterference(resDeviceAntiInterferenceList));
    }

    /**
     * 删除仪器抗干扰能力
     *
     * @RequiresPermissions("resource:interference:remove")
     */
    //@Log(title = "仪器抗干扰能力", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(resDeviceAntiInterferenceService.deleteResDeviceAntiInterferenceByIds(ids));
    }
}
