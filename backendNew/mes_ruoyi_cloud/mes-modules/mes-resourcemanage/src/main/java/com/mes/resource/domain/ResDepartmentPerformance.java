package com.mes.resource.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 机构绩效对象 tb_res_department_performance
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public class ResDepartmentPerformance extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 绩效记录ID */
    private Long performanceId;

    /** 机构ID */
    @Excel(name = "机构ID")
    private Long departmentId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String departmentName;

    /** 综合绩效(根据数据加权) */
    @Excel(name = "综合绩效(根据数据加权)")
    private String totalPerformance;

    /** 原始绩效(从审核来的有效率) */
    @Excel(name = "原始绩效(从审核来的有效率)")
    private Integer oriPerformance;

    /** 时间类别(月/季度/年) */
    @Excel(name = "时间类别(月/季度/年)")
    private String timeCategory;

    /** 时间值(1月-12月,第一季度-第四季度) */
    @Excel(name = "时间值(1月-12月,第一季度-第四季度)")
    private String timeValue;

    /** 年份(如2025) */
    @Excel(name = "年份(如2025)")
    private Integer year;

    /** 数据来源系统 */
    @Excel(name = "数据来源系统")
    private String dataSource;

    /** 同步时间 */
    @Excel(name = "同步时间")
    private String syncTime;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public void setPerformanceId(Long performanceId) {
        this.performanceId = performanceId;
    }

    public Long getPerformanceId() {
        return performanceId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setTotalPerformance(String totalPerformance) {
        this.totalPerformance = totalPerformance;
    }

    public String getTotalPerformance() {
        return totalPerformance;
    }

    public void setOriPerformance(Integer oriPerformance) {
        this.oriPerformance = oriPerformance;
    }

    public Integer getOriPerformance() {
        return oriPerformance;
    }

    public void setTimeCategory(String timeCategory) {
        this.timeCategory = timeCategory;
    }

    public String getTimeCategory() {
        return timeCategory;
    }

    public void setTimeValue(String timeValue) {
        this.timeValue = timeValue;
    }

    public String getTimeValue() {
        return timeValue;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getYear() {
        return year;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setSyncTime(String syncTime) {
        this.syncTime = syncTime;
    }

    public String getSyncTime() {
        return syncTime;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("performanceId", getPerformanceId())
            .append("departmentId", getDepartmentId()).append("departmentName", getDepartmentName())
            .append("totalPerformance", getTotalPerformance()).append("oriPerformance", getOriPerformance())
            .append("timeCategory", getTimeCategory()).append("timeValue", getTimeValue()).append("year", getYear())
            .append("dataSource", getDataSource()).append("syncTime", getSyncTime()).append("tenantId", getTenantId())
            .append("createdBy", getCreatedBy()).append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy()).append("updateTime", getUpdateTime()).toString();
    }
}
