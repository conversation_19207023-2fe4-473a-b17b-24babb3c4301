package com.mes.resource.service.impl;

import com.mes.common.core.exception.ServiceException;
import com.mes.common.core.utils.StringUtils;
import com.mes.resource.constant.RoleKeyConstant;
import com.mes.resource.domain.PubFlowNodeConfig;
import com.mes.resource.domain.ResDevice;
import com.mes.resource.domain.ResSite;
import com.mes.resource.mapper.PubFlowNodeConfigMapper;
import com.mes.resource.mapper.ResDeviceMapper;
import com.mes.resource.mapper.ResFlowTaskMapper;
import com.mes.resource.mapper.ResSiteMapper;
import com.mes.resource.service.IPubFlowNodeConfigService;
import com.mes.resource.service.OpenApiService;
import com.mes.tower.api.domain.dto.PersonBasicOpenDto;
import com.mes.tower.api.domain.dto.SysUserRoleOpenDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 流程环节与业务达配置关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class PubFlowNodeConfigServiceImpl implements IPubFlowNodeConfigService {
    @Autowired
    private PubFlowNodeConfigMapper pubFlowNodeConfigMapper;

    @Resource
    private ResFlowTaskMapper resFlowTaskMapper;

    @Resource
    private OpenApiService openApiService;

    @Resource
    private ResSiteMapper resSiteMapper;

    @Resource
    private ResDeviceMapper resDeviceMapper;

    /**
     * 查询流程环节与业务达配置关系
     *
     * @param flowNodeConfigId 流程环节与业务达配置关系主键
     * @return 流程环节与业务达配置关系
     */
    @Override
    public PubFlowNodeConfig selectPubFlowNodeConfigByFlowNodeConfigId(String flowNodeConfigId) {
        return pubFlowNodeConfigMapper.selectPubFlowNodeConfigByFlowNodeConfigId(flowNodeConfigId);
    }

    /**
     * 查询流程环节与业务达配置关系列表
     *
     * @param pubFlowNodeConfig 流程环节与业务达配置关系
     * @return 流程环节与业务达配置关系
     */
    @Override
    public List<PubFlowNodeConfig> selectPubFlowNodeConfigList(PubFlowNodeConfig pubFlowNodeConfig) {
        return pubFlowNodeConfigMapper.selectPubFlowNodeConfigList(pubFlowNodeConfig);
    }

    /**
     * 新增流程环节与业务达配置关系
     *
     * @param pubFlowNodeConfig 流程环节与业务达配置关系
     * @return 结果
     */
    @Override
    public int insertPubFlowNodeConfig(PubFlowNodeConfig pubFlowNodeConfig) {
        return pubFlowNodeConfigMapper.insertPubFlowNodeConfig(pubFlowNodeConfig);
    }

    /**
     * 批量新增流程环节与业务达配置关系
     *
     * @param pubFlowNodeConfigList 流程环节与业务达配置关系List
     * @return 结果
     */
    @Override
    public int batchInsertPubFlowNodeConfig(List<PubFlowNodeConfig> pubFlowNodeConfigList) {
        for (PubFlowNodeConfig pubFlowNodeConfig :pubFlowNodeConfigList){
        }
        return pubFlowNodeConfigMapper.batchInsertPubFlowNodeConfig(pubFlowNodeConfigList);
    }

    /**
     * 修改流程环节与业务达配置关系
     *
     * @param pubFlowNodeConfig 流程环节与业务达配置关系
     * @return 结果
     */
    @Override
    public int updatePubFlowNodeConfig(PubFlowNodeConfig pubFlowNodeConfig) {
        return pubFlowNodeConfigMapper.updatePubFlowNodeConfig(pubFlowNodeConfig);
    }

    /**
     * 批量修改流程环节与业务达配置关系
     *
     * @param pubFlowNodeConfigList 流程环节与业务达配置关系List
     * @return 结果
     */
    @Override
    public int batchUpdatePubFlowNodeConfig(List<PubFlowNodeConfig> pubFlowNodeConfigList) {
        return pubFlowNodeConfigMapper.batchUpdatePubFlowNodeConfig(pubFlowNodeConfigList);
    }

    /**
     * 批量删除流程环节与业务达配置关系
     *
     * @param flowNodeConfigIds 需要删除的流程环节与业务达配置关系主键
     * @return 结果
     */
    @Override
    public int deletePubFlowNodeConfigByFlowNodeConfigIds(String[] flowNodeConfigIds) {
        return pubFlowNodeConfigMapper.deletePubFlowNodeConfigByFlowNodeConfigIds(flowNodeConfigIds);
    }

    /**
     * 删除流程环节与业务达配置关系信息
     *
     * @param flowNodeConfigId 流程环节与业务达配置关系主键
     * @return 结果
     */
    @Override
    public int deletePubFlowNodeConfigByFlowNodeConfigId(String flowNodeConfigId) {
        return pubFlowNodeConfigMapper.deletePubFlowNodeConfigByFlowNodeConfigId(flowNodeConfigId);
    }

    /**
     * 查询流程节点对应的角色的人员列表
     */
    @Override
    public List<PersonBasicOpenDto> selectPersonListByNodeId(String nodeId, String resourceId, String resourceType) {
        if (StringUtils.isEmpty(nodeId)||StringUtils.isEmpty(resourceId)||StringUtils.isEmpty(resourceType)) {
            throw  new ServiceException("nodeId或resourceId或resourceType不能为空！");
        }
            //查询当前节点的对应审核角色
            PubFlowNodeConfig pubFlowNodeConfig = pubFlowNodeConfigMapper.selectPubFlowNodeConfigByNodeId(nodeId);
            String nodeHandle = pubFlowNodeConfig.getNodeHandle();
            //无角色不查询
            if (nodeHandle==null||nodeHandle.isEmpty()){
                return Collections.emptyList();
            }
            //如果是总站角色，不需要对人员进行筛选
            //省份/城市角色，按照对应的省份/城市字段id去筛选
            //设备厂商，对应设备品牌（设备独有角色）
            //运维单位，对应运维单位
            SysUserRoleOpenDto sysUserRoleOpenDto = new SysUserRoleOpenDto();
            ResSite resSite = new ResSite();
            ResDevice resDevice = new ResDevice();
            List<String> deptList  = new ArrayList<>();
            if (resourceType.equals("site")){
                resSite = resSiteMapper.selectResSiteById(Long.valueOf(resourceId));
            }
            if (resourceType.equals("device")){
                resDevice = resDeviceMapper.selectResDeviceById(Long.valueOf(resourceId));
            }
            if (Objects.isNull(resSite.getId()) && Objects.isNull(resDevice.getId())){
                return Collections.emptyList();
            }

            switch (resourceType + "|" + nodeHandle) {
                case "site|" + RoleKeyConstant.ROLE_RES_CENTRAL_STATION_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CENTRAL_STATION_REVIEW);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_CENTRAL_STATION_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CENTRAL_STATION_LEADER_REVIEW);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_UNIT_COMPARISON:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_UNIT_COMPARISON);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_PROVINCE_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_PROVINCE_REVIEW);
                    String province = resSite.getProvince();
                    sysUserRoleOpenDto.setDeptCode(province);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_PROVINCE_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_PROVINCE_LEADER_REVIEW);
                    String provinceLeader = resSite.getProvince();
                    sysUserRoleOpenDto.setDeptCode(provinceLeader);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_CITY_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CITY_REVIEW);
                    String city = resSite.getCity();
                    sysUserRoleOpenDto.setDeptCode(city);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_CITY_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CITY_LEADER_REVIEW);
                    String cityLeader = resSite.getCity();
                    sysUserRoleOpenDto.setDeptCode(cityLeader);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_PERSON:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_PERSON);
                    String operationId = resSite.getOperationUnit();
                    deptList.add(operationId);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;
                case "site|" + RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_LEADER:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_LEADER);
                    String operationLeaderId = resSite.getOperationUnit();
                    deptList.add(operationLeaderId);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;

                case "device|" + RoleKeyConstant.ROLE_RES_CENTRAL_STATION_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CENTRAL_STATION_REVIEW);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_CENTRAL_STATION_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CENTRAL_STATION_LEADER_REVIEW);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_UNIT_COMPARISON:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_UNIT_COMPARISON);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_PROVINCE_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_PROVINCE_REVIEW);
                    String deviceProvince = resDevice.getBindProvinceId();
                    sysUserRoleOpenDto.setDeptCode(deviceProvince);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_PROVINCE_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_PROVINCE_LEADER_REVIEW);
                    String deviceProvinceLeader = resDevice.getBindProvinceId();
                    sysUserRoleOpenDto.setDeptCode(deviceProvinceLeader);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_CITY_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CITY_REVIEW);
                    String deviceCity = resDevice.getBindCityId();
                    sysUserRoleOpenDto.setDeptCode(deviceCity);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_CITY_LEADER_REVIEW:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_CITY_LEADER_REVIEW);
                    String deviceCityLeader = resDevice.getBindCityId();
                    sysUserRoleOpenDto.setDeptCode(deviceCityLeader);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_DEVICE_INSTALL_PERSON:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_DEVICE_INSTALL_PERSON);
                    String deviceInstall = resDevice.getBindCityId();
                    deptList.add(deviceInstall);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_DEVICE_LEADER:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_DEVICE_LEADER);
                    String deviceLeader = resDevice.getDeviceBrandId();
                    deptList.add(deviceLeader);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_PERSON:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_PERSON);
                    String devOperationId = resDevice.getOperationUnitId();
                    deptList.add(devOperationId);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;
                case "device|" + RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_LEADER:
                    sysUserRoleOpenDto.setRoleKey(RoleKeyConstant.ROLE_RES_OPERATION_MAINTENANCE_LEADER);
                    String devOperationLeaderId = resDevice.getOperationUnitId();
                    deptList.add(devOperationLeaderId);
                    sysUserRoleOpenDto.setDeptList(deptList);
                    break;
            }



            return openApiService.getPersonBasicOpenDtos(sysUserRoleOpenDto);
        }
}
