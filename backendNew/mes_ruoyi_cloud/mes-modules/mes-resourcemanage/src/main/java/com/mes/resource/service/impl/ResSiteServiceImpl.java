package com.mes.resource.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mes.common.core.exception.ServiceException;
import com.mes.common.core.utils.DateUtils;
import com.mes.common.redis.service.RedisService;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.resource.config.aspect.OperationLogEndpoint;
import com.mes.resource.domain.ResFieldAll;
import com.mes.resource.domain.ResOperationHistory;
import com.mes.resource.domain.ResSite;
import com.mes.resource.domain.SysDict;
import com.mes.resource.domain.dto.GroupObjectTypeDto;
import com.mes.resource.domain.dto.PointStatisticsDto;
import com.mes.resource.domain.dto.ResSiteDto;
import com.mes.resource.domain.dto.SiteAuditDto;
import com.mes.resource.domain.dto.SiteAuditResultDto;
import com.mes.resource.domain.dto.SiteRunStatisticsDto;
import com.mes.resource.domain.vo.ImportResult;
import com.mes.resource.enums.OperationTypeEnum;
import com.mes.resource.excel.ExcelImportUtils;
import com.mes.resource.mapper.ResFieldAllMapper;
import com.mes.resource.mapper.ResOperationHistoryMapper;
import com.mes.resource.mapper.ResSiteMapper;
import com.mes.resource.mapper.SysDictMapper;
import com.mes.resource.service.IResSiteService;
import com.mes.resource.utils.JsonDiffUtils;
import com.mes.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 点位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
public class ResSiteServiceImpl implements IResSiteService {
    @Autowired
    private ResSiteMapper resSiteMapper;

    @Resource
    private ResFieldAllMapper resFieldAllMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private ResOperationHistoryMapper resOperationHistoryMapper;

    @Resource
    private SysDictMapper sysDictMapper;

    /**
     * 查询点位
     *
     * @param id 点位主键
     * @return 点位
     */
    @Override
    public ResSite selectResSiteById(Long id) {
        ResSite resSite = resSiteMapper.selectResSiteById(id);
        if (ObjectUtil.isEmpty(resSite)) {
            throw new ServiceException("当前点位信息不存在");
        }
        return resSite;
    }

    /**
     * 查询点位列表
     *
     * @param resSite 点位
     * @return 点位
     */
    @Override
    public List<ResSite> selectResSiteList(ResSite resSite) {
        return resSiteMapper.selectResSiteList(resSite);
    }

    /**
     * 新增点位
     *
     * @param resSite 点位
     * @return 结果
     */
    @Override
    @OperationLogEndpoint(module = "点位管理-点位新增", operationType = OperationTypeEnum.CREATE,
        operationContent = "点位管理-操作点位新增")
    @Transactional(rollbackFor = Exception.class)
    public int insertResSite(ResSite resSite, LoginUser loginUser) {
        if (StrUtil.isEmpty(resSite.getSiteNumber())) {
            throw new ServiceException("站点编号必须填写");
        }
        String siteNumber = resSite.getSiteNumber();
        ResSite dbSite = new ResSite();
        dbSite.setSiteNumber(siteNumber);
        List<ResSite> listStte = resSiteMapper.selectResSiteList(dbSite);
        if (CollUtil.isNotEmpty(listStte)) {
            throw new ServiceException("当前站点编号已存在,请更换站点编号");
        }
        if (StrUtil.isEmpty(resSite.getSiteName())) {
            throw new ServiceException("站点名称必须填写");
        }
        if (StrUtil.isEmpty(resSite.getMonitoringElement())) {
            throw new ServiceException("监测要素必须要填写");
        }
        if (StrUtil.isEmpty(resSite.getTenantId())) {
            resSite.setTenantId("0");
        }
        resSite.setCreateTime(DateUtils.getNowDate());
        resSite.setCreateBy(loginUser.getUsername());
        int index = resSiteMapper.insertResSite(resSite);
        if (index > 0) {
            ResOperationHistory resOperationHistory = new ResOperationHistory();
            resOperationHistory.setOperator(loginUser.getUsername());
            resOperationHistory.setOperationTime(DateUtils.getTime());
            resOperationHistory.setOperationType("新增");
            resOperationHistory.setResourceType("site");
            resOperationHistory.setResourceId(resSite.getId().toString());
            resOperationHistory.setCreatedBy(loginUser.getUsername());
            resOperationHistory.setCreateTime(DateUtils.getNowDate());
            resOperationHistoryMapper.insertResOperationHistory(resOperationHistory);
        }
        return index;
    }

    /**
     * 修改点位
     *
     * @param resSite 点位
     * @return 结果
     */
    @Override
    @OperationLogEndpoint(module = "点位管理-点位修改", operationType = OperationTypeEnum.UPDATE,
        operationContent = "点位管理-操作点位修改")
    @Transactional(rollbackFor = Exception.class)
    public int updateResSite(ResSite resSite, LoginUser loginUser) {
        //判断设备是否存在
        ResSite dbSite = selectResSiteById(resSite.getId());
        if (StrUtil.isNotEmpty(resSite.getSiteNumber())) {
            if (!dbSite.getSiteNumber().equalsIgnoreCase(resSite.getSiteNumber())) {
                ResSite querySite = new ResSite();
                querySite.setSiteNumber(resSite.getSiteNumber());
                List<ResSite> listStte = resSiteMapper.selectResSiteList(querySite);
                if (CollUtil.isNotEmpty(listStte)) {
                    throw new ServiceException("当前修改的站点编号已存在,请更换站点编号");
                }
            }
        }
        resSite.setUpdateTime(DateUtils.getNowDate());
        resSite.setUpdatedBy(loginUser.getUsername());
        int index = resSiteMapper.updateResSite(resSite);
        if (index > 0) {
            String names = "{\"id\":\"主键\",\"siteNumber\":\"站点统一编码\",\"siteName\":\"站点名称\",\"siteCode\":\"站点编码\",\"formerCodes\":\"曾用编码\",\"monitoringElement\":\"监测要素\",\"longitude\":\"经度\",\"latitude\":\"纬度\",\"siteType\":\"站点类型\",\"siteStatus\":\"站点状态\",\"siteBatch\":\"站点批次\",\"province\":\"省份\",\"city\":\"所属地市\",\"operationUnit\":\"运维单位\",\"packageId\":\"所属包件\",\"officialLongitude\":\"发文经度\",\"officialLatitude\":\"发文纬度\",\"constructionTime\":\"建设时间\",\"constructionUnit\":\"建设单位\",\"ministryAddress\":\"部发文站点地址\",\"actualAddress\":\"站点实际地址\",\"networkTime\":\"入网时间\",\"waterQualityTarget\":\"水质目标级别\",\"riverBasin\":\"所在流域\",\"waterBody\":\"所在水体\",\"riverLevel\":\"河流级别\",\"inflowWaterBody\":\"汇入水体\",\"sectionAttribute\":\"断面属性\",\"sectionDirection\":\"断面方向\",\"assessmentProvince\":\"考核省\",\"assessmentCity\":\"考核地市\",\"hasSalinity\":\"是否要盐度指标\",\"samplingPoints\":\"采样点数\",\"samplingMethod\":\"采样方式\",\"isSelfTesting\":\"是否自采自测\",\"hasAutomaticStation\":\"是否有自动站\",\"stationConstructionTime\":\"建站时间\",\"constructionProvince\":\"承建省份\",\"stationClassification\":\"站点分类\",\"siteIntroduction\":\"站点介绍\",\"upstream\":\"上游\",\"downstream\":\"下游\",\"remarks\":\"备注\",\"tenantId\":\"租户ID\",\"fullSamplingDuration\":\"全采时长\",\"siteAttribute\":\"站点属性\",\"createdBy\":\"创建人\",\"createTime\":\"创建时间\",\"updatedBy\":\"更新人\",\"updateTime\":\"更新时间\"}";
            Map<String, String> nameMap = new HashMap<>();
            JSONObject.parseObject(names).forEach((key, value) -> nameMap.put(key, value.toString()));
            // 要排除的字段集合
            Set<String> excludedFields = new HashSet<>(
                Arrays.asList("createdBy", "createTime", "updateTime", "updatedBy"));
            Map<String, Map<String, Object>> map = JsonDiffUtils.compareObjects(
                JSON.parse(JSONObject.toJSONString(dbSite)), JSON.parse(JSONObject.toJSONString(resSite)),
                excludedFields, nameMap, false);
            ResOperationHistory resOperationHistory = new ResOperationHistory();
            resOperationHistory.setOperator(loginUser.getUsername());
            resOperationHistory.setOperationTime(DateUtils.getTime());
            resOperationHistory.setOperationType("修改");
            resOperationHistory.setOperationContent(JSONObject.toJSONString(map));
            resOperationHistory.setResourceType("site");
            resOperationHistory.setResourceId(resSite.getId().toString());
            resOperationHistory.setCreatedBy(loginUser.getUsername());
            resOperationHistory.setCreateTime(DateUtils.getNowDate());
            resOperationHistoryMapper.insertResOperationHistory(resOperationHistory);
        }
        return index;
    }

    /**
     * 批量删除点位
     *
     * @param ids 需要删除的点位主键
     * @return 结果
     */
    @Override
    public int deleteResSiteByIds(Long[] ids) {
        return resSiteMapper.deleteResSiteByIds(ids);
    }

    /**
     * 删除点位信息
     *
     * @param id 点位主键
     * @return 结果
     */
    @Override
    public int deleteResSiteById(Long id) {
        return resSiteMapper.deleteResSiteById(id);
    }

    @Override
    public ImportResult<ResSite> importData(MultipartFile file, String tenantId, String userName) {
        //点位根结点字段名
        List<String> fieldNameEns = ListUtil.of("basicInfo", "businessInfo");
        // 获取所有需要导入的字段
        List<ResFieldAll> importFields = resFieldAllMapper.selectExportFieldsByParentNames(fieldNameEns);
        // 从Redis缓存获取所有字典数据
        List<SysDict> allDicts = redisService.getCacheList("dict");
        // 调用方法
        ImportResult<ResSite> result = ExcelImportUtils.parseExcel(file, importFields, allDicts, tenantId, userName,
            ResSite.class);
        // 判断结果
        if (!result.isSuccess()) {
            log.error("导入失败，错误数: {}", result.getErrors().size());
            result.getErrors().forEach(error -> log.error("第{}行错误: {}", error.getRowNum(), error.getMessage()));
        }

        // 获取成功数据
        List<ResSite> validSites = result.getSuccessData();
        int batchSize = 1000;
        for (int i = 0; i < validSites.size(); i += batchSize) {
            List<ResSite> batch = validSites.subList(i, Math.min(i + batchSize, validSites.size()));
            resSiteMapper.batchInsertResSite(batch);
        }
        return result;
    }

    /**
     * 查询点位列表
     */
    @Override
    public List<ResSiteDto> findSite(ResSiteDto resSite) {
        return resSiteMapper.findSite(resSite);
    }

    /**
     * 新增和更新方法
     *
     * @param resSite
     * @param loginUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveSite(ResSite resSite, LoginUser loginUser) {
        if (ObjectUtil.isNotEmpty(resSite.getId())) {
            return updateResSite(resSite, loginUser);
        }
        else {
            return insertResSite(resSite, loginUser);
        }
    }

    /**
     * 点位管理 - 点位批量 新增
     *
     * @param listResSite
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSiteInsert(List<ResSite> listResSite, LoginUser loginUser) {
        if (CollUtil.isEmpty(listResSite)) {
            throw new ServiceException("未获取到新增数据");
        }
        List<ResSite> batchList = new ArrayList<>();
        Map<String, String> stteNumMap = new HashMap<>();
        for (ResSite resSite : listResSite) {
            if (StrUtil.isEmpty(resSite.getSiteNumber())) {
                throw new ServiceException("站点编号必须填写");
            }
            String siteNumber = resSite.getSiteNumber();
            if (stteNumMap.containsKey(siteNumber)) {
                throw new ServiceException("当前站点编号已存在,请更换站点编号");
            }
            ResSite dbSite = new ResSite();
            dbSite.setSiteNumber(siteNumber);
            List<ResSite> listStte = resSiteMapper.selectResSiteList(dbSite);
            if (CollUtil.isNotEmpty(listStte)) {
                throw new ServiceException("当前站点编号已存在,请更换站点编号");
            }
            if (StrUtil.isEmpty(resSite.getSiteName())) {
                throw new ServiceException("站点名称必须填写");
            }
            if (StrUtil.isEmpty(resSite.getMonitoringElement())) {
                throw new ServiceException("监测要素必须要填写");
            }
            if (StrUtil.isEmpty(resSite.getTenantId())) {
                resSite.setTenantId("0");
            }
            stteNumMap.put(siteNumber, siteNumber);
            resSite.setCreateTime(DateUtils.getNowDate());
            resSite.setCreateBy(loginUser.getUsername());
            batchList.add(resSite);
        }
        return resSiteMapper.batchInsertResSite(batchList);
    }

    /**
     * 点位管理 - 单个点位详情查询
     */
    @Override
    public ResSiteDto queryDetail(Long id) {
        return resSiteMapper.selectResSiteDtoById(id);
    }

    /**
     * 点位代办已办列表查询
     *
     * @param siteAuditDto
     * @return
     */
    @Override
    public List<SiteAuditResultDto> siteAuditList(SiteAuditDto siteAuditDto) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        siteAuditDto.setUserId(String.valueOf(loginUser.getUserid()));
        return resSiteMapper.siteAuditList(siteAuditDto);
    }

    /**
     * 点位管理-站点统计
     */
    @Override
    public PointStatisticsDto pointStatistics(String monitoringElement) {
        PointStatisticsDto pointStatisticsDto = new PointStatisticsDto();
        //水
        if (monitoringElement.equalsIgnoreCase("water")) {
            Integer waterAutomaticStation = resSiteMapper.countValByMonitoringElement(monitoringElement, null);
            pointStatisticsDto.setWaterAutomaticStation(waterAutomaticStation);
            Integer waterSection = resSiteMapper.countValByMonitoringElement(monitoringElement, "Y");
            pointStatisticsDto.setWaterSection(waterSection);
        }
        //气
        else {
            List<GroupObjectTypeDto> listObject = resSiteMapper.queryValByMonitoringElement(monitoringElement);
            if (CollUtil.isNotEmpty(listObject)) {
                Map<String, Consumer<Integer>> setterMap = new HashMap<>();
                setterMap.put("01", pointStatisticsDto::setAirCityStation);
                setterMap.put("02", pointStatisticsDto::setAirBackgroundStation);
                setterMap.put("03", pointStatisticsDto::setAirAreaStation);
                for (GroupObjectTypeDto groupObjectTypeDto : listObject) {
                    if (StrUtil.isNotEmpty(groupObjectTypeDto.getKey())) {
                        Consumer<Integer> setter = setterMap.get(groupObjectTypeDto.getKey());
                        if (ObjectUtil.isNotEmpty(setter) && ObjectUtil.isNotEmpty(groupObjectTypeDto.getValue())) {
                            setter.accept(groupObjectTypeDto.getValue());
                        }
                    }
                }
            }
        }
        return pointStatisticsDto;
    }

    /**
     * 点位管理-站点运行情况
     */
    @Override
    public List<SiteRunStatisticsDto> siteRunStatistics(String monitoringElement) {
        List<SiteRunStatisticsDto> resultList = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        List<GroupObjectTypeDto> listGroupVal = resSiteMapper.queryOperationByMonitoringElement(monitoringElement);
        if (CollUtil.isNotEmpty(listGroupVal)) {
            map = listGroupVal.stream()
                .collect(Collectors.toMap(GroupObjectTypeDto::getKey, GroupObjectTypeDto::getValue));
        }
        List<SysDict> listDict = sysDictMapper.selectSysDictByClassCode("site_status");
        if (CollUtil.isNotEmpty(listDict)) {
            for (SysDict sysDict : listDict) {
                SiteRunStatisticsDto dto = new SiteRunStatisticsDto();
                dto.setSiteStatus(sysDict.getDictCode());
                dto.setSiteStatusName(sysDict.getDictValue());
                if (map.containsKey(sysDict.getDictCode())) {
                    dto.setNum(map.get(sysDict.getDictCode()));
                }
                resultList.add(dto);
            }
        }
        return resultList;
    }
}
