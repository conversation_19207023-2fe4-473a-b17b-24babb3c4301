package com.mes.resource.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mes.common.core.exception.ServiceException;
import com.mes.common.core.utils.DateUtils;
import com.mes.resource.config.aspect.OperationLogEndpoint;
import com.mes.resource.domain.ResAcceptanceDeviceRel;
import com.mes.resource.enums.OperationTypeEnum;
import com.mes.resource.mapper.ResAcceptanceDeviceRelMapper;
import com.mes.resource.service.IResAcceptanceDeviceRelService;
import com.mes.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 验收点与设备的关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class ResAcceptanceDeviceRelServiceImpl implements IResAcceptanceDeviceRelService {
    @Autowired
    private ResAcceptanceDeviceRelMapper resAcceptanceDeviceRelMapper;

    /**
     * 查询验收点与设备的关联关系
     *
     * @param id 验收点与设备的关联关系主键
     * @return 验收点与设备的关联关系
     */
    @Override
    public ResAcceptanceDeviceRel selectResAcceptanceDeviceRelById(Long id) {
        ResAcceptanceDeviceRel acceptanceDeviceRel = resAcceptanceDeviceRelMapper.selectResAcceptanceDeviceRelById(id);
        if (ObjectUtil.isEmpty(acceptanceDeviceRel)) {
            throw new ServiceException("验收点与设备的关联关系不存在");
        }
        return acceptanceDeviceRel;
    }

    /**
     * 查询验收点与设备的关联关系列表
     *
     * @param resAcceptanceDeviceRel 验收点与设备的关联关系
     * @return 验收点与设备的关联关系
     */
    @Override
    public List<ResAcceptanceDeviceRel> selectResAcceptanceDeviceRelList(
        ResAcceptanceDeviceRel resAcceptanceDeviceRel) {
        return resAcceptanceDeviceRelMapper.selectResAcceptanceDeviceRelList(resAcceptanceDeviceRel);
    }

    /**
     * 新增验收点与设备的关联关系
     *
     * @param resAcceptanceDeviceRel 验收点与设备的关联关系
     * @return 结果
     */
    @Override
    @OperationLogEndpoint(module = "验收点与设备的关联-新增关系", operationType = OperationTypeEnum.CREATE,
        operationContent = "验收点与设备的关联-新增关系")
    @Transactional(rollbackFor = Exception.class)
    public int insertResAcceptanceDeviceRel(ResAcceptanceDeviceRel resAcceptanceDeviceRel, LoginUser loginUser) {
        if (ObjectUtil.isEmpty(resAcceptanceDeviceRel.getAcceptanceId())) {
            throw new ServiceException("验收点ID必须填写");
        }
        if (ObjectUtil.isEmpty(resAcceptanceDeviceRel.getDeviceId())) {
            throw new ServiceException("设备ID必须填写");
        }
        //删除设备关系
        resAcceptanceDeviceRelMapper.deleteResAcceptanceDeviceRelByDeviceId(resAcceptanceDeviceRel.getDeviceId());
        resAcceptanceDeviceRel.setCreateTime(DateUtils.getNowDate());
        resAcceptanceDeviceRel.setCreatedBy(loginUser.getUsername());
        return resAcceptanceDeviceRelMapper.insertResAcceptanceDeviceRel(resAcceptanceDeviceRel);
    }

    /**
     * 批量新增验收点与设备的关联关系
     *
     * @param resAcceptanceDeviceRelList 验收点与设备的关联关系List
     * @return 结果
     */
    @Override
    public int batchInsertResAcceptanceDeviceRel(List<ResAcceptanceDeviceRel> resAcceptanceDeviceRelList) {
        for (ResAcceptanceDeviceRel resAcceptanceDeviceRel : resAcceptanceDeviceRelList) {
            resAcceptanceDeviceRel.setCreateTime(DateUtils.getNowDate());
        }
        return resAcceptanceDeviceRelMapper.batchInsertResAcceptanceDeviceRel(resAcceptanceDeviceRelList);
    }

    /**
     * 修改验收点与设备的关联关系
     *
     * @param resAcceptanceDeviceRel 验收点与设备的关联关系
     * @return 结果
     */
    @Override
    public int updateResAcceptanceDeviceRel(ResAcceptanceDeviceRel resAcceptanceDeviceRel) {
        resAcceptanceDeviceRel.setUpdateTime(DateUtils.getNowDate());
        return resAcceptanceDeviceRelMapper.updateResAcceptanceDeviceRel(resAcceptanceDeviceRel);
    }

    /**
     * 批量修改验收点与设备的关联关系
     *
     * @param resAcceptanceDeviceRelList 验收点与设备的关联关系List
     * @return 结果
     */
    @Override
    public int batchUpdateResAcceptanceDeviceRel(List<ResAcceptanceDeviceRel> resAcceptanceDeviceRelList) {
        return resAcceptanceDeviceRelMapper.batchUpdateResAcceptanceDeviceRel(resAcceptanceDeviceRelList);
    }

    /**
     * 批量删除验收点与设备的关联关系
     *
     * @param ids 需要删除的验收点与设备的关联关系主键
     * @return 结果
     */
    @Override
    public int deleteResAcceptanceDeviceRelByIds(Long[] ids) {
        return resAcceptanceDeviceRelMapper.deleteResAcceptanceDeviceRelByIds(ids);
    }

    /**
     * 删除验收点与设备的关联关系信息
     *
     * @param id 验收点与设备的关联关系主键
     * @return 结果
     */
    @Override
    public int deleteResAcceptanceDeviceRelById(Long id) {
        return resAcceptanceDeviceRelMapper.deleteResAcceptanceDeviceRelById(id);
    }

    /**
     * 保存验收点与设备的关联关系
     *
     * @param list 验收点与设备的关联关系List
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAcceptanceDevice(List<ResAcceptanceDeviceRel> list, LoginUser loginUser) {
        int index = 0;
        if (CollUtil.isNotEmpty(list)) {
            Long acceptanceId = list.get(0).getAcceptanceId();
            //删除验收地点关系的设备关系 然后全部新增
            resAcceptanceDeviceRelMapper.deleteResAcceptanceDeviceRelByAcceptanceId(acceptanceId);
            for (ResAcceptanceDeviceRel resAcceptanceDeviceRel : list) {
                resAcceptanceDeviceRel.setId(null);
                insertResAcceptanceDeviceRel(resAcceptanceDeviceRel, loginUser);
            }
        }
        return index;
    }
}
