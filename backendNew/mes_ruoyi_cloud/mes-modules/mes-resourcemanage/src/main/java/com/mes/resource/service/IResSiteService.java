package com.mes.resource.service;

import com.mes.resource.domain.ResSite;
import com.mes.resource.domain.dto.PointStatisticsDto;
import com.mes.resource.domain.dto.ResSiteDto;
import com.mes.resource.domain.dto.SiteAuditDto;
import com.mes.resource.domain.dto.SiteAuditResultDto;
import com.mes.resource.domain.dto.SiteRunStatisticsDto;
import com.mes.resource.domain.vo.ImportResult;
import com.mes.system.api.model.LoginUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 点位Service接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IResSiteService {
    /**
     * 查询点位
     *
     * @param id 点位主键
     * @return 点位
     */
    public ResSite selectResSiteById(Long id);

    /**
     * 查询点位列表
     *
     * @param resSite 点位
     * @return 点位集合
     */
    public List<ResSite> selectResSiteList(ResSite resSite);

    /**
     * 新增点位
     *
     * @param resSite 点位
     * @return 结果
     */
    public int insertResSite(ResSite resSite, LoginUser loginUser);

    /**
     * 修改点位
     *
     * @param resSite 点位
     * @return 结果
     */
    public int updateResSite(ResSite resSite, LoginUser loginUser);

    /**
     * 批量删除点位
     *
     * @param ids 需要删除的点位主键集合
     * @return 结果
     */
    public int deleteResSiteByIds(Long[] ids);

    /**
     * 删除点位信息
     *
     * @param id 点位主键
     * @return 结果
     */
    public int deleteResSiteById(Long id);

    /**
     * 批量导入点位数据
     *
     * @param file
     * @param tenantId
     * @param userName
     * @return
     */
    ImportResult<ResSite> importData(MultipartFile file, String tenantId, String userName);

    /**
     * 查询点位列表
     */
    List<ResSiteDto> findSite(ResSiteDto resSite);

    /**
     * 新增和更新方法
     *
     * @param resSite
     * @param loginUser
     * @return
     */
    int saveSite(ResSite resSite, LoginUser loginUser);

    /**
     * 点位管理 - 点位批量 新增
     *
     * @param listResSite
     * @return
     */
    int batchSiteInsert(List<ResSite> listResSite, LoginUser loginUser);
    /**
     * 点位管理 - 单个点位详情查询
     *
     */
    ResSiteDto queryDetail(Long id);

    /**
     * 点位管理-代办已办列表查询
     */
    List<SiteAuditResultDto> siteAuditList(SiteAuditDto siteAuditDto);
    /**
     * 点位管理-站点统计
     */
    PointStatisticsDto pointStatistics(String monitoringElement);
    /**
     * 点位管理-站点运行情况
     */
    List<SiteRunStatisticsDto> siteRunStatistics(String monitoringElement);
}
