package com.mes.resource.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 耗材入库对象 tb_res_supplies_inbound
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public class ResSuppliesInbound extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 入库ID */
    private Long inboundId;

    /** 监测要素(字典) */
    @Excel(name = "监测要素(字典)")
    private String monitoringElement;

    /** 耗材分类 */
    @Excel(name = "耗材分类")
    private String suppliesType;

    /** 耗材类型(字典) */
    @Excel(name = "耗材类型(字典)")
    private String category;

    /** 物资编码 */
    @Excel(name = "物资编码")
    private String suppliesCode;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String name;

    /** 设备厂商id */
    @Excel(name = "设备厂商id")
    private Long manufacturerId;

    /** 耗材型号 */
    @Excel(name = "耗材型号")
    private String suppliesModel;

    /** 技术指标(富文本) */
    @Excel(name = "技术指标(富文本)")
    private String technicalSpec;

    /** 所属仓库ID */
    @Excel(name = "所属仓库ID")
    private String warehouseId;

    /** 浓度值 */
    @Excel(name = "浓度值")
    private String concentrationValue;

    /** 入库时间 */
    @Excel(name = "入库时间")
    private String stockTime;

    /** 入库量 */
    @Excel(name = "入库量")
    private Integer stockInQuantity;

    /** 实时库存量 */
    @Excel(name = "实时库存量")
    private Integer currentStock;

    /** 库存单位 */
    @Excel(name = "库存单位")
    private String stockUnit;

    /** 采购合同 */
    @Excel(name = "采购合同")
    private String purchaseContract;

    /** 有效期至 */
    @Excel(name = "有效期至")
    private String expirationDate;

    /** 是否检验(Y/N) */
    @Excel(name = "是否检验(Y/N)")
    private String isInspected;

    /** 检验时间 */
    @Excel(name = "检验时间")
    private String inspectionTime;

    /** 检验人 */
    @Excel(name = "检验人")
    private String inspectionUserId;

    /** 当前状态(字典) */
    @Excel(name = "当前状态(字典)")
    private String currentStatus;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public void setInboundId(Long inboundId) {
        this.inboundId = inboundId;
    }

    public Long getInboundId() {
        return inboundId;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setSuppliesType(String suppliesType) {
        this.suppliesType = suppliesType;
    }

    public String getSuppliesType() {
        return suppliesType;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategory() {
        return category;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setManufacturerId(Long manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public Long getManufacturerId() {
        return manufacturerId;
    }

    public void setSuppliesModel(String suppliesModel) {
        this.suppliesModel = suppliesModel;
    }

    public String getSuppliesModel() {
        return suppliesModel;
    }

    public void setTechnicalSpec(String technicalSpec) {
        this.technicalSpec = technicalSpec;
    }

    public String getTechnicalSpec() {
        return technicalSpec;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setConcentrationValue(String concentrationValue) {
        this.concentrationValue = concentrationValue;
    }

    public String getConcentrationValue() {
        return concentrationValue;
    }

    public void setStockTime(String stockTime) {
        this.stockTime = stockTime;
    }

    public String getStockTime() {
        return stockTime;
    }

    public void setStockInQuantity(Integer stockInQuantity) {
        this.stockInQuantity = stockInQuantity;
    }

    public Integer getStockInQuantity() {
        return stockInQuantity;
    }

    public void setCurrentStock(Integer currentStock) {
        this.currentStock = currentStock;
    }

    public Integer getCurrentStock() {
        return currentStock;
    }

    public void setStockUnit(String stockUnit) {
        this.stockUnit = stockUnit;
    }

    public String getStockUnit() {
        return stockUnit;
    }

    public void setPurchaseContract(String purchaseContract) {
        this.purchaseContract = purchaseContract;
    }

    public String getPurchaseContract() {
        return purchaseContract;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setIsInspected(String isInspected) {
        this.isInspected = isInspected;
    }

    public String getIsInspected() {
        return isInspected;
    }

    public void setInspectionTime(String inspectionTime) {
        this.inspectionTime = inspectionTime;
    }

    public String getInspectionTime() {
        return inspectionTime;
    }

    public void setInspectionUserId(String inspectionUserId) {
        this.inspectionUserId = inspectionUserId;
    }

    public String getInspectionUserId() {
        return inspectionUserId;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("inboundId", getInboundId())
            .append("monitoringElement", getMonitoringElement()).append("suppliesType", getSuppliesType())
            .append("category", getCategory()).append("suppliesCode", getSuppliesCode()).append("name", getName())
            .append("manufacturerId", getManufacturerId()).append("suppliesModel", getSuppliesModel())
            .append("technicalSpec", getTechnicalSpec()).append("warehouseId", getWarehouseId())
            .append("concentrationValue", getConcentrationValue()).append("stockTime", getStockTime())
            .append("stockInQuantity", getStockInQuantity()).append("currentStock", getCurrentStock())
            .append("stockUnit", getStockUnit()).append("purchaseContract", getPurchaseContract())
            .append("expirationDate", getExpirationDate()).append("isInspected", getIsInspected())
            .append("inspectionTime", getInspectionTime()).append("inspectionUserId", getInspectionUserId())
            .append("currentStatus", getCurrentStatus()).append("tenantId", getTenantId())
            .append("createdBy", getCreatedBy()).append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy()).append("updateTime", getUpdateTime()).toString();
    }
}
