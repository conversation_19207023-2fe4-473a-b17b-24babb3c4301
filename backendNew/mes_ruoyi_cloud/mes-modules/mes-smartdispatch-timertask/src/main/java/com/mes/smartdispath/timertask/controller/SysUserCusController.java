package com.mes.smartdispath.timertask.controller;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.system.api.domain.SysUser;
import com.mes.smartdispath.timertask.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.timertask.domain.SysUserCus;
import com.mes.smartdispath.timertask.domain.dto.MonitorQualityResultQueryDTO;
import com.mes.smartdispath.timertask.service.IScheduMonitorQualityResultService;
import com.mes.smartdispath.timertask.service.ISysUserServiceCus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/userTest")
public class SysUserCusController extends BaseController {
    @Autowired
    private ISysUserServiceCus userServiceCus;

    @Autowired
    private IScheduMonitorQualityResultService scheduMonitorQualityResultService;

    /**
     * 获取用户列表
     */
    @PostMapping("/listTest")
    public TableDataInfo list(@RequestBody SysUserCus user) {
        startPage();
//        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        List<SysUserCus> list = userServiceCus.selectUserList(user);
        return getDataTable(list);
    }

    // ==================== 质量计算结果相关接口 ====================

    /**
     * 查询质量计算结果列表
     */
    @PostMapping("/qualityResult/list")
    public TableDataInfo listQualityResult(@RequestBody ScheduMonitorQualityResult scheduMonitorQualityResult) {
        startPage();
        List<ScheduMonitorQualityResult> list = scheduMonitorQualityResultService.selectScheduMonitorQualityResultList(scheduMonitorQualityResult);
        return getDataTable(list);
    }

    /**
     * 查询汇总的质量计算结果列表
     */
    @PostMapping("/qualityResult/collectList")
    public TableDataInfo collectQualityResultList(@RequestBody MonitorQualityResultQueryDTO queryDTO) {
        startPage();
        List<ScheduMonitorQualityResult> list = scheduMonitorQualityResultService.selectCollectMonitorQualityResultList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取质量计算结果详细信息
     */
    @GetMapping("/qualityResult/{id}")
    public AjaxResult getQualityResultInfo(@PathVariable("id") String id) {
        return success(scheduMonitorQualityResultService.selectScheduMonitorQualityResultById(id));
    }

    /**
     * 新增质量计算结果
     */
    @PostMapping("/qualityResult/add")
    public AjaxResult addQualityResult(@Validated @RequestBody ScheduMonitorQualityResult scheduMonitorQualityResult) {
        // 设置创建人信息
        try {
            SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
            scheduMonitorQualityResult.setCreateBy(sysUser.getUserName());
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
            scheduMonitorQualityResult.setCreateBy("system");
        }

        // 设置租户ID为默认值
        scheduMonitorQualityResult.setTenantId("0");

        // 设置默认状态为有效
        if (scheduMonitorQualityResult.getStatus() == null || scheduMonitorQualityResult.getStatus().isEmpty()) {
            scheduMonitorQualityResult.setStatus("A");
        }

        return toAjax(scheduMonitorQualityResultService.insertScheduMonitorQualityResult(scheduMonitorQualityResult));
    }

    /**
     * 批量新增质量计算结果
     */
    @PostMapping("/qualityResult/batchAdd")
    public AjaxResult batchAddQualityResult(@Validated @RequestBody List<ScheduMonitorQualityResult> scheduMonitorQualityResultList) {
        if (scheduMonitorQualityResultList == null || scheduMonitorQualityResultList.isEmpty()) {
            return error("批量新增数据不能为空");
        }

        // 设置创建人信息
        String createBy = "system";
        try {
            SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
            createBy = sysUser.getUserName();
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
        }

        // 为每个对象设置创建人和状态
        for (ScheduMonitorQualityResult result : scheduMonitorQualityResultList) {
            result.setCreateBy(createBy);
            // 设置租户ID为默认值
            result.setTenantId("0");
            // 设置默认状态为有效
            if (result.getStatus() == null || result.getStatus().isEmpty()) {
                result.setStatus("A");
            }
        }

        return toAjax(scheduMonitorQualityResultService.batchInsertScheduMonitorQualityResult(scheduMonitorQualityResultList));
    }

    /**
     * 修改质量计算结果
     */
    @PutMapping("/qualityResult/edit")
    public AjaxResult editQualityResult(@Validated @RequestBody ScheduMonitorQualityResult scheduMonitorQualityResult) {
        // 设置更新人信息
        try {
            SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
            scheduMonitorQualityResult.setUpdateBy(sysUser.getUserName());
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
            scheduMonitorQualityResult.setUpdateBy("system");
        }

        return toAjax(scheduMonitorQualityResultService.updateScheduMonitorQualityResult(scheduMonitorQualityResult));
    }

    /**
     * 批量修改质量计算结果
     */
    @PutMapping("/qualityResult/batchEdit")
    public AjaxResult batchEditQualityResult(@Validated @RequestBody List<ScheduMonitorQualityResult> scheduMonitorQualityResultList) {
        if (scheduMonitorQualityResultList == null || scheduMonitorQualityResultList.isEmpty()) {
            return error("批量修改数据不能为空");
        }

        // 设置更新人信息
        String updateBy = "system";
        try {
            SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
            updateBy = sysUser.getUserName();
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
        }

        // 为每个对象设置更新人
        for (ScheduMonitorQualityResult result : scheduMonitorQualityResultList) {
            result.setUpdateBy(updateBy);
        }

        return toAjax(scheduMonitorQualityResultService.batchUpdateScheduMonitorQualityResult(scheduMonitorQualityResultList));
    }

    /**
     * 删除质量计算结果
     */
    @DeleteMapping("/qualityResult/{ids}")
    public AjaxResult removeQualityResult(@PathVariable String[] ids) {
        return toAjax(scheduMonitorQualityResultService.deleteScheduMonitorQualityResultByIds(ids));
    }

    /**
     * 按照业务类型和年度汇总-查询汇总的质量计算结果列表
     */
    @PostMapping("/qualityResult/statYearCollectList")
    public TableDataInfo statYearCollectQualityResultList(@RequestBody MonitorQualityResultQueryDTO queryDTO) {
        startPage();
        List<ScheduMonitorQualityResult> list = scheduMonitorQualityResultService.selectStatYearCollectMonitorQualityResultList(queryDTO);
        return getDataTable(list);
    }

}
