package com.mes.smartdispath.timertask.service;

import com.mes.smartdispath.timertask.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.timertask.domain.dto.MonitorQualityResultQueryDTO;

import java.util.List;

/**
 * 质量计算结果Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IScheduMonitorQualityResultService {
    /**
     * 查询质量计算结果
     *
     * @param id 质量计算结果主键
     * @return 质量计算结果
     */
    public ScheduMonitorQualityResult selectScheduMonitorQualityResultById(String id);

    /**
     * 查询质量计算结果列表
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectScheduMonitorQualityResultList(ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 查询汇总的质量计算结果列表
     *
     * @param queryDTO 查询条件
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectCollectMonitorQualityResultList(MonitorQualityResultQueryDTO queryDTO);

    /**
     * 按照业务类型和年度汇总-查询汇总的质量计算结果列表
     *
     * @param queryDTO 查询条件
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectStatYearCollectMonitorQualityResultList(MonitorQualityResultQueryDTO queryDTO);

    /**
     * 新增质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    public int insertScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 批量新增质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    public int batchInsertScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList);

    /**
     * 修改质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    public int updateScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 批量修改质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    public int batchUpdateScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList);

    /**
     * 批量删除质量计算结果
     *
     * @param ids 需要删除的质量计算结果主键集合
     * @return 结果
     */
    public int deleteScheduMonitorQualityResultByIds(String[] ids);

    /**
     * 删除质量计算结果信息
     *
     * @param id 质量计算结果主键
     * @return 结果
     */
    public int deleteScheduMonitorQualityResultById(String id);
}
