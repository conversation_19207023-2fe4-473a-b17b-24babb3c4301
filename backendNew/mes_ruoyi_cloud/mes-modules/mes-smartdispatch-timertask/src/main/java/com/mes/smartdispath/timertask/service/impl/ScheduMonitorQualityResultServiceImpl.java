package com.mes.smartdispath.timertask.service.impl;

import com.mes.common.core.utils.DateUtils;
import com.mes.common.core.utils.uuid.IdUtils;
import com.mes.smartdispath.timertask.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.timertask.domain.dto.MonitorQualityResultQueryDTO;
import com.mes.smartdispath.timertask.mapper.primary.ScheduMonitorQualityResultMapper;
import com.mes.smartdispath.timertask.service.IScheduMonitorQualityResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 质量计算结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class ScheduMonitorQualityResultServiceImpl implements IScheduMonitorQualityResultService {
    @Autowired
    private ScheduMonitorQualityResultMapper scheduMonitorQualityResultMapper;

    /**
     * 查询质量计算结果
     *
     * @param id 质量计算结果主键
     * @return 质量计算结果
     */
    @Override
    public ScheduMonitorQualityResult selectScheduMonitorQualityResultById(String id) {
        return scheduMonitorQualityResultMapper.selectScheduMonitorQualityResultById(id);
    }

    /**
     * 查询质量计算结果列表
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 质量计算结果
     */
    @Override
    public List<ScheduMonitorQualityResult> selectScheduMonitorQualityResultList(ScheduMonitorQualityResult scheduMonitorQualityResult) {
        return scheduMonitorQualityResultMapper.selectScheduMonitorQualityResultList(scheduMonitorQualityResult);
    }

    /**
     * 查询汇总的质量计算结果列表
     *
     * @param queryDTO 查询条件
     * @return 质量计算结果集合
     */
    @Override
    public List<ScheduMonitorQualityResult> selectCollectMonitorQualityResultList(MonitorQualityResultQueryDTO queryDTO) {
        return scheduMonitorQualityResultMapper.selectCollectMonitorQualityResultList(queryDTO);
    }

    /**
     * 按照业务类型和年度汇总-查询汇总的质量计算结果列表
     *
     * @param queryDTO 查询条件
     * @return 质量计算结果集合
     */
    @Override
    public List<ScheduMonitorQualityResult> selectStatYearCollectMonitorQualityResultList(MonitorQualityResultQueryDTO queryDTO) {
        return scheduMonitorQualityResultMapper.selectStatYearCollectMonitorQualityResultList(queryDTO);
    }

    /**
     * 新增质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    @Override
    public int insertScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult) {
        // 设置主键ID
        if (scheduMonitorQualityResult.getId() == null || scheduMonitorQualityResult.getId().isEmpty()) {
            scheduMonitorQualityResult.setId(IdUtils.fastUUID());
        }
        // 设置创建时间
        scheduMonitorQualityResult.setCreateTime(DateUtils.getNowDate());
        return scheduMonitorQualityResultMapper.insertScheduMonitorQualityResult(scheduMonitorQualityResult);
    }

    /**
     * 批量新增质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    @Override
    public int batchInsertScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList) {
        // 为每个对象设置ID和创建时间
        for (ScheduMonitorQualityResult result : scheduMonitorQualityResultList) {
            if (result.getId() == null || result.getId().isEmpty()) {
                result.setId(IdUtils.fastUUID());
            }
            if (result.getCreateTime() == null) {
                result.setCreateTime(DateUtils.getNowDate());
            }
        }
        return scheduMonitorQualityResultMapper.batchInsertScheduMonitorQualityResult(scheduMonitorQualityResultList);
    }

    /**
     * 修改质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    @Override
    public int updateScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult) {
        scheduMonitorQualityResult.setUpdateTime(DateUtils.getNowDate());
        return scheduMonitorQualityResultMapper.updateScheduMonitorQualityResult(scheduMonitorQualityResult);
    }

    /**
     * 批量修改质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    @Override
    public int batchUpdateScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList) {
        // 为每个对象设置更新时间
        for (ScheduMonitorQualityResult result : scheduMonitorQualityResultList) {
            result.setUpdateTime(DateUtils.getNowDate());
        }
        return scheduMonitorQualityResultMapper.batchUpdateScheduMonitorQualityResult(scheduMonitorQualityResultList);
    }

    /**
     * 批量删除质量计算结果
     *
     * @param ids 需要删除的质量计算结果主键
     * @return 结果
     */
    @Override
    public int deleteScheduMonitorQualityResultByIds(String[] ids) {
        return scheduMonitorQualityResultMapper.deleteScheduMonitorQualityResultByIds(ids);
    }

    /**
     * 删除质量计算结果信息
     *
     * @param id 质量计算结果主键
     * @return 结果
     */
    @Override
    public int deleteScheduMonitorQualityResultById(String id) {
        return scheduMonitorQualityResultMapper.deleteScheduMonitorQualityResultById(id);
    }
}
