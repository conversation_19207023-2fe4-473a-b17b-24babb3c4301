<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.dataaccess.mapper.AirQcManualDataMapper">

    <resultMap type="com.mes.dataaccess.entity.AirQcManualData" id="AirQcManualDataMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="stationCode" column="station_code" jdbcType="VARCHAR"/>
        <result property="monitorTime" column="monitor_time" jdbcType="TIMESTAMP"/>
        <result property="monitorName" column="monitor_name" jdbcType="VARCHAR"/>
        <result property="monitorValue" column="monitor_value" jdbcType="VARCHAR"/>
        <result property="dataFlag" column="data_flag" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_air_qc_manual_data(station_code,monitor_time,monitor_name,monitor_value,data_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.stationCode},#{entity.monitorTime},#{entity.monitorName},#{entity.monitorValue},#{entity.dataFlag})
        </foreach>
    </insert>

    <!--查询列表数据-->
    <select id="selectList" resultMap="AirQcManualDataMap">
        select
        id, station_code, monitor_time, monitor_name, monitor_value, data_flag, create_time
        from tb_air_qc_manual_data
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="stationCode != null and stationCode != ''">
                and station_code = #{stationCode}
            </if>
            <if test="startTime != null">
                and monitor_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and monitor_time &lt;= #{endTime}
            </if>
            <if test="monitorName != null and monitorName != ''">
                and monitor_name = #{monitorName}
            </if>
            <if test="monitorValue != null">
                and monitor_value = #{monitorValue}
            </if>
            <if test="dataFlag != null and dataFlag != ''">
                and data_flag = #{dataFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>

