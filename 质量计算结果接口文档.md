# 质量计算结果接口文档

## 概述
在 `SysUserCusController` 类中添加了完整的 `tb_schedu_monitor_quality_result` 表操作接口，包含增删改查等功能。

## 已创建的文件

### 1. Domain 实体类
- **文件路径**: `ScheduMonitorQualityResult.java` (已存在)
- **作用**: 质量计算结果实体类，对应 `tb_schedu_monitor_quality_result` 表

### 2. DTO 查询对象
- **文件路径**: `MonitorQualityResultQueryDTO.java` (已存在)
- **作用**: 质量结果查询条件封装类

### 3. Mapper 接口
- **文件路径**: `ScheduMonitorQualityResultMapper.java` (已存在)
- **作用**: 数据访问层接口，定义数据库操作方法

### 4. Mapper XML
- **文件路径**: `ScheduMonitorQualityResultMapper.xml` (已存在)
- **作用**: MyBatis SQL映射文件

### 5. Service 接口 (新创建)
- **文件路径**: `IScheduMonitorQualityResultService.java`
- **作用**: 业务逻辑层接口

### 6. Service 实现类 (新创建)
- **文件路径**: `ScheduMonitorQualityResultServiceImpl.java`
- **作用**: 业务逻辑层实现

### 7. Controller 控制器 (已修改)
- **文件路径**: `SysUserCusController.java`
- **作用**: 控制层，提供REST API接口

## API 接口列表

### 1. 查询质量计算结果列表
- **URL**: `POST /userTest/qualityResult/list`
- **描述**: 分页查询质量计算结果列表
- **请求体**: `ScheduMonitorQualityResult` 对象
- **返回**: `TableDataInfo` 分页数据

### 2. 查询汇总的质量计算结果列表
- **URL**: `POST /userTest/qualityResult/collectList`
- **描述**: 分页查询汇总的质量计算结果
- **请求体**: `MonitorQualityResultQueryDTO` 对象
- **返回**: `TableDataInfo` 分页数据

### 3. 获取质量计算结果详细信息
- **URL**: `GET /userTest/qualityResult/{id}`
- **描述**: 根据ID获取单条记录详情
- **路径参数**: `id` - 记录ID
- **返回**: `AjaxResult` 包含详细信息

### 4. 新增质量计算结果 ⭐
- **URL**: `POST /userTest/qualityResult/add`
- **描述**: 新增单条质量计算结果记录
- **请求体**: `ScheduMonitorQualityResult` 对象
- **返回**: `AjaxResult` 操作结果

### 5. 批量新增质量计算结果 ⭐
- **URL**: `POST /userTest/qualityResult/batchAdd`
- **描述**: 批量新增质量计算结果记录
- **请求体**: `List<ScheduMonitorQualityResult>` 对象数组
- **返回**: `AjaxResult` 操作结果

### 6. 修改质量计算结果
- **URL**: `PUT /userTest/qualityResult/edit`
- **描述**: 修改质量计算结果记录
- **请求体**: `ScheduMonitorQualityResult` 对象
- **返回**: `AjaxResult` 操作结果

### 7. 批量修改质量计算结果
- **URL**: `PUT /userTest/qualityResult/batchEdit`
- **描述**: 批量修改质量计算结果记录
- **请求体**: `List<ScheduMonitorQualityResult>` 对象数组
- **返回**: `AjaxResult` 操作结果

### 8. 删除质量计算结果
- **URL**: `DELETE /userTest/qualityResult/{ids}`
- **描述**: 批量删除质量计算结果记录
- **路径参数**: `ids` - 记录ID数组，用逗号分隔
- **返回**: `AjaxResult` 操作结果

### 9. 按业务类型和年度汇总查询
- **URL**: `POST /userTest/qualityResult/statYearCollectList`
- **描述**: 按照业务类型和年度汇总查询
- **请求体**: `MonitorQualityResultQueryDTO` 对象
- **返回**: `TableDataInfo` 分页数据

## 请求示例

### 新增质量计算结果示例
```json
POST /userTest/qualityResult/add
Content-Type: application/json

{
    "provinceCode": "110000",
    "cityCode": "110100",
    "siteId": "SITE001",
    "siteCode": "WATER_MONITOR",
    "businessType": "WATER",
    "monitIndex": "PH",
    "accuracy": "95.5",
    "precision": "98.2",
    "effectivenessRate": "99.1",
    "captureRate": "97.8",
    "quactrlPassRate": "96.5",
    "tp": "day",
    "dt": "2025-07-19",
    "statYear": "2025",
    "status": "A"
}
```

### 批量新增示例
```json
POST /userTest/qualityResult/batchAdd
Content-Type: application/json

[
    {
        "provinceCode": "110000",
        "cityCode": "110100",
        "siteId": "SITE001",
        "businessType": "WATER",
        "monitIndex": "PH",
        "accuracy": "95.5",
        "precision": "98.2",
        "tp": "day",
        "dt": "2025-07-19",
        "statYear": "2025"
    },
    {
        "provinceCode": "110000",
        "cityCode": "110100",
        "siteId": "SITE002",
        "businessType": "AIR",
        "monitIndex": "PM2.5",
        "accuracy": "94.8",
        "precision": "97.1",
        "tp": "day",
        "dt": "2025-07-19",
        "statYear": "2025"
    }
]
```

## 特性说明

### 1. 自动字段填充
- **ID**: 自动生成UUID
- **创建时间**: 自动设置当前时间
- **更新时间**: 修改时自动设置当前时间
- **创建人/更新人**: 自动获取当前登录用户

### 2. 数据验证
- 使用 `@Validated` 注解进行参数验证
- 批量操作时检查数据是否为空

### 3. 异常处理
- 获取用户信息失败时使用默认值
- 提供友好的错误提示

### 4. 状态管理
- 默认状态设置为 "A"（有效）
- 支持状态筛选查询

## 数据库表结构
`tb_schedu_monitor_quality_result` 表包含以下主要字段：
- `id`: 主键ID
- `province_code`: 省行政区域代码
- `city_code`: 市行政区域代码
- `site_id`: 站点ID
- `business_type`: 业务分类
- `monit_index`: 监测参数
- `accuracy`: 准确度
- `precision`: 精密度
- `effectiveness_rate`: 有效率
- `capture_rate`: 获取率
- `quactrl_pass_rate`: 质控合格率
- `tp`: 时间粒度
- `dt`: 统计日期
- `stat_year`: 统计年度
- `status`: 状态
- `tenant_id`: 租户ID
- `create_time`: 创建时间
- `update_time`: 更新时间
- `create_by`: 创建人
- `update_by`: 更新人

## 注意事项
1. 所有接口都支持分页查询
2. 批量操作建议单次不超过1000条记录
3. 删除操作为物理删除，请谨慎使用
4. 建议在生产环境中添加更严格的权限控制
